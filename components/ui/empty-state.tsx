import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { LucideIcon } from 'lucide-react'

interface EmptyStateProps {
  icon?: LucideIcon
  title: string
  description?: string
  action?: {
    label: string
    onClick: () => void
    variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive'
  }
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

export function EmptyState({
  icon: Icon,
  title,
  description,
  action,
  className,
  size = 'md'
}: EmptyStateProps) {
  const sizeClasses = {
    sm: {
      container: 'py-8',
      icon: 'w-8 h-8',
      title: 'text-lg',
      description: 'text-sm'
    },
    md: {
      container: 'py-12',
      icon: 'w-12 h-12',
      title: 'text-xl',
      description: 'text-base'
    },
    lg: {
      container: 'py-16',
      icon: 'w-16 h-16',
      title: 'text-2xl',
      description: 'text-lg'
    }
  }

  const classes = sizeClasses[size]

  return (
    <div className={cn('flex flex-col items-center justify-center text-center', classes.container, className)}>
      {Icon && (
        <Icon className={cn('text-muted-foreground mb-4', classes.icon)} />
      )}
      <h3 className={cn('font-semibold text-foreground mb-2', classes.title)}>
        {title}
      </h3>
      {description && (
        <p className={cn('text-muted-foreground mb-6 max-w-md', classes.description)}>
          {description}
        </p>
      )}
      {action && (
        <Button
          variant={action.variant || 'default'}
          onClick={action.onClick}
        >
          {action.label}
        </Button>
      )}
    </div>
  )
}

interface TableEmptyStateProps {
  icon?: LucideIcon
  title: string
  description?: string
  action?: {
    label: string
    onClick: () => void
    variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive'
  }
  colSpan: number
}

export function TableEmptyState({
  icon: Icon,
  title,
  description,
  action,
  colSpan
}: TableEmptyStateProps) {
  return (
    <tr>
      <td colSpan={colSpan} className="text-center py-12">
        <EmptyState
          icon={Icon}
          title={title}
          description={description}
          action={action}
          size="md"
        />
      </td>
    </tr>
  )
}

interface CardEmptyStateProps {
  icon?: LucideIcon
  title: string
  description?: string
  action?: {
    label: string
    onClick: () => void
    variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive'
  }
  className?: string
}

export function CardEmptyState({
  icon: Icon,
  title,
  description,
  action,
  className
}: CardEmptyStateProps) {
  return (
    <Card className={className}>
      <CardContent>
        <EmptyState
          icon={Icon}
          title={title}
          description={description}
          action={action}
          size="md"
        />
      </CardContent>
    </Card>
  )
}

// Specific empty states for common scenarios
export function NoDataFound({
  entity,
  action,
  className
}: {
  entity: string
  action?: {
    label: string
    onClick: () => void
  }
  className?: string
}) {
  return (
    <EmptyState
      title={`No ${entity} found`}
      description={`There are currently no ${entity.toLowerCase()} to display. ${action ? `Get started by creating your first ${entity.toLowerCase()}.` : ''}`}
      action={action}
      className={className}
    />
  )
}

export function SearchNoResults({
  searchTerm,
  onClearSearch,
  className
}: {
  searchTerm: string
  onClearSearch: () => void
  className?: string
}) {
  return (
    <EmptyState
      title="No results found"
      description={`No results found for "${searchTerm}". Try adjusting your search terms or filters.`}
      action={{
        label: 'Clear search',
        onClick: onClearSearch,
        variant: 'outline'
      }}
      className={className}
    />
  )
}

export function LoadingState({
  message = 'Loading...',
  className
}: {
  message?: string
  className?: string
}) {
  return (
    <div className={cn('flex flex-col items-center justify-center py-12', className)}>
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"></div>
      <p className="text-muted-foreground">{message}</p>
    </div>
  )
}

export function ErrorState({
  title = 'Something went wrong',
  description = 'An error occurred while loading the data.',
  onRetry,
  className
}: {
  title?: string
  description?: string
  onRetry?: () => void
  className?: string
}) {
  return (
    <EmptyState
      title={title}
      description={description}
      action={onRetry ? {
        label: 'Try again',
        onClick: onRetry,
        variant: 'outline'
      } : undefined}
      className={className}
    />
  )
}

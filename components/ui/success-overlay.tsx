'use client'

import { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { CheckCir<PERSON>, Sparkles } from 'lucide-react'
import { cn } from '@/lib/utils'

interface SuccessOverlayProps {
  isVisible: boolean
  title?: string
  message?: string
  duration?: number
  onComplete?: () => void
  className?: string
}

export function SuccessOverlay({
  isVisible,
  title = "Success!",
  message = "Operation completed successfully",
  duration = 2000,
  onComplete,
  className
}: SuccessOverlayProps) {
  const [showContent, setShowContent] = useState(false)

  useEffect(() => {
    if (isVisible) {
      // Show content after overlay animation
      const contentTimer = setTimeout(() => {
        setShowContent(true)
      }, 200)

      // Auto-complete after duration
      const completeTimer = setTimeout(() => {
        onComplete?.()
      }, duration)

      return () => {
        clearTimeout(contentTimer)
        clearTimeout(completeTimer)
      }
    } else {
      setShowContent(false)
    }
  }, [isVisible, duration, onComplete])

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className={cn(
            "fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm",
            className
          )}
        >
          {/* Success Card */}
          <motion.div
            initial={{ scale: 0.8, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.8, opacity: 0, y: 20 }}
            transition={{ 
              type: "spring", 
              duration: 0.6,
              bounce: 0.3
            }}
            className="relative bg-background border border-border rounded-2xl p-8 shadow-2xl max-w-md mx-4"
          >
            {/* Background Sparkles */}
            <div className="absolute inset-0 overflow-hidden rounded-2xl">
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={i}
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ 
                    opacity: [0, 1, 0],
                    scale: [0, 1, 0],
                    rotate: [0, 180, 360]
                  }}
                  transition={{
                    duration: 2,
                    delay: i * 0.2,
                    repeat: Infinity,
                    repeatDelay: 1
                  }}
                  className="absolute"
                  style={{
                    left: `${20 + (i * 15)}%`,
                    top: `${15 + (i % 3) * 25}%`,
                  }}
                >
                  <Sparkles className="w-4 h-4 text-green-400/30" />
                </motion.div>
              ))}
            </div>

            {/* Content */}
            <div className="relative z-10 text-center space-y-4">
              {/* Success Icon */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ 
                  type: "spring",
                  delay: 0.2,
                  duration: 0.8,
                  bounce: 0.5
                }}
                className="flex justify-center"
              >
                <div className="relative">
                  <motion.div
                    animate={{ 
                      scale: [1, 1.1, 1],
                      rotate: [0, 5, -5, 0]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      repeatDelay: 1
                    }}
                    className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center"
                  >
                    <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
                  </motion.div>
                  
                  {/* Pulse Ring */}
                  <motion.div
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ 
                      scale: [0.8, 1.2, 1.4],
                      opacity: [0, 0.6, 0]
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: "easeOut"
                    }}
                    className="absolute inset-0 border-2 border-green-400 rounded-full"
                  />
                </div>
              </motion.div>

              {/* Text Content */}
              <AnimatePresence>
                {showContent && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.4 }}
                    className="space-y-2"
                  >
                    <h3 className="text-xl font-semibold text-foreground">
                      {title}
                    </h3>
                    <p className="text-muted-foreground text-sm">
                      {message}
                    </p>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Progress Bar */}
              <motion.div
                initial={{ width: "0%" }}
                animate={{ width: "100%" }}
                transition={{ duration: duration / 1000, ease: "linear" }}
                className="h-1 bg-green-500 rounded-full mx-auto"
                style={{ maxWidth: "200px" }}
              />
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// Preset configurations for common use cases
export const SuccessOverlayPresets = {
  registration: {
    title: "Account Created!",
    message: "Welcome to JobPortal. Your account has been created successfully.",
    duration: 2500
  },
  login: {
    title: "Welcome Back!",
    message: "You have been signed in successfully.",
    duration: 2000
  },
  profileUpdate: {
    title: "Profile Updated!",
    message: "Your profile changes have been saved successfully.",
    duration: 2000
  },
  applicationSubmitted: {
    title: "Application Sent!",
    message: "Your job application has been submitted successfully.",
    duration: 2500
  },
  jobPosted: {
    title: "Job Posted!",
    message: "Your job posting is now live and visible to candidates.",
    duration: 2500
  }
}

// Hook for easy success overlay management
export function useSuccessOverlay() {
  const [isVisible, setIsVisible] = useState(false)
  const [config, setConfig] = useState<Partial<SuccessOverlayProps>>({})

  const showSuccess = (options?: Partial<SuccessOverlayProps>) => {
    setConfig(options || {})
    setIsVisible(true)
  }

  const hideSuccess = () => {
    setIsVisible(false)
  }

  const showPreset = (preset: keyof typeof SuccessOverlayPresets, overrides?: Partial<SuccessOverlayProps>) => {
    showSuccess({
      ...SuccessOverlayPresets[preset],
      ...overrides
    })
  }

  return {
    isVisible,
    showSuccess,
    hideSuccess,
    showPreset,
    SuccessOverlay: (props: Omit<SuccessOverlayProps, 'isVisible'>) => (
      <SuccessOverlay 
        {...config} 
        {...props} 
        isVisible={isVisible} 
        onComplete={() => {
          hideSuccess()
          props.onComplete?.()
        }}
      />
    )
  }
}

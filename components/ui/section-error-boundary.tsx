'use client'

import React, { Component, ReactNode } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { AlertTriangle, RefreshCw } from 'lucide-react'
import { cn } from '@/lib/utils'

interface SectionErrorBoundaryProps {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
  className?: string
  title?: string
  description?: string
  showRetry?: boolean
  onRetry?: () => void
}

interface SectionErrorBoundaryState {
  hasError: boolean
  error?: Error
}

export class SectionErrorBoundary extends Component<
  SectionErrorBoundaryProps,
  SectionErrorBoundaryState
> {
  constructor(props: SectionErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): SectionErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('SectionErrorBoundary caught an error:', error, errorInfo)
    this.props.onError?.(error, errorInfo)
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined })
    this.props.onRetry?.()
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <Card className={cn('w-full', this.props.className)}>
          <CardContent className="p-6">
            <div className="flex flex-col items-center justify-center text-center space-y-4">
              <AlertTriangle className="w-12 h-12 text-destructive" />
              <div>
                <h3 className="text-lg font-semibold">
                  {this.props.title || 'Something went wrong'}
                </h3>
                <p className="text-muted-foreground mt-1">
                  {this.props.description || 
                   'An error occurred in this section. Please try refreshing or contact support if the problem persists.'}
                </p>
                {process.env.NODE_ENV === 'development' && this.state.error && (
                  <details className="mt-4 text-left">
                    <summary className="cursor-pointer text-sm font-medium">
                      Error Details (Development)
                    </summary>
                    <pre className="mt-2 text-xs bg-muted p-2 rounded overflow-auto">
                      {this.state.error.stack}
                    </pre>
                  </details>
                )}
              </div>
              {this.props.showRetry !== false && (
                <Button 
                  variant="outline" 
                  onClick={this.handleRetry}
                  className="mt-4"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Try Again
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )
    }

    return this.props.children
  }
}

// Hook version for functional components
export function useSectionErrorBoundary() {
  const [error, setError] = React.useState<Error | null>(null)

  const resetError = React.useCallback(() => {
    setError(null)
  }, [])

  const captureError = React.useCallback((error: Error) => {
    setError(error)
  }, [])

  return {
    error,
    resetError,
    captureError,
    hasError: !!error
  }
}

// Higher-order component for wrapping components with error boundary
export function withSectionErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Partial<SectionErrorBoundaryProps>
) {
  const WrappedComponent = (props: P) => (
    <SectionErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </SectionErrorBoundary>
  )

  WrappedComponent.displayName = `withSectionErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

// Specific error boundaries for common admin sections
export function TableErrorBoundary({ 
  children, 
  onRetry,
  className 
}: { 
  children: ReactNode
  onRetry?: () => void
  className?: string 
}) {
  return (
    <SectionErrorBoundary
      title="Failed to load table data"
      description="There was an error loading the table data. Please try again."
      onRetry={onRetry}
      className={className}
    >
      {children}
    </SectionErrorBoundary>
  )
}

export function ChartErrorBoundary({ 
  children, 
  onRetry,
  className 
}: { 
  children: ReactNode
  onRetry?: () => void
  className?: string 
}) {
  return (
    <SectionErrorBoundary
      title="Failed to load chart"
      description="There was an error loading the chart data. Please try again."
      onRetry={onRetry}
      className={className}
    >
      {children}
    </SectionErrorBoundary>
  )
}

export function StatsErrorBoundary({ 
  children, 
  onRetry,
  className 
}: { 
  children: ReactNode
  onRetry?: () => void
  className?: string 
}) {
  return (
    <SectionErrorBoundary
      title="Failed to load statistics"
      description="There was an error loading the statistics. Please try again."
      onRetry={onRetry}
      className={className}
    >
      {children}
    </SectionErrorBoundary>
  )
}

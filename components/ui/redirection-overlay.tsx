'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useTheme } from 'next-themes'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowRight, Loader2, ExternalLink, Home, User, Building2, Briefcase, Settings } from 'lucide-react'
import { cn } from '@/lib/utils'

interface RedirectionOverlayProps {
  isVisible: boolean
  destination: string
  destinationName?: string
  message?: string
  countdown?: number
  autoRedirect?: boolean
  onRedirect?: () => void
  onCancel?: () => void
  className?: string
}

// Page icons mapping for better UX
const pageIcons: Record<string, any> = {
  '/': Home,
  '/home': Home,
  '/dashboard': User,
  '/client-dashboard': User,
  '/company-dashboard': Building2,
  '/admin': Settings,
  '/admin/database': Settings,
  '/jobs': Briefcase,
  '/companies': Building2,
  '/profile': User,
  '/settings': Settings,
}

// Get friendly page names
const getPageName = (path: string): string => {
  const pageNames: Record<string, string> = {
    '/': 'Home',
    '/home': 'Home',
    '/dashboard': 'Dashboard',
    '/client-dashboard': 'Client Dashboard',
    '/company-dashboard': 'Company Dashboard',
    '/admin': 'Admin Panel',
    '/admin/database': 'Database Admin',
    '/jobs': 'Jobs',
    '/companies': 'Companies',
    '/profile': 'Profile',
    '/settings': 'Settings',
  }
  
  return pageNames[path] || path.split('/').pop()?.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Page'
}

export function RedirectionOverlay({
  isVisible,
  destination,
  destinationName,
  message,
  countdown = 3,
  autoRedirect = true,
  onRedirect,
  onCancel,
  className
}: RedirectionOverlayProps) {
  const router = useRouter()
  const { theme } = useTheme()
  const [timeLeft, setTimeLeft] = useState(countdown)
  const [isRedirecting, setIsRedirecting] = useState(false)

  const pageName = destinationName || getPageName(destination)
  const PageIcon = pageIcons[destination] || ExternalLink

  useEffect(() => {
    if (!isVisible) {
      setTimeLeft(countdown)
      setIsRedirecting(false)
      return
    }

    if (!autoRedirect) return

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          handleRedirect()
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [isVisible, countdown, autoRedirect, destination])

  const handleRedirect = () => {
    setIsRedirecting(true)
    onRedirect?.()
    
    // Small delay for UX
    setTimeout(() => {
      router.push(destination)
    }, 500)
  }

  const handleCancel = () => {
    onCancel?.()
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className={cn(
            "fixed inset-0 z-50 flex items-center justify-center bg-black/70 backdrop-blur-sm",
            className
          )}
        >
          {/* Redirection Card */}
          <motion.div
            initial={{ scale: 0.9, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.9, opacity: 0, y: 20 }}
            transition={{ 
              type: "spring", 
              duration: 0.5,
              bounce: 0.2
            }}
            className="relative bg-background border border-border rounded-2xl p-8 shadow-2xl max-w-md mx-4 min-w-[320px]"
          >
            {/* Theme-aware background gradient */}
            <div className={cn(
              "absolute inset-0 rounded-2xl opacity-5",
              theme === 'dark' 
                ? "bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500"
                : "bg-gradient-to-br from-blue-500 via-purple-600 to-pink-600"
            )} />

            {/* Content */}
            <div className="relative z-10 text-center space-y-6">
              {/* Header */}
              <div className="space-y-3">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ 
                    type: "spring",
                    delay: 0.1,
                    duration: 0.6,
                    bounce: 0.4
                  }}
                  className="flex justify-center"
                >
                  <div className={cn(
                    "w-16 h-16 rounded-full flex items-center justify-center",
                    "bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30",
                    "border border-blue-200 dark:border-blue-800"
                  )}>
                    <PageIcon className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <h3 className="text-xl font-semibold text-foreground">
                    {isRedirecting ? 'Redirecting...' : 'Redirecting to'}
                  </h3>
                  <p className="text-lg font-medium text-primary">
                    {pageName}
                  </p>
                </motion.div>
              </div>

              {/* Message */}
              {message && (
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.3 }}
                  className="text-muted-foreground text-sm"
                >
                  {message}
                </motion.p>
              )}

              {/* Countdown or Loading */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.4 }}
                className="space-y-4"
              >
                {isRedirecting ? (
                  <div className="flex items-center justify-center space-x-2">
                    <Loader2 className="w-5 h-5 animate-spin text-primary" />
                    <span className="text-sm text-muted-foreground">Taking you there...</span>
                  </div>
                ) : autoRedirect ? (
                  <div className="space-y-3">
                    <div className="flex items-center justify-center space-x-2">
                      <span className="text-2xl font-bold text-primary">{timeLeft}</span>
                      <span className="text-sm text-muted-foreground">seconds</span>
                    </div>
                    
                    {/* Progress Ring */}
                    <div className="flex justify-center">
                      <div className="relative w-12 h-12">
                        <svg className="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
                          <path
                            className="text-muted stroke-current opacity-20"
                            strokeWidth="3"
                            fill="none"
                            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          />
                          <motion.path
                            className="text-primary stroke-current"
                            strokeWidth="3"
                            strokeLinecap="round"
                            fill="none"
                            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                            initial={{ pathLength: 1 }}
                            animate={{ pathLength: timeLeft / countdown }}
                            transition={{ duration: 1, ease: "linear" }}
                            style={{
                              strokeDasharray: "100, 100",
                            }}
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    <ArrowRight className="w-5 h-5 text-primary" />
                  </div>
                )}
              </motion.div>

              {/* Action Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="flex space-x-3"
              >
                {!isRedirecting && (
                  <>
                    <button
                      onClick={handleRedirect}
                      className={cn(
                        "flex-1 px-4 py-2 rounded-lg font-medium transition-all duration-200",
                        "bg-primary text-primary-foreground hover:bg-primary/90",
                        "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                      )}
                    >
                      Go Now
                    </button>
                    
                    {onCancel && (
                      <button
                        onClick={handleCancel}
                        className={cn(
                          "flex-1 px-4 py-2 rounded-lg font-medium transition-all duration-200",
                          "bg-muted text-muted-foreground hover:bg-muted/80",
                          "focus:outline-none focus:ring-2 focus:ring-muted focus:ring-offset-2"
                        )}
                      >
                        Cancel
                      </button>
                    )}
                  </>
                )}
              </motion.div>

              {/* Destination URL */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
                className="text-xs text-muted-foreground font-mono bg-muted/30 px-3 py-1 rounded-md"
              >
                {destination}
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// Hook for easy redirection overlay management
export function useRedirectionOverlay() {
  const [isVisible, setIsVisible] = useState(false)
  const [config, setConfig] = useState<Partial<RedirectionOverlayProps>>({})

  const showRedirection = (destination: string, options?: Partial<RedirectionOverlayProps>) => {
    setConfig({ destination, ...options })
    setIsVisible(true)
  }

  const hideRedirection = () => {
    setIsVisible(false)
  }

  return {
    isVisible,
    showRedirection,
    hideRedirection,
    RedirectionOverlay: (props: Omit<RedirectionOverlayProps, 'isVisible'>) => (
      <RedirectionOverlay 
        {...config} 
        {...props} 
        isVisible={isVisible} 
        onCancel={() => {
          hideRedirection()
          props.onCancel?.()
        }}
      />
    )
  }
}

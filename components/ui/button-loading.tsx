'use client'

import { Button, ButtonProps } from '@/components/ui/button'
import { LoadingSpinner } from './loading-spinner'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'

interface ButtonLoadingProps extends ButtonProps {
  loading?: boolean
  loadingText?: string
  spinnerSize?: 'sm' | 'md' | 'lg' | 'xl'
  showProgress?: boolean
  progressValue?: number
}

export function ButtonLoading({
  loading = false,
  loadingText,
  children,
  disabled,
  className,
  spinnerSize = 'sm',
  showProgress = false,
  progressValue = 0,
  ...props
}: ButtonLoadingProps) {
  return (
    <Button
      disabled={disabled || loading}
      className={cn(
        'relative overflow-hidden transition-all duration-300',
        loading && 'cursor-not-allowed',
        className
      )}
      {...props}
    >
      {/* Progress bar background */}
      {loading && showProgress && (
        <motion.div
          initial={{ width: "0%" }}
          animate={{ width: `${progressValue}%` }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="absolute inset-0 bg-primary/20 z-0"
        />
      )}

      {/* Button content */}
      <div className="relative z-10 flex items-center justify-center">
        <AnimatePresence mode="wait">
          {loading ? (
            <motion.div
              key="loading"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.2 }}
              className="flex items-center"
            >
              <LoadingSpinner
                size={spinnerSize}
                className="mr-2"
                color="secondary"
              />
              <span>{loadingText || 'Loading...'}</span>
            </motion.div>
          ) : (
            <motion.div
              key="content"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.2 }}
            >
              {children}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </Button>
  )
}

// Alternative with overlay loading state
export function ButtonLoadingOverlay({
  loading = false,
  loadingText,
  children,
  disabled,
  className,
  spinnerSize = 'sm',
  ...props
}: ButtonLoadingProps) {
  return (
    <Button
      disabled={disabled || loading}
      className={cn(
        'relative overflow-hidden',
        className
      )}
      {...props}
    >
      {/* Loading overlay */}
      {loading && (
        <div className="absolute inset-0 bg-primary/90 flex items-center justify-center">
          <LoadingSpinner 
            size={spinnerSize} 
            color="secondary"
          />
        </div>
      )}
      
      {/* Button content */}
      <span className={cn(loading && 'opacity-50')}>
        {children}
      </span>
    </Button>
  )
}

// Submit button with loading state
export function SubmitButton({
  loading = false,
  children = 'Submit',
  loadingText = 'Submitting...',
  ...props
}: ButtonLoadingProps) {
  return (
    <ButtonLoading
      type="submit"
      loading={loading}
      loadingText={loadingText}
      {...props}
    >
      {children}
    </ButtonLoading>
  )
}

// Save button with loading state
export function SaveButton({
  loading = false,
  children = 'Save',
  loadingText = 'Saving...',
  ...props
}: ButtonLoadingProps) {
  return (
    <ButtonLoading
      loading={loading}
      loadingText={loadingText}
      {...props}
    >
      {children}
    </ButtonLoading>
  )
}

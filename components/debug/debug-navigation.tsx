'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { ThemeToggle } from '@/components/theme-toggle'
import {
  Database,
  Shield,
  Settings,
  BarChart3,
  Users,
  Building2,
  Menu,
  X,
  Home,
  ArrowLeft,
  Bug,
  Terminal,
  Activity,
  FileText,
  Search,
  Zap
} from 'lucide-react'

export function DebugNavigation() {
  const pathname = usePathname()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const debugNavItems = [
    { href: '/debugadmin', label: 'Debug Dashboard', icon: Shield },
    { href: '/debugadmin/database', label: 'Database Admin', icon: Database },
    { href: '/debugadmin/users', label: 'User Debug', icon: Users },
    { href: '/debugadmin/companies', label: 'Company Debug', icon: Building2 },
    { href: '/debugadmin/analytics', label: 'Debug Analytics', icon: BarChart3 },
    { href: '/debugadmin/logs', label: 'System Logs', icon: FileText },
    { href: '/debugadmin/performance', label: 'Performance', icon: Activity },
    { href: '/debugadmin/search', label: 'Search Debug', icon: Search },
  ]

  const isActiveLink = (href: string) => {
    if (href === '/debugadmin') {
      return pathname === href
    }
    return pathname.startsWith(href)
  }

  return (
    <nav className="bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 shadow-lg border-b border-orange-200 dark:border-orange-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Debug Logo/Brand */}
          <div className="flex items-center space-x-4">
            <Link href="/debugadmin" className="flex items-center space-x-2 group">
              <div className="relative">
                <Bug className="w-8 h-8 text-white group-hover:scale-110 transition-transform duration-200" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold text-white">Debug Admin</h1>
                <p className="text-xs text-orange-100">Development & Testing</p>
              </div>
            </Link>
            
            {/* Back to Main Site */}
            <div className="hidden md:block">
              <Button asChild variant="ghost" size="sm" className="text-white hover:bg-white/10">
                <Link href="/" className="flex items-center space-x-1">
                  <ArrowLeft className="w-4 h-4" />
                  <span>Back to Site</span>
                </Link>
              </Button>
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            {debugNavItems.map((item, index) => {
              const isActive = isActiveLink(item.href)
              return (
                <motion.div
                  key={item.href}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <Link
                    href={item.href}
                    className={cn(
                      "flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 group relative",
                      isActive
                        ? "bg-white/20 text-white border border-white/30 shadow-lg"
                        : "text-orange-100 hover:text-white hover:bg-white/10"
                    )}
                  >
                    <item.icon className={cn(
                      "w-4 h-4 group-hover:scale-110 transition-transform duration-200",
                      isActive ? "text-white" : "text-orange-200"
                    )} />
                    <span className="font-medium text-sm">{item.label}</span>
                    {isActive && (
                      <motion.div
                        layoutId="debugActiveIndicator"
                        className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full"
                        initial={false}
                        transition={{ type: "spring", stiffness: 500, damping: 30 }}
                      />
                    )}
                  </Link>
                </motion.div>
              )
            })}
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-3">
            <ThemeToggle />
            
            {/* Quick Actions */}
            <div className="hidden md:flex items-center space-x-2">
              <Button asChild variant="ghost" size="sm" className="text-white hover:bg-white/10">
                <Link href="/debugadmin/database" className="flex items-center space-x-1">
                  <Terminal className="w-4 h-4" />
                  <span>DB</span>
                </Link>
              </Button>
              
              <Button asChild variant="ghost" size="sm" className="text-white hover:bg-white/10">
                <Link href="/debugadmin/performance" className="flex items-center space-x-1">
                  <Zap className="w-4 h-4" />
                  <span>Perf</span>
                </Link>
              </Button>
            </div>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden text-white hover:bg-white/10"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? (
                <X className="w-5 h-5" />
              ) : (
                <Menu className="w-5 h-5" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="md:hidden bg-gradient-to-b from-orange-600 to-red-600 border-t border-orange-400"
          >
            <div className="px-4 py-4 space-y-2">
              {/* Back to Site - Mobile */}
              <Button asChild variant="ghost" className="w-full justify-start text-white hover:bg-white/10">
                <Link
                  href="/"
                  onClick={() => setIsMobileMenuOpen(false)}
                  className="flex items-center space-x-2"
                >
                  <Home className="w-4 h-4" />
                  <span>Back to Main Site</span>
                </Link>
              </Button>

              {/* Debug Navigation Items */}
              {debugNavItems.map((item) => {
                const isActive = isActiveLink(item.href)
                return (
                  <motion.div
                    key={item.href}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Link
                      href={item.href}
                      className={cn(
                        "flex items-center space-x-3 px-3 py-3 rounded-lg transition-all duration-200 w-full",
                        isActive
                          ? "bg-white/20 text-white border border-white/30"
                          : "text-orange-100 hover:text-white hover:bg-white/10"
                      )}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <item.icon className={cn(
                        "w-5 h-5",
                        isActive ? "text-white" : "text-orange-200"
                      )} />
                      <span className={cn(
                        "font-medium",
                        isActive ? "text-white" : "text-orange-100"
                      )}>{item.label}</span>
                    </Link>
                  </motion.div>
                )
              })}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  )
}

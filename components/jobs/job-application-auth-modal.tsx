'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { useAuthStore } from '@/stores/auth.store'
import { useToast } from '@/hooks/use-toast'
import { 
  User, 
  Mail, 
  Lock, 
  Eye, 
  EyeOff, 
  ArrowRight, 
  ArrowLeft,
  CheckCircle,
  AlertCircle,
  Briefcase,
  Building2
} from 'lucide-react'

interface JobApplicationAuthModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: (user: any) => void
  jobTitle?: string
  companyName?: string
}

interface LoginFormData {
  email: string
  password: string
  rememberMe: boolean
}

interface RegisterFormData {
  email: string
  password: string
  confirmPassword: string
  firstName: string
  lastName: string
  role: 'job_seeker' | 'company_admin' | ''
  phone: string
  location: {
    city: string
    state: string
    country: string
  }
}

export function JobApplicationAuthModal({ 
  isOpen, 
  onClose, 
  onSuccess,
  jobTitle,
  companyName 
}: JobApplicationAuthModalProps) {
  const [mode, setMode] = useState<'login' | 'register' | 'success'>('login')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [registrationSuccess, setRegistrationSuccess] = useState(false)
  
  const { login, register, loginLoading, registerLoading, error, clearError, isAuthenticated } = useAuthStore()
  const { toast } = useToast()

  // Check if user is already authenticated
  React.useEffect(() => {
    if (isAuthenticated && isOpen) {
      // User is already logged in, close modal and trigger success
      const user = useAuthStore.getState().user
      if (user) {
        onSuccess(user)
        onClose()
      }
    }
  }, [isAuthenticated, isOpen, onSuccess, onClose])

  const [loginData, setLoginData] = useState<LoginFormData>({
    email: '',
    password: '',
    rememberMe: false
  })

  const [registerData, setRegisterData] = useState<RegisterFormData>({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    role: 'job_seeker',
    phone: '',
    location: {
      city: '',
      state: '',
      country: 'United States'
    }
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateLoginForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!loginData.email) newErrors.email = 'Email is required'
    else if (!/\S+@\S+\.\S+/.test(loginData.email)) newErrors.email = 'Email is invalid'
    
    if (!loginData.password) newErrors.password = 'Password is required'
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const validateRegisterForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!registerData.email) newErrors.email = 'Email is required'
    else if (!/\S+@\S+\.\S+/.test(registerData.email)) newErrors.email = 'Email is invalid'
    
    if (!registerData.password) newErrors.password = 'Password is required'
    else if (registerData.password.length < 8) newErrors.password = 'Password must be at least 8 characters'
    
    if (!registerData.confirmPassword) newErrors.confirmPassword = 'Please confirm your password'
    else if (registerData.password !== registerData.confirmPassword) newErrors.confirmPassword = 'Passwords do not match'
    
    if (!registerData.firstName) newErrors.firstName = 'First name is required'
    if (!registerData.lastName) newErrors.lastName = 'Last name is required'
    if (!registerData.role) newErrors.role = 'Please select your role'
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Test API connectivity
  const testAPIConnection = async () => {
    try {
      console.log('🔍 Testing API connectivity...')
      const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'test123'
        })
      })
      console.log('🔍 API test response status:', response.status)
      console.log('🔍 API test response ok:', response.ok)

      if (response.status === 401 || response.status === 400) {
        console.log('🔍 API is reachable (got expected auth error)')
        return true
      }

      return response.ok
    } catch (error) {
      console.error('🔍 API connectivity test failed:', error)
      return false
    }
  }

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateLoginForm()) return

    clearError()

    try {
      console.log('🔍 Starting login process with data:', {
        email: loginData.email,
        hasPassword: !!loginData.password,
        rememberMe: loginData.rememberMe
      })

      // Test API connectivity first
      const apiReachable = await testAPIConnection()
      if (!apiReachable) {
        throw new Error('Unable to connect to authentication server. Please check your internet connection.')
      }

      const result = await login({
        email: loginData.email,
        password: loginData.password,
        rememberMe: loginData.rememberMe
      })

      console.log('🔍 Login successful, result:', result)

      toast({
        title: "Login Successful!",
        description: "Welcome back! You can now apply for this job.",
        variant: "default"
      })

      onSuccess(result.user)
      onClose()
    } catch (error) {
      console.error('🔍 Login failed with error:', error)
      console.error('🔍 Error type:', typeof error)
      console.error('🔍 Error message:', error instanceof Error ? error.message : 'Unknown error')
      console.error('🔍 Error stack:', error instanceof Error ? error.stack : 'No stack')

      const errorMessage = error instanceof Error ? error.message : "Please check your credentials and try again."

      toast({
        title: "Login Failed",
        description: errorMessage,
        variant: "destructive"
      })
    }
  }

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateRegisterForm()) return

    clearError()

    try {
      const result = await register({
        email: registerData.email,
        password: registerData.password,
        firstName: registerData.firstName,
        lastName: registerData.lastName,
        role: registerData.role as 'job_seeker' | 'company_admin',
        phone: registerData.phone || undefined,
        location: registerData.location.city ? registerData.location : undefined
      })

      // Show success state and auto-login
      setRegistrationSuccess(true)
      setMode('success')

      toast({
        title: "Registration Successful!",
        description: "Your account has been created. You can now apply for this job.",
        variant: "default"
      })

      // Auto-close after success and trigger callback
      setTimeout(() => {
        onSuccess(result.user)
        onClose()
      }, 2000)

    } catch (error) {
      console.error('Registration failed:', error)
      toast({
        title: "Registration Failed",
        description: error instanceof Error ? error.message : "Please try again.",
        variant: "destructive"
      })
    }
  }

  const switchToRegister = () => {
    setMode('register')
    setErrors({})
    clearError()
  }

  const switchToLogin = () => {
    setMode('login')
    setErrors({})
    clearError()
  }

  const resetForms = () => {
    setLoginData({
      email: '',
      password: '',
      rememberMe: false
    })
    setRegisterData({
      email: '',
      password: '',
      confirmPassword: '',
      firstName: '',
      lastName: '',
      role: 'job_seeker',
      phone: '',
      location: {
        city: '',
        state: '',
        country: 'United States'
      }
    })
    setErrors({})
    clearError()
  }

  const handleClose = () => {
    resetForms()
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center">
            {mode === 'login' ? 'Sign In to Apply' : 'Create Account to Apply'}
          </DialogTitle>
          {(jobTitle || companyName) && (
            <div className="text-center text-sm text-muted-foreground">
              {jobTitle && <div className="font-medium">{jobTitle}</div>}
              {companyName && <div>at {companyName}</div>}
            </div>
          )}
        </DialogHeader>

        <div className="space-y-6">
          <AnimatePresence mode="wait">
            {mode === 'success' ? (
              <motion.div
                key="success"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.3 }}
                className="text-center py-8"
              >
                <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Account Created Successfully!</h3>
                <p className="text-muted-foreground mb-4">
                  Welcome! You can now apply for this job.
                </p>
                <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
                  <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                  <span>Redirecting to application...</span>
                </div>
              </motion.div>
            ) : mode === 'login' ? (
              <motion.div
                key="login"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
              >
                <form onSubmit={handleLogin} className="space-y-4">
                  {/* Login Form Content */}
                  <div>
                    <Label htmlFor="login-email">Email</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      <Input
                        id="login-email"
                        type="email"
                        placeholder="Enter your email"
                        value={loginData.email}
                        onChange={(e) => setLoginData(prev => ({ ...prev, email: e.target.value }))}
                        className={`pl-10 ${errors.email ? 'border-red-500' : ''}`}
                        disabled={loginLoading}
                      />
                    </div>
                    {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
                  </div>

                  <div>
                    <Label htmlFor="login-password">Password</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      <Input
                        id="login-password"
                        type={showPassword ? 'text' : 'password'}
                        placeholder="Enter your password"
                        value={loginData.password}
                        onChange={(e) => setLoginData(prev => ({ ...prev, password: e.target.value }))}
                        className={`pl-10 pr-10 ${errors.password ? 'border-red-500' : ''}`}
                        disabled={loginLoading}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                      >
                        {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>
                    {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password}</p>}
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="remember-me"
                      checked={loginData.rememberMe}
                      onCheckedChange={(checked) => setLoginData(prev => ({ ...prev, rememberMe: checked as boolean }))}
                    />
                    <Label htmlFor="remember-me" className="text-sm">Remember me</Label>
                  </div>

                  {error && (
                    <div className="p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <AlertCircle className="w-4 h-4 text-red-500" />
                        <span className="text-sm text-red-700 dark:text-red-300">
                          {error.message || 'Login failed. Please try again.'}
                        </span>
                      </div>
                    </div>
                  )}

                  <Button 
                    type="submit" 
                    className="w-full" 
                    disabled={loginLoading}
                  >
                    {loginLoading ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                        Signing In...
                      </>
                    ) : (
                      <>
                        <ArrowRight className="w-4 h-4 mr-2" />
                        Sign In & Apply
                      </>
                    )}
                  </Button>

                  <div className="text-center">
                    <span className="text-sm text-muted-foreground">Don't have an account? </span>
                    <Button
                      type="button"
                      variant="link"
                      className="px-0 text-sm"
                      onClick={switchToRegister}
                      disabled={loginLoading}
                    >
                      Create one now
                    </Button>
                  </div>
                </form>
              </motion.div>
            ) : (
              <motion.div
                key="register"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <form onSubmit={handleRegister} className="space-y-4">
                  {/* Role Selection */}
                  <div>
                    <Label htmlFor="role">I am a</Label>
                    <Select value={registerData.role} onValueChange={(value) => setRegisterData(prev => ({ ...prev, role: value as 'job_seeker' | 'company_admin' }))}>
                      <SelectTrigger className={errors.role ? 'border-red-500' : ''}>
                        <SelectValue placeholder="Select your role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="job_seeker">
                          <div className="flex items-center space-x-2">
                            <Briefcase className="w-4 h-4" />
                            <span>Job Seeker</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="company_admin">
                          <div className="flex items-center space-x-2">
                            <Building2 className="w-4 h-4" />
                            <span>Company Admin</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.role && <p className="text-red-500 text-sm mt-1">{errors.role}</p>}
                  </div>

                  {/* Name Fields */}
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label htmlFor="firstName">First Name</Label>
                      <Input
                        id="firstName"
                        type="text"
                        placeholder="First name"
                        value={registerData.firstName}
                        onChange={(e) => setRegisterData(prev => ({ ...prev, firstName: e.target.value }))}
                        className={errors.firstName ? 'border-red-500' : ''}
                        disabled={registerLoading}
                      />
                      {errors.firstName && <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>}
                    </div>
                    <div>
                      <Label htmlFor="lastName">Last Name</Label>
                      <Input
                        id="lastName"
                        type="text"
                        placeholder="Last name"
                        value={registerData.lastName}
                        onChange={(e) => setRegisterData(prev => ({ ...prev, lastName: e.target.value }))}
                        className={errors.lastName ? 'border-red-500' : ''}
                        disabled={registerLoading}
                      />
                      {errors.lastName && <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>}
                    </div>
                  </div>

                  {/* Email */}
                  <div>
                    <Label htmlFor="register-email">Email</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      <Input
                        id="register-email"
                        type="email"
                        placeholder="Enter your email"
                        value={registerData.email}
                        onChange={(e) => setRegisterData(prev => ({ ...prev, email: e.target.value }))}
                        className={`pl-10 ${errors.email ? 'border-red-500' : ''}`}
                        disabled={registerLoading}
                      />
                    </div>
                    {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
                  </div>

                  {/* Password */}
                  <div>
                    <Label htmlFor="register-password">Password</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      <Input
                        id="register-password"
                        type={showPassword ? 'text' : 'password'}
                        placeholder="Create a password"
                        value={registerData.password}
                        onChange={(e) => setRegisterData(prev => ({ ...prev, password: e.target.value }))}
                        className={`pl-10 pr-10 ${errors.password ? 'border-red-500' : ''}`}
                        disabled={registerLoading}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                      >
                        {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>
                    {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password}</p>}
                  </div>

                  {/* Confirm Password */}
                  <div>
                    <Label htmlFor="confirm-password">Confirm Password</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      <Input
                        id="confirm-password"
                        type={showConfirmPassword ? 'text' : 'password'}
                        placeholder="Confirm your password"
                        value={registerData.confirmPassword}
                        onChange={(e) => setRegisterData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                        className={`pl-10 pr-10 ${errors.confirmPassword ? 'border-red-500' : ''}`}
                        disabled={registerLoading}
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                      >
                        {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>
                    {errors.confirmPassword && <p className="text-red-500 text-sm mt-1">{errors.confirmPassword}</p>}
                  </div>

                  {error && (
                    <div className="p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <AlertCircle className="w-4 h-4 text-red-500" />
                        <span className="text-sm text-red-700 dark:text-red-300">
                          {error.message || 'Registration failed. Please try again.'}
                        </span>
                      </div>
                    </div>
                  )}

                  <Button
                    type="submit"
                    className="w-full"
                    disabled={registerLoading}
                  >
                    {registerLoading ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                        Creating Account...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Create Account & Apply
                      </>
                    )}
                  </Button>

                  <div className="text-center">
                    <span className="text-sm text-muted-foreground">Already have an account? </span>
                    <Button
                      type="button"
                      variant="link"
                      className="px-0 text-sm"
                      onClick={switchToLogin}
                      disabled={registerLoading}
                    >
                      Sign in instead
                    </Button>
                  </div>
                </form>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </DialogContent>
    </Dialog>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Search,
  HelpCircle,
  Clock,
  CheckCircle,
  X,
  Settings
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useAuthStore } from '@/stores/auth.store'

interface QuizData {
  _id: string
  title: string
  description: string
  categoryId: {
    _id: string
    name: string
  }
  questions: any[]
  timeLimit: number
  passingScore: number
  difficulty: string
  isActive: boolean
}

interface QuizSelectorProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onQuizSelect: (quiz: QuizData, settings: QuizSettings) => void
  currentQuiz?: {
    quizId: string
    isRequired: boolean
    passingScore: number
    maxAttempts: number
  }
}

interface QuizSettings {
  isRequired: boolean
  passingScore: number
  maxAttempts: number
}

export function QuizSelector({ 
  open, 
  onOpenChange, 
  onQuizSelect, 
  currentQuiz 
}: QuizSelectorProps) {
  const [quizzes, setQuizzes] = useState<QuizData[]>([])
  const [filteredQuizzes, setFilteredQuizzes] = useState<QuizData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedQuiz, setSelectedQuiz] = useState<QuizData | null>(null)
  const [quizSettings, setQuizSettings] = useState<QuizSettings>({
    isRequired: false,
    passingScore: 70,
    maxAttempts: 3
  })
  const [categories, setCategories] = useState<any[]>([])

  const { toast } = useToast()

  useEffect(() => {
    if (open) {
      fetchQuizzes()
      fetchCategories()
    }
  }, [open])

  useEffect(() => {
    if (currentQuiz) {
      setQuizSettings({
        isRequired: currentQuiz.isRequired,
        passingScore: currentQuiz.passingScore,
        maxAttempts: currentQuiz.maxAttempts
      })
    }
  }, [currentQuiz])

  useEffect(() => {
    filterQuizzes()
  }, [quizzes, searchTerm, selectedCategory])

  const fetchQuizzes = async () => {
    try {
      const { token } = useAuthStore.getState()
      const headers: Record<string, string> = {}
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch('/api/v1/quizzes', {
        headers
      })

      if (response.ok) {
        const result = await response.json()
        setQuizzes(result.data.quizzes || [])
      }
    } catch (error) {
      console.error('Error fetching quizzes:', error)
      toast({
        title: "Error",
        description: "Failed to load quizzes",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const { token } = useAuthStore.getState()
      const headers: Record<string, string> = {}
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch('/api/v1/quiz-categories', {
        headers
      })

      if (response.ok) {
        const result = await response.json()
        setCategories(result.data.categories || [])
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
    }
  }

  const filterQuizzes = () => {
    let filtered = quizzes.filter(quiz => quiz.isActive)

    if (searchTerm) {
      filtered = filtered.filter(quiz =>
        quiz.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        quiz.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(quiz => quiz.categoryId._id === selectedCategory)
    }

    setFilteredQuizzes(filtered)
  }

  const handleQuizSelect = (quiz: QuizData) => {
    setSelectedQuiz(quiz)
  }

  const handleAttachQuiz = () => {
    if (selectedQuiz) {
      onQuizSelect(selectedQuiz, quizSettings)
      onOpenChange(false)
      setSelectedQuiz(null)
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return 'bg-green-100 text-green-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'hard':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Attach Quiz to Lesson</DialogTitle>
          <DialogDescription>
            Select a quiz to attach to this lesson and configure its settings
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[60vh]">
          {/* Quiz Selection */}
          <div className="lg:col-span-2 space-y-4">
            {/* Filters */}
            <div className="flex space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search quizzes..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category._id} value={category._id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Quiz List */}
            <div className="space-y-3 overflow-y-auto max-h-96">
              {isLoading ? (
                <div className="space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <Card key={i}>
                      <CardContent className="p-4">
                        <div className="animate-pulse space-y-2">
                          <div className="h-4 bg-muted rounded w-3/4"></div>
                          <div className="h-3 bg-muted rounded w-1/2"></div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : filteredQuizzes.length === 0 ? (
                <Card>
                  <CardContent className="p-8 text-center">
                    <HelpCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No quizzes found</h3>
                    <p className="text-muted-foreground">
                      {searchTerm || selectedCategory !== 'all' 
                        ? 'Try adjusting your search or filters'
                        : 'Create some quizzes first to attach them to lessons'
                      }
                    </p>
                  </CardContent>
                </Card>
              ) : (
                filteredQuizzes.map((quiz) => (
                  <Card 
                    key={quiz._id} 
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      selectedQuiz?._id === quiz._id ? 'ring-2 ring-primary' : ''
                    }`}
                    onClick={() => handleQuizSelect(quiz)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-semibold mb-1">{quiz.title}</h4>
                          <p className="text-sm text-muted-foreground mb-2">
                            {quiz.description}
                          </p>
                          <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                            <div className="flex items-center space-x-1">
                              <HelpCircle className="w-3 h-3" />
                              <span>{quiz.questions.length} questions</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Clock className="w-3 h-3" />
                              <span>{quiz.timeLimit} min</span>
                            </div>
                            <span>Pass: {quiz.passingScore}%</span>
                          </div>
                        </div>
                        <div className="flex flex-col items-end space-y-2">
                          <Badge className={getDifficultyColor(quiz.difficulty)}>
                            {quiz.difficulty}
                          </Badge>
                          <Badge variant="outline">
                            {quiz.categoryId.name}
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </div>

          {/* Quiz Settings */}
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold mb-3 flex items-center">
                <Settings className="w-4 h-4 mr-2" />
                Quiz Settings
              </h4>
              
              {selectedQuiz ? (
                <div className="space-y-4">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">{selectedQuiz.title}</CardTitle>
                      <CardDescription className="text-xs">
                        {selectedQuiz.questions.length} questions • {selectedQuiz.timeLimit} min
                      </CardDescription>
                    </CardHeader>
                  </Card>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="isRequired"
                        checked={quizSettings.isRequired}
                        onChange={(e) => setQuizSettings(prev => ({ 
                          ...prev, 
                          isRequired: e.target.checked 
                        }))}
                      />
                      <label htmlFor="isRequired" className="text-sm">
                        Required to complete lesson
                      </label>
                    </div>

                    <div>
                      <label className="text-sm font-medium">Passing Score (%)</label>
                      <Input
                        type="number"
                        min="0"
                        max="100"
                        value={quizSettings.passingScore}
                        onChange={(e) => setQuizSettings(prev => ({ 
                          ...prev, 
                          passingScore: parseInt(e.target.value) || 70 
                        }))}
                      />
                    </div>

                    <div>
                      <label className="text-sm font-medium">Max Attempts</label>
                      <Input
                        type="number"
                        min="1"
                        max="10"
                        value={quizSettings.maxAttempts}
                        onChange={(e) => setQuizSettings(prev => ({ 
                          ...prev, 
                          maxAttempts: parseInt(e.target.value) || 3 
                        }))}
                      />
                    </div>
                  </div>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-6 text-center">
                    <HelpCircle className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Select a quiz to configure settings
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleAttachQuiz} 
            disabled={!selectedQuiz}
          >
            <CheckCircle className="w-4 h-4 mr-2" />
            Attach Quiz
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

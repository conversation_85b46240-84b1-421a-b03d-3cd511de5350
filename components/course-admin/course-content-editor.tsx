'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Plus,
  Edit,
  Trash2,
  GripVertical,
  Play,
  FileText,
  HelpCircle,
  Clock,
  Eye,
  EyeOff,
  Save,
  X,
  ChevronDown,
  ChevronRight,
  BookOpen,
  Video,
  PenTool
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useAuthStore } from '@/stores/auth.store'
import { QuizSelector } from './quiz-selector'

interface CourseSection {
  _id: string
  title: string
  description: string
  sortOrder: number
  isPublished: boolean
  lessons: CourseLesson[]
}

interface CourseLesson {
  _id: string
  title: string
  description: string
  content: string
  videoUrl: string
  duration: number
  lessonType: 'video' | 'text' | 'quiz'
  isPreview: boolean
  isPublished: boolean
  sortOrder: number
  resources: any[]
  quiz?: {
    quizId: string
    isRequired: boolean
    passingScore: number
    maxAttempts: number
  }
}

interface CourseContentEditorProps {
  courseSlug: string
  courseTitle: string
}

export function CourseContentEditor({ courseSlug, courseTitle }: CourseContentEditorProps) {
  const [sections, setSections] = useState<CourseSection[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set())
  const [editingSection, setEditingSection] = useState<string | null>(null)
  const [editingLesson, setEditingLesson] = useState<string | null>(null)
  const [showNewSectionDialog, setShowNewSectionDialog] = useState(false)
  const [showNewLessonDialog, setShowNewLessonDialog] = useState(false)
  const [selectedSectionForLesson, setSelectedSectionForLesson] = useState<string>('')
  const [showQuizSelector, setShowQuizSelector] = useState(false)
  const [selectedLessonForQuiz, setSelectedLessonForQuiz] = useState<{ sectionId: string; lessonId: string } | null>(null)

  const { toast } = useToast()

  useEffect(() => {
    fetchSections()
  }, [courseSlug])

  const fetchSections = async () => {
    try {
      const { token } = useAuthStore.getState()
      const headers: Record<string, string> = {}
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch(`/api/v1/courses/${courseSlug}/sections`, {
        headers
      })

      if (response.ok) {
        const result = await response.json()
        setSections(result.data.sections || [])
      }
    } catch (error) {
      console.error('Error fetching sections:', error)
      toast({
        title: "Error",
        description: "Failed to load course content",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const createSection = async (sectionData: { title: string; description: string }) => {
    try {
      const { token } = useAuthStore.getState()
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch(`/api/v1/courses/${courseSlug}/sections`, {
        method: 'POST',
        headers,
        body: JSON.stringify(sectionData)
      })

      if (response.ok) {
        const result = await response.json()
        setSections(prev => [...prev, result.data.section])
        toast({
          title: "Success",
          description: "Section created successfully"
        })
        setShowNewSectionDialog(false)
      } else {
        throw new Error('Failed to create section')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create section",
        variant: "destructive"
      })
    }
  }

  const createLesson = async (sectionId: string, lessonData: any) => {
    try {
      const { token } = useAuthStore.getState()
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch(`/api/v1/courses/${courseSlug}/sections/${sectionId}/lessons`, {
        method: 'POST',
        headers,
        body: JSON.stringify(lessonData)
      })

      if (response.ok) {
        const result = await response.json()
        setSections(prev => prev.map(section => 
          section._id === sectionId 
            ? { ...section, lessons: [...(section.lessons || []), result.data.lesson] }
            : section
        ))
        toast({
          title: "Success",
          description: "Lesson created successfully"
        })
        setShowNewLessonDialog(false)
      } else {
        throw new Error('Failed to create lesson')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create lesson",
        variant: "destructive"
      })
    }
  }

  const attachQuizToLesson = async (sectionId: string, lessonId: string, quiz: any, settings: any) => {
    try {
      const { token } = useAuthStore.getState()
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch(`/api/v1/courses/${courseSlug}/sections/${sectionId}/lessons/${lessonId}/quiz`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          quizId: quiz._id,
          isRequired: settings.isRequired,
          passingScore: settings.passingScore,
          maxAttempts: settings.maxAttempts
        })
      })

      if (response.ok) {
        // Update the lesson in state
        setSections(prev => prev.map(section =>
          section._id === sectionId
            ? {
                ...section,
                lessons: section.lessons.map(lesson =>
                  lesson._id === lessonId
                    ? { ...lesson, quiz: { quizId: quiz._id, ...settings } }
                    : lesson
                )
              }
            : section
        ))
        toast({
          title: "Success",
          description: "Quiz attached to lesson successfully"
        })
      } else {
        throw new Error('Failed to attach quiz')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to attach quiz to lesson",
        variant: "destructive"
      })
    }
  }

  const toggleSectionExpansion = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev)
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId)
      } else {
        newSet.add(sectionId)
      }
      return newSet
    })
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`
  }

  const getLessonIcon = (lessonType: string) => {
    switch (lessonType) {
      case 'video':
        return <Video className="w-4 h-4" />
      case 'text':
        return <FileText className="w-4 h-4" />
      case 'quiz':
        return <HelpCircle className="w-4 h-4" />
      default:
        return <BookOpen className="w-4 h-4" />
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Course Content</h2>
        </div>
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-3">
                  <div className="h-4 bg-muted rounded w-1/3"></div>
                  <div className="h-3 bg-muted rounded w-2/3"></div>
                  <div className="space-y-2">
                    <div className="h-3 bg-muted rounded w-1/2"></div>
                    <div className="h-3 bg-muted rounded w-1/2"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Course Content</h2>
          <p className="text-muted-foreground">{courseTitle}</p>
        </div>
        <Button onClick={() => setShowNewSectionDialog(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Add Section
        </Button>
      </div>

      {/* Course Structure */}
      <div className="space-y-4">
        {sections.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <BookOpen className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No content yet</h3>
              <p className="text-muted-foreground mb-4">
                Start building your course by adding sections and lessons
              </p>
              <Button onClick={() => setShowNewSectionDialog(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add First Section
              </Button>
            </CardContent>
          </Card>
        ) : (
          sections.map((section, sectionIndex) => (
            <Card key={section._id} className="overflow-hidden">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleSectionExpansion(section._id)}
                    >
                      {expandedSections.has(section._id) ? (
                        <ChevronDown className="w-4 h-4" />
                      ) : (
                        <ChevronRight className="w-4 h-4" />
                      )}
                    </Button>
                    <div>
                      <CardTitle className="text-lg">
                        Section {sectionIndex + 1}: {section.title}
                      </CardTitle>
                      {section.description && (
                        <CardDescription>{section.description}</CardDescription>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge variant={section.isPublished ? "default" : "secondary"}>
                      {section.isPublished ? "Published" : "Draft"}
                    </Badge>
                    <Button variant="ghost" size="sm">
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>

              <AnimatePresence>
                {expandedSections.has(section._id) && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <CardContent className="pt-0">
                      <Separator className="mb-4" />
                      
                      {/* Lessons */}
                      <div className="space-y-3">
                        {section.lessons?.map((lesson, lessonIndex) => (
                          <div
                            key={lesson._id}
                            className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50"
                          >
                            <div className="flex items-center space-x-3">
                              <div className="flex items-center space-x-2 text-muted-foreground">
                                {getLessonIcon(lesson.lessonType)}
                                <span className="text-sm">{lessonIndex + 1}</span>
                              </div>
                              <div>
                                <div className="flex items-center space-x-2">
                                  <span className="font-medium">{lesson.title}</span>
                                  {lesson.isPreview && (
                                    <Badge variant="outline" className="text-xs">
                                      Preview
                                    </Badge>
                                  )}
                                  {lesson.quiz && (
                                    <Badge variant="secondary" className="text-xs">
                                      <HelpCircle className="w-3 h-3 mr-1" />
                                      Quiz
                                    </Badge>
                                  )}
                                </div>
                                {lesson.description && (
                                  <p className="text-sm text-muted-foreground">
                                    {lesson.description}
                                  </p>
                                )}
                                <div className="flex items-center space-x-4 text-xs text-muted-foreground mt-1">
                                  <div className="flex items-center space-x-1">
                                    <Clock className="w-3 h-3" />
                                    <span>{formatDuration(lesson.duration)}</span>
                                  </div>
                                  <span>Type: {lesson.lessonType}</span>
                                </div>
                              </div>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                              <Badge variant={lesson.isPublished ? "default" : "secondary"}>
                                {lesson.isPublished ? "Published" : "Draft"}
                              </Badge>
                              {!lesson.quiz ? (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedLessonForQuiz({ sectionId: section._id, lessonId: lesson._id })
                                    setShowQuizSelector(true)
                                  }}
                                >
                                  <HelpCircle className="w-4 h-4" />
                                </Button>
                              ) : (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-green-600"
                                  onClick={() => {
                                    setSelectedLessonForQuiz({ sectionId: section._id, lessonId: lesson._id })
                                    setShowQuizSelector(true)
                                  }}
                                >
                                  <HelpCircle className="w-4 h-4" />
                                </Button>
                              )}
                              <Button variant="ghost" size="sm">
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                        
                        {/* Add Lesson Button */}
                        <Button
                          variant="outline"
                          className="w-full"
                          onClick={() => {
                            setSelectedSectionForLesson(section._id)
                            setShowNewLessonDialog(true)
                          }}
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Add Lesson
                        </Button>
                      </div>
                    </CardContent>
                  </motion.div>
                )}
              </AnimatePresence>
            </Card>
          ))
        )}
      </div>

      {/* New Section Dialog */}
      <NewSectionDialog
        open={showNewSectionDialog}
        onOpenChange={setShowNewSectionDialog}
        onSubmit={createSection}
      />

      {/* New Lesson Dialog */}
      <NewLessonDialog
        open={showNewLessonDialog}
        onOpenChange={setShowNewLessonDialog}
        onSubmit={(lessonData) => createLesson(selectedSectionForLesson, lessonData)}
      />

      {/* Quiz Selector */}
      <QuizSelector
        open={showQuizSelector}
        onOpenChange={setShowQuizSelector}
        onQuizSelect={(quiz, settings) => {
          if (selectedLessonForQuiz) {
            attachQuizToLesson(
              selectedLessonForQuiz.sectionId,
              selectedLessonForQuiz.lessonId,
              quiz,
              settings
            )
          }
        }}
      />
    </div>
  )
}

// New Section Dialog Component
function NewSectionDialog({
  open,
  onOpenChange,
  onSubmit
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: { title: string; description: string }) => void
}) {
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!title.trim()) return

    setIsSubmitting(true)
    try {
      await onSubmit({ title: title.trim(), description: description.trim() })
      setTitle('')
      setDescription('')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add New Section</DialogTitle>
          <DialogDescription>
            Create a new section to organize your course content
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="text-sm font-medium">Section Title *</label>
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="e.g., Introduction to React"
              required
            />
          </div>

          <div>
            <label className="text-sm font-medium">Description</label>
            <Textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Brief description of what this section covers"
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting || !title.trim()}>
              {isSubmitting ? 'Creating...' : 'Create Section'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

// New Lesson Dialog Component
function NewLessonDialog({
  open,
  onOpenChange,
  onSubmit
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: any) => void
}) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    content: '',
    videoUrl: '',
    duration: 0,
    lessonType: 'video' as 'video' | 'text' | 'quiz',
    isPreview: false
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.title.trim()) return

    setIsSubmitting(true)
    try {
      await onSubmit(formData)
      setFormData({
        title: '',
        description: '',
        content: '',
        videoUrl: '',
        duration: 0,
        lessonType: 'video',
        isPreview: false
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Add New Lesson</DialogTitle>
          <DialogDescription>
            Create a new lesson with content, videos, or quizzes
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Lesson Title *</label>
              <Input
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="e.g., Setting up React"
                required
              />
            </div>

            <div>
              <label className="text-sm font-medium">Lesson Type</label>
              <Select
                value={formData.lessonType}
                onValueChange={(value: 'video' | 'text' | 'quiz') =>
                  setFormData(prev => ({ ...prev, lessonType: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="video">Video Lesson</SelectItem>
                  <SelectItem value="text">Text/Article</SelectItem>
                  <SelectItem value="quiz">Quiz</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <label className="text-sm font-medium">Description</label>
            <Textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Brief description of the lesson"
              rows={2}
            />
          </div>

          {formData.lessonType === 'video' && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Video URL</label>
                <Input
                  value={formData.videoUrl}
                  onChange={(e) => setFormData(prev => ({ ...prev, videoUrl: e.target.value }))}
                  placeholder="https://youtube.com/watch?v=..."
                />
              </div>

              <div>
                <label className="text-sm font-medium">Duration (minutes)</label>
                <Input
                  type="number"
                  value={formData.duration}
                  onChange={(e) => setFormData(prev => ({ ...prev, duration: parseInt(e.target.value) || 0 }))}
                  placeholder="15"
                />
              </div>
            </div>
          )}

          {formData.lessonType === 'text' && (
            <div>
              <label className="text-sm font-medium">Content</label>
              <Textarea
                value={formData.content}
                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                placeholder="Lesson content in markdown format"
                rows={6}
              />
            </div>
          )}

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="isPreview"
              checked={formData.isPreview}
              onChange={(e) => setFormData(prev => ({ ...prev, isPreview: e.target.checked }))}
            />
            <label htmlFor="isPreview" className="text-sm">
              Make this lesson available as a preview
            </label>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting || !formData.title.trim()}>
              {isSubmitting ? 'Creating...' : 'Create Lesson'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

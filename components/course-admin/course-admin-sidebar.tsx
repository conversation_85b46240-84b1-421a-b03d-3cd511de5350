'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  LayoutDashboard,
  BookOpen,
  Users,
  GraduationCap,
  BarChart3,
  FileText,
  Settings,
  FolderOpen,
  Award,
  DollarSign,
  PlayCircle,
  MessageSquare,
  Bell,
  ChevronLeft,
  ChevronRight,
  Home,
  LogOut
} from 'lucide-react'
import { useAuthStore } from '@/stores/auth.store'
import { cn } from '@/lib/utils'

const navigation = [
  {
    name: "Dashboard",
    href: "/courseadmin",
    icon: LayoutDashboard,
    description: "Overview and analytics"
  },
  {
    name: "Courses",
    href: "/courseadmin/courses",
    icon: BookO<PERSON>,
    description: "Manage all courses",
    badge: "12"
  },
  {
    name: "Categories",
    href: "/courseadmin/categories",
    icon: FolderOpen,
    description: "Manage course categories"
  },
  {
    name: "Instructors",
    href: "/courseadmin/instructors",
    icon: Users,
    description: "Manage instructors"
  },
  {
    name: "Enrollments",
    href: "/courseadmin/enrollments",
    icon: GraduationCap,
    description: "Student enrollments"
  },
  {
    name: "Reviews",
    href: "/courseadmin/reviews",
    icon: MessageSquare,
    description: "Course reviews and ratings"
  },
  {
    name: "Certificates",
    href: "/courseadmin/certificates",
    icon: Award,
    description: "Manage certificates"
  },
  {
    name: "Revenue",
    href: "/courseadmin/revenue",
    icon: DollarSign,
    description: "Revenue and payments"
  },
  {
    name: "Analytics",
    href: "/courseadmin/analytics",
    icon: BarChart3,
    description: "Performance analytics"
  },
  {
    name: "Reports",
    href: "/courseadmin/reports",
    icon: FileText,
    description: "Generate reports"
  },
  {
    name: "Settings",
    href: "/courseadmin/settings",
    icon: Settings,
    description: "Course admin settings"
  }
]

export function CourseAdminSidebar() {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const pathname = usePathname()
  const router = useRouter()
  const { user, logout } = useAuthStore()

  const handleLogout = async () => {
    await logout()
    router.push('/login')
  }

  const isActive = (href: string) => {
    if (href === '/courseadmin') {
      return pathname === href
    }
    return pathname.startsWith(href)
  }

  return (
    <motion.div
      className={cn(
        "relative flex flex-col h-full bg-card border-r border-border/50 shadow-sm",
        isCollapsed ? "w-16" : "w-64"
      )}
      animate={{ width: isCollapsed ? 64 : 256 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border/50">
        <AnimatePresence mode="wait">
          {!isCollapsed && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
              className="flex items-center space-x-2"
            >
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                <BookOpen className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-lg font-bold text-foreground">Course Admin</h2>
                <p className="text-xs text-muted-foreground">Learning Management</p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="h-8 w-8 p-0 hover:bg-accent"
        >
          {isCollapsed ? (
            <ChevronRight className="w-4 h-4" />
          ) : (
            <ChevronLeft className="w-4 h-4" />
          )}
        </Button>
      </div>

      {/* User Info */}
      <div className="p-4 border-b border-border/50">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-primary/20 to-primary/30 rounded-full flex items-center justify-center">
            <span className="text-sm font-semibold text-primary">
              {user?.name?.charAt(0) || 'A'}
            </span>
          </div>
          <AnimatePresence mode="wait">
            {!isCollapsed && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="flex-1 min-w-0"
              >
                <p className="text-sm font-medium text-foreground truncate">
                  {user?.name || 'Course Admin'}
                </p>
                <p className="text-xs text-muted-foreground truncate">
                  {user?.email || '<EMAIL>'}
                </p>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Navigation */}
      <ScrollArea className="flex-1 px-3 py-4">
        <nav className="space-y-1">
          {navigation.map((item) => {
            const active = isActive(item.href)
            
            return (
              <Link key={item.name} href={item.href}>
                <motion.div
                  className={cn(
                    "group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200",
                    active
                      ? "bg-primary text-primary-foreground shadow-sm"
                      : "text-muted-foreground hover:text-foreground hover:bg-accent"
                  )}
                  whileHover={{ x: 2 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <item.icon
                    className={cn(
                      "flex-shrink-0 w-5 h-5",
                      active ? "text-primary-foreground" : "text-muted-foreground group-hover:text-foreground"
                    )}
                  />
                  
                  <AnimatePresence mode="wait">
                    {!isCollapsed && (
                      <motion.div
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -10 }}
                        transition={{ duration: 0.2 }}
                        className="ml-3 flex-1 flex items-center justify-between"
                      >
                        <div>
                          <span className="truncate">{item.name}</span>
                          <p className="text-xs opacity-75 truncate">{item.description}</p>
                        </div>
                        {item.badge && (
                          <Badge 
                            variant={active ? "secondary" : "outline"} 
                            className="ml-2 text-xs"
                          >
                            {item.badge}
                          </Badge>
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              </Link>
            )
          })}
        </nav>
      </ScrollArea>

      {/* Footer Actions */}
      <div className="p-3 border-t border-border/50 space-y-2">
        <Link href="/">
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "w-full justify-start text-muted-foreground hover:text-foreground",
              isCollapsed && "justify-center"
            )}
          >
            <Home className="w-4 h-4" />
            <AnimatePresence mode="wait">
              {!isCollapsed && (
                <motion.span
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  transition={{ duration: 0.2 }}
                  className="ml-2"
                >
                  Back to Site
                </motion.span>
              )}
            </AnimatePresence>
          </Button>
        </Link>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleLogout}
          className={cn(
            "w-full justify-start text-muted-foreground hover:text-foreground",
            isCollapsed && "justify-center"
          )}
        >
          <LogOut className="w-4 h-4" />
          <AnimatePresence mode="wait">
            {!isCollapsed && (
              <motion.span
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -10 }}
                transition={{ duration: 0.2 }}
                className="ml-2"
              >
                Sign Out
              </motion.span>
            )}
          </AnimatePresence>
        </Button>
      </div>
    </motion.div>
  )
}

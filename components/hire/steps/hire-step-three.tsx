'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useHireStore } from '@/stores/hire.store'
import { TalentFrontend } from '@/lib/services/talent-frontend.service'
import { ContractType } from '@/types/hire.types'
import { 
  DollarSign, 
  MapPin, 
  Clock, 
  Shield, 
  FileText,
  Plus,
  X,
  AlertTriangle,
  Info
} from 'lucide-react'

interface HireStepThreeProps {
  talent: TalentFrontend
}

const CONTRACT_TYPES: Array<{
  value: ContractType
  label: string
  description: string
}> = [
  {
    value: 'employment',
    label: 'Employment Contract',
    description: 'Standard employment agreement with benefits'
  },
  {
    value: 'service',
    label: 'Service Agreement',
    description: 'Professional services contract'
  },
  {
    value: 'freelance',
    label: 'Freelance Contract',
    description: 'Independent contractor agreement'
  },
  {
    value: 'nda',
    label: 'NDA + Service Agreement',
    description: 'Service agreement with non-disclosure terms'
  }
]

const COMPENSATION_FREQUENCIES = [
  { value: 'hourly', label: 'Per Hour' },
  { value: 'daily', label: 'Per Day' },
  { value: 'weekly', label: 'Per Week' },
  { value: 'monthly', label: 'Per Month' },
  { value: 'yearly', label: 'Per Year' },
  { value: 'project', label: 'Per Project' }
]

const WORK_ARRANGEMENTS = [
  { value: 'remote', label: 'Remote', description: 'Work from anywhere' },
  { value: 'onsite', label: 'On-site', description: 'Work from office location' },
  { value: 'hybrid', label: 'Hybrid', description: 'Mix of remote and on-site' }
]

const CURRENCIES = [
  { value: 'USD', label: 'USD ($)' },
  { value: 'EUR', label: 'EUR (€)' },
  { value: 'GBP', label: 'GBP (£)' },
  { value: 'CAD', label: 'CAD (C$)' },
  { value: 'AUD', label: 'AUD (A$)' }
]

export function HireStepThree({ talent }: HireStepThreeProps) {
  const { formData, validationErrors, updateFormData } = useHireStore()
  const [newBenefit, setNewBenefit] = useState('')

  const handleAddBenefit = () => {
    if (newBenefit.trim()) {
      updateFormData({
        benefits: [...formData.benefits, newBenefit.trim()]
      })
      setNewBenefit('')
    }
  }

  const handleRemoveBenefit = (index: number) => {
    updateFormData({
      benefits: formData.benefits.filter((_, i) => i !== index)
    })
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleAddBenefit()
    }
  }

  return (
    <div className="space-y-6">
      {/* Contract Type */}
      <Card className="card-enhanced">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Contract Type
          </CardTitle>
          <CardDescription>
            Select the type of contract for this engagement
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Select 
            value={formData.contractType} 
            onValueChange={(value: ContractType) => updateFormData({ contractType: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select contract type" />
            </SelectTrigger>
            <SelectContent>
              {CONTRACT_TYPES.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  <div>
                    <div className="font-medium">{type.label}</div>
                    <div className="text-sm text-muted-foreground">{type.description}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* Compensation */}
      <Card className="card-enhanced">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="w-5 h-5" />
            Compensation
          </CardTitle>
          <CardDescription>
            Set the compensation amount and payment frequency
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="compensationAmount">Amount *</Label>
              <Input
                id="compensationAmount"
                type="number"
                value={formData.compensationAmount || ''}
                onChange={(e) => updateFormData({ compensationAmount: parseFloat(e.target.value) || 0 })}
                placeholder="0"
                min="0"
                step="0.01"
                className="mt-1"
              />
              {validationErrors.compensationAmount && (
                <div className="flex items-center gap-2 mt-1 text-sm text-destructive">
                  <AlertTriangle className="w-4 h-4" />
                  <span>{validationErrors.compensationAmount}</span>
                </div>
              )}
            </div>

            <div>
              <Label htmlFor="compensationCurrency">Currency</Label>
              <Select 
                value={formData.compensationCurrency} 
                onValueChange={(value) => updateFormData({ compensationCurrency: value })}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Currency" />
                </SelectTrigger>
                <SelectContent>
                  {CURRENCIES.map((currency) => (
                    <SelectItem key={currency.value} value={currency.value}>
                      {currency.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="compensationFrequency">Frequency *</Label>
              <Select 
                value={formData.compensationFrequency} 
                onValueChange={(value) => updateFormData({ compensationFrequency: value as any })}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Frequency" />
                </SelectTrigger>
                <SelectContent>
                  {COMPENSATION_FREQUENCIES.map((freq) => (
                    <SelectItem key={freq.value} value={freq.value}>
                      {freq.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {validationErrors.compensationFrequency && (
                <div className="flex items-center gap-2 mt-1 text-sm text-destructive">
                  <AlertTriangle className="w-4 h-4" />
                  <span>{validationErrors.compensationFrequency}</span>
                </div>
              )}
            </div>
          </div>

          {/* Benefits */}
          <div>
            <Label>Benefits & Perks</Label>
            <div className="flex gap-2 mt-1">
              <Input
                value={newBenefit}
                onChange={(e) => setNewBenefit(e.target.value)}
                placeholder="Add a benefit (e.g., Health insurance, Flexible hours)"
                onKeyPress={handleKeyPress}
                className="flex-1"
              />
              <Button 
                onClick={handleAddBenefit}
                disabled={!newBenefit.trim()}
                size="sm"
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>

            {formData.benefits.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {formData.benefits.map((benefit, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    {benefit}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveBenefit(index)}
                      className="h-4 w-4 p-0 ml-1 hover:bg-destructive hover:text-destructive-foreground"
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Work Arrangement */}
      <Card className="card-enhanced">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5" />
            Work Arrangement
          </CardTitle>
          <CardDescription>
            Define the work location and schedule requirements
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="workArrangementType">Work Type *</Label>
            <Select 
              value={formData.workArrangementType} 
              onValueChange={(value) => updateFormData({ workArrangementType: value as any })}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select work arrangement" />
              </SelectTrigger>
              <SelectContent>
                {WORK_ARRANGEMENTS.map((arrangement) => (
                  <SelectItem key={arrangement.value} value={arrangement.value}>
                    <div>
                      <div className="font-medium">{arrangement.label}</div>
                      <div className="text-sm text-muted-foreground">{arrangement.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors.workArrangementType && (
              <div className="flex items-center gap-2 mt-1 text-sm text-destructive">
                <AlertTriangle className="w-4 h-4" />
                <span>{validationErrors.workArrangementType}</span>
              </div>
            )}
          </div>

          {(formData.workArrangementType === 'onsite' || formData.workArrangementType === 'hybrid') && (
            <div>
              <Label htmlFor="workLocation">Work Location</Label>
              <Input
                id="workLocation"
                value={formData.workLocation}
                onChange={(e) => updateFormData({ workLocation: e.target.value })}
                placeholder="e.g., New York, NY or 123 Main St, City, State"
                className="mt-1"
              />
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="hoursPerWeek">Hours per Week</Label>
              <Input
                id="hoursPerWeek"
                type="number"
                value={formData.hoursPerWeek || ''}
                onChange={(e) => updateFormData({ hoursPerWeek: parseInt(e.target.value) || 40 })}
                placeholder="40"
                min="1"
                max="168"
                className="mt-1"
              />
            </div>

            <div className="flex items-center space-x-2 pt-6">
              <Switch
                id="flexibleHours"
                checked={formData.flexibleHours}
                onCheckedChange={(checked) => updateFormData({ flexibleHours: checked })}
              />
              <Label htmlFor="flexibleHours">Flexible Hours</Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contract Terms */}
      <Card className="card-enhanced">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Contract Terms
          </CardTitle>
          <CardDescription>
            Set additional contract terms and conditions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="probationPeriod">Probation Period (days)</Label>
              <Input
                id="probationPeriod"
                type="number"
                value={formData.probationPeriod || ''}
                onChange={(e) => updateFormData({ probationPeriod: parseInt(e.target.value) || 90 })}
                placeholder="90"
                min="0"
                max="365"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="noticePeriod">Notice Period (days)</Label>
              <Input
                id="noticePeriod"
                type="number"
                value={formData.noticePeriod || ''}
                onChange={(e) => updateFormData({ noticePeriod: parseInt(e.target.value) || 30 })}
                placeholder="30"
                min="0"
                max="365"
                className="mt-1"
              />
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Switch
                id="nonCompete"
                checked={formData.nonCompete}
                onCheckedChange={(checked) => updateFormData({ nonCompete: checked })}
              />
              <Label htmlFor="nonCompete">Non-Compete Agreement</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="nonDisclosure"
                checked={formData.nonDisclosure}
                onCheckedChange={(checked) => updateFormData({ nonDisclosure: checked })}
              />
              <Label htmlFor="nonDisclosure">Non-Disclosure Agreement (NDA)</Label>
            </div>
          </div>

          <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-start gap-2">
              <Info className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-blue-800">
                <p className="font-medium">Contract Terms Note:</p>
                <p className="mt-1">These terms will be included in the formal contract. You can modify them during the negotiation phase if needed.</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Next Steps Info */}
      <Card className="border-primary/20 bg-primary/5">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
              <Clock className="w-4 h-4 text-primary" />
            </div>
            <div>
              <h4 className="font-medium text-primary">Next: Review & Send</h4>
              <p className="text-sm text-muted-foreground mt-1">
                In the final step, you'll review all details and send the hire request to {talent.name}.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

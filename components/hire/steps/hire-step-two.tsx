'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useHireStore } from '@/stores/hire.store'
import { TalentFrontend } from '@/lib/services/talent-frontend.service'
import { 
  FileText, 
  Plus, 
  X, 
  Calendar, 
  Target, 
  CheckSquare,
  AlertTriangle,
  Lightbulb
} from 'lucide-react'

interface HireStepTwoProps {
  talent: TalentFrontend
}

export function HireStepTwo({ talent }: HireStepTwoProps) {
  const { formData, validationErrors, updateFormData } = useHireStore()
  const [newRequirement, setNewRequirement] = useState('')
  const [newDeliverable, setNewDeliverable] = useState('')

  const handleAddRequirement = () => {
    if (newRequirement.trim()) {
      updateFormData({
        requirements: [...formData.requirements, newRequirement.trim()]
      })
      setNewRequirement('')
    }
  }

  const handleRemoveRequirement = (index: number) => {
    updateFormData({
      requirements: formData.requirements.filter((_, i) => i !== index)
    })
  }

  const handleAddDeliverable = () => {
    if (newDeliverable.trim()) {
      updateFormData({
        deliverables: [...formData.deliverables, newDeliverable.trim()]
      })
      setNewDeliverable('')
    }
  }

  const handleRemoveDeliverable = (index: number) => {
    updateFormData({
      deliverables: formData.deliverables.filter((_, i) => i !== index)
    })
  }

  const handleKeyPress = (e: React.KeyboardEvent, action: () => void) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      action()
    }
  }

  return (
    <div className="space-y-6">
      {/* Project Title */}
      <Card className="card-enhanced">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Project Information
          </CardTitle>
          <CardDescription>
            Define the basic project details and scope
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="projectTitle">Project Title *</Label>
            <Input
              id="projectTitle"
              value={formData.projectTitle}
              onChange={(e) => updateFormData({ projectTitle: e.target.value })}
              placeholder="e.g., Senior Frontend Developer for E-commerce Platform"
              className="mt-1"
            />
            {validationErrors.projectTitle && (
              <div className="flex items-center gap-2 mt-1 text-sm text-destructive">
                <AlertTriangle className="w-4 h-4" />
                <span>{validationErrors.projectTitle}</span>
              </div>
            )}
          </div>

          <div>
            <Label htmlFor="projectDescription">Project Description *</Label>
            <Textarea
              id="projectDescription"
              value={formData.projectDescription}
              onChange={(e) => updateFormData({ projectDescription: e.target.value })}
              placeholder="Provide a detailed description of the project, role responsibilities, and what you're looking to achieve..."
              className="mt-1 min-h-[120px]"
            />
            {validationErrors.projectDescription && (
              <div className="flex items-center gap-2 mt-1 text-sm text-destructive">
                <AlertTriangle className="w-4 h-4" />
                <span>{validationErrors.projectDescription}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Requirements */}
      <Card className="card-enhanced">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5" />
            Requirements
          </CardTitle>
          <CardDescription>
            List the key requirements and qualifications needed
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              value={newRequirement}
              onChange={(e) => setNewRequirement(e.target.value)}
              placeholder="Add a requirement (e.g., 5+ years React experience)"
              onKeyPress={(e) => handleKeyPress(e, handleAddRequirement)}
              className="flex-1"
            />
            <Button 
              onClick={handleAddRequirement}
              disabled={!newRequirement.trim()}
              size="sm"
            >
              <Plus className="w-4 h-4" />
            </Button>
          </div>

          {formData.requirements.length > 0 && (
            <div className="space-y-2">
              {formData.requirements.map((requirement, index) => (
                <div key={index} className="flex items-center gap-2 p-2 bg-muted/50 rounded-md">
                  <Target className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                  <span className="flex-1 text-sm">{requirement}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveRequirement(index)}
                    className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
              ))}
            </div>
          )}

          {validationErrors.requirements && (
            <div className="flex items-center gap-2 text-sm text-destructive">
              <AlertTriangle className="w-4 h-4" />
              <span>{validationErrors.requirements}</span>
            </div>
          )}

          {formData.requirements.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Target className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No requirements added yet</p>
              <p className="text-xs">Add requirements to help the talent understand what you need</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Deliverables */}
      <Card className="card-enhanced">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckSquare className="w-5 h-5" />
            Deliverables
          </CardTitle>
          <CardDescription>
            Define what the talent should deliver upon completion
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              value={newDeliverable}
              onChange={(e) => setNewDeliverable(e.target.value)}
              placeholder="Add a deliverable (e.g., Fully functional web application)"
              onKeyPress={(e) => handleKeyPress(e, handleAddDeliverable)}
              className="flex-1"
            />
            <Button 
              onClick={handleAddDeliverable}
              disabled={!newDeliverable.trim()}
              size="sm"
            >
              <Plus className="w-4 h-4" />
            </Button>
          </div>

          {formData.deliverables.length > 0 && (
            <div className="space-y-2">
              {formData.deliverables.map((deliverable, index) => (
                <div key={index} className="flex items-center gap-2 p-2 bg-muted/50 rounded-md">
                  <CheckSquare className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                  <span className="flex-1 text-sm">{deliverable}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveDeliverable(index)}
                    className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
              ))}
            </div>
          )}

          {validationErrors.deliverables && (
            <div className="flex items-center gap-2 text-sm text-destructive">
              <AlertTriangle className="w-4 h-4" />
              <span>{validationErrors.deliverables}</span>
            </div>
          )}

          {formData.deliverables.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <CheckSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No deliverables added yet</p>
              <p className="text-xs">Add deliverables to clarify project outcomes</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Timeline */}
      <Card className="card-enhanced">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Project Timeline
          </CardTitle>
          <CardDescription>
            Set the project start date and optional end date
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startDate">Start Date *</Label>
              <Input
                id="startDate"
                type="date"
                value={formData.startDate}
                onChange={(e) => updateFormData({ startDate: e.target.value })}
                min={new Date().toISOString().split('T')[0]}
                className="mt-1"
              />
              {validationErrors.startDate && (
                <div className="flex items-center gap-2 mt-1 text-sm text-destructive">
                  <AlertTriangle className="w-4 h-4" />
                  <span>{validationErrors.startDate}</span>
                </div>
              )}
            </div>

            <div>
              <Label htmlFor="endDate">End Date (Optional)</Label>
              <Input
                id="endDate"
                type="date"
                value={formData.endDate}
                onChange={(e) => updateFormData({ endDate: e.target.value })}
                min={formData.startDate || new Date().toISOString().split('T')[0]}
                className="mt-1"
              />
              {validationErrors.endDate && (
                <div className="flex items-center gap-2 mt-1 text-sm text-destructive">
                  <AlertTriangle className="w-4 h-4" />
                  <span>{validationErrors.endDate}</span>
                </div>
              )}
            </div>
          </div>

          <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-start gap-2">
              <Lightbulb className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-blue-800">
                <p className="font-medium">Timeline Tips:</p>
                <ul className="mt-1 space-y-1 text-xs">
                  <li>• Leave end date empty for ongoing positions</li>
                  <li>• Consider talent's availability when setting start date</li>
                  <li>• Allow buffer time for onboarding and setup</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Next Steps Info */}
      <Card className="border-primary/20 bg-primary/5">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
              <FileText className="w-4 h-4 text-primary" />
            </div>
            <div>
              <h4 className="font-medium text-primary">Next: Contract Terms</h4>
              <p className="text-sm text-muted-foreground mt-1">
                In the next step, you'll set the compensation, work arrangement, and contract terms for this hire.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

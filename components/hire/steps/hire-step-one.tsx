'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { useHireStore } from '@/stores/hire.store'
import { TalentFrontend } from '@/lib/services/talent-frontend.service'
import { HireType } from '@/types/hire.types'
import { 
  Briefcase, 
  Clock, 
  Users, 
  FileText, 
  GraduationCap,
  MapPin,
  Star,
  AlertTriangle,
  Flag
} from 'lucide-react'

interface HireStepOneProps {
  talent: TalentFrontend
}

const HIRE_TYPES: Array<{
  value: HireType
  label: string
  description: string
  icon: React.ComponentType<any>
  recommended?: boolean
}> = [
  {
    value: 'full_time',
    label: 'Full-Time Employment',
    description: 'Permanent position with full benefits and long-term commitment',
    icon: Briefcase,
    recommended: true
  },
  {
    value: 'part_time',
    label: 'Part-Time Employment',
    description: 'Regular schedule with reduced hours, typically 20-30 hours per week',
    icon: Clock
  },
  {
    value: 'contract',
    label: 'Contract Work',
    description: 'Fixed-term contract for specific duration or project completion',
    icon: FileText
  },
  {
    value: 'freelance',
    label: 'Freelance Project',
    description: 'Independent contractor for specific deliverables or services',
    icon: Users
  },
  {
    value: 'internship',
    label: 'Internship',
    description: 'Learning-focused position for students or recent graduates',
    icon: GraduationCap
  },
  {
    value: 'temporary',
    label: 'Temporary Position',
    description: 'Short-term role to cover specific needs or peak periods',
    icon: Clock
  }
]

const PRIORITY_OPTIONS = [
  {
    value: 'low',
    label: 'Low Priority',
    description: 'Standard timeline, flexible start date',
    color: 'bg-gray-100 text-gray-800'
  },
  {
    value: 'medium',
    label: 'Medium Priority',
    description: 'Moderate urgency, preferred start within 4-6 weeks',
    color: 'bg-blue-100 text-blue-800'
  },
  {
    value: 'high',
    label: 'High Priority',
    description: 'Important role, need to start within 2-4 weeks',
    color: 'bg-orange-100 text-orange-800'
  },
  {
    value: 'urgent',
    label: 'Urgent',
    description: 'Critical need, immediate start required',
    color: 'bg-red-100 text-red-800'
  }
]

export function HireStepOne({ talent }: HireStepOneProps) {
  const { formData, validationErrors, updateFormData } = useHireStore()

  const handleHireTypeChange = (value: HireType) => {
    updateFormData({ hireType: value })
  }

  const handlePriorityChange = (value: string) => {
    updateFormData({ priority: value as any })
  }

  return (
    <div className="space-y-6">
      {/* Talent Summary */}
      <Card className="card-enhanced">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Talent Overview
          </CardTitle>
          <CardDescription>
            Review the talent you're about to hire
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-start gap-4">
            <Avatar className="w-16 h-16">
              <AvatarImage src={talent.avatar} alt={talent.name} />
              <AvatarFallback className="text-lg">
                {talent.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1">
              <h3 className="text-lg font-semibold">{talent.name}</h3>
              <p className="text-primary font-medium">{talent.currentTitle || talent.headline}</p>
              
              <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <MapPin className="w-4 h-4" />
                  <span>{talent.location}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  <span>{talent.rating?.toFixed(1) || 'N/A'}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Briefcase className="w-4 h-4" />
                  <span>{talent.experience?.level || 'Professional'}</span>
                </div>
              </div>
              
              {/* Skills */}
              <div className="flex flex-wrap gap-1 mt-3">
                {talent.skills.slice(0, 6).map((skill, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {skill.name || skill}
                  </Badge>
                ))}
                {talent.skills.length > 6 && (
                  <Badge variant="outline" className="text-xs">
                    +{talent.skills.length - 6} more
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Hire Type Selection */}
      <Card className="card-enhanced">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Briefcase className="w-5 h-5" />
            Employment Type
          </CardTitle>
          <CardDescription>
            Select the type of employment arrangement you're offering
          </CardDescription>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={formData.hireType}
            onValueChange={handleHireTypeChange}
            className="space-y-3"
          >
            {HIRE_TYPES.map((type) => {
              const Icon = type.icon
              return (
                <div key={type.value} className="flex items-start space-x-3">
                  <RadioGroupItem 
                    value={type.value} 
                    id={type.value}
                    className="mt-1"
                  />
                  <Label 
                    htmlFor={type.value} 
                    className="flex-1 cursor-pointer"
                  >
                    <div className="flex items-start gap-3 p-3 rounded-lg border border-muted hover:border-primary/50 transition-colors">
                      <Icon className="w-5 h-5 text-primary mt-0.5" />
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{type.label}</span>
                          {type.recommended && (
                            <Badge variant="secondary" className="text-xs">
                              Recommended
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">
                          {type.description}
                        </p>
                      </div>
                    </div>
                  </Label>
                </div>
              )
            })}
          </RadioGroup>
          
          {validationErrors.hireType && (
            <div className="flex items-center gap-2 mt-2 text-sm text-destructive">
              <AlertTriangle className="w-4 h-4" />
              <span>{validationErrors.hireType}</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Priority Selection */}
      <Card className="card-enhanced">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Flag className="w-5 h-5" />
            Priority Level
          </CardTitle>
          <CardDescription>
            Set the urgency level for this hire request
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Select value={formData.priority} onValueChange={handlePriorityChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select priority level" />
            </SelectTrigger>
            <SelectContent>
              {PRIORITY_OPTIONS.map((priority) => (
                <SelectItem key={priority.value} value={priority.value}>
                  <div className="flex items-center gap-2">
                    <Badge className={`text-xs ${priority.color}`}>
                      {priority.label}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {priority.description}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* Next Steps Info */}
      <Card className="border-primary/20 bg-primary/5">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
              <Briefcase className="w-4 h-4 text-primary" />
            </div>
            <div>
              <h4 className="font-medium text-primary">Next: Project Details</h4>
              <p className="text-sm text-muted-foreground mt-1">
                In the next step, you'll define the project scope, requirements, deliverables, and timeline for this hire.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { useHireStore } from '@/stores/hire.store'
import { TalentFrontend } from '@/lib/services/talent-frontend.service'
import { hireFrontendService } from '@/lib/services/hire-frontend.service'
import { 
  Send, 
  User, 
  Briefcase, 
  DollarSign, 
  MapPin, 
  Calendar, 
  FileText,
  Target,
  CheckSquare,
  Shield,
  Clock,
  AlertTriangle,
  Info
} from 'lucide-react'

interface HireStepFourProps {
  talent: TalentFrontend
}

export function HireStepFour({ talent }: HireStepFourProps) {
  const { formData, validationErrors, updateFormData } = useHireStore()

  const formatCurrency = (amount: number, currency: string) => {
    return hireFrontendService.formatCurrency(amount, currency)
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Not specified'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getHireTypeLabel = (type: string) => {
    return hireFrontendService.getTypeLabel(type as any)
  }

  return (
    <div className="space-y-6">
      {/* Talent Summary */}
      <Card className="card-enhanced">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="w-5 h-5" />
            Hiring Summary
          </CardTitle>
          <CardDescription>
            Review the hire request details before sending
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-start gap-4">
            <Avatar className="w-16 h-16">
              <AvatarImage src={talent.avatar} alt={talent.name} />
              <AvatarFallback className="text-lg">
                {talent.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1">
              <h3 className="text-lg font-semibold">{talent.name}</h3>
              <p className="text-primary font-medium">{talent.currentTitle || talent.headline}</p>
              <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <MapPin className="w-4 h-4" />
                  <span>{talent.location}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Briefcase className="w-4 h-4" />
                  <span>{getHireTypeLabel(formData.hireType)}</span>
                </div>
              </div>
            </div>
            
            <Badge className={hireFrontendService.getPriorityColor(formData.priority)}>
              {formData.priority.charAt(0).toUpperCase() + formData.priority.slice(1)} Priority
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Project Details Review */}
      <Card className="card-enhanced">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Project Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium text-sm text-muted-foreground">PROJECT TITLE</h4>
            <p className="mt-1">{formData.projectTitle}</p>
          </div>

          <div>
            <h4 className="font-medium text-sm text-muted-foreground">DESCRIPTION</h4>
            <p className="mt-1 text-sm leading-relaxed">{formData.projectDescription}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-sm text-muted-foreground flex items-center gap-1">
                <Target className="w-4 h-4" />
                REQUIREMENTS ({formData.requirements.length})
              </h4>
              <div className="mt-2 space-y-1">
                {formData.requirements.slice(0, 3).map((req, index) => (
                  <div key={index} className="text-sm flex items-start gap-2">
                    <span className="w-1 h-1 bg-muted-foreground rounded-full mt-2 flex-shrink-0" />
                    <span>{req}</span>
                  </div>
                ))}
                {formData.requirements.length > 3 && (
                  <p className="text-xs text-muted-foreground">
                    +{formData.requirements.length - 3} more requirements
                  </p>
                )}
              </div>
            </div>

            <div>
              <h4 className="font-medium text-sm text-muted-foreground flex items-center gap-1">
                <CheckSquare className="w-4 h-4" />
                DELIVERABLES ({formData.deliverables.length})
              </h4>
              <div className="mt-2 space-y-1">
                {formData.deliverables.slice(0, 3).map((del, index) => (
                  <div key={index} className="text-sm flex items-start gap-2">
                    <span className="w-1 h-1 bg-muted-foreground rounded-full mt-2 flex-shrink-0" />
                    <span>{del}</span>
                  </div>
                ))}
                {formData.deliverables.length > 3 && (
                  <p className="text-xs text-muted-foreground">
                    +{formData.deliverables.length - 3} more deliverables
                  </p>
                )}
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-sm text-muted-foreground flex items-center gap-1">
              <Calendar className="w-4 h-4" />
              TIMELINE
            </h4>
            <div className="mt-1 flex items-center gap-4 text-sm">
              <span>Start: {formatDate(formData.startDate)}</span>
              {formData.endDate && (
                <span>End: {formatDate(formData.endDate)}</span>
              )}
              {!formData.endDate && (
                <span className="text-muted-foreground">Ongoing position</span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contract Terms Review */}
      <Card className="card-enhanced">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="w-5 h-5" />
            Contract Terms
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">COMPENSATION</h4>
              <p className="mt-1 text-lg font-semibold">
                {formatCurrency(formData.compensationAmount, formData.compensationCurrency)}
                <span className="text-sm font-normal text-muted-foreground ml-1">
                  {formData.compensationFrequency}
                </span>
              </p>
            </div>

            <div>
              <h4 className="font-medium text-sm text-muted-foreground">WORK ARRANGEMENT</h4>
              <div className="mt-1">
                <p className="capitalize">{formData.workArrangementType}</p>
                {formData.workLocation && (
                  <p className="text-sm text-muted-foreground">{formData.workLocation}</p>
                )}
                <p className="text-sm text-muted-foreground">
                  {formData.hoursPerWeek} hours/week
                  {formData.flexibleHours && ' • Flexible hours'}
                </p>
              </div>
            </div>
          </div>

          {formData.benefits.length > 0 && (
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">BENEFITS</h4>
              <div className="flex flex-wrap gap-1 mt-1">
                {formData.benefits.map((benefit, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {benefit}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <h4 className="font-medium text-muted-foreground">CONTRACT TYPE</h4>
              <p className="mt-1 capitalize">{formData.contractType.replace('_', ' ')}</p>
            </div>
            <div>
              <h4 className="font-medium text-muted-foreground">PROBATION</h4>
              <p className="mt-1">{formData.probationPeriod} days</p>
            </div>
            <div>
              <h4 className="font-medium text-muted-foreground">NOTICE PERIOD</h4>
              <p className="mt-1">{formData.noticePeriod} days</p>
            </div>
          </div>

          <div className="flex gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Shield className="w-4 h-4 text-muted-foreground" />
              <span className={formData.nonDisclosure ? 'text-green-600' : 'text-muted-foreground'}>
                NDA {formData.nonDisclosure ? 'Required' : 'Not required'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Shield className="w-4 h-4 text-muted-foreground" />
              <span className={formData.nonCompete ? 'text-orange-600' : 'text-muted-foreground'}>
                Non-compete {formData.nonCompete ? 'Required' : 'Not required'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Message & Timeline */}
      <Card className="card-enhanced">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Send className="w-5 h-5" />
            Message & Response Timeline
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="message">Personal Message (Optional)</Label>
            <Textarea
              id="message"
              value={formData.message}
              onChange={(e) => updateFormData({ message: e.target.value })}
              placeholder="Add a personal message to accompany your hire request..."
              className="mt-1 min-h-[100px]"
            />
          </div>

          <div>
            <Label htmlFor="responseDeadline">Response Deadline (Optional)</Label>
            <Input
              id="responseDeadline"
              type="date"
              value={formData.responseDeadline}
              onChange={(e) => updateFormData({ responseDeadline: e.target.value })}
              min={new Date().toISOString().split('T')[0]}
              className="mt-1"
            />
            {validationErrors.responseDeadline && (
              <div className="flex items-center gap-2 mt-1 text-sm text-destructive">
                <AlertTriangle className="w-4 h-4" />
                <span>{validationErrors.responseDeadline}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Terms & Conditions */}
      <Card className="card-enhanced">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Terms & Conditions
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-start space-x-2">
            <Checkbox
              id="termsAccepted"
              checked={formData.termsAccepted}
              onCheckedChange={(checked) => updateFormData({ termsAccepted: !!checked })}
              className="mt-1"
            />
            <Label htmlFor="termsAccepted" className="text-sm leading-relaxed">
              I agree to the <a href="/terms" className="text-primary hover:underline">Terms of Service</a> and 
              confirm that all information provided is accurate. I understand that this hire request is binding 
              once accepted by the talent.
            </Label>
          </div>
          {validationErrors.termsAccepted && (
            <div className="flex items-center gap-2 text-sm text-destructive">
              <AlertTriangle className="w-4 h-4" />
              <span>{validationErrors.termsAccepted}</span>
            </div>
          )}

          <div className="flex items-start space-x-2">
            <Checkbox
              id="privacyAccepted"
              checked={formData.privacyAccepted}
              onCheckedChange={(checked) => updateFormData({ privacyAccepted: !!checked })}
              className="mt-1"
            />
            <Label htmlFor="privacyAccepted" className="text-sm leading-relaxed">
              I acknowledge that I have read and agree to the <a href="/privacy" className="text-primary hover:underline">Privacy Policy</a> and 
              consent to the processing of personal data as described.
            </Label>
          </div>
          {validationErrors.privacyAccepted && (
            <div className="flex items-center gap-2 text-sm text-destructive">
              <AlertTriangle className="w-4 h-4" />
              <span>{validationErrors.privacyAccepted}</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Final Notice */}
      <Card className="border-primary/20 bg-primary/5">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
              <Info className="w-4 h-4 text-primary" />
            </div>
            <div>
              <h4 className="font-medium text-primary">Ready to Send</h4>
              <p className="text-sm text-muted-foreground mt-1">
                Once you send this hire request, {talent.name} will be notified and can review, negotiate, or accept your offer. 
                You'll be able to track the status and communicate through the platform.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { TalentFrontend } from '@/lib/services/talent-frontend.service'
import { HireFrontend } from '@/types/hire.types'
import { hireFrontendService } from '@/lib/services/hire-frontend.service'
import { 
  CheckCircle, 
  Send, 
  Clock, 
  Bell, 
  MessageCircle,
  ExternalLink,
  Calendar,
  DollarSign,
  Briefcase
} from 'lucide-react'

interface HireSuccessProps {
  talent: TalentFrontend
  hireRequest: HireFrontend | null
  onClose: () => void
}

export function HireSuccess({ talent, hireRequest, onClose }: HireSuccessProps) {
  const formatCurrency = (amount: number, currency: string) => {
    return hireFrontendService.formatCurrency(amount, currency)
  }

  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getHireTypeLabel = (type: string) => {
    return hireFrontendService.getTypeLabel(type as any)
  }

  return (
    <div className="space-y-6 text-center">
      {/* Success Icon */}
      <div className="flex justify-center">
        <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
          <CheckCircle className="w-10 h-10 text-green-600" />
        </div>
      </div>

      {/* Success Message */}
      <div>
        <h2 className="text-2xl font-bold text-green-600 mb-2">
          Hire Request Sent Successfully!
        </h2>
        <p className="text-muted-foreground">
          Your hire request has been sent to {talent.name}. They will be notified immediately 
          and can review your offer.
        </p>
      </div>

      {/* Hire Request Summary */}
      {hireRequest && (
        <Card className="card-enhanced text-left">
          <CardContent className="pt-6">
            <div className="flex items-start gap-4 mb-4">
              <Avatar className="w-12 h-12">
                <AvatarImage src={talent.avatar} alt={talent.name} />
                <AvatarFallback>
                  {talent.name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1">
                <h3 className="font-semibold">{talent.name}</h3>
                <p className="text-sm text-muted-foreground">{talent.currentTitle || talent.headline}</p>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="secondary" className="text-xs">
                    {getHireTypeLabel(hireRequest.hireType)}
                  </Badge>
                  <Badge className={`text-xs ${hireFrontendService.getPriorityColor(hireRequest.priority)}`}>
                    {hireRequest.priority.charAt(0).toUpperCase() + hireRequest.priority.slice(1)} Priority
                  </Badge>
                </div>
              </div>
              
              <Badge className={hireFrontendService.getStatusColor(hireRequest.status)}>
                {hireRequest.status.replace('_', ' ').toUpperCase()}
              </Badge>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Briefcase className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="font-medium">{hireRequest.projectDetails.title}</p>
                  <p className="text-muted-foreground">Project</p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <DollarSign className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="font-medium">
                    {formatCurrency(
                      hireRequest.contractTerms.compensation.amount, 
                      hireRequest.contractTerms.compensation.currency
                    )}
                  </p>
                  <p className="text-muted-foreground">
                    {hireRequest.contractTerms.compensation.frequency}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="font-medium">
                    {formatDate(hireRequest.projectDetails.timeline.startDate)}
                  </p>
                  <p className="text-muted-foreground">Start Date</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Next Steps */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="pt-6">
          <h3 className="font-semibold text-blue-800 mb-3 flex items-center gap-2">
            <Clock className="w-5 h-5" />
            What Happens Next?
          </h3>
          <div className="space-y-3 text-left">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-xs font-medium text-blue-600">1</span>
              </div>
              <div>
                <p className="font-medium text-blue-800">Notification Sent</p>
                <p className="text-sm text-blue-600">
                  {talent.name} will receive an email and in-app notification about your hire request.
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-xs font-medium text-blue-600">2</span>
              </div>
              <div>
                <p className="font-medium text-blue-800">Review & Response</p>
                <p className="text-sm text-blue-600">
                  They can review your offer, ask questions, negotiate terms, or accept directly.
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-xs font-medium text-blue-600">3</span>
              </div>
              <div>
                <p className="font-medium text-blue-800">Communication</p>
                <p className="text-sm text-blue-600">
                  You'll be notified of their response and can communicate through the platform.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        <Button
          onClick={() => {
            // Navigate to hire management dashboard
            window.location.href = '/companies/dashboard/hiring'
          }}
          className="flex items-center gap-2"
        >
          <Briefcase className="w-4 h-4" />
          View Hire Requests
        </Button>
        
        <Button
          variant="outline"
          onClick={() => {
            // Open messaging with talent
            window.location.href = `/messages?talent=${talent.id}`
          }}
          className="flex items-center gap-2"
        >
          <MessageCircle className="w-4 h-4" />
          Message {talent.name}
        </Button>
        
        <Button
          variant="outline"
          onClick={onClose}
          className="flex items-center gap-2"
        >
          <ExternalLink className="w-4 h-4" />
          Continue Browsing
        </Button>
      </div>

      {/* Additional Info */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
          <Bell className="w-4 h-4" />
          <span>You'll receive notifications about status updates</span>
        </div>
        
        {hireRequest?.responseDeadline && (
          <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
            <Clock className="w-4 h-4" />
            <span>
              Response deadline: {formatDate(hireRequest.responseDeadline)}
            </span>
          </div>
        )}
      </div>

      {/* Tips */}
      <Card className="border-amber-200 bg-amber-50">
        <CardContent className="pt-6">
          <h4 className="font-medium text-amber-800 mb-2">💡 Pro Tips</h4>
          <ul className="text-sm text-amber-700 space-y-1 text-left">
            <li>• Be responsive to questions and feedback from the talent</li>
            <li>• Consider being flexible on terms if they're a great fit</li>
            <li>• Use the messaging system to build rapport before finalizing</li>
            <li>• Check your notifications regularly for updates</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}

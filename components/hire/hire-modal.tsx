// components/hire/hire-modal.tsx
'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { useHireStore } from '@/stores/hire.store'
import { useAuthStore } from '@/stores/auth.store'
import { TalentFrontend } from '@/lib/services/talent-frontend.service'
import { HireFrontend } from '@/types/hire.types'
import { useToast } from '@/hooks/use-toast'
import {
  X,
  CheckCircle,
  AlertCircle,
  Send
} from 'lucide-react'

interface HireModalProps {
  talent: TalentFrontend | null
  isOpen: boolean
  onClose: () => void
  onSuccess?: (hireRequest: HireFrontend) => void
}



export function HireModal({ talent, isOpen, onClose, onSuccess }: HireModalProps) {
  const { user } = useAuthStore()
  const { toast } = useToast()
  const {
    isCreating,
    createError,
    resetForm,
    createHireRequest,
    closeHireModal,
    clearErrors
  } = useHireStore()

  const [isSubmitted, setIsSubmitted] = useState(false)
  const [createdHireRequest, setCreatedHireRequest] = useState<HireFrontend | null>(null)

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen && talent) {
      resetForm()
      setIsSubmitted(false)
      setCreatedHireRequest(null)
      clearErrors()
    }
  }, [isOpen, talent, resetForm, clearErrors])

  // Check authentication
  useEffect(() => {
    if (isOpen && (!user || !['company_admin', 'recruiter'].includes(user.role))) {
      toast({
        title: "Access Denied",
        description: "Only company administrators and recruiters can send hire requests.",
        variant: "destructive"
      })
      onClose()
    }
  }, [isOpen, user, toast, onClose])

  if (!talent) return null

  const handleSubmit = async () => {
    if (!talent) return

    try {
      // Simple hire request data for now
      const hireRequestData = {
        talentId: talent.id,
        hireType: 'full_time' as const,
        projectDetails: {
          title: 'New Project',
          description: 'Project description',
          requirements: ['Requirement 1'],
          deliverables: ['Deliverable 1'],
          timeline: {
            startDate: new Date().toISOString().split('T')[0],
            milestones: []
          }
        },
        contractTerms: {
          type: 'employment' as const,
          duration: {
            startDate: new Date().toISOString().split('T')[0],
            isIndefinite: true
          },
          compensation: {
            amount: 50000,
            currency: 'USD',
            frequency: 'yearly' as const,
            benefits: []
          },
          workArrangement: {
            type: 'remote' as const,
            hoursPerWeek: 40,
            flexibleHours: true
          },
          terms: {
            probationPeriod: 90,
            noticePeriod: 30,
            nonCompete: false,
            nonDisclosure: true
          }
        },
        message: 'We would like to hire you for our project.',
        priority: 'medium' as const
      }

      const result = await createHireRequest(hireRequestData)

      if (result) {
        setCreatedHireRequest(result)
        setIsSubmitted(true)

        toast({
          title: "Hire Request Sent!",
          description: `Your hire request has been sent to ${talent.name}.`,
        })

        if (onSuccess) {
          onSuccess(result)
        }
      }
    } catch (error) {
      console.error('Failed to create hire request:', error)
      toast({
        title: "Error",
        description: "Failed to send hire request. Please try again.",
        variant: "destructive"
      })
    }
  }

  const handleClose = () => {
    if (isSubmitted) {
      setIsSubmitted(false)
      setCreatedHireRequest(null)
    }
    closeHireModal()
    onClose()
  }

  const renderStepContent = () => {
    if (isSubmitted) {
      return (
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h3 className="text-xl font-semibold text-green-600">Hire Request Sent!</h3>
          <p className="text-muted-foreground">
            Your hire request has been sent to {talent?.name}. They will be notified and can review your offer.
            {createdHireRequest && (
              <span className="block mt-2 text-sm">Request ID: {createdHireRequest.id}</span>
            )}
          </p>
          <Button onClick={handleClose} className="mt-4">
            Close
          </Button>
        </div>
      )
    }

    return (
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold">Send Hire Request</h3>
          <p className="text-muted-foreground">
            Send a hire request to {talent?.name} for your project.
          </p>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Project Title</label>
            <input
              type="text"
              className="w-full p-2 border rounded-md"
              placeholder="Enter project title"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Project Description</label>
            <textarea
              className="w-full p-2 border rounded-md h-24"
              placeholder="Describe your project and requirements"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Budget (USD)</label>
            <input
              type="number"
              className="w-full p-2 border rounded-md"
              placeholder="Enter your budget"
            />
          </div>
        </div>
      </div>
    )
  }

  const canProceed = () => {
    return talent && !isCreating
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold">
              {isSubmitted ? 'Hire Request Sent!' : `Hire ${talent?.name}`}
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        {/* Error Display */}
        {createError && (
          <div className="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
            <AlertCircle className="w-4 h-4 text-destructive" />
            <span className="text-sm text-destructive">{createError}</span>
          </div>
        )}

        {/* Step Content */}
        <div className="flex-1 overflow-y-auto">
          {renderStepContent()}
        </div>

        {/* Footer Actions */}
        {!isSubmitted && (
          <div className="flex-shrink-0 flex items-center justify-end pt-4 border-t gap-2">
            <Button
              variant="outline"
              onClick={handleClose}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={!canProceed() || isCreating}
              className="flex items-center gap-2"
            >
              {isCreating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4" />
                  Send Hire Request
                </>
              )}
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}

'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores'
import { PageLoader } from '@/components/ui/page-loader'
import { ErrorAlert } from '@/components/ui/error-alert'
import { Button } from '@/components/ui/button'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: 'admin' | 'company_admin' | 'recruiter' | 'job_seeker'
  fallbackPath?: string
  showLoader?: boolean
}

export function ProtectedRoute({ 
  children, 
  requiredRole,
  fallbackPath = '/login',
  showLoader = true
}: ProtectedRouteProps) {
  const router = useRouter()
  const { user, isAuthenticated, checkAuthStatus, isLoading } = useAuthStore()
  const [isChecking, setIsChecking] = useState(true)
  const [hasAccess, setHasAccess] = useState(false)

  useEffect(() => {
    const checkAccess = async () => {
      console.log('🔍 ProtectedRoute: Checking access')
      console.log('🔍 ProtectedRoute: Current auth state:', { isAuthenticated, hasUser: !!user })
      console.log('🔍 ProtectedRoute: Required role:', requiredRole)

      try {
        // Always check authentication status to ensure fresh state
        console.log('🔍 ProtectedRoute: Checking auth status')
        await checkAuthStatus()

        // Wait a moment for state to fully update
        await new Promise(resolve => setTimeout(resolve, 200))

        // Get the latest state after auth check and delay
        const currentState = useAuthStore.getState()
        console.log('🔍 ProtectedRoute: Auth state after check and delay:', {
          isAuthenticated: currentState.isAuthenticated,
          hasUser: !!currentState.user,
          userRole: currentState.user?.role,
          userEmail: currentState.user?.email,
          hasToken: !!currentState.token
        })

        if (!currentState.isAuthenticated || !currentState.user) {
          console.log('🔍 ProtectedRoute: Not authenticated, redirecting to login')
          router.push(`${fallbackPath}?redirect=${encodeURIComponent(window.location.pathname)}`)
          return
        }

        // Check role-based access if required
        if (requiredRole && currentState.user.role !== requiredRole) {
          console.log('🔍 ProtectedRoute: Role mismatch:', {
            required: requiredRole,
            actual: currentState.user.role,
            userEmail: currentState.user.email,
            userId: currentState.user.id
          })
          setHasAccess(false)
          setIsChecking(false)
          return
        }

        console.log('🔍 ProtectedRoute: Access granted for user:', {
          email: currentState.user.email,
          role: currentState.user.role
        })
        // User has access
        setHasAccess(true)
        setIsChecking(false)

      } catch (error) {
        console.error('🔍 ProtectedRoute: Auth check failed:', error)
        router.push(`${fallbackPath}?redirect=${encodeURIComponent(window.location.pathname)}`)
      }
    }

    checkAccess()
  }, [requiredRole, router, fallbackPath, checkAuthStatus])

  // Show loading state
  if (isChecking || isLoading) {
    return showLoader ? (
      <PageLoader 
        message="Checking authentication..." 
        fullScreen 
        size="lg" 
      />
    ) : null
  }

  // Show access denied for role-based restrictions
  if (!hasAccess && requiredRole) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-md w-full space-y-4">
          <ErrorAlert
            type="warning"
            title="Access Denied"
            message={`You need ${requiredRole.replace('_', ' ')} privileges to access this page.`}
          />
          <div className="flex space-x-2">
            <Button 
              onClick={() => router.back()}
              variant="outline"
              className="flex-1"
            >
              Go Back
            </Button>
            <Button
              onClick={() => {
                const redirectPath = user?.role === 'admin' ? '/admin' :
                                   user?.role === 'company_admin' || user?.role === 'recruiter' ? '/company-dashboard' :
                                   '/client-dashboard'
                router.push(redirectPath)
              }}
              className="flex-1"
            >
              Dashboard
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Render protected content
  return <>{children}</>
}

// Higher-order component for protecting pages
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options?: Omit<ProtectedRouteProps, 'children'>
) {
  const AuthenticatedComponent = (props: P) => (
    <ProtectedRoute {...options}>
      <Component {...props} />
    </ProtectedRoute>
  )

  AuthenticatedComponent.displayName = `withAuth(${Component.displayName || Component.name})`
  
  return AuthenticatedComponent
}

// Hook for checking authentication in components
export function useRequireAuth(requiredRole?: string) {
  const { user, isAuthenticated } = useAuthStore()
  const router = useRouter()

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    if (requiredRole && user?.role !== requiredRole) {
      // Redirect to appropriate dashboard based on user role
      const redirectPath = user?.role === 'admin' ? '/admin' :
                          user?.role === 'company_admin' || user?.role === 'recruiter' ? '/company-dashboard' :
                          '/client-dashboard'
      router.push(redirectPath)
      return
    }
  }, [isAuthenticated, user, requiredRole, router])

  return {
    isAuthenticated,
    user,
    hasRequiredRole: !requiredRole || user?.role === requiredRole
  }
}

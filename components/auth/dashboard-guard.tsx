'use client'

import { useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuthStore } from '@/stores/auth.store'

interface DashboardGuardProps {
  children: React.ReactNode
  allowedRoles: string[]
  redirectPath?: string
}

export function DashboardGuard({ 
  children, 
  allowedRoles, 
  redirectPath 
}: DashboardGuardProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { user, isAuthenticated } = useAuthStore()

  useEffect(() => {
    if (!isAuthenticated || !user) {
      // Not authenticated, redirect to login
      router.push('/auth/login')
      return
    }

    if (!allowedRoles.includes(user.role)) {
      // User doesn't have the right role, redirect to their appropriate dashboard
      const correctDashboard = getCorrectDashboardForRole(user.role)
      
      // Only redirect if we're not already on the correct dashboard
      if (!pathname.startsWith(correctDashboard)) {
        router.push(correctDashboard)
      }
      return
    }
  }, [isAuthenticated, user, router, pathname, allowedRoles])

  // Helper function to get the correct dashboard based on user role
  function getCorrectDashboardForRole(role: string): string {
    switch (role) {
      case 'admin':
        return '/admin'
      case 'company_admin':
      case 'recruiter':
        return '/company-dashboard'
      case 'job_seeker':
      default:
        return '/client-dashboard'
    }
  }

  // Don't render children if user doesn't have access
  if (!isAuthenticated || !user || !allowedRoles.includes(user.role)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return <>{children}</>
}

// Convenience components for specific dashboard types
export function AdminDashboardGuard({ children }: { children: React.ReactNode }) {
  return (
    <DashboardGuard allowedRoles={['admin']}>
      {children}
    </DashboardGuard>
  )
}

export function CompanyDashboardGuard({ children }: { children: React.ReactNode }) {
  return (
    <DashboardGuard allowedRoles={['company_admin', 'recruiter']}>
      {children}
    </DashboardGuard>
  )
}

export function ClientDashboardGuard({ children }: { children: React.ReactNode }) {
  return (
    <DashboardGuard allowedRoles={['job_seeker']}>
      {children}
    </DashboardGuard>
  )
}

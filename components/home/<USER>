"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  BookOpen, 
  ArrowRight, 
  Star, 
  Users, 
  Clock, 
  Award,
  TrendingUp,
  Zap,
  Play,
  ChevronRight,
  Loader2,
  GraduationCap
} from "lucide-react"
import { useCourseCategories, useCourses } from "@/stores/course.store"
import { CourseCard } from "@/components/courses/course-card"
import { cn } from "@/lib/utils"

export function CoursesSection() {
  const { categories, isLoading: isLoadingCategories, fetchCategories } = useCourseCategories()
  const { courses, isLoading: isLoadingCourses, fetchCourses } = useCourses()
  
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  useEffect(() => {
    fetchCategories()
    // Fetch featured courses initially
    fetchCourses({ featured: true }, { limit: 6 })
  }, [fetchCategories, fetchCourses])

  const handleCategoryClick = (categoryId: string) => {
    setSelectedCategory(categoryId)
    fetchCourses({ category: categoryId }, { limit: 6 })
  }

  const handleShowAllCourses = () => {
    setSelectedCategory(null)
    fetchCourses({ featured: true }, { limit: 6 })
  }

  const featuredCourses = courses.slice(0, 6)

  return (
    <section className="py-16 sm:py-20 bg-gradient-to-br from-background via-background to-muted/20 scroll-mt-20" id="courses">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12 sm:mb-16"
        >
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-primary/10 rounded-full">
              <GraduationCap className="w-8 h-8 text-primary" />
            </div>
          </div>
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
            Professional <span className="text-primary">Courses</span>
          </h2>
          <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Master new skills with our premium courses designed by industry experts. 
            From beginner to advanced, find the perfect course to advance your career.
          </p>
        </motion.div>

        {/* Category Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="flex flex-wrap justify-center gap-2 sm:gap-3 mb-12"
        >
          <Button
            variant={selectedCategory === null ? "default" : "outline"}
            onClick={handleShowAllCourses}
            className="text-sm sm:text-base"
          >
            <TrendingUp className="w-4 h-4 mr-2" />
            Featured
          </Button>
          
          {categories.slice(0, 5).map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              onClick={() => handleCategoryClick(category.id)}
              className="text-sm sm:text-base"
            >
              <span className="mr-2">{category.icon}</span>
              <span className="hidden sm:inline">{category.name}</span>
              <span className="sm:hidden">{category.name.split(' ')[0]}</span>
            </Button>
          ))}
        </motion.div>

        {/* Courses Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {isLoadingCourses ? (
            <div className="text-center py-12">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
              <p className="text-muted-foreground">Loading courses...</p>
            </div>
          ) : featuredCourses.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 mb-12">
              {featuredCourses.map((course, index) => (
                <motion.div
                  key={course._id.toString()}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.1 * index }}
                >
                  <CourseCard course={course} viewMode="grid" />
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <BookOpen className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-xl font-semibold mb-2">No Courses Found</h3>
              <p className="text-muted-foreground mb-4">
                {selectedCategory 
                  ? "No courses available in this category yet." 
                  : "No featured courses available at the moment."
                }
              </p>
              <Button onClick={handleShowAllCourses} variant="outline">
                View All Courses
              </Button>
            </div>
          )}
        </motion.div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 mb-12"
        >
          <Card className="glass border-primary/20 text-center">
            <CardContent className="p-4 sm:p-6">
              <div className="text-2xl sm:text-3xl font-bold text-primary mb-2">
                {courses.length}+
              </div>
              <p className="text-sm sm:text-base text-muted-foreground">Premium Courses</p>
            </CardContent>
          </Card>
          
          <Card className="glass border-green-500/20 text-center">
            <CardContent className="p-4 sm:p-6">
              <div className="text-2xl sm:text-3xl font-bold text-green-600 mb-2">
                {courses.reduce((sum, course) => sum + course.totalStudents, 0).toLocaleString()}+
              </div>
              <p className="text-sm sm:text-base text-muted-foreground">Students Enrolled</p>
            </CardContent>
          </Card>
          
          <Card className="glass border-orange-500/20 text-center">
            <CardContent className="p-4 sm:p-6">
              <div className="text-2xl sm:text-3xl font-bold text-orange-600 mb-2">
                {categories.length}+
              </div>
              <p className="text-sm sm:text-base text-muted-foreground">Course Categories</p>
            </CardContent>
          </Card>
          
          <Card className="glass border-purple-500/20 text-center">
            <CardContent className="p-4 sm:p-6">
              <div className="text-2xl sm:text-3xl font-bold text-purple-600 mb-2">
                {courses.length > 0 
                  ? (courses.reduce((sum, course) => sum + course.rating, 0) / courses.length).toFixed(1)
                  : '4.8'
                }
              </div>
              <p className="text-sm sm:text-base text-muted-foreground">Average Rating</p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center"
        >
          <Card className="glass bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20 max-w-2xl mx-auto">
            <CardContent className="p-6 sm:p-8">
              <div className="flex items-center justify-center mb-4">
                <div className="p-3 bg-primary/10 rounded-full">
                  <Zap className="w-6 h-6 text-primary" />
                </div>
              </div>
              <h3 className="text-xl sm:text-2xl font-bold mb-3">
                Ready to Start Learning?
              </h3>
              <p className="text-muted-foreground mb-6">
                Join thousands of professionals who are advancing their careers with our expert-led courses.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
                <Link href="/courses">
                  <Button size="lg" className="w-full sm:w-auto">
                    <BookOpen className="w-4 h-4 mr-2" />
                    Browse All Courses
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </Link>
                
                <Link href="/courses?price=free">
                  <Button variant="outline" size="lg" className="w-full sm:w-auto">
                    <Play className="w-4 h-4 mr-2" />
                    Try Free Courses
                  </Button>
                </Link>
              </div>
              
              <div className="flex items-center justify-center mt-6 text-sm text-muted-foreground">
                <Award className="w-4 h-4 mr-2" />
                <span>Get certificates upon completion</span>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Featured Categories Preview */}
        {!isLoadingCategories && categories.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="mt-16"
          >
            <h3 className="text-2xl font-bold text-center mb-8">Explore by Category</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              {categories.map((category, index) => (
                <motion.div
                  key={category.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.4, delay: 0.1 * index }}
                  whileHover={{ scale: 1.05 }}
                >
                  <Link href={`/courses?category=${category.id}`}>
                    <Card className="glass hover:shadow-lg transition-all duration-300 border-border/50 hover:border-primary/30 cursor-pointer h-full">
                      <CardContent className="p-4 text-center">
                        <div className="text-3xl mb-3">{category.icon}</div>
                        <h4 className="font-semibold text-sm mb-1">{category.name}</h4>
                        <p className="text-xs text-muted-foreground line-clamp-2">
                          {category.description}
                        </p>
                        <ChevronRight className="w-4 h-4 mx-auto mt-2 text-primary" />
                      </CardContent>
                    </Card>
                  </Link>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </div>
    </section>
  )
}

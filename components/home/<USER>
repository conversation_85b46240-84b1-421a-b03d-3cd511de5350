// components\home\location-based-jobs-section-simple.tsx
'use client'

import React, { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { JobApplicationModal } from '@/components/jobs/job-application-modal'
import { type JobFrontend } from '@/lib/services/jobs-frontend.service'
import {
  MapPin,
  DollarSign,
  Briefcase,
  Clock,
  Users,
  Eye,
  Heart,
  Bookmark,
  Share2,
  Zap
} from 'lucide-react'
import Link from 'next/link'

interface JobsResponse {
  jobs: JobFrontend[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export function LocationBasedJobsSection() {
  const [jobs, setJobs] = useState<JobFrontend[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [likedJobs, setLikedJobs] = useState<Set<string>>(new Set())
  const [bookmarkedJobs, setBookmarkedJobs] = useState<Set<string>>(new Set())
  const [applicationJob, setApplicationJob] = useState<JobFrontend | null>(null)

  // Fetch jobs from API
  useEffect(() => {
    const fetchJobs = async () => {
      try {
        console.log('LocationBasedJobsSection: Fetching jobs...')
        setIsLoading(true)
        setError(null)
        
        const response = await fetch('/api/v1/jobs/home/<USER>')
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        
        const data: JobsResponse = await response.json()
        console.log('LocationBasedJobsSection: Received data:', data)
        
        setJobs(data.jobs || [])
      } catch (err) {
        console.error('LocationBasedJobsSection: Error fetching jobs:', err)
        setError(err instanceof Error ? err.message : 'Failed to fetch jobs')
      } finally {
        setIsLoading(false)
      }
    }

    fetchJobs()
  }, [])

  const toggleLike = (jobId: string) => {
    setLikedJobs(prev => {
      const newSet = new Set(prev)
      if (newSet.has(jobId)) {
        newSet.delete(jobId)
      } else {
        newSet.add(jobId)
      }
      return newSet
    })
  }

  const toggleBookmark = (jobId: string) => {
    setBookmarkedJobs(prev => {
      const newSet = new Set(prev)
      if (newSet.has(jobId)) {
        newSet.delete(jobId)
      } else {
        newSet.add(jobId)
      }
      return newSet
    })
  }

  const renderJobCard = (job: JobFrontend) => {
    return (
      <Card
        className={`glass hover:shadow-xl transition-all duration-300 ${
          job.isFeatured ? "border-primary/50 bg-primary/5" : "border-border/50"
        } ${job.isUrgent ? "ring-2 ring-orange-500/20" : ""}`}
      >
        {job.isUrgent && (
          <div className="absolute -top-2 -right-2">
            <Badge className="bg-orange-500 text-white animate-pulse">
              <Zap className="w-3 h-3 mr-1" />
              Urgent
            </Badge>
          </div>
        )}

        <CardHeader className="pb-3 md:pb-4">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3 md:space-x-4 flex-1 min-w-0">
              <Avatar className="w-10 h-10 md:w-12 md:h-12 flex-shrink-0">
                <AvatarImage src={job.company?.logo || "/placeholder.svg"} alt={job.company?.name || 'Company'} />
                <AvatarFallback>{job.company?.name?.[0] || 'C'}</AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h3 className="text-base md:text-xl font-bold hover:text-primary transition-colors duration-200 cursor-pointer truncate">
                  {job.title}
                </h3>
                <p className="text-sm md:text-base text-muted-foreground truncate">{job.company?.name || 'Company'}</p>
                <div className="flex items-center space-x-2 md:space-x-4 text-xs md:text-sm text-muted-foreground mt-1">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>{job.createdAt ? new Date(job.createdAt).toLocaleDateString() : 'Recently'}</span>
                  </div>
                  <div className="hidden sm:flex items-center space-x-1">
                    <Users className="w-3 h-3" />
                    <span>{job.applicationsCount || 0} applicants</span>
                  </div>
                  <div className="hidden md:flex items-center space-x-1">
                    <Eye className="w-3 h-3" />
                    <span>{job.viewsCount || 0} views</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-1.5 md:space-x-2 flex-shrink-0">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={(e) => {
                  e.stopPropagation()
                  toggleLike(job._id)
                }}
                className={`p-1.5 md:p-2 rounded-full transition-colors duration-200 ${
                  likedJobs.has(job._id) ? "bg-red-100 text-red-500" : "bg-muted hover:bg-muted/80"
                }`}
              >
                <Heart className={`w-3 h-3 md:w-4 md:h-4 ${likedJobs.has(job._id) ? "fill-current" : ""}`} />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={(e) => {
                  e.stopPropagation()
                  toggleBookmark(job._id)
                }}
                className={`p-1.5 md:p-2 rounded-full transition-colors duration-200 ${
                  bookmarkedJobs.has(job._id)
                    ? "bg-primary/10 text-primary"
                    : "bg-muted hover:bg-muted/80"
                }`}
              >
                <Bookmark className={`w-3 h-3 md:w-4 md:h-4 ${bookmarkedJobs.has(job._id) ? "fill-current" : ""}`} />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="p-2 rounded-full bg-muted hover:bg-muted/80 transition-colors duration-200"
              >
                <Share2 className="w-4 h-4" />
              </motion.button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4 text-muted-foreground" />
              <span>
                {job.location?.city && job.location?.country 
                  ? `${job.location.city}, ${job.location.country}`
                  : job.location?.remote 
                    ? 'Remote'
                    : 'Location not specified'
                }
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <DollarSign className="w-4 h-4 text-muted-foreground" />
              <span className="font-semibold text-primary">
                {job.salary?.min && job.salary?.max 
                  ? `$${job.salary.min.toLocaleString()} - $${job.salary.max.toLocaleString()}`
                  : job.salary?.min 
                    ? `$${job.salary.min.toLocaleString()}+`
                    : 'Salary not specified'
                }
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Briefcase className="w-4 h-4 text-muted-foreground" />
              <span className="capitalize">{job.type || 'Full-time'}</span>
            </div>
          </div>

          {job.tags && job.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {job.tags.slice(0, 4).map((skill: string) => (
                <Badge key={skill} variant="secondary" className="text-xs">
                  {skill}
                </Badge>
              ))}
              {job.tags.length > 4 && (
                <Badge variant="outline" className="text-xs">
                  +{job.tags.length - 4} more
                </Badge>
              )}
            </div>
          )}

          <div className="flex items-center justify-between pt-4 border-t border-border/50">
            <div className="flex items-center space-x-4">
              <Badge variant="secondary" className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                Open for Applications
              </Badge>
              {job.isFeatured && (
                <Badge className="hidden sm:inline-flex bg-primary/10 text-primary border-primary/20">Featured</Badge>
              )}
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  setApplicationJob(job)
                }}
              >
                Quick Apply
              </Button>
              <Button size="sm" asChild>
                <Link href={`/jobs/${job._id}`}>View Details</Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <section className="py-16 bg-gradient-to-br from-background via-background to-primary/5">
      <div className="container mx-auto px-4 max-w-7xl">
        <div className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-bold mb-4">
              Available Job Opportunities
            </h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
              Discover exciting career opportunities from top companies around the world.
            </p>
          </motion.div>
        </div>

        <div className="max-w-4xl mx-auto space-y-6">
          {isLoading ? (
            // Loading skeleton
            Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <Card className="glass">
                  <CardHeader>
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-muted rounded-full"></div>
                      <div className="space-y-2 flex-1">
                        <div className="h-4 bg-muted rounded w-3/4"></div>
                        <div className="h-3 bg-muted rounded w-1/2"></div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="h-3 bg-muted rounded w-full"></div>
                      <div className="h-3 bg-muted rounded w-2/3"></div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ))
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-500 mb-4">Error: {error}</p>
              <Button onClick={() => window.location.reload()}>
                Try Again
              </Button>
            </div>
          ) : jobs.length > 0 ? (
            jobs.map((job, index) => (
              <motion.div
                key={job._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                className="relative"
              >
                {renderJobCard(job)}
              </motion.div>
            ))
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground">No jobs available at the moment. Please check back later.</p>
            </div>
          )}
        </div>
      </div>

      {/* Job Application Modal */}
      {applicationJob && (
        <JobApplicationModal
          job={applicationJob}
          isOpen={!!applicationJob}
          onClose={() => setApplicationJob(null)}
          onSubmit={(applicationData) => {
            console.log('Application submitted:', applicationData)
            // Handle successful application submission
            setApplicationJob(null)
          }}
        />
      )}
    </section>
  )
}

// components\home\location-based-jobs-section.tsx
'use client'

import React, { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useLocationStore } from '@/stores/location-store'
import { useHomeJobsStore } from '@/stores/home-jobs.store'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { JobApplicationModal } from '@/components/jobs/job-application-modal'
import { type JobFrontend } from '@/lib/services/jobs-frontend.service'
import {
  MapPin,
  DollarSign,
  Briefcase,
  Clock,
  Users,
  Eye,
  Heart,
  Bookmark,
  Share2,
  Zap,
  Building2,
  Globe,
  Navigation,
  Target,
  Plane
} from 'lucide-react'
import Link from 'next/link'

export function LocationBasedJobsSection() {
  const { currentLocation } = useLocationStore()
  const {
    jobsByLocation,
    locationStats,
    currentLocationLevel,
    userInteractions,
    isLoadingLocationJobs,
    isLoadingStats,
    error,
    fetchAllLocationLevels,
    fetchLocationStats,
    setCurrentLocationLevel,
    toggleLikeJob,
    toggleBookmarkJob,
    trackJobView,
    trackJobShare
  } = useHomeJobsStore()

  console.log('LocationBasedJobsSection: Store state:', {
    jobsByLocation,
    locationStats,
    currentLocationLevel,
    isLoadingLocationJobs,
    isLoadingStats,
    error,
    currentLocation
  })
  
  const [applicationJob, setApplicationJob] = useState<JobFrontend | null>(null)

  // Helper function to get application status info
  const getApplicationStatusInfo = (job: JobFrontend) => {
    if (userInteractions.appliedJobs.has(job._id)) {
      return { label: 'Applied', color: 'blue' }
    }
    if (job.applicationsCount && job.maxApplicants && job.applicationsCount >= job.maxApplicants) {
      return { label: 'Position Filled', color: 'red' }
    }
    if (job.applicationDeadline) {
      const deadline = new Date(job.applicationDeadline)
      const now = new Date()
      const daysLeft = Math.ceil((deadline.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
      if (daysLeft <= 0) {
        return { label: 'Deadline Passed', color: 'red' }
      }
      if (daysLeft <= 3) {
        return { label: `${daysLeft} days left`, color: 'orange' }
      }
    }
    return { label: 'Open for Applications', color: 'green' }
  }

  const renderJobCard = (job: JobFrontend) => {
    const applicationStatusInfo = getApplicationStatusInfo(job)

    return (
      <Card
        className={`glass hover:shadow-xl transition-all duration-300 ${
          job.isFeatured ? "border-primary/50 bg-primary/5" : "border-border/50"
        } ${job.isUrgent ? "ring-2 ring-orange-500/20" : ""}`}
        onClick={() => trackJobView(job._id)}
      >
        {job.isUrgent && (
          <div className="absolute -top-2 -right-2">
            <Badge className="bg-orange-500 text-white animate-pulse">
              <Zap className="w-3 h-3 mr-1" />
              Urgent
            </Badge>
          </div>
        )}

        <CardHeader className="pb-3 md:pb-4">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3 md:space-x-4 flex-1 min-w-0">
              <Avatar className="w-10 h-10 md:w-12 md:h-12 flex-shrink-0">
                <AvatarImage src={job.company?.logo || "/placeholder.svg"} alt={job.company?.name || 'Company'} />
                <AvatarFallback>{job.company?.name?.[0] || 'C'}</AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h3 className="text-base md:text-xl font-bold hover:text-primary transition-colors duration-200 cursor-pointer truncate">
                  {job.title}
                </h3>
                <p className="text-sm md:text-base text-muted-foreground truncate">{job.company?.name || 'Company'}</p>
                <div className="flex items-center space-x-2 md:space-x-4 text-xs md:text-sm text-muted-foreground mt-1">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>{job.createdAt ? new Date(job.createdAt).toLocaleDateString() : 'Recently'}</span>
                  </div>
                  <div className="hidden sm:flex items-center space-x-1">
                    <Users className="w-3 h-3" />
                    <span>{job.applicationsCount || 0} applicants</span>
                  </div>
                  <div className="hidden md:flex items-center space-x-1">
                    <Eye className="w-3 h-3" />
                    <span>{job.viewsCount || 0} views</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-1.5 md:space-x-2 flex-shrink-0">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={(e) => {
                  e.stopPropagation()
                  toggleLikeJob(job._id)
                }}
                className={`p-1.5 md:p-2 rounded-full transition-colors duration-200 ${
                  userInteractions.likedJobs.has(job._id) ? "bg-red-100 text-red-500" : "bg-muted hover:bg-muted/80"
                }`}
              >
                <Heart className={`w-3 h-3 md:w-4 md:h-4 ${userInteractions.likedJobs.has(job._id) ? "fill-current" : ""}`} />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={(e) => {
                  e.stopPropagation()
                  toggleBookmarkJob(job._id)
                }}
                className={`p-1.5 md:p-2 rounded-full transition-colors duration-200 ${
                  userInteractions.bookmarkedJobs.has(job._id)
                    ? "bg-primary/10 text-primary"
                    : "bg-muted hover:bg-muted/80"
                }`}
              >
                <Bookmark className={`w-3 h-3 md:w-4 md:h-4 ${userInteractions.bookmarkedJobs.has(job._id) ? "fill-current" : ""}`} />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={(e) => {
                  e.stopPropagation()
                  trackJobShare(job._id, 'general')
                }}
                className="p-2 rounded-full bg-muted hover:bg-muted/80 transition-colors duration-200"
              >
                <Share2 className="w-4 h-4" />
              </motion.button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4 text-muted-foreground" />
              <span>
                {job.location?.city && job.location?.country 
                  ? `${job.location.city}, ${job.location.country}`
                  : job.location?.remote 
                    ? 'Remote'
                    : 'Location not specified'
                }
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <DollarSign className="w-4 h-4 text-muted-foreground" />
              <span className="font-semibold text-primary">
                {job.salary?.min && job.salary?.max 
                  ? `$${job.salary.min.toLocaleString()} - $${job.salary.max.toLocaleString()}`
                  : job.salary?.min 
                    ? `$${job.salary.min.toLocaleString()}+`
                    : 'Salary not specified'
                }
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Briefcase className="w-4 h-4 text-muted-foreground" />
              <span className="capitalize">{job.type || 'Full-time'}</span>
            </div>
          </div>

          {job.tags && job.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {job.tags.slice(0, 4).map((skill: string) => (
                <Badge key={skill} variant="secondary" className="text-xs">
                  {skill}
                </Badge>
              ))}
              {job.tags.length > 4 && (
                <Badge variant="outline" className="text-xs">
                  +{job.tags.length - 4} more
                </Badge>
              )}
            </div>
          )}

          <div className="flex items-center justify-between pt-4 border-t border-border/50">
            <div className="flex items-center space-x-4">
              <Badge
                variant="secondary"
                className={`text-xs ${
                  applicationStatusInfo.color === 'green' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                  applicationStatusInfo.color === 'orange' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                  applicationStatusInfo.color === 'red' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                  'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                }`}
              >
                {applicationStatusInfo.label}
              </Badge>
              {job.isFeatured && (
                <Badge className="hidden sm:inline-flex bg-primary/10 text-primary border-primary/20">Featured</Badge>
              )}
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  setApplicationJob(job)
                }}
              >
                Quick Apply
              </Button>
              <Button size="sm" asChild>
                <Link href={`/jobs/${job._id}`}>View Details</Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Test API call
  useEffect(() => {
    const testApiCall = async () => {
      try {
        console.log('LocationBasedJobsSection: Testing direct API call...')
        const response = await fetch('/api/v1/jobs/home/<USER>')
        const data = await response.json()
        console.log('LocationBasedJobsSection: Direct API response:', data)
      } catch (error) {
        console.error('LocationBasedJobsSection: Direct API error:', error)
      }
    }

    testApiCall()
  }, [])

  // Fetch jobs when location changes
  useEffect(() => {
    console.log('LocationBasedJobsSection: currentLocation changed:', currentLocation)
    console.log('LocationBasedJobsSection: jobsByLocation:', jobsByLocation)
    console.log('LocationBasedJobsSection: locationStats:', locationStats)
    console.log('LocationBasedJobsSection: isLoadingLocationJobs:', isLoadingLocationJobs)
    console.log('LocationBasedJobsSection: error:', error)

    // Always try to fetch some jobs, even without location
    const userLocationData = currentLocation ? {
      city: currentLocation.city,
      region: currentLocation.region,
      country: currentLocation.country,
      continent: currentLocation.continent
    } : undefined

    console.log('LocationBasedJobsSection: Fetching jobs for location:', userLocationData)
    console.log('LocationBasedJobsSection: fetchAllLocationLevels function:', fetchAllLocationLevels)

    // Fetch all location levels and stats
    fetchAllLocationLevels(userLocationData)
    if (userLocationData) {
      fetchLocationStats(userLocationData)
    }
  }, [currentLocation, fetchAllLocationLevels, fetchLocationStats])

  // Set active tab based on current location level
  useEffect(() => {
    setCurrentLocationLevel(currentLocationLevel)
  }, [currentLocationLevel, setCurrentLocationLevel])

  const getJobsByLocationLevel = (level: keyof typeof jobsByLocation): JobFrontend[] => {
    return jobsByLocation[level] || []
  }

  // Reusable function to render job lists with loading states
  const renderJobList = (level: keyof typeof jobsByLocation, emptyMessage: string) => {
    const jobs = getJobsByLocationLevel(level)
    console.log(`LocationBasedJobsSection: renderJobList for ${level}:`, {
      level,
      jobs,
      jobsLength: jobs.length,
      isLoadingLocationJobs,
      emptyMessage,
      jobsByLocationState: jobsByLocation
    })

    // Force show loading if we have no jobs and are not loading
    const shouldShowLoading = isLoadingLocationJobs || (jobs.length === 0 && !error)

    return (
      <div className="max-w-4xl mx-auto space-y-6">
        {shouldShowLoading ? (
          // Loading skeleton
          Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <Card className="glass">
                <CardHeader>
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-muted rounded-full"></div>
                    <div className="space-y-2 flex-1">
                      <div className="h-4 bg-muted rounded w-3/4"></div>
                      <div className="h-3 bg-muted rounded w-1/2"></div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="h-3 bg-muted rounded w-full"></div>
                    <div className="h-3 bg-muted rounded w-2/3"></div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))
        ) : jobs.length > 0 ? (
          jobs.map((job, index) => (
            <motion.div
              key={job._id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              className="relative"
            >
              {renderJobCard(job)}
            </motion.div>
          ))
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">{emptyMessage}</p>
            {error && (
              <p className="text-red-500 mt-2">Error: {error}</p>
            )}
            <div className="mt-4 text-sm text-muted-foreground">
              <p>Debug Info:</p>
              <p>Level: {level}</p>
              <p>Jobs Length: {jobs.length}</p>
              <p>Loading: {isLoadingLocationJobs.toString()}</p>
              <p>Store State: {JSON.stringify(jobsByLocation, null, 2)}</p>
            </div>
          </div>
        )}
      </div>
    )
  }

  if (!currentLocation) {
    return (
      <section className="py-16 bg-gradient-to-br from-background via-background to-primary/5">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-4xl font-bold mb-4">
                Discover Opportunities Worldwide
              </h2>
              <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
                Enable location access to see personalized job recommendations based on your area,
                or explore our global opportunities below.
              </p>

              {renderJobList('local', 'No jobs available at the moment. Please check back later.')}
            </motion.div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-16 bg-gradient-to-br from-background via-background to-primary/5">
      <div className="container mx-auto px-4 max-w-7xl">
        <div className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-bold mb-4">
              Jobs Near You in{" "}
              <span className="text-primary">{currentLocation.city}</span>
            </h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
              Discover opportunities tailored to your location and preferences.
              From local startups to international corporations.
            </p>
          </motion.div>
        </div>

        {/* Location-based Job Tabs */}
        <Tabs value={currentLocationLevel} onValueChange={(value) => setCurrentLocationLevel(value as 'local' | 'regional' | 'national' | 'continental' | 'international')} className="w-full">
          <TabsList className="grid w-full grid-cols-5 mb-8 bg-card/50 backdrop-blur-sm">
            <TabsTrigger value="local" className="flex items-center space-x-2">
              <Building2 className="w-4 h-4" />
              <span className="hidden sm:inline">Local</span>
              <Badge variant="secondary" className="ml-1 text-xs">
                {isLoadingStats ? '...' : locationStats.local}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="regional" className="flex items-center space-x-2">
              <Navigation className="w-4 h-4" />
              <span className="hidden sm:inline">Regional</span>
              <Badge variant="secondary" className="ml-1 text-xs">
                {isLoadingStats ? '...' : locationStats.regional}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="national" className="flex items-center space-x-2">
              <Target className="w-4 h-4" />
              <span className="hidden sm:inline">National</span>
              <Badge variant="secondary" className="ml-1 text-xs">
                {isLoadingStats ? '...' : locationStats.national}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="continental" className="flex items-center space-x-2">
              <Globe className="w-4 h-4" />
              <span className="hidden sm:inline">Continental</span>
              <Badge variant="secondary" className="ml-1 text-xs">
                {isLoadingStats ? '...' : locationStats.continental}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="international" className="flex items-center space-x-2">
              <Plane className="w-4 h-4" />
              <span className="hidden sm:inline">International</span>
              <Badge variant="secondary" className="ml-1 text-xs">
                {isLoadingStats ? '...' : locationStats.international}
              </Badge>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="local" className="mt-8">
            {renderJobList('local', 'No local jobs available at the moment.')}
          </TabsContent>

          <TabsContent value="regional" className="mt-8">
            {renderJobList('regional', 'No regional jobs available at the moment.')}
          </TabsContent>

          <TabsContent value="national" className="mt-8">
            {renderJobList('national', 'No national jobs available at the moment.')}
          </TabsContent>

          <TabsContent value="continental" className="mt-8">
            {renderJobList('continental', 'No continental jobs available at the moment.')}
          </TabsContent>

          <TabsContent value="international" className="mt-8">
            {renderJobList('international', 'No international jobs available at the moment.')}
          </TabsContent>
        </Tabs>
      </div>

      {/* Job Application Modal */}
      {applicationJob && (
        <JobApplicationModal
          job={applicationJob}
          isOpen={!!applicationJob}
          onClose={() => setApplicationJob(null)}
        />
      )}
    </section>
  )
}

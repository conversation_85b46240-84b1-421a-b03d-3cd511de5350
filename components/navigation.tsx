"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { ThemeToggle } from "@/components/theme-toggle"
import { JobPreferencesModal } from "@/components/preferences/job-preferences-modal"
import { Menu, X, Briefcase, User, Building2, Home, Settings, LogOut, Shield, Database, Users, Brain, BookOpen } from "lucide-react"
import { cn } from "@/lib/utils"
import { useAuthStore } from "@/stores/auth.store"

export function Navigation() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [showPreferencesModal, setShowPreferencesModal] = useState(false)
  const pathname = usePathname()

  // Auth state
  const { user, isAuthenticated, logout } = useAuthStore()

  const handleLogout = async () => {
    try {
      await logout()
      setIsMobileMenuOpen(false)
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const navItems = [
    { href: "/", label: "Home", icon: Home },
    { href: "/jobs", label: "Find Jobs", icon: Briefcase },
    { href: "/companies", label: "Companies", icon: Building2 },
    { href: "/talent", label: "Find Talent", icon: User },
    { href: "/courses", label: "Courses", icon: BookOpen },
    { href: "/quiz", label: "Skills Assessment", icon: Brain },
  ]

  // Admin navigation items (only for real admin users)
  const adminNavItems = user?.role === 'admin' ? [
    { href: "/admin", label: "Admin Panel", icon: Shield },
    { href: "/admin/users", label: "User Management", icon: Users },
    { href: "/admin/companies", label: "Company Management", icon: Building2 },
  ] : []

  // Combine nav items (no debug admin in main navigation)
  const allNavItems = adminNavItems.length > 0
    ? [...navItems, ...adminNavItems]
    : navItems

  // Helper function to get correct dashboard path based on user role
  const getDashboardPath = (userRole: string | undefined) => {
    switch (userRole) {
      case 'admin':
        return '/admin'
      case 'company_admin':
      case 'recruiter':
        return '/company-dashboard'
      case 'job_seeker':
      default:
        return '/client-dashboard'
    }
  }

  // Helper function to get dashboard label based on user role
  const getDashboardLabel = (userRole: string | undefined) => {
    switch (userRole) {
      case 'admin':
        return 'Admin Dashboard'
      case 'company_admin':
      case 'recruiter':
        return 'Company Dashboard'
      case 'job_seeker':
      default:
        return 'Client Dashboard'
    }
  }

  const isActiveLink = (href: string) => {
    if (href === "/") {
      return pathname === "/"
    }
    return pathname.startsWith(href)
  }

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-300",
        isScrolled
          ? "nav-enhanced shadow-2xl shadow-primary/15"
          : "bg-background/80 backdrop-blur-lg border-b border-border/40 shadow-xl shadow-primary/10",
      )}
    >
      <nav className="container mx-auto px-4 h-16 flex items-center justify-between">
        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <Link href="/" className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-primary via-primary/95 to-primary/80 rounded-xl flex items-center justify-center shadow-xl shadow-primary/30 theme-glow">
              <Briefcase className="w-5 h-5 text-primary-foreground" />
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-primary via-primary/95 to-primary/80 bg-clip-text text-transparent">
              JobPortal
            </span>
          </Link>
        </motion.div>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center space-x-8">
          {allNavItems.map((item, index) => {
            const isActive = isActiveLink(item.href)
            const isAdminItem = adminNavItems.some(adminItem => adminItem.href === item.href)
            return (
              <motion.div
                key={item.href}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Link
                  href={item.href}
                  className={cn(
                    "flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 group relative",
                    isActive
                      ? "text-primary bg-primary/10 border border-primary/20 theme-glow"
                      : "text-muted-foreground hover:text-foreground hover:bg-accent/50",
                    isAdminItem && "border border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-950/20"
                  )}
                >
                  <item.icon className={cn(
                    "w-4 h-4 group-hover:scale-110 transition-transform duration-200",
                    isActive ? "text-primary" : "",
                    isAdminItem && "text-orange-600 dark:text-orange-400"
                  )} />
                  <span className={cn(
                    "font-medium",
                    isAdminItem && "text-orange-700 dark:text-orange-300"
                  )}>{item.label}</span>
                  {isActive && (
                    <motion.div
                      layoutId="activeIndicator"
                      className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full"
                      initial={false}
                      transition={{ type: "spring", stiffness: 500, damping: 30 }}
                    />
                  )}
                </Link>
              </motion.div>
            )
          })}
        </div>

        <div className="flex items-center space-x-4">
          {/* Job Preferences Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowPreferencesModal(true)}
            className="hidden md:flex items-center space-x-2 font-medium hover:bg-primary/10 hover:text-primary transition-colors duration-200"
          >
            <Settings className="w-4 h-4" />
            <span>Preferences</span>
          </Button>

          <ThemeToggle />
          <div className="hidden md:flex items-center space-x-3">
            {isAuthenticated ? (
              <>
                <span className="text-sm text-muted-foreground">
                  Welcome, {user?.profile?.firstName || user?.email}
                </span>
                <Button asChild className="button-premium font-medium">
                  <Link href={getDashboardPath(user?.role)}>
                    {getDashboardLabel(user?.role)}
                  </Link>
                </Button>
                <Button
                  variant="ghost"
                  onClick={handleLogout}
                  className="font-medium"
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  Sign Out
                </Button>
              </>
            ) : (
              <>
                <Button variant="ghost" asChild className="font-medium">
                  <Link href="/login">Sign In</Link>
                </Button>
                <Button asChild className="button-premium font-medium">
                  <Link href="/register">Get Started</Link>
                </Button>
              </>
            )}
          </div>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <AnimatePresence mode="wait">
              {isMobileMenuOpen ? (
                <motion.div
                  key="close"
                  initial={{ rotate: -90, opacity: 0 }}
                  animate={{ rotate: 0, opacity: 1 }}
                  exit={{ rotate: 90, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <X className="w-5 h-5" />
                </motion.div>
              ) : (
                <motion.div
                  key="menu"
                  initial={{ rotate: 90, opacity: 0 }}
                  animate={{ rotate: 0, opacity: 1 }}
                  exit={{ rotate: -90, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <Menu className="w-5 h-5" />
                </motion.div>
              )}
            </AnimatePresence>
          </Button>
        </div>
      </nav>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden bg-background/98 backdrop-blur-xl border-b border-border/60 shadow-xl"
          >
            <div className="container mx-auto px-4 py-4 space-y-4">
              {allNavItems.map((item, index) => {
                const isActive = isActiveLink(item.href)
                const isAdminItem = adminNavItems.some(adminItem => adminItem.href === item.href)
                return (
                  <motion.div
                    key={item.href}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Link
                      href={item.href}
                      className={cn(
                        "flex items-center space-x-3 p-2 rounded-lg transition-colors duration-200",
                        isActive
                          ? "bg-primary/10 text-primary border border-primary/20"
                          : "hover:bg-accent",
                        isAdminItem && "border border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-950/20"
                      )}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <item.icon className={cn(
                        "w-5 h-5",
                        isActive ? "text-primary" : "",
                        isAdminItem && "text-orange-600 dark:text-orange-400"
                      )} />
                      <span className={cn(
                        isActive ? "font-semibold" : "",
                        isAdminItem && "text-orange-700 dark:text-orange-300"
                      )}>{item.label}</span>
                    </Link>
                  </motion.div>
                )
              })}
              <div className="flex flex-col space-y-2 pt-4 border-t border-border/50">
                <Button
                  variant="ghost"
                  onClick={() => {
                    setShowPreferencesModal(true)
                    setIsMobileMenuOpen(false)
                  }}
                  className="flex items-center space-x-2 justify-start"
                >
                  <Settings className="w-4 h-4" />
                  <span>Job Preferences</span>
                </Button>

                {isAuthenticated ? (
                  <>
                    <div className="px-2 py-1 text-sm text-muted-foreground">
                      Welcome, {user?.profile?.firstName || user?.email}
                    </div>
                    <Button asChild>
                      <Link
                        href={getDashboardPath(user?.role)}
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        {getDashboardLabel(user?.role)}
                      </Link>
                    </Button>
                    <Button
                      variant="ghost"
                      onClick={handleLogout}
                      className="flex items-center space-x-2 justify-start"
                    >
                      <LogOut className="w-4 h-4" />
                      <span>Sign Out</span>
                    </Button>
                  </>
                ) : (
                  <>
                    <Button variant="ghost" asChild>
                      <Link href="/login" onClick={() => setIsMobileMenuOpen(false)}>
                        Sign In
                      </Link>
                    </Button>
                    <Button asChild>
                      <Link href="/register" onClick={() => setIsMobileMenuOpen(false)}>
                        Get Started
                      </Link>
                    </Button>
                  </>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Job Preferences Modal */}
      <JobPreferencesModal
        isOpen={showPreferencesModal}
        onClose={() => setShowPreferencesModal(false)}
      />
    </motion.header>
  )
}

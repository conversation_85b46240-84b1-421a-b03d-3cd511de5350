'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { JobApplicationModal } from '@/components/jobs/job-application-modal'
import { getApplicationStatusInfo } from '@/lib/job-data'
import { 
  MapPin, 
  DollarSign, 
  Clock, 
  Eye, 
  Heart, 
  Briefcase,
  Users,
  Share2,
  Bookmark,
  Zap,
  Search,
  X
} from 'lucide-react'
import Link from 'next/link'
import { type JobFrontend } from '@/lib/services/jobs-frontend.service'

interface SearchedJobsResultsProps {
  jobs: JobFrontend[]
  searchQuery: string
  onClearSearch: () => void
  isLoading?: boolean
  error?: string | null
  totalResults?: number
  searchTime?: number
  appliedFilters?: string[]
}

export function SearchedJobsResults({
  jobs,
  searchQuery,
  onClearSearch,
  isLoading = false,
  error = null,
  totalResults = 0,
  searchTime = 0,
  appliedFilters = []
}: SearchedJobsResultsProps) {
  const [likedJobs, setLikedJobs] = useState<Set<string>>(new Set())
  const [bookmarkedJobs, setBookmarkedJobs] = useState<Set<string>>(new Set())
  const [applicationJob, setApplicationJob] = useState<JobFrontend | null>(null)

  const toggleLike = (jobId: string) => {
    setLikedJobs(prev => {
      const newSet = new Set(prev)
      if (newSet.has(jobId)) {
        newSet.delete(jobId)
      } else {
        newSet.add(jobId)
      }
      return newSet
    })
  }

  const toggleBookmark = (jobId: string) => {
    setBookmarkedJobs(prev => {
      const newSet = new Set(prev)
      if (newSet.has(jobId)) {
        newSet.delete(jobId)
      } else {
        newSet.add(jobId)
      }
      return newSet
    })
  }

  const renderJobCard = (job: JobFrontend) => {
    return (
      <Card
        key={job.id}
        className={`glass hover:shadow-xl transition-all duration-300 ${
          job.featured ? "border-primary/50 bg-primary/5" : "border-border/50"
        } ${job.urgent ? "ring-2 ring-orange-500/20" : ""} relative`}
      >
        {job.urgent && (
          <div className="absolute -top-2 -right-2">
            <Badge className="bg-orange-500 text-white animate-pulse">
              <Zap className="w-3 h-3 mr-1" />
              Urgent
            </Badge>
          </div>
        )}

        <CardHeader className="pb-4">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4 flex-1 min-w-0">
              <Avatar className="w-12 h-12 flex-shrink-0">
                <AvatarImage src={job.company.logo || "/placeholder.svg"} alt={job.company.name || 'Company'} />
                <AvatarFallback>{(job.company.name || 'Company')[0]}</AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h3 className="text-xl font-bold hover:text-primary transition-colors duration-200 cursor-pointer truncate">
                  {job.title}
                </h3>
                <p className="text-muted-foreground truncate">{job.company.name || 'Company'}</p>
                <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>{job.posted ? new Date(job.posted).toLocaleDateString() : 'Recently'}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Users className="w-3 h-3" />
                    <span>{job.applicants || 0} applicants</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Eye className="w-3 h-3" />
                    <span>{job.views || 0} views</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2 flex-shrink-0">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={(e) => {
                  e.stopPropagation()
                  toggleLike(job._id)
                }}
                className={`p-2 rounded-full transition-colors duration-200 ${
                  likedJobs.has(job._id) ? "bg-red-100 text-red-500" : "bg-muted hover:bg-muted/80"
                }`}
              >
                <Heart className={`w-4 h-4 ${likedJobs.has(job._id) ? "fill-current" : ""}`} />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={(e) => {
                  e.stopPropagation()
                  toggleBookmark(job._id)
                }}
                className={`p-2 rounded-full transition-colors duration-200 ${
                  bookmarkedJobs.has(job._id)
                    ? "bg-primary/10 text-primary"
                    : "bg-muted hover:bg-muted/80"
                }`}
              >
                <Bookmark className={`w-4 h-4 ${bookmarkedJobs.has(job._id) ? "fill-current" : ""}`} />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="p-2 rounded-full bg-muted hover:bg-muted/80 transition-colors duration-200"
              >
                <Share2 className="w-4 h-4" />
              </motion.button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4 text-muted-foreground" />
              <span>
                {job.locationDetails?.city && job.locationDetails?.country
                  ? `${job.locationDetails.city}, ${job.locationDetails.country}`
                  : job.remote
                    ? 'Remote'
                    : job.location || 'Location not specified'
                }
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <DollarSign className="w-4 h-4 text-muted-foreground" />
              <span className="font-semibold text-primary">
                {job.salary.min && job.salary.max
                  ? `${job.salary.currency}${job.salary.min.toLocaleString()} - ${job.salary.currency}${job.salary.max.toLocaleString()}`
                  : job.salary.min
                    ? `${job.salary.currency}${job.salary.min.toLocaleString()}+`
                    : 'Salary not specified'
                }
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Briefcase className="w-4 h-4 text-muted-foreground" />
              <span className="capitalize">{job.type || 'Full-time'}</span>
            </div>
          </div>

          {job.skills && job.skills.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {job.skills.slice(0, 4).map((skill: string) => (
                <Badge key={skill} variant="secondary" className="text-xs">
                  {skill}
                </Badge>
              ))}
              {job.skills.length > 4 && (
                <Badge variant="outline" className="text-xs">
                  +{job.skills.length - 4} more
                </Badge>
              )}
            </div>
          )}

          <div className="flex items-center justify-between pt-4 border-t border-border/50">
            <div className="flex items-center space-x-4">
              <Badge variant="secondary" className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                Open for Applications
              </Badge>
              {job.featured && (
                <Badge className="bg-primary/10 text-primary border-primary/20">Featured</Badge>
              )}
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  setApplicationJob(job)
                }}
              >
                Quick Apply
              </Button>
              <Button size="sm" asChild>
                <Link href={`/jobs/${job.id}`}>View Details</Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <section className="py-12 bg-background/95 backdrop-blur-sm">
      <div className="container mx-auto px-4">
        {/* Search Results Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <Search className="w-5 h-5 text-primary" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">Search Results</h2>
                <p className="text-muted-foreground">
                  Found {jobs.length} job{jobs.length !== 1 ? 's' : ''} matching your search
                </p>
                {jobs.length > 0 && (
                  <p className="text-sm text-muted-foreground mt-1">
                    Results sorted by relevance • Showing best matches first
                  </p>
                )}
              </div>
            </div>
            <Button
              variant="outline"
              onClick={onClearSearch}
              className="flex items-center space-x-2"
            >
              <X className="w-4 h-4" />
              <span>Clear Search</span>
            </Button>
          </div>

          {/* Search Query Display */}
          <div className="flex items-center space-x-2 p-3 bg-primary/5 border border-primary/20 rounded-lg">
            <Search className="w-4 h-4 text-primary" />
            <span className="text-sm">
              Searching for: <span className="font-semibold text-primary">"{searchQuery}"</span>
            </span>
          </div>
        </motion.div>

        {/* Results */}
        {jobs.length > 0 ? (
          <div className="max-w-4xl mx-auto space-y-6">
            <AnimatePresence>
              {jobs.map((job, index) => (
                <motion.div
                  key={job._id}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -50 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                  className="relative"
                >
                  {renderJobCard(job)}
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center py-12"
          >
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="w-8 h-8 text-muted-foreground" />
            </div>
            <h3 className="text-xl font-semibold mb-2">No jobs found</h3>
            <p className="text-muted-foreground mb-4">
              We couldn't find any jobs matching your search criteria.
            </p>
            <div className="text-sm text-muted-foreground mb-6 space-y-1">
              <p>Try:</p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>Using broader search terms (e.g., "dev" instead of "developer")</li>
                <li>Checking your spelling</li>
                <li>Searching by location only</li>
                <li>Using partial words (e.g., "clin" for clinical jobs)</li>
              </ul>
            </div>
            <Button onClick={onClearSearch} variant="outline">
              Clear Search and Browse All Jobs
            </Button>
          </motion.div>
        )}

        {/* Load More Button */}
        {jobs.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-center mt-12"
          >
            <Button size="lg" variant="outline" className="text-lg px-8 bg-transparent">
              Load More Results
            </Button>
          </motion.div>
        )}
      </div>

      {/* Job Application Modal */}
      {applicationJob && (
        <JobApplicationModal
          job={applicationJob}
          isOpen={!!applicationJob}
          onClose={() => setApplicationJob(null)}
          onSubmit={(applicationData) => {
            console.log('Application submitted:', applicationData)
            setApplicationJob(null)
          }}
        />
      )}
    </section>
  )
}

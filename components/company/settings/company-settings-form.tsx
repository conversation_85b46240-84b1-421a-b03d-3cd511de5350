'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'sonner'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Save, RotateCcw, Settings, Bell, Shield, Palette, Users, Plug } from 'lucide-react'
import { useCompanyStore } from '@/stores/company.store'
import { companySettingsSchema, type CompanySettings } from '@/types/company-settings.types'
import { NotificationSettings } from './notification-settings'
import { BrandingSettings } from './branding-settings'

interface CompanySettingsFormProps {
  companyId: string
}

export function CompanySettingsForm({ companyId }: CompanySettingsFormProps) {
  const [activeTab, setActiveTab] = useState('general')
  const [hasChanges, setHasChanges] = useState(false)
  
  const {
    settings,
    settingsLoading,
    settingsUpdateLoading,
    settingsError,
    fetchCompanySettings,
    updateCompanySettings,
    resetCompanySettings,
    clearSettingsError
  } = useCompanyStore()

  const form = useForm<CompanySettings>({
    resolver: zodResolver(companySettingsSchema),
    defaultValues: settings || undefined,
    mode: 'onChange' // Enable real-time validation
  })

  const { handleSubmit, watch, reset, formState: { isDirty, errors, isValid } } = form

  // Watch for form changes
  useEffect(() => {
    setHasChanges(isDirty)
  }, [isDirty])

  // Load settings on mount
  useEffect(() => {
    fetchCompanySettings()
  }, [fetchCompanySettings])

  // Update form when settings change
  useEffect(() => {
    if (settings) {
      reset(settings)
    }
  }, [settings, reset])

  const onSubmit = async (data: CompanySettings) => {
    try {
      await updateCompanySettings(data)
      setHasChanges(false)
      toast.success('Settings updated successfully!')
    } catch (error) {
      console.error('Failed to update settings:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to update settings'
      toast.error(errorMessage)
    }
  }

  const handleReset = async () => {
    if (confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
      try {
        await resetCompanySettings()
        setHasChanges(false)
        toast.success('Settings reset to defaults successfully!')
      } catch (error) {
        console.error('Failed to reset settings:', error)
        const errorMessage = error instanceof Error ? error.message : 'Failed to reset settings'
        toast.error(errorMessage)
      }
    }
  }

  if (settingsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading settings...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Company Settings</h1>
          <p className="text-muted-foreground">
            Manage your company preferences and configurations
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={settingsUpdateLoading}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset to Defaults
          </Button>
          <Button
            onClick={handleSubmit(onSubmit)}
            disabled={!hasChanges || settingsUpdateLoading || !isValid}
          >
            {settingsUpdateLoading ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Changes
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {settingsError && (
        <Alert variant="destructive">
          <AlertDescription>
            {settingsError}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearSettingsError}
              className="ml-2"
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Settings Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="general">
              <Settings className="h-4 w-4 mr-2" />
              General
            </TabsTrigger>
            <TabsTrigger value="notifications">
              <Bell className="h-4 w-4 mr-2" />
              Notifications
            </TabsTrigger>
            <TabsTrigger value="privacy">
              <Shield className="h-4 w-4 mr-2" />
              Privacy
            </TabsTrigger>
            <TabsTrigger value="branding">
              <Palette className="h-4 w-4 mr-2" />
              Branding
            </TabsTrigger>
            <TabsTrigger value="team">
              <Users className="h-4 w-4 mr-2" />
              Team
            </TabsTrigger>
            <TabsTrigger value="integrations">
              <Plug className="h-4 w-4 mr-2" />
              Integrations
            </TabsTrigger>
          </TabsList>

          {/* General Settings */}
          <TabsContent value="general" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Profile Settings</CardTitle>
                  <CardDescription>
                    Control how your company profile appears to job seekers
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Public Profile</Label>
                      <p className="text-sm text-muted-foreground">
                        Allow your company profile to be visible to all users
                      </p>
                    </div>
                    <Switch
                      {...form.register('allowPublicProfile')}
                      checked={watch('allowPublicProfile')}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Show Salary Ranges</Label>
                      <p className="text-sm text-muted-foreground">
                        Display salary information in job postings
                      </p>
                    </div>
                    <Switch
                      {...form.register('showSalaryRanges')}
                      checked={watch('showSalaryRanges')}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Allow Direct Contact</Label>
                      <p className="text-sm text-muted-foreground">
                        Let candidates contact your company directly
                      </p>
                    </div>
                    <Switch
                      {...form.register('allowDirectContact')}
                      checked={watch('allowDirectContact')}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Show Company Stats</Label>
                      <p className="text-sm text-muted-foreground">
                        Display company statistics on your profile
                      </p>
                    </div>
                    <Switch
                      {...form.register('showCompanyStats')}
                      checked={watch('showCompanyStats')}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Application Settings</CardTitle>
                  <CardDescription>
                    Configure how applications are handled
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="autoRejectAfterDays">Auto-reject after (days)</Label>
                    <Input
                      id="autoRejectAfterDays"
                      type="number"
                      min="1"
                      max="365"
                      {...form.register('autoRejectAfterDays', { valueAsNumber: true })}
                      placeholder="Optional"
                      className={errors.autoRejectAfterDays ? 'border-red-500' : ''}
                    />
                    {errors.autoRejectAfterDays && (
                      <p className="text-sm text-red-500">
                        {errors.autoRejectAfterDays.message}
                      </p>
                    )}
                    <p className="text-sm text-muted-foreground">
                      Automatically reject applications after this many days
                    </p>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Require Cover Letter</Label>
                      <p className="text-sm text-muted-foreground">
                        Make cover letters mandatory for applications
                      </p>
                    </div>
                    <Switch
                      {...form.register('requireCoverLetter')}
                      checked={watch('requireCoverLetter')}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Allow Remote Applications</Label>
                      <p className="text-sm text-muted-foreground">
                        Accept applications from remote candidates
                      </p>
                    </div>
                    <Switch
                      {...form.register('allowRemoteApplications')}
                      checked={watch('allowRemoteApplications')}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Auto Response</Label>
                      <p className="text-sm text-muted-foreground">
                        Send automatic confirmation emails to applicants
                      </p>
                    </div>
                    <Switch
                      {...form.register('autoResponseEnabled')}
                      checked={watch('autoResponseEnabled')}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Application Deadline Reminders</Label>
                      <p className="text-sm text-muted-foreground">
                        Send reminders before application deadlines
                      </p>
                    </div>
                    <Switch
                      {...form.register('applicationDeadlineReminder')}
                      checked={watch('applicationDeadlineReminder')}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Notifications Settings */}
          <TabsContent value="notifications">
            <NotificationSettings />
          </TabsContent>

          <TabsContent value="privacy">
            <Card>
              <CardHeader>
                <CardTitle>Privacy & Security</CardTitle>
                <CardDescription>
                  Manage your privacy and security preferences
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">Privacy settings will be implemented here.</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="branding">
            <BrandingSettings />
          </TabsContent>

          <TabsContent value="team">
            <Card>
              <CardHeader>
                <CardTitle>Team Settings</CardTitle>
                <CardDescription>
                  Configure team collaboration and permissions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">Team settings will be implemented here.</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="integrations">
            <Card>
              <CardHeader>
                <CardTitle>Integrations</CardTitle>
                <CardDescription>
                  Connect with external services and tools
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">Integration settings will be implemented here.</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </form>
    </div>
  )
}

'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  BookOpen,
  Play,
  Clock,
  Award,
  TrendingUp,
  Star,
  Calendar,
  Target,
  Users,
  CheckCircle,
  BarChart3
} from 'lucide-react'

interface UserEnrollment {
  _id: string
  courseId: {
    _id: string
    title: string
    slug: string
    thumbnail: string
    price: number
    level: string
    duration: number
    rating: number
    categoryId: {
      name: string
      icon: string
      color: string
    }
  }
  progress: number
  enrolledAt: string
  lastAccessedAt: string
  completedAt?: string
  isActive: boolean
  completedLessons: number
  totalLessons: number
}

interface EnrollmentStats {
  totalEnrollments: number
  activeEnrollments: number
  completedCourses: number
  totalHoursLearned: number
  averageProgress: number
}

export function MyCourses() {
  const [enrollments, setEnrollments] = useState<UserEnrollment[]>([])
  const [stats, setStats] = useState<EnrollmentStats>({
    totalEnrollments: 0,
    activeEnrollments: 0,
    completedCourses: 0,
    totalHoursLearned: 0,
    averageProgress: 0
  })
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('all')

  useEffect(() => {
    fetchEnrollments()
  }, [])

  const fetchEnrollments = async () => {
    try {
      const response = await fetch('/api/v1/users/enrollments')
      if (response.ok) {
        const result = await response.json()
        setEnrollments(result.data.enrollments)
        setStats(result.data.stats)
      }
    } catch (error) {
      console.error('Error fetching enrollments:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filterEnrollments = (status: string) => {
    switch (status) {
      case 'active':
        return enrollments.filter(e => e.progress < 100 && e.isActive)
      case 'completed':
        return enrollments.filter(e => e.progress === 100)
      case 'all':
      default:
        return enrollments
    }
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    return hours > 0 ? `${hours}h` : `${minutes}m`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const getProgressColor = (progress: number) => {
    if (progress === 100) return 'bg-green-500'
    if (progress >= 50) return 'bg-blue-500'
    return 'bg-yellow-500'
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-2">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-8 bg-muted rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Courses</p>
                <p className="text-2xl font-bold">{stats.totalEnrollments}</p>
              </div>
              <BookOpen className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">In Progress</p>
                <p className="text-2xl font-bold">{stats.activeEnrollments}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold">{stats.completedCourses}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Hours Learned</p>
                <p className="text-2xl font-bold">{Math.round(stats.totalHoursLearned / 60)}</p>
              </div>
              <Clock className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Course Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="all">All Courses ({enrollments.length})</TabsTrigger>
            <TabsTrigger value="active">In Progress ({stats.activeEnrollments})</TabsTrigger>
            <TabsTrigger value="completed">Completed ({stats.completedCourses})</TabsTrigger>
          </TabsList>
          
          <Button asChild>
            <Link href="/courses">
              <BookOpen className="w-4 h-4 mr-2" />
              Browse Courses
            </Link>
          </Button>
        </div>

        <TabsContent value={activeTab} className="space-y-4">
          {filterEnrollments(activeTab).length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <BookOpen className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  {activeTab === 'completed' ? 'No completed courses yet' : 
                   activeTab === 'active' ? 'No courses in progress' : 
                   'No courses enrolled'}
                </h3>
                <p className="text-muted-foreground mb-4">
                  {activeTab === 'completed' ? 'Complete your current courses to see them here.' :
                   activeTab === 'active' ? 'Start learning to see your progress here.' :
                   'Enroll in courses to start your learning journey.'}
                </p>
                <Button asChild>
                  <Link href="/courses">Browse Available Courses</Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filterEnrollments(activeTab).map((enrollment) => (
                <Card key={enrollment._id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="aspect-video relative">
                    <img 
                      src={enrollment.courseId.thumbnail} 
                      alt={enrollment.courseId.title}
                      className="object-cover w-full h-full"
                    />
                    <div className="absolute top-2 left-2">
                      <Badge 
                        style={{ backgroundColor: enrollment.courseId.categoryId.color }}
                        className="text-white"
                      >
                        {enrollment.courseId.categoryId.icon} {enrollment.courseId.categoryId.name}
                      </Badge>
                    </div>
                    <div className="absolute top-2 right-2">
                      {enrollment.progress === 100 ? (
                        <Badge className="bg-green-100 text-green-800">
                          <Award className="w-3 h-3 mr-1" />
                          Completed
                        </Badge>
                      ) : (
                        <Badge variant="secondary">
                          {enrollment.progress}% Complete
                        </Badge>
                      )}
                    </div>
                  </div>

                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div>
                        <h3 className="font-semibold line-clamp-2 mb-1">
                          {enrollment.courseId.title}
                        </h3>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <Clock className="w-3 h-3" />
                            <span>{formatDuration(enrollment.courseId.duration)}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Star className="w-3 h-3 text-yellow-500 fill-current" />
                            <span>{enrollment.courseId.rating.toFixed(1)}</span>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {enrollment.courseId.level}
                          </Badge>
                        </div>
                      </div>

                      {/* Progress */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span>Progress</span>
                          <span>{enrollment.progress}%</span>
                        </div>
                        <Progress value={enrollment.progress} className="h-2" />
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>{enrollment.completedLessons} of {enrollment.totalLessons} lessons</span>
                          <span>Last accessed: {formatDate(enrollment.lastAccessedAt)}</span>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex space-x-2">
                        <Button asChild className="flex-1">
                          <Link href={`/courses/${enrollment.courseId.slug}`}>
                            <Play className="w-4 h-4 mr-2" />
                            {enrollment.progress === 100 ? 'Review' : 'Continue'}
                          </Link>
                        </Button>
                        {enrollment.progress === 100 && (
                          <Button variant="outline" size="sm">
                            <Award className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </motion.div>
  )
}

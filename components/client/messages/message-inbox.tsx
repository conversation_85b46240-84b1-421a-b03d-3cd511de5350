'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  MessageCircle, 
  Send, 
  Search,
  Mail,
  MailOpen,
  Clock,
  User,
  Building
} from 'lucide-react'
import { useAuthStore } from '@/stores/auth.store'
import { useToast } from '@/hooks/use-toast'

interface Conversation {
  threadId: string
  participant: {
    id: string
    name: string
    avatar: string
    role: string
    title: string
    isOnline: boolean
  }
  lastMessage: {
    id: string
    content: string
    subject: string
    sentAt: string
    isFromMe: boolean
    isRead: boolean
  }
  messageCount: number
  unreadCount: number
  updatedAt: string
}

export function MessageInbox() {
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [activeTab, setActiveTab] = useState('all')
  const [totalUnread, setTotalUnread] = useState(0)
  const { token } = useAuthStore()
  const { toast } = useToast()

  useEffect(() => {
    fetchConversations()
  }, [activeTab])

  const fetchConversations = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/v1/clients/messages/conversations?type=${activeTab}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setConversations(data.data.conversations)
        setTotalUnread(data.data.totalUnread)
      }
    } catch (error) {
      console.error('Failed to fetch conversations:', error)
      toast({
        title: "Error",
        description: "Failed to load conversations",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const openConversation = (threadId: string) => {
    setSelectedConversation(threadId)
    // Navigate to conversation view
    window.location.href = `/client-dashboard/messages/${threadId}`
  }

  const filteredConversations = conversations.filter(conversation =>
    conversation.participant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conversation.lastMessage.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conversation.lastMessage.content.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const getInitials = (name: string) => {
    return name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2)
  }

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60)
      return `${hours}h ago`
    } else {
      const days = Math.floor(diffInMinutes / 1440)
      return `${days}d ago`
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'recruiter':
        return <Building className="w-3 h-3" />
      case 'client':
        return <User className="w-3 h-3" />
      default:
        return <User className="w-3 h-3" />
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'recruiter':
        return 'bg-blue-500'
      case 'client':
        return 'bg-green-500'
      default:
        return 'bg-gray-500'
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <Card key={i}>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gray-200 rounded-full animate-pulse" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  <div className="h-3 bg-gray-200 rounded animate-pulse w-3/4" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold flex items-center">
            <MessageCircle className="w-6 h-6 mr-2" />
            Messages
          </h1>
          <p className="text-muted-foreground">
            {conversations.length} conversation{conversations.length !== 1 ? 's' : ''} • {totalUnread} unread
          </p>
        </div>
        
        <Button onClick={() => window.location.href = '/client-dashboard/messages/compose'}>
          <Send className="w-4 h-4 mr-2" />
          New Message
        </Button>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
        <Input
          placeholder="Search conversations..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all">All Messages</TabsTrigger>
          <TabsTrigger value="unread" className="relative">
            Unread
            {totalUnread > 0 && (
              <Badge variant="destructive" className="ml-2 h-5 w-5 p-0 text-xs">
                {totalUnread > 99 ? '99+' : totalUnread}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="sent">Sent</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {filteredConversations.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <MessageCircle className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">
                    {searchQuery ? 'No conversations found' : 'No messages yet'}
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    {searchQuery 
                      ? 'Try adjusting your search criteria' 
                      : 'Start a conversation with recruiters or other professionals'
                    }
                  </p>
                  {!searchQuery && (
                    <Button onClick={() => window.location.href = '/client-dashboard/messages/compose'}>
                      <Send className="w-4 h-4 mr-2" />
                      Send Your First Message
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-2">
              {filteredConversations.map((conversation) => (
                <Card 
                  key={conversation.threadId} 
                  className={`cursor-pointer hover:shadow-md transition-shadow ${
                    conversation.unreadCount > 0 ? 'border-primary/50 bg-primary/5' : ''
                  }`}
                  onClick={() => openConversation(conversation.threadId)}
                >
                  <CardContent className="pt-4 pb-4">
                    <div className="flex items-start space-x-4">
                      {/* Avatar */}
                      <div className="relative">
                        <Avatar className="w-12 h-12">
                          <AvatarImage src={conversation.participant.avatar} alt={conversation.participant.name} />
                          <AvatarFallback>
                            {getInitials(conversation.participant.name)}
                          </AvatarFallback>
                        </Avatar>
                        {conversation.participant.isOnline && (
                          <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full" />
                        )}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <div className="flex items-center space-x-2">
                            <h3 className="font-semibold text-sm truncate">
                              {conversation.participant.name}
                            </h3>
                            <Badge 
                              variant="secondary" 
                              className={`${getRoleColor(conversation.participant.role)} text-white text-xs`}
                            >
                              {getRoleIcon(conversation.participant.role)}
                              <span className="ml-1 capitalize">{conversation.participant.role}</span>
                            </Badge>
                          </div>
                          <div className="flex items-center space-x-2">
                            {conversation.unreadCount > 0 && (
                              <Badge variant="destructive" className="h-5 w-5 p-0 text-xs">
                                {conversation.unreadCount > 9 ? '9+' : conversation.unreadCount}
                              </Badge>
                            )}
                            <span className="text-xs text-muted-foreground">
                              {getTimeAgo(conversation.updatedAt)}
                            </span>
                          </div>
                        </div>

                        <p className="text-xs text-muted-foreground mb-1">
                          {conversation.participant.title}
                        </p>

                        <div className="flex items-center space-x-2">
                          {conversation.lastMessage.isRead ? (
                            <MailOpen className="w-3 h-3 text-muted-foreground" />
                          ) : (
                            <Mail className="w-3 h-3 text-primary" />
                          )}
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">
                              {conversation.lastMessage.subject}
                            </p>
                            <p className="text-xs text-muted-foreground truncate">
                              {conversation.lastMessage.isFromMe ? 'You: ' : ''}
                              {conversation.lastMessage.content}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center justify-between mt-2">
                          <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                            <MessageCircle className="w-3 h-3" />
                            <span>{conversation.messageCount} message{conversation.messageCount !== 1 ? 's' : ''}</span>
                          </div>
                          <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                            <Clock className="w-3 h-3" />
                            <span>{getTimeAgo(conversation.lastMessage.sentAt)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Heart, 
  HeartOff, 
  MapPin, 
  DollarSign, 
  Clock, 
  Building,
  Star,
  TrendingUp,
  ExternalLink,
  RefreshCw
} from 'lucide-react'
import { useAuthStore } from '@/stores/auth.store'
import { useToast } from '@/hooks/use-toast'

interface JobRecommendation {
  id: string
  title: string
  company: string
  location: string
  salary?: {
    min: number
    max: number
    currency: string
    period: string
  }
  type: string
  matchScore: number
  postedDate: string
  description: string
  requirements: string[]
  remote: boolean
  urgent: boolean
  featured: boolean
}

export function JobRecommendations() {
  const [recommendations, setRecommendations] = useState<JobRecommendation[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [savedJobs, setSavedJobs] = useState<Set<string>>(new Set())
  const { token } = useAuthStore()
  const { toast } = useToast()

  useEffect(() => {
    fetchRecommendations()
  }, [])

  const fetchRecommendations = async (refresh = false) => {
    try {
      if (refresh) {
        setRefreshing(true)
      } else {
        setLoading(true)
      }

      const response = await fetch('/api/v1/clients/jobs/recommendations?limit=10', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setRecommendations(data.data)
      }
    } catch (error) {
      console.error('Failed to fetch recommendations:', error)
      toast({
        title: "Error",
        description: "Failed to load job recommendations",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const saveJob = async (jobId: string) => {
    try {
      const response = await fetch('/api/v1/clients/jobs/saved', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ jobId })
      })

      if (response.ok) {
        setSavedJobs(prev => new Set([...prev, jobId]))
        toast({
          title: "Job saved",
          description: "Job added to your saved jobs",
        })
      } else {
        throw new Error('Failed to save job')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save job",
        variant: "destructive"
      })
    }
  }

  const unsaveJob = async (jobId: string) => {
    try {
      const response = await fetch(`/api/v1/clients/jobs/saved?jobId=${jobId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        setSavedJobs(prev => {
          const newSet = new Set(prev)
          newSet.delete(jobId)
          return newSet
        })
        toast({
          title: "Job removed",
          description: "Job removed from saved jobs",
        })
      } else {
        throw new Error('Failed to unsave job')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove job from saved jobs",
        variant: "destructive"
      })
    }
  }

  const getMatchScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-50'
    if (score >= 60) return 'text-yellow-600 bg-yellow-50'
    return 'text-red-600 bg-red-50'
  }

  const getMatchScoreText = (score: number) => {
    if (score >= 80) return 'Excellent Match'
    if (score >= 60) return 'Good Match'
    return 'Fair Match'
  }

  const formatSalary = (salary: JobRecommendation['salary']) => {
    if (!salary) return 'Salary not specified'
    const { min, max, currency, period } = salary
    const formatAmount = (amount: number) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency || 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(amount)
    }
    
    if (min && max) {
      return `${formatAmount(min)} - ${formatAmount(max)} ${period || 'yearly'}`
    } else if (min) {
      return `From ${formatAmount(min)} ${period || 'yearly'}`
    }
    return 'Salary not specified'
  }

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <Card key={i}>
            <CardContent className="pt-6">
              <div className="space-y-3">
                <div className="h-6 bg-gray-200 rounded animate-pulse" />
                <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
                <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold flex items-center">
            <TrendingUp className="w-6 h-6 mr-2 text-primary" />
            Job Recommendations
          </h1>
          <p className="text-muted-foreground">
            Personalized job matches based on your profile and preferences
          </p>
        </div>
        
        <Button 
          variant="outline" 
          onClick={() => fetchRecommendations(true)}
          disabled={refreshing}
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Recommendations List */}
      {recommendations.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <TrendingUp className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">No recommendations available</h3>
              <p className="text-muted-foreground mb-4">
                Complete your profile to get personalized job recommendations
              </p>
              <Button onClick={() => window.location.href = '/client-dashboard/profile/edit'}>
                Complete Profile
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {recommendations.map((job) => (
            <Card key={job.id} className="hover:shadow-md transition-shadow">
              <CardContent className="pt-6">
                <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
                  {/* Job Info */}
                  <div className="flex-1 space-y-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className="text-lg font-semibold hover:text-primary cursor-pointer">
                            {job.title}
                          </h3>
                          {job.featured && (
                            <Badge variant="default" className="bg-yellow-500">
                              <Star className="w-3 h-3 mr-1" />
                              Featured
                            </Badge>
                          )}
                          {job.urgent && (
                            <Badge variant="destructive">Urgent</Badge>
                          )}
                        </div>
                        <div className="flex items-center space-x-2 text-muted-foreground">
                          <Building className="w-4 h-4" />
                          <span>{job.company}</span>
                        </div>
                      </div>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => savedJobs.has(job.id) ? unsaveJob(job.id) : saveJob(job.id)}
                        className={savedJobs.has(job.id) ? 'text-red-500' : 'text-muted-foreground'}
                      >
                        {savedJobs.has(job.id) ? <Heart className="w-4 h-4 fill-current" /> : <HeartOff className="w-4 h-4" />}
                      </Button>
                    </div>

                    {/* Match Score */}
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium">Match Score:</span>
                        <div className="flex items-center space-x-2">
                          <Progress value={job.matchScore} className="w-20 h-2" />
                          <span className={`text-sm font-semibold px-2 py-1 rounded ${getMatchScoreColor(job.matchScore)}`}>
                            {job.matchScore}%
                          </span>
                        </div>
                      </div>
                      <Badge variant="outline" className={getMatchScoreColor(job.matchScore)}>
                        {getMatchScoreText(job.matchScore)}
                      </Badge>
                    </div>

                    <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <MapPin className="w-4 h-4" />
                        <span>{job.location}</span>
                        {job.remote && <Badge variant="secondary">Remote</Badge>}
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <DollarSign className="w-4 h-4" />
                        <span>{formatSalary(job.salary)}</span>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4" />
                        <span>{getTimeAgo(job.postedDate)}</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">{job.type}</Badge>
                      {job.requirements.slice(0, 3).map((req, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {req}
                        </Badge>
                      ))}
                      {job.requirements.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{job.requirements.length - 3} more
                        </Badge>
                      )}
                    </div>

                    {job.description && (
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {job.description}
                      </p>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col sm:flex-row gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => window.location.href = `/jobs/${job.id}`}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      View Job
                    </Button>
                    <Button 
                      size="sm"
                      onClick={() => window.location.href = `/jobs/${job.id}/apply`}
                    >
                      Apply Now
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}

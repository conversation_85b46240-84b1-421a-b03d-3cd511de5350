'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { 
  Bell, 
  BellOff, 
  Plus, 
  Edit, 
  Trash2, 
  Clock,
  Target,
  MapPin,
  DollarSign
} from 'lucide-react'
import { useAuthStore } from '@/stores/auth.store'
import { useToast } from '@/hooks/use-toast'

interface JobAlert {
  id: string
  name: string
  criteria: {
    keywords?: string[]
    location?: string
    jobType?: string
    experienceLevel?: string
    salaryMin?: number
    salaryMax?: number
    industries?: string[]
    companies?: string[]
  }
  frequency: 'immediate' | 'daily' | 'weekly'
  isActive: boolean
  createdAt: string
  lastTriggered?: string
  matchCount: number
}

export function JobAlertsList() {
  const [jobAlerts, setJobAlerts] = useState<JobAlert[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const { token } = useAuthStore()
  const { toast } = useToast()

  useEffect(() => {
    fetchJobAlerts()
  }, [])

  const fetchJobAlerts = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/v1/clients/jobs/alerts', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setJobAlerts(data.data.jobAlerts)
      }
    } catch (error) {
      console.error('Failed to fetch job alerts:', error)
      toast({
        title: "Error",
        description: "Failed to load job alerts",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const toggleAlert = async (alertId: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/v1/clients/jobs/alerts?alertId=${alertId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ isActive })
      })

      if (response.ok) {
        setJobAlerts(prev => 
          prev.map(alert => 
            alert.id === alertId ? { ...alert, isActive } : alert
          )
        )
        toast({
          title: isActive ? "Alert activated" : "Alert deactivated",
          description: `Job alert has been ${isActive ? 'activated' : 'deactivated'}`,
        })
      } else {
        throw new Error('Failed to update alert')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update job alert",
        variant: "destructive"
      })
    }
  }

  const deleteAlert = async (alertId: string) => {
    if (!confirm('Are you sure you want to delete this job alert?')) return

    try {
      const response = await fetch(`/api/v1/clients/jobs/alerts?alertId=${alertId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        setJobAlerts(prev => prev.filter(alert => alert.id !== alertId))
        toast({
          title: "Alert deleted",
          description: "Job alert has been deleted",
        })
      } else {
        throw new Error('Failed to delete alert')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete job alert",
        variant: "destructive"
      })
    }
  }

  const getFrequencyText = (frequency: string) => {
    switch (frequency) {
      case 'immediate': return 'Immediate'
      case 'daily': return 'Daily'
      case 'weekly': return 'Weekly'
      default: return frequency
    }
  }

  const getFrequencyColor = (frequency: string) => {
    switch (frequency) {
      case 'immediate': return 'bg-red-500'
      case 'daily': return 'bg-blue-500'
      case 'weekly': return 'bg-green-500'
      default: return 'bg-gray-500'
    }
  }

  const formatCriteria = (criteria: JobAlert['criteria']) => {
    const parts = []
    
    if (criteria.keywords?.length) {
      parts.push(`Keywords: ${criteria.keywords.join(', ')}`)
    }
    if (criteria.location) {
      parts.push(`Location: ${criteria.location}`)
    }
    if (criteria.jobType) {
      parts.push(`Type: ${criteria.jobType}`)
    }
    if (criteria.experienceLevel) {
      parts.push(`Level: ${criteria.experienceLevel}`)
    }
    if (criteria.salaryMin || criteria.salaryMax) {
      const salaryRange = []
      if (criteria.salaryMin) salaryRange.push(`$${criteria.salaryMin.toLocaleString()}+`)
      if (criteria.salaryMax) salaryRange.push(`up to $${criteria.salaryMax.toLocaleString()}`)
      parts.push(`Salary: ${salaryRange.join(' ')}`)
    }
    
    return parts.length > 0 ? parts.join(' • ') : 'No specific criteria'
  }

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardContent className="pt-6">
              <div className="space-y-3">
                <div className="h-6 bg-gray-200 rounded animate-pulse" />
                <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
                <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Job Alerts</h1>
          <p className="text-muted-foreground">
            {jobAlerts.length} {jobAlerts.length === 1 ? 'alert' : 'alerts'} • {jobAlerts.filter(a => a.isActive).length} active
          </p>
        </div>
        
        <Button onClick={() => setShowCreateForm(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Create Alert
        </Button>
      </div>

      {/* Alerts List */}
      {jobAlerts.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Bell className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">No job alerts</h3>
              <p className="text-muted-foreground mb-4">
                Create job alerts to get notified when new jobs match your criteria
              </p>
              <Button onClick={() => setShowCreateForm(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Alert
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {jobAlerts.map((alert) => (
            <Card key={alert.id} className={`${alert.isActive ? 'border-primary/20' : 'border-muted'}`}>
              <CardContent className="pt-6">
                <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
                  {/* Alert Info */}
                  <div className="flex-1 space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        {alert.isActive ? (
                          <Bell className="w-5 h-5 text-primary" />
                        ) : (
                          <BellOff className="w-5 h-5 text-muted-foreground" />
                        )}
                        <div>
                          <h3 className="text-lg font-semibold">{alert.name}</h3>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge 
                              variant="secondary" 
                              className={`${getFrequencyColor(alert.frequency)} text-white`}
                            >
                              {getFrequencyText(alert.frequency)}
                            </Badge>
                            <Badge variant={alert.isActive ? "default" : "secondary"}>
                              {alert.isActive ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      
                      <Switch
                        checked={alert.isActive}
                        onCheckedChange={(checked) => toggleAlert(alert.id, checked)}
                      />
                    </div>

                    <div className="text-sm text-muted-foreground">
                      <p>{formatCriteria(alert.criteria)}</p>
                    </div>

                    <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Target className="w-4 h-4" />
                        <span>{alert.matchCount} matches found</span>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4" />
                        <span>Created {getTimeAgo(alert.createdAt)}</span>
                      </div>
                      
                      {alert.lastTriggered && (
                        <div className="flex items-center space-x-1">
                          <Bell className="w-4 h-4" />
                          <span>Last triggered {getTimeAgo(alert.lastTriggered)}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col sm:flex-row gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => {/* TODO: Open edit form */}}
                    >
                      <Edit className="w-4 h-4 mr-2" />
                      Edit
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => deleteAlert(alert.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}

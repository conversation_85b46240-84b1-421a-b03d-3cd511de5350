'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Bell, 
  BellOff, 
  Check, 
  CheckCheck, 
  Trash2, 
  Settings,
  Mail,
  MessageCircle,
  Briefcase,
  Building,
  Users,
  AlertCircle,
  CheckCircle,
  Info,
  AlertTriangle
} from 'lucide-react'
import { useAuthStore } from '@/stores/auth.store'
import { useToast } from '@/hooks/use-toast'

interface Notification {
  id: string
  title: string
  message: string
  category: string
  type: 'info' | 'success' | 'warning' | 'error'
  isRead: boolean
  createdAt: string
  readAt?: string
  actionUrl?: string
  metadata?: Record<string, any>
}

export function NotificationCenter() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([])
  const [activeTab, setActiveTab] = useState('all')
  const [unreadCount, setUnreadCount] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const { token } = useAuthStore()
  const { toast } = useToast()

  useEffect(() => {
    fetchNotifications()
  }, [activeTab, currentPage])

  const fetchNotifications = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/v1/clients/notifications?type=${activeTab}&page=${currentPage}&limit=20`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setNotifications(data.data.notifications)
        setUnreadCount(data.data.unreadCount)
        setTotalPages(data.data.pagination.totalPages)
      }
    } catch (error) {
      console.error('Failed to fetch notifications:', error)
      toast({
        title: "Error",
        description: "Failed to load notifications",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleNotificationAction = async (notificationIds: string[], action: 'read' | 'unread' | 'delete') => {
    try {
      const response = await fetch('/api/v1/clients/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          notificationIds,
          action
        })
      })

      if (response.ok) {
        const data = await response.json()
        setUnreadCount(data.data.unreadCount)
        
        if (action === 'delete') {
          setNotifications(prev => prev.filter(n => !notificationIds.includes(n.id)))
        } else {
          setNotifications(prev => prev.map(n => 
            notificationIds.includes(n.id) 
              ? { ...n, isRead: action === 'read', readAt: action === 'read' ? new Date().toISOString() : undefined }
              : n
          ))
        }
        
        setSelectedNotifications([])
        
        toast({
          title: "Success",
          description: `Notifications ${action === 'delete' ? 'deleted' : `marked as ${action}`} successfully`,
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${action} notifications`,
        variant: "destructive"
      })
    }
  }

  const markAllAsRead = async () => {
    const unreadIds = notifications.filter(n => !n.isRead).map(n => n.id)
    if (unreadIds.length > 0) {
      await handleNotificationAction(unreadIds, 'read')
    }
  }

  const deleteSelected = async () => {
    if (selectedNotifications.length > 0) {
      await handleNotificationAction(selectedNotifications, 'delete')
    }
  }

  const toggleSelectNotification = (notificationId: string) => {
    setSelectedNotifications(prev => 
      prev.includes(notificationId)
        ? prev.filter(id => id !== notificationId)
        : [...prev, notificationId]
    )
  }

  const selectAllVisible = () => {
    const visibleIds = notifications.map(n => n.id)
    setSelectedNotifications(visibleIds)
  }

  const clearSelection = () => {
    setSelectedNotifications([])
  }

  const getNotificationIcon = (category: string, type: string) => {
    const iconClass = "w-5 h-5"
    
    switch (category) {
      case 'job_alert':
        return <Briefcase className={iconClass} />
      case 'application_update':
        return <CheckCircle className={iconClass} />
      case 'message':
        return <MessageCircle className={iconClass} />
      case 'company_update':
        return <Building className={iconClass} />
      case 'networking':
        return <Users className={iconClass} />
      default:
        switch (type) {
          case 'success':
            return <CheckCircle className={iconClass} />
          case 'warning':
            return <AlertTriangle className={iconClass} />
          case 'error':
            return <AlertCircle className={iconClass} />
          default:
            return <Info className={iconClass} />
        }
    }
  }

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200'
      default:
        return 'text-blue-600 bg-blue-50 border-blue-200'
    }
  }

  const getCategoryLabel = (category: string) => {
    const labels: Record<string, string> = {
      'job_alert': 'Job Alert',
      'application_update': 'Application Update',
      'message': 'Message',
      'company_update': 'Company Update',
      'networking': 'Networking',
      'system': 'System'
    }
    return labels[category] || category
  }

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60)
      return `${hours}h ago`
    } else {
      const days = Math.floor(diffInMinutes / 1440)
      return `${days}d ago`
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <Card key={i}>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  <div className="h-3 bg-gray-200 rounded animate-pulse w-3/4" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold flex items-center">
            <Bell className="w-6 h-6 mr-2" />
            Notifications
          </h1>
          <p className="text-muted-foreground">
            {notifications.length} notification{notifications.length !== 1 ? 's' : ''} • {unreadCount} unread
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={markAllAsRead}
            disabled={unreadCount === 0}
          >
            <CheckCheck className="w-4 h-4 mr-2" />
            Mark All Read
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => window.location.href = '/client-dashboard/settings/notifications'}
          >
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedNotifications.length > 0 && (
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">
                  {selectedNotifications.length} selected
                </span>
                <Button variant="ghost" size="sm" onClick={clearSelection}>
                  Clear
                </Button>
              </div>
              <div className="flex items-center space-x-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleNotificationAction(selectedNotifications, 'read')}
                >
                  <Check className="w-4 h-4 mr-2" />
                  Mark Read
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleNotificationAction(selectedNotifications, 'unread')}
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Mark Unread
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={deleteSelected}
                  className="text-red-500 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="unread" className="relative">
            Unread
            {unreadCount > 0 && (
              <Badge variant="destructive" className="ml-2 h-5 w-5 p-0 text-xs">
                {unreadCount > 99 ? '99+' : unreadCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="read">Read</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {notifications.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <BellOff className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">No notifications</h3>
                  <p className="text-muted-foreground">
                    {activeTab === 'unread' 
                      ? "You're all caught up! No unread notifications."
                      : "You don't have any notifications yet."
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-2">
              {/* Select All Option */}
              <div className="flex items-center space-x-2 px-2">
                <Checkbox
                  checked={selectedNotifications.length === notifications.length}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      selectAllVisible()
                    } else {
                      clearSelection()
                    }
                  }}
                />
                <span className="text-sm text-muted-foreground">Select all visible</span>
              </div>

              {notifications.map((notification) => (
                <Card 
                  key={notification.id} 
                  className={`cursor-pointer transition-all ${
                    !notification.isRead ? 'border-primary/50 bg-primary/5' : ''
                  } ${selectedNotifications.includes(notification.id) ? 'ring-2 ring-primary' : ''}`}
                >
                  <CardContent className="pt-4 pb-4">
                    <div className="flex items-start space-x-3">
                      {/* Selection Checkbox */}
                      <Checkbox
                        checked={selectedNotifications.includes(notification.id)}
                        onCheckedChange={() => toggleSelectNotification(notification.id)}
                        onClick={(e) => e.stopPropagation()}
                      />

                      {/* Notification Icon */}
                      <div className={`p-2 rounded-full ${getNotificationColor(notification.type)}`}>
                        {getNotificationIcon(notification.category, notification.type)}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <div className="flex items-center space-x-2">
                            <h3 className="font-semibold text-sm truncate">
                              {notification.title}
                            </h3>
                            <Badge variant="secondary" className="text-xs">
                              {getCategoryLabel(notification.category)}
                            </Badge>
                            {!notification.isRead && (
                              <div className="w-2 h-2 bg-primary rounded-full" />
                            )}
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {getTimeAgo(notification.createdAt)}
                          </span>
                        </div>

                        <p className="text-sm text-muted-foreground mb-2">
                          {notification.message}
                        </p>

                        {notification.actionUrl && (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              window.location.href = notification.actionUrl!
                            }}
                          >
                            View Details
                          </Button>
                        )}
                      </div>

                      {/* Actions */}
                      <div className="flex flex-col space-y-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleNotificationAction([notification.id], notification.isRead ? 'unread' : 'read')
                          }}
                        >
                          {notification.isRead ? (
                            <Mail className="w-4 h-4" />
                          ) : (
                            <Check className="w-4 h-4" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleNotificationAction([notification.id], 'delete')
                          }}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          
          <div className="flex items-center space-x-1">
            {[...Array(Math.min(totalPages, 5))].map((_, i) => {
              const page = i + 1
              return (
                <Button
                  key={page}
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentPage(page)}
                >
                  {page}
                </Button>
              )
            })}
          </div>
          
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}

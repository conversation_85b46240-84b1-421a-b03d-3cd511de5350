'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Search, 
  Filter, 
  MapPin, 
  DollarSign, 
  Briefcase, 
  Building,
  Star,
  Zap,
  X,
  Plus,
  Sparkles
} from 'lucide-react'
import { useAuthStore } from '@/stores/auth.store'
import { useToast } from '@/hooks/use-toast'
import { debounce } from '@/lib/utils/performance'

interface SearchFilters {
  query: string
  location: string
  remote: boolean
  jobType: string[]
  experienceLevel: string[]
  salaryMin: number
  salaryMax: number
  industries: string[]
  companies: string[]
  skills: string[]
  datePosted: string
  sortBy: string
  sortOrder: string
  useAI: boolean
}

interface SearchResult {
  id: string
  title: string
  company: {
    id: string
    name: string
    logo?: string
    location: string
    industry: string
    isVerified: boolean
  }
  location: string
  remote: boolean
  type: string
  experienceLevel: string
  salary?: {
    min: number
    max: number
    currency: string
    period: string
  }
  description: string
  requirements: string[]
  skills: string[]
  postedDate: string
  urgentHiring: boolean
  featured: boolean
  matchScore: number
  relevanceScore: number
}

export function AdvancedSearch() {
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    location: '',
    remote: false,
    jobType: [],
    experienceLevel: [],
    salaryMin: 0,
    salaryMax: 200000,
    industries: [],
    companies: [],
    skills: [],
    datePosted: 'anytime',
    sortBy: 'relevance',
    sortOrder: 'desc',
    useAI: false
  })

  const [results, setResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const [suggestions, setSuggestions] = useState<any[]>([])
  const [recommendations, setRecommendations] = useState<any[]>([])
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    hasNext: false,
    hasPrev: false
  })

  const { token } = useAuthStore()
  const { toast } = useToast()

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (searchFilters: SearchFilters) => {
      await performSearch(searchFilters)
    }, 500),
    []
  )

  useEffect(() => {
    if (filters.query.length > 2) {
      debouncedSearch(filters)
    }
  }, [filters.query, debouncedSearch])

  const performSearch = async (searchFilters: SearchFilters, page: number = 1) => {
    try {
      setLoading(true)
      
      const response = await fetch('/api/v1/clients/search/advanced', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          ...searchFilters,
          page,
          limit: 20,
          includeRecommendations: true
        })
      })

      if (response.ok) {
        const data = await response.json()
        setResults(data.data.jobs)
        setPagination(data.data.pagination)
        setSuggestions(data.data.suggestions || [])
        setRecommendations(data.data.recommendations || [])
      } else {
        throw new Error('Search failed')
      }
    } catch (error) {
      console.error('Search error:', error)
      toast({
        title: "Search Error",
        description: "Failed to perform search. Please try again.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const updateFilter = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const addToArrayFilter = (key: keyof SearchFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: [...(prev[key] as string[]), value]
    }))
  }

  const removeFromArrayFilter = (key: keyof SearchFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: (prev[key] as string[]).filter(item => item !== value)
    }))
  }

  const clearFilters = () => {
    setFilters({
      query: '',
      location: '',
      remote: false,
      jobType: [],
      experienceLevel: [],
      salaryMin: 0,
      salaryMax: 200000,
      industries: [],
      companies: [],
      skills: [],
      datePosted: 'anytime',
      sortBy: 'relevance',
      sortOrder: 'desc',
      useAI: false
    })
    setResults([])
  }

  const formatSalary = (salary: SearchResult['salary']) => {
    if (!salary) return 'Salary not specified'
    const { min, max, currency, period } = salary
    const formatAmount = (amount: number) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency || 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(amount)
    }
    
    if (min && max) {
      return `${formatAmount(min)} - ${formatAmount(max)} ${period || 'yearly'}`
    } else if (min) {
      return `From ${formatAmount(min)} ${period || 'yearly'}`
    }
    return 'Salary not specified'
  }

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  return (
    <div className="space-y-6">
      {/* Search Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Search className="w-5 h-5 mr-2" />
            Advanced Job Search
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Main Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search for jobs, companies, or skills..."
              value={filters.query}
              onChange={(e) => updateFilter('query', e.target.value)}
              className="pl-10 text-lg h-12"
            />
            {filters.useAI && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <Badge variant="secondary" className="bg-purple-100 text-purple-700">
                  <Sparkles className="w-3 h-3 mr-1" />
                  AI
                </Badge>
              </div>
            )}
          </div>

          {/* Quick Filters */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant={filters.remote ? "default" : "outline"}
              size="sm"
              onClick={() => updateFilter('remote', !filters.remote)}
            >
              Remote
            </Button>
            <Button
              variant={filters.useAI ? "default" : "outline"}
              size="sm"
              onClick={() => updateFilter('useAI', !filters.useAI)}
            >
              <Sparkles className="w-4 h-4 mr-1" />
              AI Search
            </Button>
            {filters.datePosted !== 'anytime' && (
              <Badge variant="secondary">
                Posted: {filters.datePosted}
                <X 
                  className="w-3 h-3 ml-1 cursor-pointer" 
                  onClick={() => updateFilter('datePosted', 'anytime')}
                />
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Filters Sidebar */}
        <div className="lg:col-span-1 space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Filters</CardTitle>
                <Button variant="ghost" size="sm" onClick={clearFilters}>
                  Clear All
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Location */}
              <div className="space-y-2">
                <Label>Location</Label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="City, state, or country"
                    value={filters.location}
                    onChange={(e) => updateFilter('location', e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Job Type */}
              <div className="space-y-2">
                <Label>Job Type</Label>
                <div className="space-y-2">
                  {['full-time', 'part-time', 'contract', 'freelance', 'internship'].map(type => (
                    <div key={type} className="flex items-center space-x-2">
                      <Checkbox
                        id={type}
                        checked={filters.jobType.includes(type)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            addToArrayFilter('jobType', type)
                          } else {
                            removeFromArrayFilter('jobType', type)
                          }
                        }}
                      />
                      <Label htmlFor={type} className="capitalize">{type.replace('-', ' ')}</Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Experience Level */}
              <div className="space-y-2">
                <Label>Experience Level</Label>
                <div className="space-y-2">
                  {['entry', 'mid', 'senior', 'executive'].map(level => (
                    <div key={level} className="flex items-center space-x-2">
                      <Checkbox
                        id={level}
                        checked={filters.experienceLevel.includes(level)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            addToArrayFilter('experienceLevel', level)
                          } else {
                            removeFromArrayFilter('experienceLevel', level)
                          }
                        }}
                      />
                      <Label htmlFor={level} className="capitalize">{level}</Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Salary Range */}
              <div className="space-y-2">
                <Label>Salary Range</Label>
                <div className="space-y-4">
                  <div className="px-2">
                    <Slider
                      value={[filters.salaryMin, filters.salaryMax]}
                      onValueChange={([min, max]) => {
                        updateFilter('salaryMin', min)
                        updateFilter('salaryMax', max)
                      }}
                      max={300000}
                      min={0}
                      step={5000}
                      className="w-full"
                    />
                  </div>
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>${filters.salaryMin.toLocaleString()}</span>
                    <span>${filters.salaryMax.toLocaleString()}</span>
                  </div>
                </div>
              </div>

              {/* Date Posted */}
              <div className="space-y-2">
                <Label>Date Posted</Label>
                <Select value={filters.datePosted} onValueChange={(value) => updateFilter('datePosted', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="anytime">Anytime</SelectItem>
                    <SelectItem value="today">Today</SelectItem>
                    <SelectItem value="week">Past Week</SelectItem>
                    <SelectItem value="month">Past Month</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Sort Options */}
              <div className="space-y-2">
                <Label>Sort By</Label>
                <Select value={filters.sortBy} onValueChange={(value) => updateFilter('sortBy', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="relevance">Relevance</SelectItem>
                    <SelectItem value="date">Date Posted</SelectItem>
                    <SelectItem value="salary">Salary</SelectItem>
                    <SelectItem value="company">Company</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Recommendations */}
          {recommendations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <Star className="w-4 h-4 mr-2" />
                  Recommended
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recommendations.map((rec, index) => (
                    <div key={index} className="p-2 border rounded-lg hover:bg-muted/50 cursor-pointer">
                      <h4 className="font-medium text-sm">{rec.title}</h4>
                      <p className="text-xs text-muted-foreground">{rec.company.name}</p>
                      <div className="flex items-center mt-1">
                        <Badge variant="secondary" className="text-xs">
                          {rec.matchScore}% match
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Results */}
        <div className="lg:col-span-3 space-y-4">
          {/* Results Header */}
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold">
                {loading ? 'Searching...' : `${pagination.totalCount} jobs found`}
              </h2>
              {filters.query && (
                <p className="text-muted-foreground">
                  Results for "{filters.query}"
                </p>
              )}
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => performSearch(filters)}
                disabled={loading}
              >
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
            </div>
          </div>

          {/* Search Suggestions */}
          {suggestions.length > 0 && (
            <Card>
              <CardContent className="pt-4">
                <div className="flex flex-wrap gap-2">
                  <span className="text-sm text-muted-foreground">Suggestions:</span>
                  {suggestions.map((suggestion, index) => (
                    <Badge 
                      key={index} 
                      variant="outline" 
                      className="cursor-pointer hover:bg-primary hover:text-primary-foreground"
                      onClick={() => updateFilter('query', suggestion.text)}
                    >
                      {suggestion.text}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Job Results */}
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <Card key={i}>
                  <CardContent className="pt-6">
                    <div className="space-y-3">
                      <div className="h-6 bg-gray-200 rounded animate-pulse" />
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : results.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <Search className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">No jobs found</h3>
                  <p className="text-muted-foreground mb-4">
                    Try adjusting your search criteria or filters
                  </p>
                  <Button onClick={clearFilters}>
                    Clear Filters
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {results.map((job) => (
                <Card key={job.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="pt-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-3">
                        <div className="flex items-start justify-between">
                          <div>
                            <div className="flex items-center space-x-2 mb-1">
                              <h3 className="text-lg font-semibold hover:text-primary cursor-pointer">
                                {job.title}
                              </h3>
                              {job.featured && (
                                <Badge variant="default" className="bg-yellow-500">
                                  <Star className="w-3 h-3 mr-1" />
                                  Featured
                                </Badge>
                              )}
                              {job.urgentHiring && (
                                <Badge variant="destructive">
                                  <Zap className="w-3 h-3 mr-1" />
                                  Urgent
                                </Badge>
                              )}
                            </div>
                            <div className="flex items-center space-x-2 text-muted-foreground">
                              <Building className="w-4 h-4" />
                              <span>{job.company.name}</span>
                              {job.company.isVerified && (
                                <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                                  Verified
                                </Badge>
                              )}
                            </div>
                          </div>
                          
                          <div className="text-right">
                            <div className="flex items-center space-x-2 mb-1">
                              <Badge variant="outline">
                                {job.matchScore}% match
                              </Badge>
                              {filters.useAI && (
                                <Badge variant="outline" className="bg-purple-50 text-purple-700">
                                  AI: {job.relevanceScore}
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {getTimeAgo(job.postedDate)}
                            </p>
                          </div>
                        </div>

                        <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <MapPin className="w-4 h-4" />
                            <span>{job.location}</span>
                            {job.remote && <Badge variant="secondary">Remote</Badge>}
                          </div>
                          
                          <div className="flex items-center space-x-1">
                            <DollarSign className="w-4 h-4" />
                            <span>{formatSalary(job.salary)}</span>
                          </div>
                          
                          <div className="flex items-center space-x-1">
                            <Briefcase className="w-4 h-4" />
                            <span className="capitalize">{job.type}</span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="capitalize">{job.experienceLevel}</Badge>
                          {job.skills.slice(0, 3).map((skill, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {skill}
                            </Badge>
                          ))}
                          {job.skills.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{job.skills.length - 3} more
                            </Badge>
                          )}
                        </div>

                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {job.description}
                        </p>
                      </div>

                      <div className="flex flex-col space-y-2 ml-4">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => window.location.href = `/jobs/${job.id}`}
                        >
                          View Details
                        </Button>
                        <Button 
                          size="sm"
                          onClick={() => window.location.href = `/jobs/${job.id}/apply`}
                        >
                          Apply Now
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center space-x-2">
              <Button
                variant="outline"
                onClick={() => performSearch(filters, pagination.currentPage - 1)}
                disabled={!pagination.hasPrev || loading}
              >
                Previous
              </Button>
              
              <div className="flex items-center space-x-1">
                {[...Array(Math.min(pagination.totalPages, 5))].map((_, i) => {
                  const page = i + 1
                  return (
                    <Button
                      key={page}
                      variant={pagination.currentPage === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => performSearch(filters, page)}
                      disabled={loading}
                    >
                      {page}
                    </Button>
                  )
                })}
              </div>
              
              <Button
                variant="outline"
                onClick={() => performSearch(filters, pagination.currentPage + 1)}
                disabled={!pagination.hasNext || loading}
              >
                Next
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

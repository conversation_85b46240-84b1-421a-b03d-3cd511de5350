'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Input } from '@/components/ui/input'
import { 
  User, 
  MapPin, 
  Building, 
  MessageCircle, 
  UserMinus,
  Search,
  Star,
  Users,
  Briefcase,
  UserPlus
} from 'lucide-react'
import { useAuthStore } from '@/stores/auth.store'
import { useToast } from '@/hooks/use-toast'

interface FollowedRecruiter {
  id: string
  name: string
  email: string
  title: string
  company?: {
    _id: string
    name: string
    logo?: string
  }
  specializations: string[]
  experience: number
  location: string
  avatar: string
  isVerified: boolean
  connectionDate: string
  activeJobsCount: number
}

interface NetworkingRecommendation {
  id: string
  name: string
  title: string
  company?: {
    _id: string
    name: string
    logo?: string
  }
  specializations: string[]
  avatar: string
  isVerified: boolean
  reason: string
}

export function FollowedRecruitersList() {
  const [recruiters, setRecruiters] = useState<FollowedRecruiter[]>([])
  const [recommendations, setRecommendations] = useState<NetworkingRecommendation[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const { token } = useAuthStore()
  const { toast } = useToast()

  useEffect(() => {
    fetchFollowedRecruiters()
  }, [currentPage])

  const fetchFollowedRecruiters = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/v1/clients/recruiters/followed?page=${currentPage}&limit=10`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setRecruiters(data.data.followedRecruiters)
        setRecommendations(data.data.networkingRecommendations)
        setTotalPages(data.data.pagination.totalPages)
      }
    } catch (error) {
      console.error('Failed to fetch followed recruiters:', error)
      toast({
        title: "Error",
        description: "Failed to load followed recruiters",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const unfollowRecruiter = async (recruiterId: string) => {
    try {
      const response = await fetch(`/api/v1/clients/recruiters/${recruiterId}/follow`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        setRecruiters(prev => prev.filter(recruiter => recruiter.id !== recruiterId))
        toast({
          title: "Recruiter unfollowed",
          description: "You are no longer following this recruiter",
        })
      } else {
        throw new Error('Failed to unfollow recruiter')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to unfollow recruiter",
        variant: "destructive"
      })
    }
  }

  const followRecruiter = async (recruiterId: string) => {
    try {
      const response = await fetch(`/api/v1/clients/recruiters/${recruiterId}/follow`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        // Remove from recommendations and refresh the list
        setRecommendations(prev => prev.filter(rec => rec.id !== recruiterId))
        await fetchFollowedRecruiters()
        toast({
          title: "Recruiter followed",
          description: "You are now following this recruiter",
        })
      } else {
        throw new Error('Failed to follow recruiter')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to follow recruiter",
        variant: "destructive"
      })
    }
  }

  const startConversation = (recruiterId: string, recruiterName: string) => {
    // Navigate to messaging with pre-filled recipient
    window.location.href = `/client-dashboard/messages?recipient=${recruiterId}&name=${encodeURIComponent(recruiterName)}`
  }

  const filteredRecruiters = recruiters.filter(recruiter =>
    recruiter.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    recruiter.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    recruiter.company?.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const getInitials = (name: string) => {
    return name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2)
  }

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))
    
    if (diffInDays === 0) {
      return 'Today'
    } else if (diffInDays === 1) {
      return 'Yesterday'
    } else if (diffInDays < 30) {
      return `${diffInDays} days ago`
    } else {
      const diffInMonths = Math.floor(diffInDays / 30)
      return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-4">
            {[...Array(3)].map((_, i) => (
              <Card key={i}>
                <CardContent className="pt-6">
                  <div className="space-y-3">
                    <div className="h-6 bg-gray-200 rounded animate-pulse" />
                    <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
                    <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="space-y-4">
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Followed Recruiters</h1>
          <p className="text-muted-foreground">
            {recruiters.length} {recruiters.length === 1 ? 'recruiter' : 'recruiters'} in your network
          </p>
        </div>
        
        {/* Search */}
        <div className="relative w-full sm:w-80">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search recruiters..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recruiters List */}
        <div className="lg:col-span-2 space-y-4">
          {filteredRecruiters.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <User className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">No recruiters found</h3>
                  <p className="text-muted-foreground mb-4">
                    {searchQuery ? 'No recruiters match your search criteria' : 'Start following recruiters to build your network'}
                  </p>
                  {!searchQuery && (
                    <Button onClick={() => window.location.href = '/recruiters'}>
                      Browse Recruiters
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            filteredRecruiters.map((recruiter) => (
              <Card key={recruiter.id} className="hover:shadow-md transition-shadow">
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      <Avatar className="w-16 h-16">
                        <AvatarImage src={recruiter.avatar} alt={recruiter.name} />
                        <AvatarFallback className="text-lg font-semibold">
                          {getInitials(recruiter.name)}
                        </AvatarFallback>
                      </Avatar>
                      
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center space-x-2">
                          <h3 className="text-lg font-semibold">{recruiter.name}</h3>
                          {recruiter.isVerified && (
                            <Badge variant="default" className="bg-blue-500">
                              <Star className="w-3 h-3 mr-1" />
                              Verified
                            </Badge>
                          )}
                        </div>
                        
                        <p className="text-sm text-muted-foreground">{recruiter.title}</p>
                        
                        <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                          {recruiter.company && (
                            <div className="flex items-center space-x-1">
                              <Building className="w-4 h-4" />
                              <span>{recruiter.company.name}</span>
                            </div>
                          )}
                          
                          {recruiter.location && (
                            <div className="flex items-center space-x-1">
                              <MapPin className="w-4 h-4" />
                              <span>{recruiter.location}</span>
                            </div>
                          )}
                          
                          <div className="flex items-center space-x-1">
                            <Briefcase className="w-4 h-4" />
                            <span>{recruiter.experience} years experience</span>
                          </div>
                        </div>

                        {recruiter.specializations.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {recruiter.specializations.slice(0, 3).map((spec, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {spec}
                              </Badge>
                            ))}
                            {recruiter.specializations.length > 3 && (
                              <Badge variant="secondary" className="text-xs">
                                +{recruiter.specializations.length - 3} more
                              </Badge>
                            )}
                          </div>
                        )}

                        <div className="flex items-center space-x-4 text-sm">
                          <div className="flex items-center space-x-1">
                            <Briefcase className="w-4 h-4 text-blue-500" />
                            <span>{recruiter.activeJobsCount} active jobs</span>
                          </div>
                          <div className="text-muted-foreground">
                            Connected {getTimeAgo(recruiter.connectionDate)}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col space-y-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => startConversation(recruiter.id, recruiter.name)}
                      >
                        <MessageCircle className="w-4 h-4 mr-2" />
                        Message
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => unfollowRecruiter(recruiter.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <UserMinus className="w-4 h-4 mr-2" />
                        Unfollow
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Networking Recommendations Sidebar */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="w-5 h-5 mr-2" />
                Suggested Connections
              </CardTitle>
            </CardHeader>
            <CardContent>
              {recommendations.length === 0 ? (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No recommendations available
                </p>
              ) : (
                <div className="space-y-4">
                  {recommendations.map((recommendation) => (
                    <div key={recommendation.id} className="border-b border-muted last:border-0 pb-3 last:pb-0">
                      <div className="flex items-start space-x-3">
                        <Avatar className="w-10 h-10">
                          <AvatarImage src={recommendation.avatar} alt={recommendation.name} />
                          <AvatarFallback className="text-sm">
                            {getInitials(recommendation.name)}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center space-x-1">
                            <h4 className="text-sm font-medium">{recommendation.name}</h4>
                            {recommendation.isVerified && (
                              <Star className="w-3 h-3 text-blue-500" />
                            )}
                          </div>
                          <p className="text-xs text-muted-foreground">{recommendation.title}</p>
                          {recommendation.company && (
                            <p className="text-xs text-muted-foreground">{recommendation.company.name}</p>
                          )}
                          <p className="text-xs text-blue-600">{recommendation.reason}</p>
                          <Button
                            size="sm"
                            variant="outline"
                            className="w-full mt-2"
                            onClick={() => followRecruiter(recommendation.id)}
                          >
                            <UserPlus className="w-3 h-3 mr-1" />
                            Follow
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          
          <div className="flex items-center space-x-1">
            {[...Array(totalPages)].map((_, i) => (
              <Button
                key={i}
                variant={currentPage === i + 1 ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentPage(i + 1)}
              >
                {i + 1}
              </Button>
            ))}
          </div>
          
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}

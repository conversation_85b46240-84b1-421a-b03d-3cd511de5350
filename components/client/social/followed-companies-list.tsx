'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Input } from '@/components/ui/input'
import { 
  Building, 
  Users, 
  MapPin, 
  ExternalLink, 
  Search,
  UserMinus,
  Briefcase,
  Globe,
  Calendar,
  TrendingUp
} from 'lucide-react'
import { useAuthStore } from '@/stores/auth.store'
import { useToast } from '@/hooks/use-toast'

interface FollowedCompany {
  _id: string
  name: string
  logo?: string
  location?: string
  industry?: string
  size?: string
  website?: string
  description?: string
  verification?: {
    isVerified: boolean
  }
  jobCount?: number
  followerCount?: number
  recentUpdates?: Array<{
    type: string
    title: string
    content: string
    createdAt: string
  }>
}

interface CompanyUpdate {
  id: string
  type: string
  title: string
  content: string
  createdAt: string
  company: {
    id: string
    name: string
    logo?: string
  }
}

export function FollowedCompaniesList() {
  const [companies, setCompanies] = useState<FollowedCompany[]>([])
  const [recentUpdates, setRecentUpdates] = useState<CompanyUpdate[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const { token } = useAuthStore()
  const { toast } = useToast()

  useEffect(() => {
    fetchFollowedCompanies()
  }, [currentPage])

  const fetchFollowedCompanies = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/v1/clients/companies/followed?page=${currentPage}&limit=10`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setCompanies(data.data.followedCompanies)
        setRecentUpdates(data.data.recentUpdates)
        setTotalPages(data.data.pagination.totalPages)
      }
    } catch (error) {
      console.error('Failed to fetch followed companies:', error)
      toast({
        title: "Error",
        description: "Failed to load followed companies",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const unfollowCompany = async (companyId: string) => {
    try {
      const response = await fetch(`/api/v1/clients/companies/${companyId}/follow`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        setCompanies(prev => prev.filter(company => company._id !== companyId))
        toast({
          title: "Company unfollowed",
          description: "You are no longer following this company",
        })
      } else {
        throw new Error('Failed to unfollow company')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to unfollow company",
        variant: "destructive"
      })
    }
  }

  const filteredCompanies = companies.filter(company =>
    company.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    company.industry?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const getCompanyInitials = (name: string) => {
    return name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2)
  }

  const getUpdateTypeIcon = (type: string) => {
    switch (type) {
      case 'job_posting':
        return <Briefcase className="w-4 h-4" />
      case 'company_news':
        return <TrendingUp className="w-4 h-4" />
      case 'event':
        return <Calendar className="w-4 h-4" />
      default:
        return <Building className="w-4 h-4" />
    }
  }

  const getUpdateTypeColor = (type: string) => {
    switch (type) {
      case 'job_posting':
        return 'bg-blue-500'
      case 'company_news':
        return 'bg-green-500'
      case 'event':
        return 'bg-purple-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-4">
            {[...Array(3)].map((_, i) => (
              <Card key={i}>
                <CardContent className="pt-6">
                  <div className="space-y-3">
                    <div className="h-6 bg-gray-200 rounded animate-pulse" />
                    <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
                    <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="space-y-4">
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Followed Companies</h1>
          <p className="text-muted-foreground">
            {companies.length} {companies.length === 1 ? 'company' : 'companies'} followed
          </p>
        </div>
        
        {/* Search */}
        <div className="relative w-full sm:w-80">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search companies..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Companies List */}
        <div className="lg:col-span-2 space-y-4">
          {filteredCompanies.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <Building className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">No companies found</h3>
                  <p className="text-muted-foreground mb-4">
                    {searchQuery ? 'No companies match your search criteria' : 'Start following companies to see them here'}
                  </p>
                  {!searchQuery && (
                    <Button onClick={() => window.location.href = '/companies'}>
                      Browse Companies
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            filteredCompanies.map((company) => (
              <Card key={company._id} className="hover:shadow-md transition-shadow">
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      <Avatar className="w-16 h-16">
                        <AvatarImage src={company.logo} alt={company.name} />
                        <AvatarFallback className="text-lg font-semibold">
                          {getCompanyInitials(company.name)}
                        </AvatarFallback>
                      </Avatar>
                      
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center space-x-2">
                          <h3 className="text-lg font-semibold">{company.name}</h3>
                          {company.verification?.isVerified && (
                            <Badge variant="default" className="bg-blue-500">
                              Verified
                            </Badge>
                          )}
                        </div>
                        
                        <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                          {company.industry && (
                            <div className="flex items-center space-x-1">
                              <Building className="w-4 h-4" />
                              <span>{company.industry}</span>
                            </div>
                          )}
                          
                          {company.location && (
                            <div className="flex items-center space-x-1">
                              <MapPin className="w-4 h-4" />
                              <span>{company.location}</span>
                            </div>
                          )}
                          
                          {company.size && (
                            <div className="flex items-center space-x-1">
                              <Users className="w-4 h-4" />
                              <span>{company.size}</span>
                            </div>
                          )}
                        </div>

                        {company.description && (
                          <p className="text-sm text-muted-foreground line-clamp-2">
                            {company.description}
                          </p>
                        )}

                        <div className="flex items-center space-x-4 text-sm">
                          <div className="flex items-center space-x-1">
                            <Briefcase className="w-4 h-4 text-blue-500" />
                            <span>{company.jobCount || 0} open jobs</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Users className="w-4 h-4 text-green-500" />
                            <span>{company.followerCount || 0} followers</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col space-y-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(company.website, '_blank')}
                        disabled={!company.website}
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Visit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => unfollowCompany(company._id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <UserMinus className="w-4 h-4 mr-2" />
                        Unfollow
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Recent Updates Sidebar */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="w-5 h-5 mr-2" />
                Recent Updates
              </CardTitle>
            </CardHeader>
            <CardContent>
              {recentUpdates.length === 0 ? (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No recent updates from followed companies
                </p>
              ) : (
                <div className="space-y-4">
                  {recentUpdates.slice(0, 5).map((update, index) => (
                    <div key={index} className="border-b border-muted last:border-0 pb-3 last:pb-0">
                      <div className="flex items-start space-x-3">
                        <div className={`w-8 h-8 rounded-full ${getUpdateTypeColor(update.type)} flex items-center justify-center text-white`}>
                          {getUpdateTypeIcon(update.type)}
                        </div>
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium">{update.company.name}</span>
                            <span className="text-xs text-muted-foreground">
                              {getTimeAgo(update.createdAt)}
                            </span>
                          </div>
                          <h4 className="text-sm font-medium">{update.title}</h4>
                          <p className="text-xs text-muted-foreground line-clamp-2">
                            {update.content}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          
          <div className="flex items-center space-x-1">
            {[...Array(totalPages)].map((_, i) => (
              <Button
                key={i}
                variant={currentPage === i + 1 ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentPage(i + 1)}
              >
                {i + 1}
              </Button>
            ))}
          </div>
          
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}

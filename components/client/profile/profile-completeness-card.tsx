'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  CheckCircle, 
  AlertCircle, 
  TrendingUp, 
  Eye,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { useAuthStore } from '@/stores/auth.store'

interface ProfileCompletenessData {
  completeness: number
  completedSections: string[]
  missingSections: string[]
  recommendations: string[]
}

export function ProfileCompletenessCard() {
  const [completenessData, setCompletenessData] = useState<ProfileCompletenessData | null>(null)
  const [loading, setLoading] = useState(true)
  const [showDetails, setShowDetails] = useState(false)
  const { token } = useAuthStore()

  useEffect(() => {
    fetchCompleteness()
  }, [])

  const fetchCompleteness = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/v1/clients/me/completeness', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setCompletenessData(data.data)
      }
    } catch (error) {
      console.error('Failed to fetch profile completeness:', error)
    } finally {
      setLoading(false)
    }
  }

  const getCompletenessColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600'
    if (percentage >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getCompletenessMessage = (percentage: number) => {
    if (percentage >= 90) return 'Excellent! Your profile is nearly complete.'
    if (percentage >= 80) return 'Great! Your profile looks good.'
    if (percentage >= 60) return 'Good start! Add more details to improve visibility.'
    if (percentage >= 40) return 'Keep going! Your profile needs more information.'
    return 'Let\'s get started! Complete your profile to attract employers.'
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="w-5 h-5" />
            <span>Profile Completeness</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
            <div className="h-6 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!completenessData) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <AlertCircle className="w-8 h-8 mx-auto mb-2" />
            <p>Unable to load profile completeness</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-t-lg">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <TrendingUp className="w-5 h-5 text-primary" />
            <span>Profile Completeness</span>
          </div>
          <Badge 
            variant={completenessData.completeness >= 80 ? "default" : "secondary"}
            className={getCompletenessColor(completenessData.completeness)}
          >
            {completenessData.completeness}%
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="space-y-6">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="font-medium">Progress</span>
              <span className={getCompletenessColor(completenessData.completeness)}>
                {completenessData.completeness}%
              </span>
            </div>
            <Progress 
              value={completenessData.completeness} 
              className="h-3"
            />
            <p className="text-sm text-muted-foreground">
              {getCompletenessMessage(completenessData.completeness)}
            </p>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="flex items-center justify-center space-x-1 text-green-600 mb-1">
                <CheckCircle className="w-4 h-4" />
                <span className="font-semibold">{completenessData.completedSections.length}</span>
              </div>
              <p className="text-xs text-green-700">Completed</p>
            </div>
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <div className="flex items-center justify-center space-x-1 text-orange-600 mb-1">
                <AlertCircle className="w-4 h-4" />
                <span className="font-semibold">{completenessData.missingSections.length}</span>
              </div>
              <p className="text-xs text-orange-700">Remaining</p>
            </div>
          </div>

          {/* Toggle Details */}
          <Button
            variant="ghost"
            onClick={() => setShowDetails(!showDetails)}
            className="w-full justify-between"
          >
            <span>View Details</span>
            {showDetails ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
          </Button>

          {/* Detailed Breakdown */}
          {showDetails && (
            <div className="space-y-4 pt-4 border-t">
              {/* Completed Sections */}
              {completenessData.completedSections.length > 0 && (
                <div>
                  <h4 className="font-medium text-green-700 mb-2 flex items-center">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Completed Sections
                  </h4>
                  <div className="space-y-1">
                    {completenessData.completedSections.map((section, index) => (
                      <div key={index} className="flex items-center space-x-2 text-sm">
                        <CheckCircle className="w-3 h-3 text-green-500" />
                        <span>{section}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Missing Sections */}
              {completenessData.missingSections.length > 0 && (
                <div>
                  <h4 className="font-medium text-orange-700 mb-2 flex items-center">
                    <AlertCircle className="w-4 h-4 mr-2" />
                    Improve Your Profile
                  </h4>
                  <div className="space-y-2">
                    {completenessData.recommendations.map((recommendation, index) => (
                      <div key={index} className="flex items-start space-x-2 text-sm">
                        <AlertCircle className="w-3 h-3 text-orange-500 mt-0.5 flex-shrink-0" />
                        <span className="text-muted-foreground">{recommendation}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Action Button */}
          {completenessData.completeness < 100 && (
            <Button 
              className="w-full"
              onClick={() => window.location.href = '/client-dashboard/profile/edit'}
            >
              <Eye className="w-4 h-4 mr-2" />
              Complete Profile
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

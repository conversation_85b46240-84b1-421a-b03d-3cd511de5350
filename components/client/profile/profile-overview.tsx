'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'
import { 
  User, 
  MapPin, 
  Mail, 
  Phone, 
  Calendar,
  Briefcase,
  GraduationCap,
  Award,
  Languages,
  Edit,
  Eye,
  Download,
  Share2,
  Settings
} from 'lucide-react'
import { useClientStore } from '@/stores/client.store'
import { useAuthStore } from '@/stores/auth.store'
import { ProfileCompletenessCard } from './profile-completeness-card'
import { ProfileVisibilitySettings } from './profile-visibility-settings'

export function ProfileOverview() {
  const { client, fetchClientProfile } = useClientStore()
  const { user } = useAuthStore()
  const [showVisibilitySettings, setShowVisibilitySettings] = useState(false)

  useEffect(() => {
    if (!client) {
      fetchClientProfile()
    }
  }, [client, fetchClientProfile])

  const exportProfile = async (format: 'json' | 'pdf') => {
    try {
      const response = await fetch(`/api/v1/clients/me/export?format=${format}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (response.ok) {
        if (format === 'json') {
          const data = await response.json()
          const blob = new Blob([JSON.stringify(data.data, null, 2)], { type: 'application/json' })
          const url = URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = 'profile-export.json'
          a.click()
          URL.revokeObjectURL(url)
        } else {
          // For PDF, the API returns a download URL
          const data = await response.json()
          window.open(data.data.downloadUrl, '_blank')
        }
      }
    } catch (error) {
      console.error('Export failed:', error)
    }
  }

  if (!client || !user) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="h-20 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const fullName = `${user.profile?.firstName || ''} ${user.profile?.lastName || ''}`.trim() || 'Unknown User'
  const initials = fullName.split(' ').map(n => n[0]).join('').toUpperCase()

  return (
    <div className="space-y-6">
      {/* Profile Header */}
      <Card className="border-0 shadow-lg">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row md:items-start md:space-x-6 space-y-4 md:space-y-0">
            {/* Avatar and Basic Info */}
            <div className="flex flex-col items-center md:items-start space-y-4">
              <Avatar className="w-24 h-24">
                <AvatarImage src={user.profile?.avatar} alt={fullName} />
                <AvatarFallback className="text-lg font-semibold">
                  {initials}
                </AvatarFallback>
              </Avatar>
              
              <div className="text-center md:text-left">
                <h1 className="text-2xl font-bold">{fullName}</h1>
                {client.currentTitle && (
                  <p className="text-lg text-muted-foreground">{client.currentTitle}</p>
                )}
                {client.headline && (
                  <p className="text-sm text-muted-foreground mt-1">{client.headline}</p>
                )}
              </div>
            </div>

            {/* Contact Information */}
            <div className="flex-1 space-y-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {user.email && (
                  <div className="flex items-center space-x-2 text-sm">
                    <Mail className="w-4 h-4 text-muted-foreground" />
                    <span>{user.email}</span>
                  </div>
                )}
                
                {user.profile?.phone && (
                  <div className="flex items-center space-x-2 text-sm">
                    <Phone className="w-4 h-4 text-muted-foreground" />
                    <span>{user.profile.phone}</span>
                  </div>
                )}
                
                {user.profile?.location && (
                  <div className="flex items-center space-x-2 text-sm">
                    <MapPin className="w-4 h-4 text-muted-foreground" />
                    <span>{user.profile.location}</span>
                  </div>
                )}
                
                <div className="flex items-center space-x-2 text-sm">
                  <Calendar className="w-4 h-4 text-muted-foreground" />
                  <span>Member since {new Date(user.createdAt).toLocaleDateString()}</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-2 pt-4">
                <Button size="sm" onClick={() => window.location.href = '/client-dashboard/profile/edit'}>
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Profile
                </Button>
                
                <Button variant="outline" size="sm" onClick={() => setShowVisibilitySettings(!showVisibilitySettings)}>
                  <Settings className="w-4 h-4 mr-2" />
                  Privacy
                </Button>
                
                <Button variant="outline" size="sm" onClick={() => exportProfile('json')}>
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
                
                <Button variant="outline" size="sm">
                  <Share2 className="w-4 h-4 mr-2" />
                  Share
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Privacy Settings (Collapsible) */}
      {showVisibilitySettings && (
        <ProfileVisibilitySettings />
      )}

      {/* Profile Completeness */}
      <ProfileCompletenessCard />

      {/* Professional Summary */}
      {client.summary && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="w-5 h-5 mr-2" />
              Professional Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground leading-relaxed">{client.summary}</p>
          </CardContent>
        </Card>
      )}

      {/* Experience Overview */}
      {client.experience && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Briefcase className="w-5 h-5 mr-2" />
              Experience
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-2xl font-bold text-primary">{client.experience.yearsOfExperience || 0}</div>
                <div className="text-sm text-muted-foreground">Years Experience</div>
              </div>
              
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-lg font-semibold capitalize">{client.experience.level || 'Not specified'}</div>
                <div className="text-sm text-muted-foreground">Experience Level</div>
              </div>
              
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-lg font-semibold">{client.workHistory?.length || 0}</div>
                <div className="text-sm text-muted-foreground">Previous Roles</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Skills */}
      {client.skills && client.skills.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Award className="w-5 h-5 mr-2" />
              Skills
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {client.skills.map((skill, index) => (
                <Badge key={index} variant="secondary" className="text-sm">
                  {skill}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Education */}
      {client.education && client.education.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <GraduationCap className="w-5 h-5 mr-2" />
              Education
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {client.education.map((edu, index) => (
                <div key={index} className="border-l-2 border-primary/20 pl-4">
                  <h4 className="font-semibold">{edu.degree}</h4>
                  <p className="text-muted-foreground">{edu.institution}</p>
                  {edu.graduationYear && (
                    <p className="text-sm text-muted-foreground">Graduated: {edu.graduationYear}</p>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Languages */}
      {client.languages && client.languages.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Languages className="w-5 h-5 mr-2" />
              Languages
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {client.languages.map((lang, index) => (
                <div key={index} className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                  <span className="font-medium">{lang.language}</span>
                  <Badge variant="outline" className="capitalize">
                    {lang.proficiency}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Activity Stats */}
      {client.activity && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Eye className="w-5 h-5 mr-2" />
              Profile Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-2xl font-bold text-primary">{client.activity.profileViews || 0}</div>
                <div className="text-sm text-muted-foreground">Profile Views</div>
              </div>
              
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-2xl font-bold text-primary">{client.activity.searchAppearances || 0}</div>
                <div className="text-sm text-muted-foreground">Search Appearances</div>
              </div>
              
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-lg font-semibold">{client.activity.profileCompleteness || 0}%</div>
                <div className="text-sm text-muted-foreground">Profile Complete</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Eye, 
  EyeOff, 
  Shield, 
  Users, 
  Globe,
  Save,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import { useAuthStore } from '@/stores/auth.store'
import { useToast } from '@/hooks/use-toast'

interface VisibilitySettings {
  profileVisibility: 'public' | 'private' | 'recruiters_only'
  showSalaryExpectation: boolean
  showCurrentCompany: boolean
  allowRecruiterContact: boolean
  showProfileToCurrentEmployer: boolean
}

export function ProfileVisibilitySettings() {
  const [settings, setSettings] = useState<VisibilitySettings>({
    profileVisibility: 'public',
    showSalaryExpectation: true,
    showCurrentCompany: true,
    allowRecruiterContact: true,
    showProfileToCurrentEmployer: false
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const { token } = useAuthStore()
  const { toast } = useToast()

  useEffect(() => {
    fetchVisibilitySettings()
  }, [])

  const fetchVisibilitySettings = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/v1/clients/me/visibility', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setSettings(data.data.privacy)
      }
    } catch (error) {
      console.error('Failed to fetch visibility settings:', error)
    } finally {
      setLoading(false)
    }
  }

  const saveSettings = async () => {
    try {
      setSaving(true)
      const response = await fetch('/api/v1/clients/me/visibility', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(settings)
      })

      if (response.ok) {
        toast({
          title: "Settings Updated",
          description: "Your privacy settings have been saved successfully.",
        })
      } else {
        throw new Error('Failed to save settings')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save privacy settings. Please try again.",
        variant: "destructive"
      })
    } finally {
      setSaving(false)
    }
  }

  const getVisibilityIcon = (visibility: string) => {
    switch (visibility) {
      case 'public':
        return <Globe className="w-4 h-4" />
      case 'recruiters_only':
        return <Users className="w-4 h-4" />
      case 'private':
        return <EyeOff className="w-4 h-4" />
      default:
        return <Eye className="w-4 h-4" />
    }
  }

  const getVisibilityDescription = (visibility: string) => {
    switch (visibility) {
      case 'public':
        return 'Your profile is visible to everyone, including search engines'
      case 'recruiters_only':
        return 'Only verified recruiters and hiring managers can view your profile'
      case 'private':
        return 'Your profile is hidden from public view'
      default:
        return ''
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="w-5 h-5" />
            <span>Privacy Settings</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
            <div className="h-6 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-t-lg">
        <CardTitle className="flex items-center space-x-2">
          <Shield className="w-5 h-5 text-primary" />
          <span>Privacy Settings</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="space-y-6">
          {/* Profile Visibility */}
          <div className="space-y-4">
            <div>
              <Label className="text-base font-medium">Profile Visibility</Label>
              <p className="text-sm text-muted-foreground mt-1">
                Control who can see your profile
              </p>
            </div>
            
            <RadioGroup
              value={settings.profileVisibility}
              onValueChange={(value) => 
                setSettings(prev => ({ 
                  ...prev, 
                  profileVisibility: value as 'public' | 'private' | 'recruiters_only' 
                }))
              }
              className="space-y-3"
            >
              <div className="flex items-center space-x-3 p-3 border rounded-lg">
                <RadioGroupItem value="public" id="public" />
                <div className="flex-1">
                  <Label htmlFor="public" className="flex items-center space-x-2 cursor-pointer">
                    <Globe className="w-4 h-4 text-green-600" />
                    <span className="font-medium">Public</span>
                  </Label>
                  <p className="text-sm text-muted-foreground mt-1">
                    Visible to everyone, including search engines
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3 p-3 border rounded-lg">
                <RadioGroupItem value="recruiters_only" id="recruiters_only" />
                <div className="flex-1">
                  <Label htmlFor="recruiters_only" className="flex items-center space-x-2 cursor-pointer">
                    <Users className="w-4 h-4 text-blue-600" />
                    <span className="font-medium">Recruiters Only</span>
                  </Label>
                  <p className="text-sm text-muted-foreground mt-1">
                    Only verified recruiters and hiring managers
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3 p-3 border rounded-lg">
                <RadioGroupItem value="private" id="private" />
                <div className="flex-1">
                  <Label htmlFor="private" className="flex items-center space-x-2 cursor-pointer">
                    <EyeOff className="w-4 h-4 text-red-600" />
                    <span className="font-medium">Private</span>
                  </Label>
                  <p className="text-sm text-muted-foreground mt-1">
                    Hidden from public view
                  </p>
                </div>
              </div>
            </RadioGroup>
          </div>

          {/* Additional Privacy Controls */}
          <div className="space-y-4">
            <div>
              <Label className="text-base font-medium">Additional Privacy Controls</Label>
              <p className="text-sm text-muted-foreground mt-1">
                Fine-tune what information is visible
              </p>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <Label htmlFor="salary" className="font-medium">Show Salary Expectations</Label>
                  <p className="text-sm text-muted-foreground">
                    Display your expected salary range
                  </p>
                </div>
                <Switch
                  id="salary"
                  checked={settings.showSalaryExpectation}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({ ...prev, showSalaryExpectation: checked }))
                  }
                />
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <Label htmlFor="company" className="font-medium">Show Current Company</Label>
                  <p className="text-sm text-muted-foreground">
                    Display your current employer
                  </p>
                </div>
                <Switch
                  id="company"
                  checked={settings.showCurrentCompany}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({ ...prev, showCurrentCompany: checked }))
                  }
                />
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <Label htmlFor="contact" className="font-medium">Allow Recruiter Contact</Label>
                  <p className="text-sm text-muted-foreground">
                    Let recruiters send you messages
                  </p>
                </div>
                <Switch
                  id="contact"
                  checked={settings.allowRecruiterContact}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({ ...prev, allowRecruiterContact: checked }))
                  }
                />
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <Label htmlFor="employer" className="font-medium">Hide from Current Employer</Label>
                  <p className="text-sm text-muted-foreground">
                    Don't show profile to your current company
                  </p>
                </div>
                <Switch
                  id="employer"
                  checked={!settings.showProfileToCurrentEmployer}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({ ...prev, showProfileToCurrentEmployer: !checked }))
                  }
                />
              </div>
            </div>
          </div>

          {/* Privacy Alert */}
          {settings.profileVisibility === 'private' && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Your profile is currently private. Recruiters and employers won't be able to find you.
              </AlertDescription>
            </Alert>
          )}

          {/* Save Button */}
          <Button 
            onClick={saveSettings} 
            disabled={saving}
            className="w-full"
          >
            {saving ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Save Privacy Settings
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

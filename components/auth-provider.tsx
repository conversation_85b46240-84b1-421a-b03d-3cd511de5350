"use client"

import { useEffect, useState } from 'react'
import { useAuthStore } from '@/stores/auth.store'

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { checkAuthStatus, hydrateFromStorage } = useAuthStore()
  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    const initializeAuth = async () => {
      console.log('🔍 AuthProvider: Initializing authentication')

      // Manually hydrate from storage first
      hydrateFromStorage()

      // Wait a moment for state to update
      await new Promise(resolve => setTimeout(resolve, 200))

      const currentState = useAuthStore.getState()
      console.log('🔍 AuthProvider: Current state after hydration:', {
        hasToken: !!currentState.token,
        hasRefreshToken: !!currentState.refreshToken,
        isAuthenticated: currentState.isAuthenticated,
        hasUser: !!currentState.user
      })

      try {
        // Only check auth status if we have a token
        if (currentState.token) {
          console.log('🔍 AuthProvider: Token found, checking auth status')
          await checkAuthStatus()
        } else {
          console.log('🔍 AuthProvider: No token found, skipping auth check')
        }
        console.log('🔍 AuthProvider: Auth status check completed')
      } catch (error) {
        console.error('🔍 AuthProvider: Auth status check failed:', error)
      } finally {
        setIsInitialized(true)
        console.log('🔍 AuthProvider: Initialization completed')
      }
    }

    // Only initialize once
    if (!isInitialized) {
      initializeAuth()
    }
  }, [checkAuthStatus, hydrateFromStorage, isInitialized])

  // Don't render children until auth is initialized to prevent flash
  // of unauthenticated content
  if (!isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return <>{children}</>
}

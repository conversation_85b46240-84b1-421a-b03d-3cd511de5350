"use client"

import { useEffect, useRef } from "react"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Brain, CheckCircle, Clock, Star, Trophy, Target, ArrowRight, RotateCcw, Award, Loader2, Zap } from "lucide-react"
import { useQuizCategories, useQuizSession, useQuizResults, useQuizProgression } from "@/stores/quiz.store"
import { AwardModal } from "@/components/quiz/award-modal"
import { RegistrationPrompt } from "@/components/quiz/registration-prompt"
import { PhaseTransition } from "@/components/quiz/phase-transition"
import { EnhancedResultsModal } from "@/components/quiz/enhanced-results-modal"

// Remove static data - now using dynamic data from store

export function SkillsAssessment() {
  // Quiz store hooks
  const { categories, isLoading, error, fetchCategories } = useQuizCategories()
  const {
    session,
    currentQuestionIndex,
    selectedAnswer,
    timeLeft,
    isActive,
    isSubmitting,
    progress,
    startQuiz,
    selectAnswer,
    nextQuestion,
    resetQuiz,
    updateTimer
  } = useQuizSession()
  const {
    result,
    showResults,
    showRegistrationPrompt,
    showAwardModal,
    showLoginForm,
    setShowResults,
    setShowRegistrationPrompt,
    setShowAwardModal,
    setShowLoginForm
  } = useQuizResults()
  const {
    quizPhase,
    firstPhaseAnswers,
    wrongAnswers,
    totalQuestionsAnswered,
    showPhaseTransition,
    proceedToSecondPhase,
    skipSecondPhase,
    startSecondPhase,
    completeQuiz
  } = useQuizProgression()

  // Timer ref
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  // Load categories on mount
  useEffect(() => {
    fetchCategories()
  }, [fetchCategories])

  // Timer effect
  useEffect(() => {
    if (isActive && timeLeft > 0) {
      timerRef.current = setInterval(() => {
        updateTimer(timeLeft - 1)
      }, 1000)
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
    }
  }, [isActive, timeLeft, updateTimer])

  // Handlers
  const handleStartQuiz = async (categoryId: string) => {
    try {
      await startQuiz(categoryId)
    } catch (error) {
      console.error('Failed to start quiz:', error)
      // Could show error toast here
    }
  }

  const handleAnswerSelect = (answerIndex: string) => {
    selectAnswer(parseInt(answerIndex))
  }

  const handleRegister = () => {
    // Navigate to registration page
    window.location.href = '/register'
  }

  const handleDownloadCertificate = (awardId: string) => {
    // Implement certificate download
    console.log('Download certificate for award:', awardId)
  }

  const getSkillLevel = (score: number) => {
    if (score >= 90) return { level: "Expert", color: "text-green-600", icon: Trophy }
    if (score >= 75) return { level: "Advanced", color: "text-blue-600", icon: Star }
    if (score >= 60) return { level: "Intermediate", color: "text-orange-600", icon: Target }
    return { level: "Beginner", color: "text-gray-600", icon: Award }
  }

  return (
    <section className="py-12 md:py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8 md:mb-16"
        >
          <div className="inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 rounded-full bg-primary/10 text-primary text-xs md:text-sm font-medium mb-4 md:mb-6">
            <Brain className="w-3 h-3 md:w-4 md:h-4 mr-1.5 md:mr-2" />
            Skills Assessment
          </div>
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6">
            Test Your <span className="text-primary">Expertise</span>
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto px-4 md:px-0">
            <span className="hidden md:inline">
              Take our comprehensive skills assessment to showcase your abilities and get matched with relevant
              opportunities.
            </span>
            <span className="md:hidden">
              Test your skills and get better job matches.
            </span>
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          <AnimatePresence mode="wait">
            {!session ? (
              // Category Selection
              <>
                {isLoading ? (
                  <motion.div
                    key="loading"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-center py-12"
                  >
                    <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
                    <p className="text-muted-foreground">Loading quiz categories...</p>
                  </motion.div>
                ) : error ? (
                  <motion.div
                    key="error"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-center py-12"
                  >
                    <p className="text-red-500 mb-4">{error}</p>
                    <Button onClick={fetchCategories} variant="outline">
                      Try Again
                    </Button>
                  </motion.div>
                ) : (
                  <motion.div
                    key="categories"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6"
                  >
                    {categories.map((category, index) => (
                      <motion.div
                        key={category.id}
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Card
                          className="glass cursor-pointer hover:shadow-xl transition-all duration-300 border-border/50 hover:border-primary/20 relative"
                          onClick={() => handleStartQuiz(category.id)}
                        >
                          {/* Difficulty Badge */}
                          <div className="absolute top-3 right-3 z-10">
                            <Badge
                              variant="secondary"
                              className={`text-xs ${
                                category.difficulty === 'expert' ? 'bg-red-100 text-red-800' :
                                category.difficulty === 'advanced' ? 'bg-purple-100 text-purple-800' :
                                category.difficulty === 'intermediate' ? 'bg-blue-100 text-blue-800' :
                                'bg-green-100 text-green-800'
                              }`}
                            >
                              {category.difficulty}
                            </Badge>
                          </div>

                          <CardHeader className="pb-3 md:pb-6">
                            <CardTitle className="flex items-center space-x-3">
                              <div className="text-2xl md:text-3xl">{category.icon}</div>
                              <div className="flex-1 min-w-0">
                                <h3 className="text-lg md:text-xl font-bold truncate">{category.name}</h3>
                                <p className="text-xs md:text-sm text-muted-foreground">
                                  <span className="hidden sm:inline">
                                    {category.availableQuestions || category.totalQuestions} questions • {category.estimatedTime} minutes
                                  </span>
                                  <span className="sm:hidden">
                                    {category.estimatedTime} min • {category.availableQuestions || category.totalQuestions} questions
                                  </span>
                                </p>
                              </div>
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="space-y-3 md:space-y-4">
                            <div className="flex flex-wrap gap-1.5 md:gap-2">
                              {category.skills.slice(0, 3).map((skill) => (
                                <Badge key={skill} variant="secondary" className="text-xs">
                                  {skill}
                                </Badge>
                              ))}
                              {category.skills.length > 3 && (
                                <Badge variant="secondary" className="text-xs">
                                  +{category.skills.length - 3}
                                </Badge>
                              )}
                            </div>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                <Clock className="w-4 h-4" />
                                <span>Pass: {category.passingScore}%</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Zap className="w-4 h-4 text-primary" />
                                <ArrowRight className="w-5 h-5 text-primary" />
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </motion.div>
                )}

                {/* View All Assessments Link */}
                {!isLoading && !error && categories.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="text-center mt-6 md:mt-8"
                  >
                    <Button
                      variant="outline"
                      asChild
                      className="bg-background/50 backdrop-blur-sm border-primary/20 hover:bg-primary/10"
                    >
                      <Link href="/quiz">
                        <Brain className="w-4 h-4 mr-2" />
                        View All Assessments
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Link>
                    </Button>
                  </motion.div>
                )}
              </>
            ) : showPhaseTransition ? (
              // Phase Transition
              <PhaseTransition
                firstPhaseScore={{
                  correctAnswers: firstPhaseAnswers.filter(a => a.isCorrect).length,
                  totalQuestions: firstPhaseAnswers.length,
                  percentage: Math.round((firstPhaseAnswers.filter(a => a.isCorrect).length / firstPhaseAnswers.length) * 100),
                  totalPoints: firstPhaseAnswers.reduce((sum, a) => sum + (a.points || 0), 0),
                  maxPoints: firstPhaseAnswers.length * 10 // Assuming 10 points per question
                }}
                categoryName={session?.category.name || ''}
                categoryIcon={session?.category.icon || '📚'}
                onProceed={proceedToSecondPhase}
                onSkip={skipSecondPhase}
              />
            ) : !showResults ? (
              // Assessment Questions
              <motion.div
                key="assessment"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
              >
                <Card className="glass">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center space-x-2">
                        <div className="text-2xl">{session.category.icon}</div>
                        <span>{session.category.name}</span>
                      </CardTitle>
                      <div className="flex items-center space-x-4">
                        <div className="text-sm text-muted-foreground">
                          {currentQuestionIndex + 1} of {session.questions.length}
                        </div>
                        <div className="flex items-center space-x-2 text-sm">
                          <Clock className="w-4 h-4" />
                          <span className={timeLeft <= 60 ? "text-red-500 font-bold" : ""}>
                            {Math.floor(timeLeft / 60)}:{(timeLeft % 60).toString().padStart(2, "0")}
                          </span>
                        </div>
                      </div>
                    </div>
                    <Progress value={progress} className="h-2" />
                  </CardHeader>
                  <CardContent className="space-y-8">
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-xl font-semibold">{session.questions[currentQuestionIndex].question}</h3>
                        <Badge variant="outline" className="text-xs">
                          {session.questions[currentQuestionIndex].points} pts
                        </Badge>
                      </div>

                      <RadioGroup value={selectedAnswer !== null ? selectedAnswer.toString() : ""} onValueChange={handleAnswerSelect}>
                        <div className="space-y-4">
                          {session.questions[currentQuestionIndex].options.map((option, index) => (
                            <motion.div
                              key={index}
                              whileHover={{ scale: 1.01 }}
                              className="flex items-center space-x-3 p-4 rounded-lg border border-border/50 hover:border-primary/20 hover:bg-primary/5 transition-all duration-200 cursor-pointer"
                            >
                              <RadioGroupItem value={index.toString()} id={`option-${index}`} />
                              <Label htmlFor={`option-${index}`} className="flex-1 cursor-pointer">
                                {option}
                              </Label>
                            </motion.div>
                          ))}
                        </div>
                      </RadioGroup>
                    </div>

                    <div className="flex items-center justify-between">
                      <Button variant="outline" onClick={resetQuiz}>
                        <RotateCcw className="w-4 h-4 mr-2" />
                        Start Over
                      </Button>
                      <Button
                        onClick={nextQuestion}
                        disabled={selectedAnswer === null || isSubmitting}
                        className="min-w-[120px]"
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Submitting...
                          </>
                        ) : (
                          <>
                            {currentQuestionIndex === session.questions.length - 1 ? "Finish" : "Next"}
                            <ArrowRight className="w-4 h-4 ml-2" />
                          </>
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ) : result ? (
              // Results
              <motion.div
                key="results"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="text-center space-y-8"
              >
                <Card className="glass">
                  <CardContent className="p-12">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2, type: "spring" }}
                      className="mb-8"
                    >
                      <div className="w-32 h-32 mx-auto bg-primary/10 rounded-full flex items-center justify-center mb-6">
                        {(() => {
                          const { icon: Icon } = getSkillLevel(result.score)
                          return <Icon className="w-16 h-16 text-primary" />
                        })()}
                      </div>
                      <h3 className="text-4xl font-bold mb-2">{result.score}%</h3>
                      <p className={`text-xl font-semibold ${result.skillLevel.color}`}>
                        {result.skillLevel.level} Level
                      </p>
                      {result.passed && (
                        <Badge className="mt-3 bg-green-100 text-green-800 border-green-200">
                          <Trophy className="w-3 h-3 mr-1" />
                          Passed! 🎉
                        </Badge>
                      )}
                    </motion.div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                      <div className="p-4 rounded-lg bg-muted/30">
                        <CheckCircle className="w-8 h-8 text-green-500 mx-auto mb-2" />
                        <div className="text-2xl font-bold">{result.correctAnswers}</div>
                        <div className="text-sm text-muted-foreground">Correct Answers</div>
                      </div>
                      <div className="p-4 rounded-lg bg-muted/30">
                        <Clock className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                        <div className="text-2xl font-bold">
                          {Math.floor(result.timeSpent / 60)}:{(result.timeSpent % 60).toString().padStart(2, "0")}
                        </div>
                        <div className="text-sm text-muted-foreground">Time Taken</div>
                      </div>
                      <div className="p-4 rounded-lg bg-muted/30">
                        <Star className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
                        <div className="text-2xl font-bold">{result.totalPoints}</div>
                        <div className="text-sm text-muted-foreground">Points Earned</div>
                      </div>
                    </div>

                    {/* Awards Section */}
                    {result.earnedAwards.length > 0 && (
                      <div className="mb-8">
                        <h4 className="text-lg font-semibold mb-4">🏆 Awards Earned:</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {result.earnedAwards.map((award) => (
                            <div key={award.id} className="p-4 rounded-lg bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200">
                              <div className="flex items-center space-x-3">
                                <div
                                  className="w-10 h-10 rounded-full flex items-center justify-center text-lg"
                                  style={{ backgroundColor: award.badgeColor + '20', color: award.badgeColor }}
                                >
                                  {award.badgeIcon}
                                </div>
                                <div>
                                  <h5 className="font-semibold text-sm">{award.name}</h5>
                                  <p className="text-xs text-muted-foreground">{award.description}</p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="space-y-4 mb-8">
                      <h4 className="text-lg font-semibold">Recommended Next Steps:</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                        <div className="p-4 rounded-lg bg-primary/5 border border-primary/20">
                          <h5 className="font-semibold text-primary mb-2">Job Matches</h5>
                          <p className="text-sm text-muted-foreground">
                            You&apos;re qualified for {result.recommendations.jobMatches} positions.
                          </p>
                        </div>
                        <div className="p-4 rounded-lg bg-primary/5 border border-primary/20">
                          <h5 className="font-semibold text-primary mb-2">Next Steps</h5>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            {result.recommendations.nextSteps.map((step, index) => (
                              <li key={index}>• {step}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                      <Button size="lg" className="text-lg px-8">
                        View Matching Jobs
                      </Button>
                      <Button variant="outline" size="lg" onClick={resetQuiz}>
                        Take Another Assessment
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ) : null}
          </AnimatePresence>

          {/* Modals */}
          {result && (
            <>
              <EnhancedResultsModal
                result={{
                  score: result.score,
                  totalQuestions: totalQuestionsAnswered || result.totalQuestions,
                  correctAnswers: result.correctAnswers,
                  totalPoints: result.totalPoints,
                  maxPoints: result.maxPoints,
                  timeSpent: result.timeSpent,
                  category: {
                    name: result.category.name,
                    icon: result.category.icon,
                    difficulty: result.category.difficulty
                  },
                  phase: quizPhase,
                  earnedAward: result.earnedAwards && result.earnedAwards.length > 0
                }}
                isOpen={showResults}
                onClose={() => {
                  setShowResults(false)
                  resetQuiz()
                }}
                showRegistration={!showRegistrationPrompt}
              />

              <AwardModal
                isOpen={showAwardModal}
                onClose={() => setShowAwardModal(false)}
                awards={result.earnedAwards}
                onDownload={handleDownloadCertificate}
                onRegister={handleRegister}
                isRegistered={false} // TODO: Check actual user auth status
              />

              <RegistrationPrompt
                isOpen={showRegistrationPrompt}
                onClose={() => setShowRegistrationPrompt(false)}
                onRegister={handleRegister}
                score={result.score}
                categoryName={result.category.name}
                skillLevel={result.skillLevel.level}
              />
            </>
          )}
        </div>
      </div>
    </section>
  )
}

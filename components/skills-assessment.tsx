"use client"

import { useEffect, useRef } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Brain, CheckCircle, Clock, Star, Trophy, Target, ArrowRight, RotateCcw, Award, Loader2, Zap, AlertCircle } from "lucide-react"
import { useQuizCategories, useQuizSession, useQuizResults, useQuizProgression } from "@/stores/quiz.store"
import { AwardModal } from "@/components/quiz/award-modal"
import { RegistrationPrompt } from "@/components/quiz/registration-prompt"
import { PhaseTransition } from "@/components/quiz/phase-transition"
import { EnhancedResultsModal } from "@/components/quiz/enhanced-results-modal"

// Remove static data - now using dynamic data from store

export function SkillsAssessment() {
  const searchParams = useSearchParams()
  const router = useRouter()

  // Quiz store hooks
  const { categories, isLoading, error, fetchCategories } = useQuizCategories()
  const {
    session,
    currentQuestionIndex,
    selectedAnswer,
    timeLeft,
    isActive,
    isSubmitting,
    progress,
    startQuiz,
    selectAnswer,
    nextQuestion,
    resetQuiz,
    updateTimer
  } = useQuizSession()
  const {
    result,
    showResults,
    showRegistrationPrompt,
    showAwardModal,
    setShowResults,
    setShowRegistrationPrompt,
    setShowAwardModal
  } = useQuizResults()
  const {
    quizPhase,
    firstPhaseAnswers,
    totalQuestionsAnswered,
    showPhaseTransition,
    proceedToSecondPhase,
    skipSecondPhase
  } = useQuizProgression()

  // Timer ref
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  // Load categories on mount
  useEffect(() => {
    fetchCategories()
  }, [fetchCategories])

  // Handle URL parameters for category selection
  useEffect(() => {
    const categoryId = searchParams.get('category')
    if (categoryId && categories.length > 0) {
      // Redirect to dedicated quiz detail page
      router.push(`/quiz/${categoryId}`)
    }
  }, [searchParams, categories, router])

  // Timer effect
  useEffect(() => {
    if (isActive && timeLeft > 0) {
      timerRef.current = setInterval(() => {
        updateTimer(timeLeft - 1)
      }, 1000)
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
    }
  }, [isActive, timeLeft, updateTimer])

  // Handlers
  const handleStartQuiz = (categoryId: string) => {
    // Redirect to dedicated quiz detail page
    router.push(`/quiz/${categoryId}`)
  }

  const handleAnswerSelect = (answerIndex: string) => {
    selectAnswer(parseInt(answerIndex))
  }

  const handleRegister = () => {
    // Navigate to registration page
    window.location.href = '/register'
  }

  const handleDownloadCertificate = (awardId: string) => {
    // Implement certificate download
    console.log('Download certificate for award:', awardId)
  }

  const getSkillLevel = (score: number) => {
    if (score >= 90) return { level: "Expert", color: "text-green-600", icon: Trophy }
    if (score >= 75) return { level: "Advanced", color: "text-blue-600", icon: Star }
    if (score >= 60) return { level: "Intermediate", color: "text-orange-600", icon: Target }
    return { level: "Beginner", color: "text-gray-600", icon: Award }
  }

  return (
    <section id="skills-assessment" className="py-12 md:py-20 bg-muted/30 scroll-mt-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8 md:mb-16"
        >
          <div className="inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 rounded-full bg-primary/10 text-primary text-xs md:text-sm font-medium mb-4 md:mb-6">
            <Brain className="w-3 h-3 md:w-4 md:h-4 mr-1.5 md:mr-2" />
            Skills Assessment
          </div>
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6 leading-tight">
            Test Your <span className="text-primary">Expertise</span>
          </h2>
          <p className="text-sm sm:text-base md:text-lg lg:text-xl text-muted-foreground max-w-3xl mx-auto px-2 sm:px-4 md:px-0 leading-relaxed">
            <span className="hidden sm:inline">
              Take our comprehensive skills assessment to showcase your abilities and get matched with relevant
              opportunities.
            </span>
            <span className="sm:hidden">
              Test your skills and get better job matches.
            </span>
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          <AnimatePresence mode="wait">
            {!session ? (
              // Category Selection
              <>
                {isLoading ? (
                  <motion.div
                    key="loading"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-center py-8 sm:py-12"
                  >
                    <Loader2 className="w-6 h-6 sm:w-8 sm:h-8 animate-spin mx-auto mb-3 sm:mb-4 text-primary" />
                    <p className="text-sm sm:text-base text-muted-foreground">Loading quiz categories...</p>
                  </motion.div>
                ) : error ? (
                  <motion.div
                    key="error"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-center py-8 sm:py-12 px-4"
                  >
                    <div className="text-red-500 mb-4">
                      <AlertCircle className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2" />
                      <p className="font-medium text-sm sm:text-base">Failed to load quiz categories</p>
                      <p className="text-xs sm:text-sm text-muted-foreground mt-1 max-w-md mx-auto">{error}</p>
                    </div>
                    <Button onClick={fetchCategories} variant="outline" className="text-sm sm:text-base">
                      <RotateCcw className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2" />
                      Try Again
                    </Button>
                  </motion.div>
                ) : (
                  <motion.div
                    key="categories"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-3 sm:gap-4 md:gap-6"
                  >
                    {categories.map((category, index) => (
                      <motion.div
                        key={category.id}
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Card
                          className="glass cursor-pointer hover:shadow-xl transition-all duration-300 border-border/50 hover:border-primary/20 relative h-full"
                          onClick={() => handleStartQuiz(category.id)}
                        >
                          {/* Difficulty Badge */}
                          <div className="absolute top-2 right-2 sm:top-3 sm:right-3 z-10">
                            <Badge
                              variant="secondary"
                              className={`text-xs ${
                                category.difficulty === 'expert' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                                category.difficulty === 'advanced' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                                category.difficulty === 'intermediate' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                                'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                              }`}
                            >
                              {category.difficulty}
                            </Badge>
                          </div>

                          <CardHeader className="pb-3 md:pb-4 pt-4 sm:pt-6">
                            <CardTitle className="flex items-start space-x-3">
                              <div className="text-xl sm:text-2xl md:text-3xl flex-shrink-0">{category.icon}</div>
                              <div className="flex-1 min-w-0 pr-8 sm:pr-10">
                                <h3 className="text-base sm:text-lg md:text-xl font-bold leading-tight mb-1">{category.name}</h3>
                                <p className="text-xs sm:text-sm text-muted-foreground">
                                  <span className="hidden sm:inline">
                                    {category.availableQuestions || category.totalQuestions} questions • {category.estimatedTime} minutes
                                  </span>
                                  <span className="sm:hidden">
                                    {category.estimatedTime}m • {category.availableQuestions || category.totalQuestions}q
                                  </span>
                                </p>
                              </div>
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="space-y-3 md:space-y-4 pt-0">
                            <div className="flex flex-wrap gap-1 sm:gap-1.5 md:gap-2">
                              {category.skills.slice(0, 3).map((skill) => (
                                <Badge key={skill} variant="secondary" className="text-xs px-2 py-0.5">
                                  {skill}
                                </Badge>
                              ))}
                              {category.skills.length > 3 && (
                                <Badge variant="secondary" className="text-xs px-2 py-0.5">
                                  +{category.skills.length - 3}
                                </Badge>
                              )}
                            </div>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-1.5 sm:space-x-2 text-xs sm:text-sm text-muted-foreground">
                                <Clock className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
                                <span>Pass: {category.passingScore}%</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Zap className="w-3 h-3 sm:w-4 sm:h-4 text-primary" />
                                <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 text-primary" />
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </motion.div>
                )}

                {/* View All Assessments Link */}
                {!isLoading && !error && categories.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="text-center mt-6 md:mt-8"
                  >
                    <Button
                      variant="outline"
                      asChild
                      className="bg-background/50 backdrop-blur-sm border-primary/20 hover:bg-primary/10 text-sm sm:text-base px-4 sm:px-6 py-2 sm:py-3"
                    >
                      <Link href="/quiz">
                        <Brain className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2" />
                        <span className="hidden sm:inline">View All Assessments</span>
                        <span className="sm:hidden">All Assessments</span>
                        <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4 ml-1.5 sm:ml-2" />
                      </Link>
                    </Button>
                  </motion.div>
                )}
              </>
            ) : showPhaseTransition ? (
              // Phase Transition
              <PhaseTransition
                firstPhaseScore={{
                  correctAnswers: firstPhaseAnswers.filter(a => a.isCorrect).length,
                  totalQuestions: firstPhaseAnswers.length,
                  percentage: Math.round((firstPhaseAnswers.filter(a => a.isCorrect).length / firstPhaseAnswers.length) * 100),
                  totalPoints: firstPhaseAnswers.reduce((sum, a) => sum + (a.points || 0), 0),
                  maxPoints: firstPhaseAnswers.length * 10 // Assuming 10 points per question
                }}
                categoryName={session?.category.name || ''}
                categoryIcon={session?.category.icon || '📚'}
                onProceed={proceedToSecondPhase}
                onSkip={skipSecondPhase}
              />
            ) : !showResults ? (
              // Assessment Questions
              <motion.div
                key="assessment"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
              >
                <Card className="glass">
                  <CardHeader className="pb-4 sm:pb-6">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                      <CardTitle className="flex items-center space-x-2 sm:space-x-3">
                        <div className="text-xl sm:text-2xl">{session.category.icon}</div>
                        <span className="text-base sm:text-lg md:text-xl font-bold">{session.category.name}</span>
                      </CardTitle>
                      <div className="flex items-center justify-between sm:justify-end sm:space-x-4">
                        <div className="text-xs sm:text-sm text-muted-foreground">
                          {currentQuestionIndex + 1} of {session.questions.length}
                        </div>
                        <div className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm">
                          <Clock className="w-3 h-3 sm:w-4 sm:h-4" />
                          <span className={timeLeft <= 60 ? "text-red-500 font-bold" : ""}>
                            {Math.floor(timeLeft / 60)}:{(timeLeft % 60).toString().padStart(2, "0")}
                          </span>
                        </div>
                      </div>
                    </div>
                    <Progress value={progress} className="h-2 mt-3 sm:mt-4" />
                  </CardHeader>
                  <CardContent className="space-y-6 sm:space-y-8">
                    <div>
                      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-4 sm:mb-6 space-y-2 sm:space-y-0">
                        <h3 className="text-base sm:text-lg md:text-xl font-semibold leading-tight pr-0 sm:pr-4">{session.questions[currentQuestionIndex].question}</h3>
                        <Badge variant="outline" className="text-xs self-start sm:self-auto flex-shrink-0">
                          {session.questions[currentQuestionIndex].points} pts
                        </Badge>
                      </div>

                      <RadioGroup value={selectedAnswer !== null ? selectedAnswer.toString() : ""} onValueChange={handleAnswerSelect}>
                        <div className="space-y-3 sm:space-y-4">
                          {session.questions[currentQuestionIndex].options.map((option, index) => (
                            <motion.div
                              key={index}
                              whileHover={{ scale: 1.01 }}
                              className="flex items-start space-x-3 p-3 sm:p-4 rounded-lg border border-border/50 hover:border-primary/20 hover:bg-primary/5 transition-all duration-200 cursor-pointer"
                            >
                              <RadioGroupItem value={index.toString()} id={`option-${index}`} className="mt-0.5 flex-shrink-0" />
                              <Label htmlFor={`option-${index}`} className="flex-1 cursor-pointer text-sm sm:text-base leading-relaxed">
                                {option}
                              </Label>
                            </motion.div>
                          ))}
                        </div>
                      </RadioGroup>
                    </div>

                    <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between space-y-3 sm:space-y-0 sm:space-x-4">
                      <Button variant="outline" onClick={resetQuiz} className="order-2 sm:order-1">
                        <RotateCcw className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2" />
                        <span className="hidden sm:inline">Start Over</span>
                        <span className="sm:hidden">Reset</span>
                      </Button>
                      <Button
                        onClick={nextQuestion}
                        disabled={selectedAnswer === null || isSubmitting}
                        className="min-w-[120px] sm:min-w-[140px] order-1 sm:order-2"
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2 animate-spin" />
                            <span className="hidden sm:inline">Submitting...</span>
                            <span className="sm:hidden">Submitting...</span>
                          </>
                        ) : (
                          <>
                            {currentQuestionIndex === session.questions.length - 1 ? "Finish" : "Next"}
                            <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4 ml-1.5 sm:ml-2" />
                          </>
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ) : result ? (
              // Results
              <motion.div
                key="results"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="text-center space-y-8"
              >
                <Card className="glass">
                  <CardContent className="p-12">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2, type: "spring" }}
                      className="mb-8"
                    >
                      <div className="w-32 h-32 mx-auto bg-primary/10 rounded-full flex items-center justify-center mb-6">
                        {(() => {
                          const { icon: Icon } = getSkillLevel(result.score)
                          return <Icon className="w-16 h-16 text-primary" />
                        })()}
                      </div>
                      <h3 className="text-4xl font-bold mb-2">{result.score}%</h3>
                      <p className={`text-xl font-semibold ${result.skillLevel.color}`}>
                        {result.skillLevel.level} Level
                      </p>
                      {result.passed && (
                        <Badge className="mt-3 bg-green-100 text-green-800 border-green-200">
                          <Trophy className="w-3 h-3 mr-1" />
                          Passed! 🎉
                        </Badge>
                      )}
                    </motion.div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                      <div className="p-4 rounded-lg bg-muted/30">
                        <CheckCircle className="w-8 h-8 text-green-500 mx-auto mb-2" />
                        <div className="text-2xl font-bold">{result.correctAnswers}</div>
                        <div className="text-sm text-muted-foreground">Correct Answers</div>
                      </div>
                      <div className="p-4 rounded-lg bg-muted/30">
                        <Clock className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                        <div className="text-2xl font-bold">
                          {Math.floor(result.timeSpent / 60)}:{(result.timeSpent % 60).toString().padStart(2, "0")}
                        </div>
                        <div className="text-sm text-muted-foreground">Time Taken</div>
                      </div>
                      <div className="p-4 rounded-lg bg-muted/30">
                        <Star className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
                        <div className="text-2xl font-bold">{result.totalPoints}</div>
                        <div className="text-sm text-muted-foreground">Points Earned</div>
                      </div>
                    </div>

                    {/* Awards Section */}
                    {result.earnedAwards.length > 0 && (
                      <div className="mb-8">
                        <h4 className="text-lg font-semibold mb-4">🏆 Awards Earned:</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {result.earnedAwards.map((award) => (
                            <div key={award.id} className="p-4 rounded-lg bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200">
                              <div className="flex items-center space-x-3">
                                <div
                                  className="w-10 h-10 rounded-full flex items-center justify-center text-lg"
                                  style={{ backgroundColor: award.badgeColor + '20', color: award.badgeColor }}
                                >
                                  {award.badgeIcon}
                                </div>
                                <div>
                                  <h5 className="font-semibold text-sm">{award.name}</h5>
                                  <p className="text-xs text-muted-foreground">{award.description}</p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="space-y-4 mb-8">
                      <h4 className="text-lg font-semibold">Recommended Next Steps:</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                        <div className="p-4 rounded-lg bg-primary/5 border border-primary/20">
                          <h5 className="font-semibold text-primary mb-2">Job Matches</h5>
                          <p className="text-sm text-muted-foreground">
                            You&apos;re qualified for {result.recommendations.jobMatches} positions.
                          </p>
                        </div>
                        <div className="p-4 rounded-lg bg-primary/5 border border-primary/20">
                          <h5 className="font-semibold text-primary mb-2">Next Steps</h5>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            {result.recommendations.nextSteps.map((step, index) => (
                              <li key={index}>• {step}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                      <Button size="lg" className="text-lg px-8">
                        View Matching Jobs
                      </Button>
                      <Button variant="outline" size="lg" onClick={resetQuiz}>
                        Take Another Assessment
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ) : null}
          </AnimatePresence>

          {/* Modals */}
          {result && (
            <>
              <EnhancedResultsModal
                result={{
                  score: result.score,
                  totalQuestions: totalQuestionsAnswered || result.totalQuestions,
                  correctAnswers: result.correctAnswers,
                  totalPoints: result.totalPoints,
                  maxPoints: result.maxPoints,
                  timeSpent: result.timeSpent,
                  category: {
                    name: result.category.name,
                    icon: result.category.icon,
                    difficulty: result.category.difficulty
                  },
                  phase: quizPhase,
                  earnedAward: result.earnedAwards && result.earnedAwards.length > 0
                }}
                isOpen={showResults}
                onClose={() => {
                  setShowResults(false)
                  resetQuiz()
                }}
                showRegistration={!showRegistrationPrompt}
              />

              <AwardModal
                isOpen={showAwardModal}
                onClose={() => setShowAwardModal(false)}
                awards={result.earnedAwards}
                onDownload={handleDownloadCertificate}
                onRegister={handleRegister}
                isRegistered={false} // TODO: Check actual user auth status
              />

              <RegistrationPrompt
                isOpen={showRegistrationPrompt}
                onClose={() => setShowRegistrationPrompt(false)}
                onRegister={handleRegister}
                score={result.score}
                categoryName={result.category.name}
                skillLevel={result.skillLevel.level}
              />
            </>
          )}
        </div>
      </div>
    </section>
  )
}

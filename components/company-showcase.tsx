// "use client"

// import { motion } from "framer-motion"
// import { <PERSON><PERSON> } from "@/components/ui/button"
// import { Card, CardContent } from "@/components/ui/card"
// import { Badge } from "@/components/ui/badge"
// import { Building2, Users, MapPin, ExternalLink } from "lucide-react"
// import Image from "next/image"

// const companies = [
//   {
//     id: 1,
//     name: "TechCorp Inc.",
//     logo: "/images/companies/techcorp-logo.jpg",
//     industry: "Technology",
//     size: "1000+ employees",
//     location: "San Francisco, CA",
//     openJobs: 15,
//     description: "Leading technology company focused on innovative solutions.",
//     featured: true,
//   },
//   {
//     id: 2,
//     name: "InnovateLab",
//     logo: "/images/companies/innovatelab-logo.jpg",
//     industry: "Startup",
//     size: "50-200 employees",
//     location: "Austin, TX",
//     openJobs: 8,
//     description: "Fast-growing startup revolutionizing the industry.",
//     featured: false,
//   },
//   {
//     id: 3,
//     name: "DesignStudio",
//     logo: "/images/companies/globaltech-logo.jpg",
//     industry: "Design",
//     size: "100-500 employees",
//     location: "New York, NY",
//     openJobs: 12,
//     description: "Creative agency with award-winning design solutions.",
//     featured: true,
//   },
//   {
//     id: 4,
//     name: "CloudTech",
//     logo: "/images/hero/office-meeting.jpg",
//     industry: "Cloud Services",
//     size: "500-1000 employees",
//     location: "Seattle, WA",
//     openJobs: 20,
//     description: "Cloud infrastructure and services provider.",
//     featured: false,
//   },
// ]

// export function CompanyShowcase() {
//   const containerVariants = {
//     hidden: { opacity: 0 },
//     visible: {
//       opacity: 1,
//       transition: {
//         staggerChildren: 0.15,
//       },
//     },
//   }

//   const itemVariants = {
//     hidden: { opacity: 0, y: 30 },
//     visible: {
//       opacity: 1,
//       y: 0,
//       transition: {
//         duration: 0.6,
//         ease: "easeOut",
//       },
//     },
//   }

//   return (
//     <section className="py-20">
//       <div className="container mx-auto px-4">
//         <motion.div
//           initial={{ opacity: 0, y: 30 }}
//           whileInView={{ opacity: 1, y: 0 }}
//           viewport={{ once: true }}
//           transition={{ duration: 0.6 }}
//           className="text-center mb-16"
//         >
//           <h2 className="text-4xl md:text-5xl font-bold mb-6">
//             Top <span className="text-primary">Companies</span> Hiring
//           </h2>
//           <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
//             Join industry leaders and innovative startups that are shaping the future of work.
//           </p>
//         </motion.div>

//         <motion.div
//           variants={containerVariants}
//           initial="hidden"
//           whileInView="visible"
//           viewport={{ once: true }}
//           className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12"
//         >
//           {companies.map((company, index) => (
//             <motion.div key={company.id} variants={itemVariants}>
//               <Card className="group hover:shadow-xl transition-all duration-300 border-border/50 hover:border-primary/20 bg-background/80 backdrop-blur-sm relative overflow-hidden h-full">
//                 {company.featured && (
//                   <div className="absolute top-4 right-4 z-10">
//                     <Badge className="bg-primary/10 text-primary border-primary/20">Featured</Badge>
//                   </div>
//                 )}

//                 <CardContent className="p-8">
//                   <div className="flex items-start space-x-6 mb-6">
//                     <motion.div
//                       whileHover={{ scale: 1.1 }}
//                       className="w-20 h-20 rounded-2xl bg-muted flex items-center justify-center overflow-hidden flex-shrink-0"
//                     >
//                       <Image
//                         src={company.logo || "/placeholder.svg"}
//                         alt={`${company.name} logo`}
//                         width={80}
//                         height={80}
//                         className="w-full h-full object-cover"
//                       />
//                     </motion.div>
//                     <div className="flex-1">
//                       <h3 className="text-2xl font-bold mb-2 group-hover:text-primary transition-colors duration-200">
//                         {company.name}
//                       </h3>
//                       <p className="text-muted-foreground mb-4">{company.description}</p>
//                       <div className="space-y-2">
//                         <div className="flex items-center space-x-2 text-sm text-muted-foreground">
//                           <Building2 className="w-4 h-4" />
//                           <span>{company.industry}</span>
//                         </div>
//                         <div className="flex items-center space-x-2 text-sm text-muted-foreground">
//                           <Users className="w-4 h-4" />
//                           <span>{company.size}</span>
//                         </div>
//                         <div className="flex items-center space-x-2 text-sm text-muted-foreground">
//                           <MapPin className="w-4 h-4" />
//                           <span>{company.location}</span>
//                         </div>
//                       </div>
//                     </div>
//                   </div>

//                   <div className="flex items-center justify-between pt-6 border-t border-border/50">
//                     <div className="text-sm">
//                       <span className="font-semibold text-primary">{company.openJobs}</span>
//                       <span className="text-muted-foreground ml-1">open positions</span>
//                     </div>
//                     <Button
//                       variant="outline"
//                       size="sm"
//                       className="group-hover:scale-105 transition-transform duration-200 bg-transparent"
//                     >
//                       View Jobs
//                       <ExternalLink className="w-4 h-4 ml-2" />
//                     </Button>
//                   </div>
//                 </CardContent>
//               </Card>
//             </motion.div>
//           ))}
//         </motion.div>

//         <motion.div
//           initial={{ opacity: 0, y: 20 }}
//           whileInView={{ opacity: 1, y: 0 }}
//           viewport={{ once: true }}
//           transition={{ duration: 0.6, delay: 0.3 }}
//           className="text-center"
//         >
//           <Button size="lg" variant="outline" className="text-lg px-8 bg-transparent">
//             Explore All Companies
//             <ExternalLink className="w-5 h-5 ml-2" />
//           </Button>
//         </motion.div>
//       </div>
//     </section>
//   )
// }


"use client"

import { useEffect } from "react"
import { motion, Variants } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Building2, Users, MapPin, ExternalLink, Loader2, Shield } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useCompanyShowcaseData, useCompanyShowcaseActions } from "@/stores/company-showcase.store"
import { companiesShowcaseService } from "@/lib/services/companies-showcase.service"

// Remove static data - now using dynamic data from store

const containerVariants: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.15 },
  },
}

const cardVariants: Variants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut" as const,
    },
  },
}

export function CompanyShowcase() {
  const { companies, isLoading, error } = useCompanyShowcaseData()
  const { fetchShowcaseCompanies } = useCompanyShowcaseActions()

  // Load companies on component mount
  useEffect(() => {
    fetchShowcaseCompanies({ limit: 6 })
  }, [fetchShowcaseCompanies])

  // Show loading state
  if (isLoading && companies.length === 0) {
    return (
      <section className="py-12 md:py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8 md:mb-16">
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6">
              Top <span className="text-primary">Companies</span> Hiring
            </h2>
            <p className="text-base sm:text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto px-4 md:px-0">
              <span className="hidden md:inline">
                Join industry leaders and innovative startups that are shaping the future of work.
              </span>
              <span className="md:hidden">
                Top companies hiring now
              </span>
            </p>
          </div>

          <div className="flex justify-center items-center py-20">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
              <p className="text-muted-foreground">Loading companies...</p>
            </div>
          </div>
        </div>
      </section>
    )
  }

  // Show error state with fallback
  if (error && companies.length === 0) {
    return (
      <section className="py-12 md:py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8 md:mb-16">
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6">
              Top <span className="text-primary">Companies</span> Hiring
            </h2>
            <p className="text-base sm:text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto px-4 md:px-0">
              <span className="hidden md:inline">
                Join industry leaders and innovative startups that are shaping the future of work.
              </span>
              <span className="md:hidden">
                Top companies hiring now
              </span>
            </p>
          </div>

          <div className="text-center py-20">
            <p className="text-muted-foreground mb-4">Unable to load companies at the moment.</p>
            <Button
              onClick={() => fetchShowcaseCompanies({ limit: 6 })}
              variant="outline"
            >
              Try Again
            </Button>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-12 md:py-20">
      <div className="container mx-auto px-4">

        {/* Section Heading */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8 md:mb-16"
        >
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6">
            Top <span className="text-primary">Companies</span> Hiring
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto px-4 md:px-0">
            <span className="hidden md:inline">
              Join industry leaders and innovative startups that are shaping the future of work.
            </span>
            <span className="md:hidden">
              Top companies hiring now
            </span>
          </p>
        </motion.div>

        {/* Companies Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-8 mb-8 md:mb-12"
        >
          {companies.map((company) => (
            <motion.div key={company.id} variants={cardVariants}>
              <Card className="group hover:shadow-xl transition-all duration-300 border-border/50 hover:border-primary/20 bg-background/80 backdrop-blur-sm relative overflow-hidden h-full">

                {/* Badges */}
                <div className="absolute top-3 right-3 md:top-4 md:right-4 z-10 flex flex-col gap-2">
                  {company.featured && (
                    <Badge className="bg-primary/10 text-primary border-primary/20 text-xs md:text-sm">Featured</Badge>
                  )}
                  {company.verified && (
                    <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200 text-xs md:text-sm">
                      <Shield className="w-3 h-3 mr-1" />
                      Verified
                    </Badge>
                  )}
                </div>

                <CardContent className="p-4 md:p-8">
                  <div className="flex items-start space-x-4 md:space-x-6 mb-4 md:mb-6">
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      className="w-16 h-16 md:w-20 md:h-20 rounded-xl md:rounded-2xl bg-muted flex items-center justify-center overflow-hidden flex-shrink-0"
                    >
                      <Image
                        src={companiesShowcaseService.getCompanyLogoUrl(company)}
                        alt={`${company.name} logo`}
                        width={80}
                        height={80}
                        className="w-full h-full object-cover"
                      />
                    </motion.div>

                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg md:text-2xl font-bold mb-2 group-hover:text-primary transition-colors duration-200 truncate">
                        {company.name}
                      </h3>
                      <p className="text-sm md:text-base text-muted-foreground mb-3 md:mb-4 line-clamp-2">
                        {company.description}
                      </p>
                      <div className="space-y-1.5 md:space-y-2">
                        <div className="flex items-center space-x-2 text-xs md:text-sm text-muted-foreground">
                          <Building2 className="w-3 h-3 md:w-4 md:h-4" />
                          <span>{company.industry}</span>
                        </div>
                        {/* Hide size on mobile */}
                        <div className="hidden sm:flex items-center space-x-2 text-xs md:text-sm text-muted-foreground">
                          <Users className="w-3 h-3 md:w-4 md:h-4" />
                          <span>{company.size}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-xs md:text-sm text-muted-foreground">
                          <MapPin className="w-3 h-3 md:w-4 md:h-4" />
                          <span className="truncate">{company.location}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-4 md:pt-6 border-t border-border/50">
                    <div className="text-xs md:text-sm">
                      <span className="font-semibold text-primary">{company.openJobs}</span>
                      <span className="text-muted-foreground ml-1">
                        <span className="hidden sm:inline">open positions</span>
                        <span className="sm:hidden">jobs</span>
                      </span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="group-hover:scale-105 transition-transform duration-200 bg-transparent text-xs md:text-sm"
                      asChild
                    >
                      <Link href={`/companies/${company.slug}`}>
                        <span className="hidden sm:inline">View Jobs</span>
                        <span className="sm:hidden">Jobs</span>
                        <ExternalLink className="w-3 h-3 md:w-4 md:h-4 ml-1 md:ml-2" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Call to Action Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-center"
        >
          <Button size="lg" variant="outline" className="text-lg px-8 bg-transparent" asChild>
            <Link href="/companies">
              Explore All Companies
              <ExternalLink className="w-5 h-5 ml-2" />
            </Link>
          </Button>
        </motion.div>

      </div>
    </section>
  )
}

"use client"

import { useEffect, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { 
  Clock, 
  ArrowRight, 
  RotateCcw, 
  Loader2,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Trophy,
  Target
} from "lucide-react"
import { useQuizSession, useQuizResults, useQuizProgression } from "@/stores/quiz.store"
import { PhaseTransition } from "./phase-transition"
import { EnhancedResultsModal } from "./enhanced-results-modal"

export function QuizInterface() {
  const {
    session,
    currentQuestionIndex,
    selectedAnswer,
    timeLeft,
    isActive,
    isSubmitting,
    progress,
    selectAnswer,
    nextQuestion,
    resetQuiz,
    updateTimer
  } = useQuizSession()

  const {
    result,
    showResults,
    setShowResults
  } = useQuizResults()

  const {
    quizPhase,
    firstPhaseAnswers,
    totalQuestionsAnswered,
    showPhaseTransition,
    proceedToSecondPhase,
    skipSecondPhase
  } = useQuizProgression()

  const timerRef = useRef<NodeJS.Timeout | null>(null)

  // Timer effect
  useEffect(() => {
    if (isActive && timeLeft > 0) {
      timerRef.current = setInterval(() => {
        updateTimer(timeLeft - 1)
      }, 1000)
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
    }
  }, [isActive, timeLeft, updateTimer])

  const handleAnswerSelect = (answerIndex: string) => {
    selectAnswer(parseInt(answerIndex))
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`
  }

  const getTimeColor = () => {
    if (timeLeft <= 60) return "text-red-500"
    if (timeLeft <= 300) return "text-orange-500"
    return "text-foreground"
  }

  if (!session) {
    return (
      <div className="text-center py-20">
        <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-orange-500" />
        <h2 className="text-2xl font-bold mb-2">No Active Quiz Session</h2>
        <p className="text-muted-foreground">Please start a quiz to continue.</p>
      </div>
    )
  }

  // Show phase transition
  if (showPhaseTransition) {
    return (
      <PhaseTransition
        firstPhaseScore={{
          correctAnswers: firstPhaseAnswers.filter(a => a.isCorrect).length,
          totalQuestions: firstPhaseAnswers.length,
          percentage: Math.round((firstPhaseAnswers.filter(a => a.isCorrect).length / firstPhaseAnswers.length) * 100),
          totalPoints: firstPhaseAnswers.reduce((sum, a) => sum + (a.points || 0), 0),
          maxPoints: firstPhaseAnswers.length * 10
        }}
        categoryName={session.category.name}
        categoryIcon={session.category.icon}
        onProceed={proceedToSecondPhase}
        onSkip={skipSecondPhase}
      />
    )
  }

  const currentQuestion = session.questions[currentQuestionIndex]

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        {/* Quiz Header */}
        <Card className="glass border-primary/20">
          <CardHeader className="pb-4 sm:pb-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
              <CardTitle className="flex items-center space-x-2 sm:space-x-3">
                <div className="text-xl sm:text-2xl">{session.category.icon}</div>
                <div>
                  <h2 className="text-base sm:text-lg md:text-xl font-bold">{session.category.name}</h2>
                  <p className="text-xs sm:text-sm text-muted-foreground">
                    {quizPhase === 'first' ? 'Phase 1' : 'Phase 2'} Assessment
                  </p>
                </div>
              </CardTitle>
              
              <div className="flex items-center justify-between sm:justify-end sm:space-x-4">
                <div className="text-xs sm:text-sm text-muted-foreground">
                  Question {currentQuestionIndex + 1} of {session.questions.length}
                </div>
                <div className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm">
                  <Clock className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className={`font-mono ${getTimeColor()}`}>
                    {formatTime(timeLeft)}
                  </span>
                </div>
              </div>
            </div>
            
            <div className="mt-4">
              <div className="flex items-center justify-between text-xs sm:text-sm text-muted-foreground mb-2">
                <span>Progress</span>
                <span>{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          </CardHeader>
        </Card>

        {/* Question Card */}
        <Card className="glass">
          <CardContent className="space-y-6 sm:space-y-8 pt-6">
            {/* Question Header */}
            <div>
              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-4 sm:mb-6 space-y-2 sm:space-y-0">
                <h3 className="text-base sm:text-lg md:text-xl font-semibold leading-tight pr-0 sm:pr-4">
                  {currentQuestion.question}
                </h3>
                <div className="flex items-center space-x-2 self-start sm:self-auto flex-shrink-0">
                  <Badge variant="outline" className="text-xs">
                    {currentQuestion.points} pts
                  </Badge>
                  <Badge 
                    variant="secondary" 
                    className={`text-xs ${
                      currentQuestion.difficulty === 'expert' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                      currentQuestion.difficulty === 'advanced' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                      currentQuestion.difficulty === 'intermediate' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                      'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    }`}
                  >
                    {currentQuestion.difficulty}
                  </Badge>
                </div>
              </div>

              {/* Question Tags */}
              {currentQuestion.tags && currentQuestion.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 sm:gap-2 mb-4">
                  {currentQuestion.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* Answer Options */}
            <div>
              <RadioGroup 
                value={selectedAnswer !== null ? selectedAnswer.toString() : ""} 
                onValueChange={handleAnswerSelect}
              >
                <div className="space-y-3 sm:space-y-4">
                  {currentQuestion.options.map((option, index) => (
                    <motion.div
                      key={index}
                      whileHover={{ scale: 1.01 }}
                      className="flex items-start space-x-3 p-3 sm:p-4 rounded-lg border border-border/50 hover:border-primary/20 hover:bg-primary/5 transition-all duration-200 cursor-pointer"
                    >
                      <RadioGroupItem 
                        value={index.toString()} 
                        id={`option-${index}`} 
                        className="mt-0.5 flex-shrink-0" 
                      />
                      <Label 
                        htmlFor={`option-${index}`} 
                        className="flex-1 cursor-pointer text-sm sm:text-base leading-relaxed"
                      >
                        <span className="font-medium mr-2">
                          {String.fromCharCode(65 + index)}.
                        </span>
                        {option}
                      </Label>
                    </motion.div>
                  ))}
                </div>
              </RadioGroup>
            </div>

            {/* Navigation Buttons */}
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between space-y-3 sm:space-y-0 sm:space-x-4 pt-4 border-t border-border/50">
              <Button 
                variant="outline" 
                onClick={resetQuiz}
                className="order-2 sm:order-1"
              >
                <RotateCcw className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2" />
                <span className="hidden sm:inline">Start Over</span>
                <span className="sm:hidden">Reset</span>
              </Button>
              
              <Button
                onClick={nextQuestion}
                disabled={selectedAnswer === null || isSubmitting}
                className="min-w-[120px] sm:min-w-[140px] order-1 sm:order-2"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2 animate-spin" />
                    <span className="hidden sm:inline">Submitting...</span>
                    <span className="sm:hidden">Submitting...</span>
                  </>
                ) : (
                  <>
                    {currentQuestionIndex === session.questions.length - 1 ? (
                      quizPhase === 'first' ? 'Complete Phase 1' : 'Finish Quiz'
                    ) : 'Next Question'}
                    <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4 ml-1.5 sm:ml-2" />
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Time Warning */}
        {timeLeft <= 60 && timeLeft > 0 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="fixed bottom-4 right-4 z-50"
          >
            <Card className="bg-red-500/10 border-red-500/20">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2 text-red-600">
                  <AlertTriangle className="w-4 h-4" />
                  <span className="text-sm font-medium">
                    Time running out: {formatTime(timeLeft)}
                  </span>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </motion.div>

      {/* Results Modal */}
      {result && (
        <EnhancedResultsModal
          result={{
            score: result.score,
            totalQuestions: totalQuestionsAnswered || result.totalQuestions,
            correctAnswers: result.correctAnswers,
            totalPoints: result.totalPoints,
            maxPoints: result.maxPoints,
            timeSpent: result.timeSpent,
            category: {
              name: result.category.name,
              icon: result.category.icon,
              difficulty: result.category.difficulty
            },
            phase: quizPhase,
            earnedAward: result.earnedAwards && result.earnedAwards.length > 0
          }}
          isOpen={showResults}
          onClose={() => {
            setShowResults(false)
            resetQuiz()
          }}
          showRegistration={true}
        />
      )}
    </>
  )
}

"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { 
  Trophy, 
  Star, 
  CheckCircle, 
  XCircle,
  Clock,
  Target,
  Award,
  User,
  Mail,
  Lock,
  Eye,
  EyeOff,
  ArrowRight,
  Download,
  Share2,
  Loader2
} from "lucide-react"
import { toast } from "sonner"

interface QuizResult {
  score: number
  totalQuestions: number
  correctAnswers: number
  totalPoints: number
  maxPoints: number
  timeSpent: number
  category: {
    name: string
    icon: string
    difficulty: string
  }
  phase: 'first' | 'second' | 'completed'
  earnedAward?: boolean
}

interface EnhancedResultsModalProps {
  result: QuizResult
  isOpen: boolean
  onClose: () => void
  showRegistration?: boolean
}

export function EnhancedResultsModal({ 
  result, 
  isOpen, 
  onClose, 
  showRegistration = true 
}: EnhancedResultsModalProps) {
  const [showLoginForm, setShowLoginForm] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  })

  const isExcellent = result.score >= 90
  const isGood = result.score >= 70
  const isPassing = result.score >= 60

  const getPerformanceMessage = () => {
    if (isExcellent) return "Outstanding performance! 🌟"
    if (isGood) return "Great job! Well done! 👏"
    if (isPassing) return "Good effort! Keep practicing! 💪"
    return "Keep learning and try again! 📚"
  }

  const getPerformanceColor = () => {
    if (isExcellent) return "text-yellow-600"
    if (isGood) return "text-green-600"
    if (isPassing) return "text-blue-600"
    return "text-orange-600"
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleRegistration = async () => {
    if (!formData.name || !formData.email || !formData.password) {
      toast.error("Please fill in all required fields")
      return
    }

    if (formData.password !== formData.confirmPassword) {
      toast.error("Passwords don't match")
      return
    }

    if (formData.password.length < 6) {
      toast.error("Password must be at least 6 characters")
      return
    }

    setIsSubmitting(true)
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: formData.name,
          email: formData.email,
          password: formData.password,
          quizResult: result
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success("Registration successful! Logging you in...")
        
        // Auto-login after registration
        await handleLogin(formData.email, formData.password)
      } else {
        toast.error(data.error || "Registration failed")
      }
    } catch (error) {
      toast.error("Registration failed. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleLogin = async (email?: string, password?: string) => {
    const loginEmail = email || formData.email
    const loginPassword = password || formData.password

    if (!loginEmail || !loginPassword) {
      toast.error("Please enter email and password")
      return
    }

    setIsSubmitting(true)
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: loginEmail,
          password: loginPassword
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success("Login successful! Redirecting to dashboard...")
        
        // Redirect to client dashboard
        setTimeout(() => {
          window.location.href = '/client-dashboard'
        }, 1500)
      } else {
        toast.error(data.error || "Login failed")
      }
    } catch (error) {
      toast.error("Login failed. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleShareResults = () => {
    const shareText = `I just scored ${result.score}% on the ${result.category.name} assessment! 🎯`
    if (navigator.share) {
      navigator.share({
        title: 'Quiz Results',
        text: shareText,
        url: window.location.href
      })
    } else {
      navigator.clipboard.writeText(shareText)
      toast.success("Results copied to clipboard!")
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        className="w-full max-w-2xl max-h-[90vh] overflow-y-auto"
      >
        <Card className="glass border-primary/20">
          <CardHeader className="text-center pb-4">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <div className="text-4xl">{result.category.icon}</div>
              <div>
                <CardTitle className="text-2xl font-bold">Assessment Complete!</CardTitle>
                <p className="text-muted-foreground">{result.category.name}</p>
              </div>
            </div>

            {/* Score Display */}
            <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-6 border border-primary/20">
              <div className="text-center mb-4">
                <div className="text-5xl font-bold text-primary mb-2">
                  {result.score}%
                </div>
                <p className={`text-lg font-semibold ${getPerformanceColor()}`}>
                  {getPerformanceMessage()}
                </p>
              </div>

              <Progress value={result.score} className="h-3 mb-4" />

              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-xl font-bold text-foreground">
                    {result.correctAnswers}/{result.totalQuestions}
                  </div>
                  <p className="text-xs text-muted-foreground">Correct</p>
                </div>
                <div>
                  <div className="text-xl font-bold text-foreground">
                    {result.totalPoints}
                  </div>
                  <p className="text-xs text-muted-foreground">Points</p>
                </div>
                <div>
                  <div className="text-xl font-bold text-foreground">
                    {Math.round(result.timeSpent / 60)}m
                  </div>
                  <p className="text-xs text-muted-foreground">Time</p>
                </div>
              </div>

              {result.earnedAward && (
                <div className="mt-4 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                  <div className="flex items-center justify-center space-x-2">
                    <Award className="w-5 h-5 text-yellow-500" />
                    <span className="font-semibold text-yellow-600">Certificate Earned!</span>
                  </div>
                  <p className="text-sm text-muted-foreground text-center mt-1">
                    You've earned a digital certificate for this assessment
                  </p>
                </div>
              )}
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Action Buttons */}
            <div className="flex flex-wrap gap-2 justify-center">
              <Button variant="outline" size="sm" onClick={handleShareResults}>
                <Share2 className="w-4 h-4 mr-2" />
                Share Results
              </Button>
              {result.earnedAward && (
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Download Certificate
                </Button>
              )}
            </div>

            {showRegistration && (
              <>
                <Separator />
                
                {/* Registration/Login Section */}
                <div className="space-y-4">
                  <div className="text-center">
                    <h3 className="text-lg font-semibold mb-2">Save Your Results & Get More!</h3>
                    <p className="text-sm text-muted-foreground">
                      Create an account to track your progress, earn certificates, and access your dashboard
                    </p>
                  </div>

                  <AnimatePresence mode="wait">
                    {!showLoginForm ? (
                      <motion.div
                        key="register"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        className="space-y-4"
                      >
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="name">Full Name</Label>
                            <div className="relative">
                              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                              <Input
                                id="name"
                                placeholder="Enter your name"
                                value={formData.name}
                                onChange={(e) => handleInputChange('name', e.target.value)}
                                className="pl-10"
                              />
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="email">Email Address</Label>
                            <div className="relative">
                              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                              <Input
                                id="email"
                                type="email"
                                placeholder="Enter your email"
                                value={formData.email}
                                onChange={(e) => handleInputChange('email', e.target.value)}
                                className="pl-10"
                              />
                            </div>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="password">Password</Label>
                            <div className="relative">
                              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                              <Input
                                id="password"
                                type={showPassword ? "text" : "password"}
                                placeholder="Create password"
                                value={formData.password}
                                onChange={(e) => handleInputChange('password', e.target.value)}
                                className="pl-10 pr-10"
                              />
                              <button
                                type="button"
                                onClick={() => setShowPassword(!showPassword)}
                                className="absolute right-3 top-1/2 transform -translate-y-1/2"
                              >
                                {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                              </button>
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="confirmPassword">Confirm Password</Label>
                            <div className="relative">
                              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                              <Input
                                id="confirmPassword"
                                type="password"
                                placeholder="Confirm password"
                                value={formData.confirmPassword}
                                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                                className="pl-10"
                              />
                            </div>
                          </div>
                        </div>

                        <Button 
                          onClick={handleRegistration}
                          disabled={isSubmitting}
                          className="w-full"
                        >
                          {isSubmitting ? (
                            <>
                              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                              Creating Account...
                            </>
                          ) : (
                            <>
                              <User className="w-4 h-4 mr-2" />
                              Create Account & Access Dashboard
                              <ArrowRight className="w-4 h-4 ml-2" />
                            </>
                          )}
                        </Button>

                        <div className="text-center">
                          <button
                            onClick={() => setShowLoginForm(true)}
                            className="text-sm text-primary hover:underline"
                          >
                            Already have an account? Sign in
                          </button>
                        </div>
                      </motion.div>
                    ) : (
                      <motion.div
                        key="login"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        className="space-y-4"
                      >
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="loginEmail">Email Address</Label>
                            <div className="relative">
                              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                              <Input
                                id="loginEmail"
                                type="email"
                                placeholder="Enter your email"
                                value={formData.email}
                                onChange={(e) => handleInputChange('email', e.target.value)}
                                className="pl-10"
                              />
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="loginPassword">Password</Label>
                            <div className="relative">
                              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                              <Input
                                id="loginPassword"
                                type={showPassword ? "text" : "password"}
                                placeholder="Enter your password"
                                value={formData.password}
                                onChange={(e) => handleInputChange('password', e.target.value)}
                                className="pl-10 pr-10"
                              />
                              <button
                                type="button"
                                onClick={() => setShowPassword(!showPassword)}
                                className="absolute right-3 top-1/2 transform -translate-y-1/2"
                              >
                                {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                              </button>
                            </div>
                          </div>
                        </div>

                        <Button 
                          onClick={() => handleLogin()}
                          disabled={isSubmitting}
                          className="w-full"
                        >
                          {isSubmitting ? (
                            <>
                              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                              Signing In...
                            </>
                          ) : (
                            <>
                              <Lock className="w-4 h-4 mr-2" />
                              Sign In & Access Dashboard
                              <ArrowRight className="w-4 h-4 ml-2" />
                            </>
                          )}
                        </Button>

                        <div className="text-center">
                          <button
                            onClick={() => setShowLoginForm(false)}
                            className="text-sm text-primary hover:underline"
                          >
                            Don't have an account? Create one
                          </button>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </>
            )}

            <Separator />

            {/* Close Button */}
            <div className="text-center">
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}

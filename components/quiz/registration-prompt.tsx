"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  UserPlus, 
  Trophy, 
  BookOpen, 
  Download, 
  Star, 
  Briefcase,
  Gift,
  Sparkles
} from "lucide-react"

interface RegistrationPromptProps {
  isOpen: boolean
  onClose: () => void
  onRegister: () => void
  score: number
  categoryName: string
  skillLevel: string
}

export function RegistrationPrompt({ 
  isOpen, 
  onClose, 
  onRegister, 
  score, 
  categoryName,
  skillLevel 
}: RegistrationPromptProps) {
  const benefits = [
    {
      icon: Trophy,
      title: "Digital Certificates",
      description: "Download professional certificates for your achievements",
      color: "text-yellow-500"
    },
    {
      icon: BookOpen,
      title: "Free AI Training",
      description: "Access exclusive AI-powered courses and tutorials",
      color: "text-blue-500"
    },
    {
      icon: Briefcase,
      title: "Job Matching",
      description: "Get matched with relevant job opportunities",
      color: "text-green-500"
    },
    {
      icon: Star,
      title: "Profile Showcase",
      description: "Display your skills and awards on your professional profile",
      color: "text-purple-500"
    },
    {
      icon: Gift,
      title: "Exclusive Rewards",
      description: "Unlock special perks and early access to new features",
      color: "text-pink-500"
    }
  ]

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2 text-2xl">
            <Sparkles className="w-6 h-6 text-primary" />
            <span>Excellent Performance! 🎉</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Score Highlight */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center p-6 rounded-lg bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20"
          >
            <div className="text-4xl font-bold text-primary mb-2">{score}%</div>
            <div className="text-lg font-semibold mb-1">{skillLevel} Level</div>
            <div className="text-muted-foreground">in {categoryName}</div>
            <Badge className="mt-3 bg-green-100 text-green-800 border-green-200">
              <Trophy className="w-3 h-3 mr-1" />
              Passed with Excellence!
            </Badge>
          </motion.div>

          {/* Registration Benefits */}
          <div className="space-y-4">
            <h3 className="text-xl font-semibold text-center">
              🚀 Unlock Your Full Potential
            </h3>
            <p className="text-center text-muted-foreground">
              Join thousands of professionals who have advanced their careers with our platform
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={benefit.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="h-full hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-start space-x-3">
                        <div className={`flex-shrink-0 ${benefit.color}`}>
                          <benefit.icon className="w-5 h-5" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="font-semibold text-sm mb-1">{benefit.title}</h4>
                          <p className="text-xs text-muted-foreground">{benefit.description}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Special Offer */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="p-4 rounded-lg bg-gradient-to-r from-green-50 to-blue-50 border border-green-200"
          >
            <div className="text-center">
              <div className="text-lg font-semibold text-green-800 mb-2">
                🎁 Limited Time Offer
              </div>
              <p className="text-sm text-green-700 mb-3">
                Register now and get <strong>3 months free access</strong> to our premium AI training courses!
              </p>
              <Badge className="bg-green-100 text-green-800 border-green-200">
                <Gift className="w-3 h-3 mr-1" />
                Worth $297 - Free for high performers!
              </Badge>
            </div>
          </motion.div>

          {/* Action Buttons */}
          <div className="space-y-3 pt-4 border-t">
            <Button onClick={onRegister} size="lg" className="w-full text-lg py-6">
              <UserPlus className="w-5 h-5 mr-2" />
              Create Free Account & Claim Rewards
            </Button>
            
            <div className="text-center">
              <Button variant="ghost" onClick={onClose} className="text-sm">
                Maybe later, continue as guest
              </Button>
            </div>
            
            <div className="text-center text-xs text-muted-foreground">
              ✅ Free forever • ✅ No credit card required • ✅ Instant access
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

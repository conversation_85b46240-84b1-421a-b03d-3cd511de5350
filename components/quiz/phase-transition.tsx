"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Trophy, 
  Target, 
  ArrowRight, 
  CheckCircle, 
  XCircle,
  Clock,
  Zap,
  Star
} from "lucide-react"

interface PhaseTransitionProps {
  firstPhaseScore: {
    correctAnswers: number
    totalQuestions: number
    percentage: number
    totalPoints: number
    maxPoints: number
  }
  categoryName: string
  categoryIcon: string
  onProceed: () => void
  onSkip: () => void
}

export function PhaseTransition({ 
  firstPhaseScore, 
  categoryName, 
  categoryIcon, 
  onProceed, 
  onSkip 
}: PhaseTransitionProps) {
  const isGoodScore = firstPhaseScore.percentage >= 70
  const wrongAnswers = firstPhaseScore.totalQuestions - firstPhaseScore.correctAnswers

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="max-w-2xl mx-auto"
    >
      <Card className="glass border-primary/20">
        <CardHeader className="text-center pb-4">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="text-4xl">{categoryIcon}</div>
            <div>
              <CardTitle className="text-2xl font-bold">Phase 1 Complete!</CardTitle>
              <p className="text-muted-foreground">{categoryName} Assessment</p>
            </div>
          </div>
          
          {/* Score Display */}
          <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-6 border border-primary/20">
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-foreground">
                  {firstPhaseScore.correctAnswers}/{firstPhaseScore.totalQuestions}
                </div>
                <p className="text-sm text-muted-foreground">Correct Answers</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">
                  {firstPhaseScore.percentage}%
                </div>
                <p className="text-sm text-muted-foreground">Score</p>
              </div>
            </div>
            
            <Progress 
              value={firstPhaseScore.percentage} 
              className="h-3 mb-2" 
            />
            
            <div className="flex items-center justify-center space-x-4 text-sm">
              <div className="flex items-center space-x-1">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>{firstPhaseScore.correctAnswers} correct</span>
              </div>
              {wrongAnswers > 0 && (
                <div className="flex items-center space-x-1">
                  <XCircle className="w-4 h-4 text-red-500" />
                  <span>{wrongAnswers} incorrect</span>
                </div>
              )}
              <div className="flex items-center space-x-1">
                <Star className="w-4 h-4 text-yellow-500" />
                <span>{firstPhaseScore.totalPoints} points</span>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Performance Message */}
          <div className="text-center">
            {isGoodScore ? (
              <div className="space-y-2">
                <div className="flex items-center justify-center space-x-2">
                  <Trophy className="w-5 h-5 text-yellow-500" />
                  <span className="font-semibold text-green-600">Great job!</span>
                </div>
                <p className="text-muted-foreground">
                  You're performing well! Want to test your knowledge further?
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                <div className="flex items-center justify-center space-x-2">
                  <Target className="w-5 h-5 text-blue-500" />
                  <span className="font-semibold text-blue-600">Room for improvement!</span>
                </div>
                <p className="text-muted-foreground">
                  Let's review those tricky questions and test you on more topics.
                </p>
              </div>
            )}
          </div>

          {/* Second Phase Info */}
          <div className="bg-muted/30 rounded-lg p-4 border border-muted-foreground/20">
            <h3 className="font-semibold mb-2 flex items-center space-x-2">
              <Zap className="w-4 h-4 text-primary" />
              <span>Phase 2: Extended Assessment</span>
            </h3>
            <ul className="text-sm text-muted-foreground space-y-1">
              {wrongAnswers > 0 && (
                <li>• Review {wrongAnswers} question{wrongAnswers > 1 ? 's' : ''} you got wrong</li>
              )}
              <li>• Answer {Math.max(0, 10 - wrongAnswers)} additional questions</li>
              <li>• Get a more comprehensive skill evaluation</li>
              <li>• Improve your overall score and understanding</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button 
              onClick={onProceed}
              className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground"
            >
              <ArrowRight className="w-4 h-4 mr-2" />
              Continue to Phase 2
              <Clock className="w-4 h-4 ml-2" />
            </Button>
            
            <Button 
              variant="outline" 
              onClick={onSkip}
              className="flex-1"
            >
              <Trophy className="w-4 h-4 mr-2" />
              Finish & See Results
            </Button>
          </div>

          {/* Encouragement */}
          <div className="text-center text-xs text-muted-foreground">
            <p>
              {isGoodScore 
                ? "Challenge yourself with more questions to showcase your expertise!" 
                : "Phase 2 will help you learn and improve your score!"
              }
            </p>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

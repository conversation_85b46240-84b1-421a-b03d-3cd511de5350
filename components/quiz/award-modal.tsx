"use client"

import { motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Trophy, Download, Star, Gift, Sparkles } from "lucide-react"
import { type Award } from "@/lib/services/quiz.service"

interface AwardModalProps {
  isOpen: boolean
  onClose: () => void
  awards: Award[]
  onDownload?: (awardId: string) => void
  onRegister?: () => void
  isRegistered?: boolean
}

export function AwardModal({ 
  isOpen, 
  onClose, 
  awards, 
  onDownload, 
  onRegister,
  isRegistered = false 
}: AwardModalProps) {
  const getRarityColor = (rarity: string) => {
    const colors = {
      common: 'bg-gray-100 text-gray-800 border-gray-200',
      uncommon: 'bg-green-100 text-green-800 border-green-200',
      rare: 'bg-blue-100 text-blue-800 border-blue-200',
      epic: 'bg-purple-100 text-purple-800 border-purple-200',
      legendary: 'bg-yellow-100 text-yellow-800 border-yellow-200'
    }
    return colors[rarity as keyof typeof colors] || colors.common
  }

  const getRarityIcon = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return <Sparkles className="w-4 h-4" />
      case 'epic': return <Star className="w-4 h-4" />
      case 'rare': return <Gift className="w-4 h-4" />
      default: return <Trophy className="w-4 h-4" />
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2 text-2xl">
            <Trophy className="w-6 h-6 text-yellow-500" />
            <span>Congratulations! 🎉</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div className="text-center">
            <p className="text-lg text-muted-foreground">
              You've earned {awards.length} {awards.length === 1 ? 'award' : 'awards'} for your excellent performance!
            </p>
          </div>

          <div className="space-y-4">
            {awards.map((award, index) => (
              <motion.div
                key={award.id}
                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ delay: index * 0.2 }}
                className="p-6 rounded-lg border bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20"
              >
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div 
                      className="w-16 h-16 rounded-full flex items-center justify-center text-2xl"
                      style={{ backgroundColor: award.badgeColor + '20', color: award.badgeColor }}
                    >
                      {award.badgeIcon}
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="text-xl font-bold">{award.name}</h3>
                      <Badge className={getRarityColor(award.rarity)}>
                        {getRarityIcon(award.rarity)}
                        <span className="ml-1 capitalize">{award.rarity}</span>
                      </Badge>
                    </div>
                    
                    <p className="text-muted-foreground mb-3">{award.description}</p>
                    
                    {award.benefits.length > 0 && (
                      <div className="space-y-2">
                        <h4 className="font-semibold text-sm">Benefits:</h4>
                        <ul className="space-y-1">
                          {award.benefits.map((benefit, idx) => (
                            <li key={idx} className="text-sm text-muted-foreground flex items-center">
                              <Star className="w-3 h-3 mr-2 text-yellow-500" />
                              {benefit}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          <div className="space-y-4 pt-4 border-t">
            {!isRegistered ? (
              <div className="text-center space-y-4">
                <div className="p-4 rounded-lg bg-primary/5 border border-primary/20">
                  <h4 className="font-semibold text-primary mb-2">🎯 Unlock More Benefits!</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Register now to download your certificates, showcase awards on your profile, 
                    and unlock exclusive AI training courses!
                  </p>
                  <Button onClick={onRegister} className="w-full">
                    Register & Download Certificates
                  </Button>
                </div>
                <Button variant="outline" onClick={onClose} className="w-full">
                  Continue as Guest
                </Button>
              </div>
            ) : (
              <div className="flex flex-col sm:flex-row gap-3">
                <Button 
                  onClick={() => awards.forEach(award => onDownload?.(award.id))}
                  className="flex-1"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download All Certificates
                </Button>
                <Button variant="outline" onClick={onClose} className="flex-1">
                  View in Profile
                </Button>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

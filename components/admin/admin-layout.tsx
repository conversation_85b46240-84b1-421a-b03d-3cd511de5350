'use client'

import React from 'react'
import { AdminSidebar } from '@/components/admin/admin-sidebar'
import { AdminTopbar } from '@/components/admin/admin-topbar'
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar'
import { SectionErrorBoundary } from '@/components/ui/section-error-boundary'

interface AdminLayoutProps {
  children: React.ReactNode
  className?: string
  withErrorBoundary?: boolean
  errorBoundaryTitle?: string
  errorBoundaryDescription?: string
}

export function AdminLayout({
  children,
  className,
  withErrorBoundary = true,
  errorBoundaryTitle,
  errorBoundaryDescription
}: AdminLayoutProps) {
  const content = withErrorBoundary ? (
    <SectionErrorBoundary
      title={errorBoundaryTitle || "Page Error"}
      description={errorBoundaryDescription || "An error occurred while loading this page."}
    >
      {children}
    </SectionErrorBoundary>
  ) : children

  return (
    <SidebarProvider>
      <AdminSidebar />
      <SidebarInset>
        <AdminTopbar />
        <main className={className}>
          {content}
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}

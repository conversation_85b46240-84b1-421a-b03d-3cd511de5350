'use client'

import React from 'react'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { ChevronRight, Home } from 'lucide-react'
import { cn } from '@/lib/utils'

interface BreadcrumbItem {
  label: string
  href?: string
  icon?: React.ComponentType<{ className?: string }>
}

interface AdminBreadcrumbProps {
  className?: string
  customItems?: BreadcrumbItem[]
}

export function AdminBreadcrumb({ className, customItems }: AdminBreadcrumbProps) {
  const pathname = usePathname()

  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    if (customItems) return customItems

    const pathSegments = pathname.split('/').filter(Boolean)
    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'Admin', href: '/admin', icon: Home }
    ]

    // Map path segments to readable labels
    const segmentLabels: Record<string, string> = {
      'admin': 'Admin',
      'dashboard': 'Dashboard',
      'users': 'Users',
      'companies': 'Companies',
      'jobs': 'Jobs',
      'categories': 'Categories',
      'applications': 'Applications',
      'analytics': 'Analytics',
      'tracking': 'Success Tracking',
      'disputes': 'Disputes',
      'reports': 'Reports',
      'settings': 'Settings',
      'profile': 'Profile',
      'notifications': 'Notifications',
      'billing': 'Billing',
      'content': 'Content',
      'moderation': 'Moderation',
      'featured': 'Featured',
      'pending': 'Pending Review',
      'active': 'Active',
      'successful-hires': 'Successful Hires'
    }

    let currentPath = ''
    
    for (let i = 1; i < pathSegments.length; i++) {
      const segment = pathSegments[i]
      currentPath += `/${segment}`
      
      const label = segmentLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1)
      
      // Don't add href for the last item (current page)
      const href = i === pathSegments.length - 1 ? undefined : `/admin${currentPath}`
      
      breadcrumbs.push({ label, href })
    }

    return breadcrumbs
  }

  const breadcrumbs = generateBreadcrumbs()

  if (breadcrumbs.length <= 1) {
    return null
  }

  return (
    <nav className={cn('flex items-center space-x-1 text-sm text-muted-foreground', className)}>
      {breadcrumbs.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <ChevronRight className="w-4 h-4 text-muted-foreground/50" />
          )}
          
          {item.href ? (
            <Link
              href={item.href}
              className="flex items-center space-x-1 hover:text-foreground transition-colors duration-200 px-2 py-1 rounded-md hover:bg-muted/50"
            >
              {item.icon && <item.icon className="w-4 h-4" />}
              <span>{item.label}</span>
            </Link>
          ) : (
            <div className="flex items-center space-x-1 text-foreground font-medium px-2 py-1 bg-primary/10 rounded-md">
              {item.icon && <item.icon className="w-4 h-4" />}
              <span>{item.label}</span>
            </div>
          )}
        </React.Fragment>
      ))}
    </nav>
  )
}

// Specific breadcrumb components for common admin pages
export function JobsBreadcrumb() {
  return <AdminBreadcrumb />
}

export function ApplicationsBreadcrumb() {
  return <AdminBreadcrumb />
}

export function UsersBreadcrumb() {
  return <AdminBreadcrumb />
}

export function CompaniesBreadcrumb() {
  return <AdminBreadcrumb />
}

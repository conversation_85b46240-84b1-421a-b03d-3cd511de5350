'use client'

import React from 'react'
import { AdminBreadcrumb } from './admin-breadcrumb'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { LucideIcon } from 'lucide-react'

interface AdminPageHeaderProps {
  title: string
  description?: string
  icon?: LucideIcon
  badge?: {
    text: string
    variant?: 'default' | 'secondary' | 'destructive' | 'outline'
  }
  actions?: React.ReactNode
  breadcrumbItems?: Array<{
    label: string
    href?: string
    icon?: LucideIcon
  }>
  className?: string
}

export function AdminPageHeader({
  title,
  description,
  icon: Icon,
  badge,
  actions,
  breadcrumbItems,
  className
}: AdminPageHeaderProps) {
  return (
    <div className={cn('space-y-4 pb-6 border-b border-border/50', className)}>
      {/* Breadcrumb */}
      <AdminBreadcrumb customItems={breadcrumbItems} />
      
      {/* Header Content */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {Icon && (
            <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
              <Icon className="w-6 h-6 text-primary" />
            </div>
          )}
          
          <div>
            <div className="flex items-center space-x-3">
              <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
              {badge && (
                <Badge variant={badge.variant || 'secondary'}>
                  {badge.text}
                </Badge>
              )}
            </div>
            {description && (
              <p className="text-muted-foreground mt-1">{description}</p>
            )}
          </div>
        </div>
        
        {actions && (
          <div className="flex items-center space-x-2">
            {actions}
          </div>
        )}
      </div>
    </div>
  )
}

// Specific page headers for common admin pages
interface JobsPageHeaderProps {
  title?: string
  description?: string
  actions?: React.ReactNode
}

export function JobsPageHeader({ 
  title = "Job Management", 
  description = "Manage and monitor job postings across the platform",
  actions 
}: JobsPageHeaderProps) {
  return (
    <AdminPageHeader
      title={title}
      description={description}
      actions={actions}
    />
  )
}

export function ApplicationsPageHeader({ 
  title = "Application Management", 
  description = "Monitor and manage job applications across the platform",
  actions 
}: JobsPageHeaderProps) {
  return (
    <AdminPageHeader
      title={title}
      description={description}
      actions={actions}
    />
  )
}

export function UsersPageHeader({ 
  title = "User Management", 
  description = "Manage user accounts and permissions",
  actions 
}: JobsPageHeaderProps) {
  return (
    <AdminPageHeader
      title={title}
      description={description}
      actions={actions}
    />
  )
}

export function CompaniesPageHeader({ 
  title = "Company Management", 
  description = "Manage company profiles and verification status",
  actions 
}: JobsPageHeaderProps) {
  return (
    <AdminPageHeader
      title={title}
      description={description}
      actions={actions}
    />
  )
}

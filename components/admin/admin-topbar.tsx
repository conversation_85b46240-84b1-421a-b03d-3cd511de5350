"use client"

import React, { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useAuthStore } from "@/stores/auth.store"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ThemeToggle } from "@/components/theme-toggle"
import { SidebarTrigger } from "@/components/ui/sidebar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { 
  Search, 
  Bell, 
  Settings, 
  User, 
  LogOut, 
  Shield,
  ChevronDown,
  Database,
  Users,
  Building2,
  Briefcase,
  CreditCard,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Activity,
  Globe,
  HelpCircle,
  Zap
} from "lucide-react"
import { cn } from "@/lib/utils"

interface AdminTopbarProps {
  className?: string
}

interface Notification {
  id: number
  title: string
  message: string
  time: string
  type: 'info' | 'warning' | 'success' | 'error'
  unread: boolean
}

interface QuickAction {
  title: string
  description: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  category: string
}

export function AdminTopbar({ className }: AdminTopbarProps) {
  const router = useRouter()
  const { user, logout } = useAuthStore()
  const [searchQuery, setSearchQuery] = useState("")
  const [searchOpen, setSearchOpen] = useState(false)
  
  const [notifications] = useState<Notification[]>([
    { 
      id: 1, 
      title: "New company verification", 
      message: "TechCorp Inc. submitted verification documents", 
      time: "5 min ago", 
      type: 'info',
      unread: true 
    },
    { 
      id: 2, 
      title: "Content flagged", 
      message: "Job posting flagged for inappropriate content", 
      time: "15 min ago", 
      type: 'warning',
      unread: true 
    },
    { 
      id: 3, 
      title: "Payment processed", 
      message: "Monthly subscription payment received from StartupXYZ", 
      time: "1 hour ago", 
      type: 'success',
      unread: false 
    },
    { 
      id: 4, 
      title: "System alert", 
      message: "Database backup completed successfully", 
      time: "2 hours ago", 
      type: 'info',
      unread: false 
    },
  ])

  const quickActions: QuickAction[] = [
    {
      title: "User Management",
      description: "Manage all platform users",
      href: "/admin/users",
      icon: Users,
      category: "Users"
    },
    {
      title: "Company Verification",
      description: "Review pending company verifications",
      href: "/admin/companies/pending",
      icon: Building2,
      category: "Companies"
    },
    {
      title: "Job Moderation",
      description: "Review flagged job postings",
      href: "/admin/jobs/pending",
      icon: Briefcase,
      category: "Content"
    },
    {
      title: "Revenue Dashboard",
      description: "View financial analytics",
      href: "/admin/billing",
      icon: CreditCard,
      category: "Billing"
    },
    {
      title: "System Analytics",
      description: "Platform performance metrics",
      href: "/admin/analytics",
      icon: BarChart3,
      category: "Analytics"
    },
    {
      title: "Database Admin",
      description: "Database management tools",
      href: "/admin/system/database",
      icon: Database,
      category: "System"
    }
  ]

  const unreadCount = notifications.filter(n => n.unread).length

  const handleLogout = async () => {
    await logout()
    router.push("/login")
  }

  const handleSearch = (action: QuickAction) => {
    router.push(action.href)
    setSearchOpen(false)
    setSearchQuery("")
  }

  const filteredActions = quickActions.filter(action =>
    action.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    action.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    action.category.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'warning': return <AlertTriangle className="w-4 h-4 text-orange-500" />
      case 'success': return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'error': return <AlertTriangle className="w-4 h-4 text-red-500" />
      default: return <Bell className="w-4 h-4 text-blue-500" />
    }
  }

  return (
    <header className={cn(
      "sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
      className
    )}>
      <div className="flex h-16 items-center justify-between px-6">
        {/* Left Section */}
        <div className="flex items-center space-x-4">
          {/* Sidebar Toggle */}
          <SidebarTrigger className="md:hidden" />
          
          {/* Admin Logo/Title */}
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
              <Shield className="w-5 h-5 text-white" />
            </div>
            <div className="hidden md:block">
              <h1 className="text-lg font-semibold bg-gradient-to-r from-red-500 to-red-600 bg-clip-text text-transparent">
                Admin Dashboard
              </h1>
              <p className="text-xs text-muted-foreground">System Management & Control</p>
            </div>
          </div>
        </div>

        {/* Center Section - Search */}
        <div className="flex-1 max-w-md mx-4">
          <Popover open={searchOpen} onOpenChange={setSearchOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={searchOpen}
                className="w-full justify-start text-muted-foreground"
              >
                <Search className="mr-2 h-4 w-4" />
                Search admin actions...
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[400px] p-0" align="center">
              <Command>
                <CommandInput 
                  placeholder="Search admin actions..." 
                  value={searchQuery}
                  onValueChange={setSearchQuery}
                />
                <CommandList>
                  <CommandEmpty>No actions found.</CommandEmpty>
                  {Object.entries(
                    filteredActions.reduce((acc, action) => {
                      if (!acc[action.category]) acc[action.category] = []
                      acc[action.category].push(action)
                      return acc
                    }, {} as Record<string, QuickAction[]>)
                  ).map(([category, actions]) => (
                    <CommandGroup key={category} heading={category}>
                      {actions.map((action) => (
                        <CommandItem
                          key={action.href}
                          onSelect={() => handleSearch(action)}
                          className="cursor-pointer"
                        >
                          <action.icon className="mr-2 h-4 w-4" />
                          <div className="flex flex-col">
                            <span className="font-medium">{action.title}</span>
                            <span className="text-xs text-muted-foreground">{action.description}</span>
                          </div>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  ))}
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-4">
          {/* Quick Actions */}
          <div className="hidden lg:flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => router.push('/admin/system/health')}
              className="text-green-600 border-green-200 hover:bg-green-50"
            >
              <Activity className="w-4 h-4 mr-2" />
              System Health
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => router.push('/admin/companies/pending')}
              className="text-orange-600 border-orange-200 hover:bg-orange-50"
            >
              <AlertTriangle className="w-4 h-4 mr-2" />
              Pending (3)
            </Button>
          </div>

          {/* Notifications */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="icon" className="relative">
                <Bell className="w-5 h-5" />
                {unreadCount > 0 && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-2 -right-2 w-5 h-5 flex items-center justify-center p-0 text-xs"
                  >
                    {unreadCount > 9 ? "9+" : unreadCount}
                  </Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80" align="end">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold">Notifications</h4>
                  <Badge variant="secondary">{unreadCount} new</Badge>
                </div>
                <div className="space-y-3 max-h-80 overflow-y-auto">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={cn(
                        "p-3 rounded-lg border transition-colors cursor-pointer hover:bg-muted/50",
                        notification.unread ? "bg-primary/5 border-primary/20" : "bg-muted/20"
                      )}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3 flex-1">
                          {getNotificationIcon(notification.type)}
                          <div className="flex-1 min-w-0">
                            <p className="font-medium text-sm">{notification.title}</p>
                            <p className="text-sm text-muted-foreground mt-1">{notification.message}</p>
                            <p className="text-xs text-muted-foreground mt-2">{notification.time}</p>
                          </div>
                        </div>
                        {notification.unread && (
                          <div className="w-2 h-2 bg-primary rounded-full mt-1 flex-shrink-0" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
                <div className="pt-3 border-t">
                  <Button variant="ghost" size="sm" className="w-full">
                    View all notifications
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>

          {/* Theme Toggle */}
          <ThemeToggle />

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center space-x-2 px-3">
                <Avatar className="w-8 h-8">
                  <AvatarImage src={user?.profile?.avatar} alt={user?.profile?.firstName} />
                  <AvatarFallback className="bg-gradient-to-br from-red-500 to-red-600 text-white">
                    {user?.profile?.firstName?.[0]}{user?.profile?.lastName?.[0]}
                  </AvatarFallback>
                </Avatar>
                <div className="hidden md:block text-left">
                  <p className="text-sm font-medium">{user?.profile?.firstName} {user?.profile?.lastName}</p>
                  <p className="text-xs text-muted-foreground">System Administrator</p>
                </div>
                <ChevronDown className="w-4 h-4 text-muted-foreground" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end">
              <DropdownMenuLabel>Admin Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => router.push("/admin/profile")}>
                <User className="mr-2 h-4 w-4" />
                Admin Profile
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push("/admin/system/settings")}>
                <Settings className="mr-2 h-4 w-4" />
                System Settings
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push("/admin/system/security")}>
                <Shield className="mr-2 h-4 w-4" />
                Security Settings
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push("/admin/system/audit")}>
                <Database className="mr-2 h-4 w-4" />
                Audit Logs
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => router.push("/admin/support")}>
                <HelpCircle className="mr-2 h-4 w-4" />
                Admin Help
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push("/")}>
                <Globe className="mr-2 h-4 w-4" />
                View Site
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleLogout} className="text-red-600 focus:text-red-600">
                <LogOut className="mr-2 h-4 w-4" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}

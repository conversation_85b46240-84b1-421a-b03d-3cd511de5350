'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { TableErrorBoundary } from '@/components/ui/section-error-boundary'
import { cn } from '@/lib/utils'

interface AdminTableWrapperProps {
  children: React.ReactNode
  title?: string
  description?: string
  className?: string
  onRetry?: () => void
  withErrorBoundary?: boolean
  actions?: React.ReactNode
}

export function AdminTableWrapper({
  children,
  title,
  description,
  className,
  onRetry,
  withErrorBoundary = true,
  actions
}: AdminTableWrapperProps) {
  const tableContent = withErrorBoundary ? (
    <TableErrorBoundary onRetry={onRetry}>
      {children}
    </TableErrorBoundary>
  ) : children

  if (title || description || actions) {
    return (
      <Card className={cn('w-full', className)}>
        {(title || description || actions) && (
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <div>
              {title && <CardTitle>{title}</CardTitle>}
              {description && <CardDescription>{description}</CardDescription>}
            </div>
            {actions && <div className="flex items-center space-x-2">{actions}</div>}
          </CardHeader>
        )}
        <CardContent className="p-0">
          {tableContent}
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn('w-full', className)}>
      {tableContent}
    </div>
  )
}

// Specific wrappers for common admin tables
export function JobsTableWrapper({ 
  children, 
  onRetry, 
  className,
  actions 
}: { 
  children: React.ReactNode
  onRetry?: () => void
  className?: string
  actions?: React.ReactNode
}) {
  return (
    <AdminTableWrapper
      title="Jobs"
      description="Manage and monitor job postings"
      onRetry={onRetry}
      className={className}
      actions={actions}
    >
      {children}
    </AdminTableWrapper>
  )
}

export function ApplicationsTableWrapper({ 
  children, 
  onRetry, 
  className,
  actions 
}: { 
  children: React.ReactNode
  onRetry?: () => void
  className?: string
  actions?: React.ReactNode
}) {
  return (
    <AdminTableWrapper
      title="Applications"
      description="Monitor and manage job applications"
      onRetry={onRetry}
      className={className}
      actions={actions}
    >
      {children}
    </AdminTableWrapper>
  )
}

export function UsersTableWrapper({ 
  children, 
  onRetry, 
  className,
  actions 
}: { 
  children: React.ReactNode
  onRetry?: () => void
  className?: string
  actions?: React.ReactNode
}) {
  return (
    <AdminTableWrapper
      title="Users"
      description="Manage user accounts and permissions"
      onRetry={onRetry}
      className={className}
      actions={actions}
    >
      {children}
    </AdminTableWrapper>
  )
}

export function CompaniesTableWrapper({ 
  children, 
  onRetry, 
  className,
  actions 
}: { 
  children: React.ReactNode
  onRetry?: () => void
  className?: string
  actions?: React.ReactNode
}) {
  return (
    <AdminTableWrapper
      title="Companies"
      description="Manage company profiles and verification"
      onRetry={onRetry}
      className={className}
      actions={actions}
    >
      {children}
    </AdminTableWrapper>
  )
}

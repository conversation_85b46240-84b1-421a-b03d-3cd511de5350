"use client"

import React, { useState, useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { 
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarTrigger,
  useSidebar
} from "@/components/ui/sidebar"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { 
  LayoutDashboard,
  Users,
  Building2,
  Briefcase,
  FileText,
  CreditCard,
  BarChart3,
  Settings,
  Shield,
  Database,
  MessageSquare,
  Flag,
  Bell,
  Mail,
  HelpCircle,
  Search,
  Filter,
  Eye,
  UserCheck,
  UserX,
  Building,
  CheckCircle,
  XCircle,
  TrendingUp,
  DollarSign,
  Receipt,
  RefreshCw,
  AlertTriangle,
  FileCheck,
  Globe,
  Lock,
  Zap,
  Home,
  LogOut,
  ChevronRight,
  ChevronDown
} from "lucide-react"
import { cn } from "@/lib/utils"

interface SidebarItem {
  title: string
  icon: React.ComponentType<{ className?: string }>
  href?: string
  badge?: string | number
  items?: SidebarItem[]
  isActive?: boolean
}

export function AdminSidebar() {
  const router = useRouter()
  const pathname = usePathname()
  const { state } = useSidebar()
  const [openSections, setOpenSections] = useState<string[]>([])

  const toggleSection = (section: string) => {
    setOpenSections(prev =>
      prev.includes(section)
        ? prev.filter(s => s !== section)
        : [...prev, section]
    )
  }

  // Enhanced active state checking
  const isActive = (href: string) => {
    if (!href) return false

    // Exact match
    if (pathname === href) return true

    // For dashboard, only match exact path
    if (href === '/admin') return pathname === '/admin'

    // For other paths, check if current path starts with the href
    return pathname.startsWith(href + '/') || pathname.startsWith(href + '?')
  }

  // Check if any child item is active
  const hasActiveChild = (items: SidebarItem[] = []) => {
    return items.some(item => item.href && isActive(item.href))
  }

  // Get current page title for display
  const getCurrentPageTitle = () => {
    const pathSegments = pathname.split('/').filter(Boolean)

    // Map paths to readable titles
    const pathTitles: Record<string, string> = {
      '/admin': 'Dashboard',
      '/admin/dashboard': 'Dashboard',
      '/admin/users': 'User Management',
      '/admin/companies': 'Company Management',
      '/admin/jobs': 'Job Management',
      '/admin/jobs/categories': 'Job Categories',
      '/admin/jobs/featured': 'Featured Jobs',
      '/admin/jobs/pending': 'Pending Jobs',
      '/admin/jobs/active': 'Active Jobs',
      '/admin/applications': 'Application Management',
      '/admin/applications/analytics': 'Application Analytics',
      '/admin/applications/tracking': 'Success Tracking',
      '/admin/applications/disputes': 'Dispute Resolution',
      '/admin/reports': 'Reports & Analytics',
      '/admin/settings': 'System Settings',
      '/admin/profile': 'Admin Profile',
      '/admin/notifications': 'Notifications',
      '/admin/billing': 'Billing & Revenue',
      '/admin/content': 'Content Management',
      '/admin/content/moderation': 'Content Moderation'
    }

    return pathTitles[pathname] || pathSegments[pathSegments.length - 1]?.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Dashboard'
  }

  const sidebarItems: SidebarItem[] = [
    {
      title: "Dashboard",
      icon: LayoutDashboard,
      href: "/admin"
    },
    {
      title: "User Management",
      icon: Users,
      items: [
        { title: "All Users", icon: Users, href: "/admin/users" },
        { title: "Job Seekers", icon: Search, href: "/admin/users/job-seekers" },
        { title: "Company Admins", icon: Building, href: "/admin/users/company-admins" },
        { title: "Recruiters", icon: UserCheck, href: "/admin/users/recruiters" },
        { title: "Active Users", icon: Eye, href: "/admin/users/active" },
        { title: "Suspended Users", icon: UserX, href: "/admin/users/suspended" }
      ]
    },
    {
      title: "Company Management",
      icon: Building2,
      items: [
        { title: "All Companies", icon: Building2, href: "/admin/companies" },
        { title: "Pending Verification", icon: AlertTriangle, href: "/admin/companies/pending", badge: "3" },
        { title: "Verified Companies", icon: CheckCircle, href: "/admin/companies/verified" },
        { title: "Rejected Companies", icon: XCircle, href: "/admin/companies/rejected" },
        { title: "Company Analytics", icon: TrendingUp, href: "/admin/companies/analytics" }
      ]
    },
    {
      title: "Job Management",
      icon: Briefcase,
      items: [
        { title: "All Jobs", icon: Briefcase, href: "/admin/jobs" },
        { title: "Active Jobs", icon: CheckCircle, href: "/admin/jobs/active" },
        { title: "Pending Review", icon: AlertTriangle, href: "/admin/jobs/pending", badge: "5" },
        { title: "Expired Jobs", icon: XCircle, href: "/admin/jobs/expired" },
        { title: "Job Categories", icon: Filter, href: "/admin/jobs/categories" },
        { title: "Featured Jobs", icon: Zap, href: "/admin/jobs/featured" }
      ]
    },
    {
      title: "Application Management",
      icon: FileText,
      items: [
        { title: "All Applications", icon: FileText, href: "/admin/applications" },
        { title: "Application Analytics", icon: BarChart3, href: "/admin/applications/analytics" },
        { title: "Success Tracking", icon: TrendingUp, href: "/admin/applications/tracking" },
        { title: "Dispute Resolution", icon: AlertTriangle, href: "/admin/applications/disputes" }
      ]
    },
    {
      title: "Billing & Revenue",
      icon: CreditCard,
      items: [
        { title: "Revenue Dashboard", icon: DollarSign, href: "/admin/billing" },
        { title: "Subscriptions", icon: RefreshCw, href: "/admin/billing/subscriptions" },
        { title: "Invoices", icon: Receipt, href: "/admin/billing/invoices" },
        { title: "Payment Methods", icon: CreditCard, href: "/admin/billing/payments" },
        { title: "Refunds", icon: RefreshCw, href: "/admin/billing/refunds" },
        { title: "Financial Reports", icon: BarChart3, href: "/admin/billing/reports" }
      ]
    },
    {
      title: "Content Moderation",
      icon: Shield,
      items: [
        { title: "Content Review", icon: Eye, href: "/admin/content" },
        { title: "Reported Content", icon: Flag, href: "/admin/content/reports", badge: "2" },
        { title: "Auto-flagged Items", icon: AlertTriangle, href: "/admin/content/flagged" },
        { title: "Content Policies", icon: FileCheck, href: "/admin/content/policies" },
        { title: "Moderation Log", icon: FileText, href: "/admin/content/logs" }
      ]
    },
    {
      title: "Analytics & Reports",
      icon: BarChart3,
      items: [
        { title: "Platform Analytics", icon: TrendingUp, href: "/admin/analytics" },
        { title: "User Analytics", icon: Users, href: "/admin/analytics/users" },
        { title: "Revenue Analytics", icon: DollarSign, href: "/admin/analytics/revenue" },
        { title: "Job Market Trends", icon: Briefcase, href: "/admin/analytics/jobs" },
        { title: "Custom Reports", icon: FileText, href: "/admin/analytics/reports" }
      ]
    },
    {
      title: "Communication",
      icon: MessageSquare,
      items: [
        { title: "System Messages", icon: MessageSquare, href: "/admin/communication/messages" },
        { title: "Email Campaigns", icon: Mail, href: "/admin/communication/emails" },
        { title: "Notifications", icon: Bell, href: "/admin/communication/notifications" },
        { title: "Announcements", icon: Globe, href: "/admin/communication/announcements" },
        { title: "Templates", icon: FileText, href: "/admin/communication/templates" }
      ]
    },
    {
      title: "System Management",
      icon: Settings,
      items: [
        { title: "Platform Settings", icon: Settings, href: "/admin/system/settings" },
        { title: "Feature Flags", icon: Flag, href: "/admin/system/features" },
        { title: "Security Settings", icon: Lock, href: "/admin/system/security" },
        { title: "Database Admin", icon: Database, href: "/admin/system/database" },
        { title: "System Health", icon: TrendingUp, href: "/admin/system/health" },
        { title: "Audit Logs", icon: FileText, href: "/admin/system/audit" }
      ]
    },
    {
      title: "Support & Help",
      icon: HelpCircle,
      items: [
        { title: "Support Tickets", icon: HelpCircle, href: "/admin/support/tickets", badge: "7" },
        { title: "Knowledge Base", icon: FileText, href: "/admin/support/knowledge" },
        { title: "FAQ Management", icon: HelpCircle, href: "/admin/support/faq" },
        { title: "Live Chat", icon: MessageSquare, href: "/admin/support/chat" },
        { title: "Support Analytics", icon: BarChart3, href: "/admin/support/analytics" }
      ]
    }
  ]

  // Auto-expand sections with active children
  useEffect(() => {
    const sectionsToOpen: string[] = []

    sidebarItems.forEach(item => {
      if (item.items && hasActiveChild(item.items)) {
        const sectionKey = item.title.toLowerCase().replace(/\s+/g, '-')
        sectionsToOpen.push(sectionKey)
      }
    })

    setOpenSections(prev => {
      const newSections = [...new Set([...prev, ...sectionsToOpen])]
      return newSections
    })
  }, [pathname])

  const renderSidebarItem = (item: SidebarItem, level: number = 0) => {
    const hasSubItems = item.items && item.items.length > 0
    const sectionKey = item.title.toLowerCase().replace(/\s+/g, '-')
    const isOpen = openSections.includes(sectionKey)
    const itemIsActive = item.href ? isActive(item.href) : false
    const hasActiveChild = item.items ? item.items.some(subItem => subItem.href && isActive(subItem.href)) : false

    if (hasSubItems) {
      return (
        <Collapsible key={item.title} open={isOpen} onOpenChange={() => toggleSection(sectionKey)}>
          <SidebarMenuItem>
            <CollapsibleTrigger asChild>
              <SidebarMenuButton
                className={cn(
                  "w-full justify-between hover:bg-primary/10 transition-all duration-200",
                  itemIsActive && "bg-primary text-primary-foreground font-medium shadow-sm",
                  hasActiveChild && !itemIsActive && "bg-primary/5 text-primary font-medium border-l-2 border-primary",
                  level > 0 && "ml-4"
                )}
              >
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <item.icon className="w-5 h-5" />
                    {hasActiveChild && (
                      <div className="absolute -top-1 -right-1 w-2 h-2 bg-primary rounded-full animate-pulse" />
                    )}
                  </div>
                  {state === "expanded" && (
                    <>
                      <span className="font-medium">{item.title}</span>
                      {item.badge && (
                        <Badge variant="secondary" className="ml-auto bg-primary/20 text-primary">
                          {item.badge}
                        </Badge>
                      )}
                    </>
                  )}
                </div>
                {state === "expanded" && (
                  isOpen ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />
                )}
              </SidebarMenuButton>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarMenuSub>
                {item.items?.map((subItem) => (
                  <SidebarMenuSubItem key={subItem.title}>
                    <SidebarMenuSubButton
                      asChild
                      className={cn(
                        "hover:bg-primary/10 transition-all duration-200 relative",
                        subItem.href && isActive(subItem.href) && "bg-primary text-primary-foreground font-medium shadow-sm border-l-2 border-primary-foreground"
                      )}
                    >
                      <a href={subItem.href} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="relative">
                            <subItem.icon className="w-4 h-4" />
                            {subItem.href && isActive(subItem.href) && (
                              <div className="absolute -top-1 -right-1 w-1.5 h-1.5 bg-primary-foreground rounded-full" />
                            )}
                          </div>
                          <span>{subItem.title}</span>
                        </div>
                        {subItem.badge && (
                          <Badge variant="secondary" className="bg-primary/20 text-primary">
                            {subItem.badge}
                          </Badge>
                        )}
                      </a>
                    </SidebarMenuSubButton>
                  </SidebarMenuSubItem>
                ))}
              </SidebarMenuSub>
            </CollapsibleContent>
          </SidebarMenuItem>
        </Collapsible>
      )
    }

    return (
      <SidebarMenuItem key={item.title}>
        <SidebarMenuButton
          asChild
          className={cn(
            "hover:bg-primary/10 transition-all duration-200 relative",
            itemIsActive && "bg-primary text-primary-foreground font-medium shadow-sm border-l-4 border-primary-foreground",
            level > 0 && "ml-4"
          )}
        >
          <a href={item.href} className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <item.icon className="w-5 h-5" />
                {itemIsActive && (
                  <div className="absolute -top-1 -right-1 w-2 h-2 bg-primary-foreground rounded-full" />
                )}
              </div>
              {state === "expanded" && <span className="font-medium">{item.title}</span>}
            </div>
            {state === "expanded" && item.badge && (
              <Badge variant="secondary" className="bg-primary/20 text-primary">
                {item.badge}
              </Badge>
            )}
          </a>
        </SidebarMenuButton>
      </SidebarMenuItem>
    )
  }

  return (
    <Sidebar variant="sidebar" collapsible="icon" className="border-r bg-card/50 backdrop-blur-sm">
      <SidebarHeader className="border-b border-border/50 bg-gradient-to-r from-red-500/10 to-red-600/10">
        <div className="flex items-center space-x-3 px-4 py-4">
          <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg">
            <Shield className="w-5 h-5 text-white" />
          </div>
          {state === "expanded" && (
            <div className="flex-1">
              <h2 className="text-lg font-bold bg-gradient-to-r from-red-500 to-red-600 bg-clip-text text-transparent">
                Admin Control
              </h2>
              <p className="text-xs text-muted-foreground font-medium">
                System Management
              </p>
            </div>
          )}
        </div>

        {/* Current Page Indicator */}
        {state === "expanded" && (
          <div className="px-4 pb-3">
            <div className="text-xs font-medium text-muted-foreground mb-1">Current Page</div>
            <div className="text-sm font-medium text-foreground bg-primary/5 px-2 py-1 rounded-md border-l-2 border-primary">
              {getCurrentPageTitle()}
            </div>
          </div>
        )}
      </SidebarHeader>

      <SidebarContent className="bg-gradient-to-b from-background/50 to-background">
        <ScrollArea className="flex-1">
          <SidebarMenu className="px-3 py-6 space-y-2">
            {/* Quick Actions */}
            {state === "expanded" && (
              <>
                <div className="px-3 py-2">
                  <h3 className="text-xs font-bold text-muted-foreground uppercase tracking-wider">
                    Quick Actions
                  </h3>
                </div>
                <SidebarMenuItem>
                  <Button
                    variant="default"
                    size="sm"
                    className="w-full justify-start mb-3 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 shadow-md"
                    onClick={() => router.push('/admin/users')}
                  >
                    <Users className="w-4 h-4 mr-2" />
                    Manage Users
                  </Button>
                </SidebarMenuItem>
                <Separator className="my-4 bg-border/50" />
              </>
            )}

            {/* Navigation Items */}
            {sidebarItems.map((item) => renderSidebarItem(item))}
          </SidebarMenu>
        </ScrollArea>
      </SidebarContent>

      <SidebarFooter className="border-t border-border/50 bg-gradient-to-r from-red-500/10 to-red-600/10 p-4">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="hover:bg-red-500/10 transition-all duration-200 rounded-lg"
            >
              <a href="/" className="flex items-center">
                <Home className="w-4 h-4 mr-3 text-red-500/70" />
                <span className="font-medium">Back to Site</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
          {state === "expanded" && (
            <div className="mt-3 px-3">
              <div className="text-xs text-muted-foreground">
                <p className="font-medium">System Status</p>
                <p className="text-green-600 hover:text-green-700 cursor-pointer">
                  All Systems Operational
                </p>
              </div>
            </div>
          )}
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  )
}

'use client'

import React, { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Search, 
  MoreHorizontal,
  Eye,
  Download,
  FileText,
  User,
  Building2,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  Filter,
  TrendingUp,
  Users,
  Target,
  Award
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { TableEmptyState, ErrorState } from '@/components/ui/empty-state'

interface Application {
  _id: string
  applicant: {
    _id: string
    firstName: string
    lastName: string
    email: string
    avatar?: string
    profile?: {
      phone?: string
      location?: string
      experience?: string
    }
  }
  job: {
    _id: string
    title: string
    company: {
      _id: string
      name: string
      logo?: string
    }
    location: {
      city: string
      state: string
      remote: boolean
    }
    salary: {
      min: number
      max: number
      currency: string
    }
  }
  status: 'pending' | 'reviewing' | 'shortlisted' | 'interviewed' | 'offered' | 'hired' | 'rejected' | 'withdrawn'
  appliedAt: Date
  updatedAt: Date
  coverLetter?: string
  resumeUrl?: string
  customAnswers?: Array<{
    question: string
    answer: string
  }>
  notes?: string
  rating?: number
  interviewScheduled?: Date
  salaryExpectation?: number
}

interface ApplicationManagementProps {
  className?: string
}

export function ApplicationManagement({ className }: ApplicationManagementProps) {
  const router = useRouter()
  const { token } = useAuthStore()
  const [applications, setApplications] = useState<Application[]>([])
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  })
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Filters
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [jobFilter, setJobFilter] = useState<string>('all')
  const [dateFilter, setDateFilter] = useState<string>('all')

  const fetchApplications = async () => {
    try {
      setIsLoading(true)
      setError(null)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(searchQuery && { search: searchQuery }),
        ...(statusFilter && statusFilter !== 'all' && { status: statusFilter }),
        ...(jobFilter && jobFilter !== 'all' && { job: jobFilter }),
        ...(dateFilter && dateFilter !== 'all' && { dateRange: dateFilter })
      })

      const response = await fetch(`/api/v1/admin/applications?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch applications')
      }

      const data = await response.json()
      setApplications(data.data.applications || [])
      setPagination(prev => ({
        ...prev,
        total: data.data.total || 0,
        totalPages: data.data.totalPages || 0
      }))
    } catch (error) {
      console.error('Failed to fetch applications:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch applications')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchApplications()
  }, [pagination.page, pagination.limit, statusFilter, jobFilter, dateFilter])

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }))
    fetchApplications()
  }

  const handleApplicationAction = async (action: string, applicationId: string, data?: any) => {
    try {
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/v1/admin/applications/${applicationId}/${action}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data || {})
      })

      if (!response.ok) {
        throw new Error(`Failed to ${action} application`)
      }

      await fetchApplications()
    } catch (error) {
      console.error(`Failed to ${action} application:`, error)
      setError(error instanceof Error ? error.message : `Failed to ${action} application`)
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: 'secondary' as const, label: 'Pending', icon: Clock },
      reviewing: { variant: 'default' as const, label: 'Reviewing', icon: Eye },
      shortlisted: { variant: 'default' as const, label: 'Shortlisted', icon: Target },
      interviewed: { variant: 'default' as const, label: 'Interviewed', icon: Users },
      offered: { variant: 'default' as const, label: 'Offered', icon: Award },
      hired: { variant: 'default' as const, label: 'Hired', icon: CheckCircle },
      rejected: { variant: 'destructive' as const, label: 'Rejected', icon: XCircle },
      withdrawn: { variant: 'outline' as const, label: 'Withdrawn', icon: AlertTriangle }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    const IconComponent = config.icon
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <IconComponent className="w-3 h-3" />
        {config.label}
      </Badge>
    )
  }

  const formatSalary = (salary: Application['job']['salary']) => {
    if (!salary.min && !salary.max) return 'Not specified'
    
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: salary.currency || 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })
    
    if (salary.min && salary.max) {
      return `${formatter.format(salary.min)} - ${formatter.format(salary.max)}`
    } else if (salary.min) {
      return `${formatter.format(salary.min)}+`
    } else {
      return `Up to ${formatter.format(salary.max)}`
    }
  }



  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Application Management</h2>
          <p className="text-muted-foreground">
            Monitor and manage job applications across the platform
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={fetchApplications} disabled={isLoading}>
            <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Applications</p>
                <p className="text-2xl font-bold">{pagination.total}</p>
              </div>
              <FileText className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Review</p>
                <p className="text-2xl font-bold">{applications.filter(a => a.status === 'pending').length}</p>
              </div>
              <Clock className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Successful Hires</p>
                <p className="text-2xl font-bold">{applications.filter(a => a.status === 'hired').length}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
                <p className="text-2xl font-bold">
                  {applications.length > 0 ? 
                    Math.round((applications.filter(a => a.status === 'hired').length / applications.length) * 100) : 0
                  }%
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Applications Management</CardTitle>
          <CardDescription>Search, filter, and manage job applications</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search by applicant name, email, or job title..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="pl-10"
                />
              </div>
            </div>
            <Button onClick={handleSearch} disabled={isLoading}>
              <Search className="w-4 h-4 mr-2" />
              Search
            </Button>
          </div>

          <div className="flex flex-wrap gap-4 mb-6">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="reviewing">Reviewing</SelectItem>
                <SelectItem value="shortlisted">Shortlisted</SelectItem>
                <SelectItem value="interviewed">Interviewed</SelectItem>
                <SelectItem value="offered">Offered</SelectItem>
                <SelectItem value="hired">Hired</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
                <SelectItem value="withdrawn">Withdrawn</SelectItem>
              </SelectContent>
            </Select>

            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Date Range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="quarter">This Quarter</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Applications Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Applicant</TableHead>
                  <TableHead>Job & Company</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Applied Date</TableHead>
                  <TableHead>Last Updated</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {error ? (
                  <TableEmptyState
                    title="Failed to load applications"
                    description={error}
                    action={{
                      label: 'Retry',
                      onClick: fetchApplications,
                      variant: 'outline'
                    }}
                    colSpan={6}
                  />
                ) : isLoading ? (
                  <TableEmptyState
                    title="Loading applications..."
                    colSpan={6}
                  />
                ) : applications.length === 0 ? (
                  <TableEmptyState
                    title="No applications found"
                    description="Applications will appear here when candidates apply for jobs."
                    colSpan={6}
                  />
                ) : (
                  applications.map((application) => (
                    <TableRow key={application._id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar className="w-10 h-10">
                            <AvatarImage src={application.applicant.avatar} />
                            <AvatarFallback>
                              {application.applicant.firstName[0]}{application.applicant.lastName[0]}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">
                              {application.applicant.firstName} {application.applicant.lastName}
                            </p>
                            <p className="text-sm text-muted-foreground">{application.applicant.email}</p>
                            {application.applicant.profile?.location && (
                              <p className="text-xs text-muted-foreground">{application.applicant.profile.location}</p>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <p className="font-medium">{application.job.title}</p>
                          <div className="flex items-center space-x-2">
                            <Avatar className="w-6 h-6">
                              <AvatarImage src={application.job.company.logo} />
                              <AvatarFallback>
                                <Building2 className="w-3 h-3" />
                              </AvatarFallback>
                            </Avatar>
                            <p className="text-sm text-muted-foreground">{application.job.company.name}</p>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            {application.job.location.remote ? 'Remote' : `${application.job.location.city}, ${application.job.location.state}`}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(application.status)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Calendar className="w-4 h-4 mr-1" />
                          {new Date(application.appliedAt).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Clock className="w-4 h-4 mr-1" />
                          {new Date(application.updatedAt).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => router.push(`/admin/applications/${application._id}`)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => router.push(`/admin/applications/${application._id}/timeline`)}>
                              <Clock className="mr-2 h-4 w-4" />
                              View Timeline
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {application.status === 'pending' && (
                              <DropdownMenuItem 
                                onClick={() => handleApplicationAction('review', application._id)}
                                className="text-blue-600"
                              >
                                <Eye className="mr-2 h-4 w-4" />
                                Start Review
                              </DropdownMenuItem>
                            )}
                            {application.status === 'reviewing' && (
                              <DropdownMenuItem 
                                onClick={() => handleApplicationAction('shortlist', application._id)}
                                className="text-green-600"
                              >
                                <Target className="mr-2 h-4 w-4" />
                                Shortlist
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => router.push(`/admin/applications/${application._id}/notes`)}
                            >
                              <FileText className="mr-2 h-4 w-4" />
                              Add Notes
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <p className="text-sm text-muted-foreground">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} applications
              </p>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                  disabled={pagination.page <= 1 || isLoading}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                  disabled={pagination.page >= pagination.totalPages || isLoading}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

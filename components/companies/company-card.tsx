'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  MapPin,
  Star,
  Users,
  Calendar,
  Eye,
  Heart,
  MessageCircle,
  Building,
  CheckCircle,
  TrendingUp,
  Award,
  Briefcase,
  ExternalLink,
  Globe,
  Target
} from 'lucide-react'
import { CompanyFrontend } from '@/stores/companies-frontend.store'
import { useCompaniesStore } from '@/stores'

// Legacy interface for backward compatibility
interface Company {
  id: number
  name: string
  industry: string
  location: string
  logo: string
  rating: number
  size: string
  founded: number
  description: string
  specialties: string[]
  openJobs: number
  followers: number
  website: string
  benefits: string[]
  culture: string
  verified: boolean
}

interface CompanyCardProps {
  company: CompanyFrontend | Company
  viewMode: 'grid' | 'list'
  onViewProfile: (company: CompanyFrontend | Company) => void
  onFollow: (company: CompanyFrontend | Company) => void
  onViewJobs: (company: CompanyFrontend | Company) => void
  isFollowing?: boolean
}

export function CompanyCard({
  company,
  viewMode,
  onViewProfile,
  onFollow,
  onViewJobs,
  isFollowing = false
}: CompanyCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  // Get real job data from store
  const {
    companyJobs,
    jobStats,
    getCompanyJobs,
    getCompanyJobStats
  } = useCompaniesStore()

  // Get company ID for job fetching
  const companyId = (company as any)?._id || (company as any)?.slug || String(company.id)

  // Load job data when component mounts
  useEffect(() => {
    if (companyId && !companyJobs[companyId] && !jobStats[companyId]) {
      // Load basic job stats for the card
      getCompanyJobStats(companyId).catch(() => {
        // Silently handle errors - will fall back to static data
      })
    }
  }, [companyId, companyJobs, jobStats, getCompanyJobStats])

  // Get real job count with fallback to original data
  const getRealJobCount = (): number => {
    const stats = jobStats[companyId]
    if (stats) {
      return stats.openJobs
    }
    // Fallback to original company data
    return isNewFormat(company) ? company.stats.activeJobs : (company as any).openJobs || 0
  }

  // Helper function to check if company is new format
  const isNewFormat = (comp: any): comp is CompanyFrontend => {
    return typeof comp.id === 'string' && comp.primaryLocation !== undefined
  }

  // Helper function to get company identifier for navigation
  const getCompanyIdentifier = (comp: any): string => {
    console.log('🔍 CompanyCard getCompanyIdentifier called:', {
      company: {
        _id: comp._id,
        slug: comp.slug,
        id: comp.id,
        name: comp.name
      }
    })

    // If it has a slug, use that (backend companies)
    if ('slug' in comp && comp.slug) {
      console.log('✅ CompanyCard using slug:', comp.slug)
      return comp.slug
    }
    // If it has _id, use that (backend companies without slug)
    if ('_id' in comp && comp._id) {
      console.log('✅ CompanyCard using _id:', comp._id)
      return comp._id
    }
    // Fallback to id (legacy companies)
    console.log('⚠️ CompanyCard using legacy id:', comp.id?.toString())
    return comp.id?.toString() || ''
  }

  // Normalize company data for backward compatibility
  const normalizedCompany = isNewFormat(company) ? {
    id: company.id,
    name: company.name,
    industry: company.industries[0] || 'Technology',
    location: company.primaryLocation.displayName,
    logo: company.logo || '',
    rating: company.rating,
    size: company.size,
    founded: company.founded || new Date().getFullYear(),
    description: company.description,
    specialties: company.specialties,
    openJobs: getRealJobCount(),
    followers: company.stats.followerCount,
    website: company.website || '',
    benefits: company.culture.benefits,
    culture: company.culture.workEnvironment || '',
    verified: company.verification.isVerified
  } : {
    ...company as Company,
    openJobs: getRealJobCount()
  }

  const getCompanySizeLabel = (size: string) => {
    switch (size) {
      case 'startup':
        return '1-10 employees'
      case 'small':
        return '11-50 employees'
      case 'medium':
        return '51-200 employees'
      case 'large':
        return '201-1000 employees'
      case 'enterprise':
        return '1000+ employees'
      default:
        return size
    }
  }

  const handleFollow = () => {
    if (onFollow) onFollow(company)
  }

  if (viewMode === 'list') {
    return (
      <>
        {/* List View - Hidden on Mobile */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          onHoverStart={() => setIsHovered(true)}
          onHoverEnd={() => setIsHovered(false)}
          className="hidden md:block"
        >
        <Card className={`card-premium transition-all duration-300 ${
          isHovered ? 'scale-[1.02] theme-glow' : ''
        }`}>
          <CardContent className="p-6">
            <div className="flex items-start space-x-6">
              {/* Logo and Verification */}
              <div className="relative">
                <Avatar className="w-20 h-20 border-2 border-primary/20">
                  <AvatarImage src={normalizedCompany.logo} alt={normalizedCompany.name} />
                  <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-lg">
                    {normalizedCompany.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                  </AvatarFallback>
                </Avatar>
                {normalizedCompany.verified && (
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-background flex items-center justify-center">
                    <CheckCircle className="w-3 h-3 text-white" />
                  </div>
                )}
              </div>

              {/* Main Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h3 className="text-xl font-bold mb-1 truncate">{normalizedCompany.name}</h3>
                    <p className="text-primary font-medium mb-2">{normalizedCompany.industry}</p>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <MapPin className="w-4 h-4" />
                        <span>{normalizedCompany.location}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        <span>{normalizedCompany.rating}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Users className="w-4 h-4" />
                        <span>{getCompanySizeLabel(normalizedCompany.size)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>Founded {normalizedCompany.founded}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleFollow}
                      className={`p-2 ${isFollowing ? 'text-red-500' : 'text-muted-foreground'}`}
                    >
                      <Heart className={`w-4 h-4 ${isFollowing ? 'fill-current' : ''}`} />
                    </Button>
                    <div className="text-right">
                      <div className="text-lg font-bold text-primary">{normalizedCompany.openJobs}</div>
                      <div className="text-xs text-muted-foreground">open jobs</div>
                    </div>
                  </div>
                </div>

                <p className="text-muted-foreground mb-4 line-clamp-2">{normalizedCompany.description}</p>

                {/* Specialties */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {normalizedCompany.specialties.slice(0, 5).map((specialty) => (
                    <Badge key={specialty} variant="secondary" className="theme-glow">
                      {specialty}
                    </Badge>
                  ))}
                  {normalizedCompany.specialties.length > 5 && (
                    <Badge variant="outline">+{normalizedCompany.specialties.length - 5} more</Badge>
                  )}
                </div>

                {/* Stats and Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-6 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4 text-blue-500" />
                      <span>{normalizedCompany.followers.toLocaleString()} followers</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Briefcase className="w-4 h-4 text-green-500" />
                      <span>{normalizedCompany.openJobs} open positions</span>
                    </div>
                    {normalizedCompany.verified && (
                      <div className="flex items-center space-x-1">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span>Verified Company</span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => onViewJobs(company)}
                    >
                      <Briefcase className="w-4 h-4 mr-2" />
                      View Jobs
                    </Button>
                    <Button 
                      size="sm" 
                      className="button-premium"
                      onClick={() => onViewProfile(company)}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Quick View
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const companyIdentifier = getCompanyIdentifier(company)
                        window.open(`/company/${companyIdentifier}`, '_blank')
                      }}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Full Profile
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        </motion.div>

        {/* Mobile Grid View Fallback - Shown on Mobile when list view is selected */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          onHoverStart={() => setIsHovered(true)}
          onHoverEnd={() => setIsHovered(false)}
          className="block md:hidden"
        >
          <Card className={`card-premium h-full transition-all duration-300 ${
            isHovered ? 'scale-105 theme-glow' : ''
          }`}>
            <CardContent className="p-4">
              {/* Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="relative">
                  <Avatar className="w-12 h-12 border-2 border-primary/20">
                    <AvatarImage src={normalizedCompany.logo} alt={normalizedCompany.name} />
                    <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-sm">
                      {normalizedCompany.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                    </AvatarFallback>
                  </Avatar>
                  {normalizedCompany.verified && (
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-background flex items-center justify-center">
                      <CheckCircle className="w-2 h-2 text-white" />
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleFollow}
                    className={`p-1.5 ${isFollowing ? 'text-red-500' : 'text-muted-foreground'}`}
                  >
                    <Heart className={`w-3 h-3 ${isFollowing ? 'fill-current' : ''}`} />
                  </Button>
                  <div className="text-right">
                    <div className="text-sm font-bold text-primary">{normalizedCompany.openJobs}</div>
                    <div className="text-xs text-muted-foreground">jobs</div>
                  </div>
                </div>
              </div>

              {/* Company Info */}
              <div className="mb-3">
                <h3 className="text-base font-bold mb-1 truncate">{normalizedCompany.name}</h3>
                <p className="text-primary font-medium text-xs mb-2 truncate">{normalizedCompany.industry}</p>
                <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <MapPin className="w-3 h-3" />
                    <span className="truncate">{normalizedCompany.location}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                    <span>{normalizedCompany.rating}</span>
                  </div>
                </div>
              </div>

              {/* Description */}
              <p className="text-muted-foreground text-xs mb-3 line-clamp-2">{normalizedCompany.description}</p>

              {/* Specialties */}
              <div className="flex flex-wrap gap-1 mb-3">
                {normalizedCompany.specialties.slice(0, 2).map((specialty) => (
                  <Badge key={specialty} variant="secondary" className="text-xs theme-glow">
                    {specialty}
                  </Badge>
                ))}
                {normalizedCompany.specialties.length > 2 && (
                  <Badge variant="outline" className="text-xs">+{normalizedCompany.specialties.length - 2}</Badge>
                )}
              </div>

              {/* Actions */}
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  className="button-premium flex-1 text-xs"
                  onClick={() => onViewProfile(company)}
                >
                  <Building className="w-3 h-3 mr-1" />
                  View
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onViewJobs(company)}
                  className="flex-1 text-xs"
                >
                  <Briefcase className="w-3 h-3 mr-1" />
                  Jobs
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </>
    )
  }

  // Grid View
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <Card className={`card-premium h-full transition-all duration-300 ${
        isHovered ? 'scale-105 theme-glow' : ''
      }`}>
        <CardContent className="p-4 md:p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-3 md:mb-4">
            <div className="relative">
              <Avatar className="w-12 h-12 md:w-16 md:h-16 border-2 border-primary/20">
                <AvatarImage src={normalizedCompany.logo} alt={normalizedCompany.name} />
                <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-sm md:text-base">
                  {normalizedCompany.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                </AvatarFallback>
              </Avatar>
              {normalizedCompany.verified && (
                <div className="absolute -bottom-1 -right-1 w-4 h-4 md:w-5 md:h-5 bg-green-500 rounded-full border-2 border-background flex items-center justify-center">
                  <CheckCircle className="w-2 h-2 md:w-3 md:h-3 text-white" />
                </div>
              )}
            </div>

            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleFollow}
                className={`p-1.5 md:p-2 ${isFollowing ? 'text-red-500' : 'text-muted-foreground'}`}
              >
                <Heart className={`w-3 h-3 md:w-4 md:h-4 ${isFollowing ? 'fill-current' : ''}`} />
              </Button>
              <div className="text-right">
                <div className="text-sm md:text-lg font-bold text-primary">{normalizedCompany.openJobs}</div>
                <div className="text-xs text-muted-foreground">jobs</div>
              </div>
            </div>
          </div>

          {/* Company Info */}
          <div className="mb-3">
            <h3 className="text-base md:text-lg font-bold mb-1 truncate">{normalizedCompany.name}</h3>
            <p className="text-primary font-medium text-xs md:text-sm mb-2 truncate">{normalizedCompany.industry}</p>
            <div className="flex items-center space-x-2 md:space-x-3 text-xs text-muted-foreground">
              <div className="flex items-center space-x-1">
                <MapPin className="w-3 h-3" />
                <span className="truncate">{normalizedCompany.location}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                <span>{normalizedCompany.rating}</span>
              </div>
            </div>
          </div>

          {/* Description */}
          <p className="text-muted-foreground text-xs md:text-sm mb-3 md:mb-4 line-clamp-2 md:line-clamp-3">{normalizedCompany.description}</p>

          {/* Specialties - Reduced on mobile */}
          <div className="flex flex-wrap gap-1 mb-3 md:mb-4">
            {normalizedCompany.specialties.slice(0, 2).map((specialty) => (
              <Badge key={specialty} variant="secondary" className="text-xs theme-glow">
                {specialty}
              </Badge>
            ))}
            {normalizedCompany.specialties.length > 2 && (
              <Badge variant="outline" className="text-xs">+{normalizedCompany.specialties.length - 2}</Badge>
            )}
          </div>

          {/* Stats - Simplified for mobile */}
          <div className="grid grid-cols-2 gap-2 mb-3 md:mb-4 text-xs">
            <div className="flex items-center space-x-1 text-muted-foreground">
              <Users className="w-3 h-3 text-blue-500" />
              <span className="truncate">
                <span className="hidden sm:inline">{normalizedCompany.followers.toLocaleString()} followers</span>
                <span className="sm:hidden">{normalizedCompany.followers > 1000 ? `${Math.round(normalizedCompany.followers/1000)}k` : normalizedCompany.followers}</span>
              </span>
            </div>
            {/* Hide founded year on mobile */}
            <div className="hidden sm:flex items-center space-x-1 text-muted-foreground">
              <Calendar className="w-3 h-3" />
              <span>Founded {normalizedCompany.founded}</span>
            </div>
            <div className="flex items-center space-x-1 text-muted-foreground">
              <Building className="w-3 h-3" />
              <span className="truncate">
                <span className="hidden md:inline">{getCompanySizeLabel(normalizedCompany.size)}</span>
                <span className="md:hidden">{normalizedCompany.size}</span>
              </span>
            </div>
            {normalizedCompany.verified && (
              <div className="flex items-center space-x-1 text-muted-foreground">
                <CheckCircle className="w-3 h-3 text-green-500" />
                <span>Verified</span>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex flex-col space-y-2">
            <Button 
              size="sm" 
              className="w-full button-premium"
              onClick={() => onViewProfile(company)}
            >
              <Eye className="w-4 h-4 mr-2" />
              Quick View
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={() => onViewJobs(company)}
            >
              <Briefcase className="w-4 h-4 mr-2" />
              View Jobs ({normalizedCompany.openJobs})
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={() => {
                const companyIdentifier = getCompanyIdentifier(company)
                window.open(`/company/${companyIdentifier}`, '_blank')
              }}
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Full Profile
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

// components/companies/company-detail-modal.tsx
'use client'

import React, { useState } from 'react'
import { useCompaniesStore } from '@/stores'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  MapPin,
  Star,
  Users,
  Calendar,
  Heart,
  CheckCircle,
  Briefcase,
  ExternalLink,
  Share2,
  Shield,
  Coffee,
  Gamepad2,
  GraduationCap,
  Plane,
  DollarSign
} from 'lucide-react'

// Extended company interface for modal
interface ExtendedCompany {
  id?: string | number
  _id?: string
  name?: string
  slug?: string
  description?: string
  logo?: string
  industry?: string | string[]
  industries?: string[]
  location?: unknown
  size?: string
  founded?: number | string
  followers?: number
  openJobs?: number
  totalApplications?: number
  views?: number
  profileViews?: number
  rating?: number
  [key: string]: unknown
}

// Job interface for modal
interface ModalJob {
  id: string | number
  _id?: string
  title: string
  location?: string
  type?: string
  posted?: string
  description?: string
  requirements?: string[]
  department?: string
  salary?: string
}

// Team member interface for modal
interface TeamMember {
  name: string
  role: string
  avatar?: string
  bio?: string
}

interface CompanyDetailModalProps {
  company: ExtendedCompany | null
  originalCompany?: ExtendedCompany // The original company data for navigation
  isOpen: boolean
  onClose: () => void
  onFollow: (company: ExtendedCompany) => void
  onViewJobs: (company: ExtendedCompany) => void
}

export function CompanyDetailModal({
  company,
  originalCompany,
  isOpen,
  onClose,
  onFollow,
  onViewJobs
}: CompanyDetailModalProps) {
  const [activeTab, setActiveTab] = useState('overview')
  const [isFollowing, setIsFollowing] = useState(false)

  // Get job-related data from companies store
  const {
    companyJobs,
    jobStats,
    jobsLoading,
    getCompanyJobs,
    getCompanyJobStats
  } = useCompaniesStore()

  // Get company ID for job fetching
  const companyId = company?._id || (company?.id ? String(company.id) : undefined)

  // Load job data when modal opens
  React.useEffect(() => {
    if (isOpen && companyId) {
      // Load jobs if not already loaded
      if (!companyJobs[companyId]) {
        getCompanyJobs(companyId, 1, 5).catch(() => {
          // Silently handle errors
        })
      }

      // Load job stats if not already loaded
      if (!jobStats[companyId]) {
        getCompanyJobStats(companyId).catch(() => {
          // Silently handle errors
        })
      }
    }
  }, [isOpen, companyId, companyJobs, jobStats, getCompanyJobs, getCompanyJobStats])

  // Get real job statistics
  const getJobStatistics = () => {
    const jobs = companyId ? (companyJobs[companyId] || []) : []
    const stats = companyId ? jobStats[companyId] : undefined

    if (stats) {
      return {
        openJobs: stats.activeJobs || jobs.length,
        totalApplications: stats.totalApplications || 0,
        followers: stats.company?.followerCount || company?.followers || 0,
        profileViews: stats.company?.profileViews || 0,
        recentJobs: stats.recentJobs || 0,
        jobGrowth: stats.jobGrowth || 0
      }
    }

    // Fallback to company data
    return {
      openJobs: company?.openJobs || jobs.length || 0,
      totalApplications: company?.totalApplications || 0,
      followers: company?.followers || 0,
      profileViews: company?.views || company?.profileViews || 0,
      recentJobs: 0,
      jobGrowth: 0
    }
  }

  const jobStatistics = getJobStatistics()

  // Get the correct company identifier for navigation
  const getCompanyIdentifier = (): string => {
    console.log('🔍 Modal getCompanyIdentifier called:', {
      originalCompany: originalCompany ? {
        _id: originalCompany._id,
        slug: originalCompany.slug,
        id: originalCompany.id,
        name: originalCompany.name
      } : null,
      convertedCompany: company ? {
        id: company.id,
        name: company.name
      } : null
    })

    if (originalCompany) {
      // If it has a slug, use that (backend companies)
      if ('slug' in originalCompany && originalCompany.slug) {
        console.log('✅ Using slug:', originalCompany.slug)
        return String(originalCompany.slug)
      }
      // If it has _id, use that (backend companies without slug)
      if ('_id' in originalCompany && originalCompany._id) {
        console.log('✅ Using _id:', originalCompany._id)
        return String(originalCompany._id)
      }
      // Fallback to id (legacy companies)
      console.log('⚠️ Using legacy id:', originalCompany.id?.toString())
      return originalCompany.id ? String(originalCompany.id) : ''
    }
    // Fallback to the converted company ID
    console.log('❌ Using converted company ID:', company?.id?.toString())
    return company?.id?.toString() || ''
  }

  if (!company) return null

  const getCompanySizeLabel = (size?: string) => {
    if (!size) return 'Not specified'
    switch (size) {
      case 'startup':
        return '1-10 employees'
      case 'small':
        return '11-50 employees'
      case 'medium':
        return '51-200 employees'
      case 'large':
        return '201-1000 employees'
      case 'enterprise':
        return '1000+ employees'
      default:
        return size
    }
  }

  // Safe accessors for company data
  const companyName = company.name || 'Company Name'
  const companyLocation = typeof company.location === 'string' ? company.location : 'Location not specified'
  const companySpecialties = Array.isArray(company.specialties) ? company.specialties : []
  const companyCulture = typeof company.culture === 'string' ? company.culture : 'Company culture information not available'
  const isVerified = Boolean(company.verified)

  // Get jobs from store or fallback to company jobs
  const jobs = companyId ? (companyJobs[companyId] || []) : (Array.isArray(company.jobs) ? company.jobs : [])

  // Get team data
  const team = Array.isArray(company.team) ? company.team : []

  const mockBenefits = [
    { icon: Shield, title: "Health Insurance", description: "Comprehensive medical, dental, and vision coverage" },
    { icon: Coffee, title: "Flexible Work", description: "Remote-first culture with flexible hours" },
    { icon: DollarSign, title: "Stock Options", description: "Equity participation for all employees" },
    { icon: Plane, title: "Unlimited PTO", description: "Take time off when you need it" },
    { icon: GraduationCap, title: "Learning Budget", description: "$2,000 annual professional development budget" },
    { icon: Gamepad2, title: "Team Events", description: "Regular team building and social events" }
  ]

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0 flex flex-col">
        <div className="flex flex-col h-full max-h-[90vh]">
          {/* Header */}
          <div className="p-6 border-b bg-gradient-to-r from-primary/5 to-primary/10">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                <div className="relative">
                  <Avatar className="w-20 h-20 border-2 border-primary/20">
                    <AvatarImage src={company.logo} alt={companyName} />
                    <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-xl">
                      {companyName.split(' ').map((n: string) => n[0]).join('').slice(0, 2)}
                    </AvatarFallback>
                  </Avatar>
                  {isVerified && (
                    <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-background flex items-center justify-center">
                      <CheckCircle className="w-3 h-3 text-white" />
                    </div>
                  )}
                </div>
                
                <div className="flex-1">
                  <h1 className="text-2xl font-bold mb-1">{companyName}</h1>
                  <p className="text-primary font-medium text-lg mb-2">{company.industry}</p>
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-3">
                    <div className="flex items-center space-x-1">
                      <MapPin className="w-4 h-4" />
                      <span>{companyLocation}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span>{company.rating}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4" />
                      <span>{getCompanySizeLabel(company.size)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>Founded {company.founded}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {isVerified && (
                      <Badge variant="secondary" className="bg-green-500 text-white">
                        Verified Company
                      </Badge>
                    )}
                    <Badge variant="outline">
                      {jobStatistics.followers.toLocaleString()} followers
                    </Badge>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <div className="text-right mr-4">
                  <div className="text-3xl font-bold text-primary">
                    {jobsLoading ? '...' : jobStatistics.openJobs}
                  </div>
                  <div className="text-sm text-muted-foreground">open jobs</div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsFollowing(!isFollowing)}
                  className={`p-2 ${isFollowing ? 'text-red-500' : 'text-muted-foreground'}`}
                >
                  <Heart className={`w-5 h-5 ${isFollowing ? 'fill-current' : ''}`} />
                </Button>
                <Button variant="ghost" size="sm" className="p-2">
                  <Share2 className="w-5 h-5" />
                </Button>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3 mt-4">
              <Button
                className="button-premium flex-1"
                onClick={() => onViewJobs(company)}
              >
                <Briefcase className="w-4 h-4 mr-2" />
                View Jobs ({jobsLoading ? '...' : jobStatistics.openJobs})
              </Button>
              <Button 
                variant="outline" 
                className="flex-1"
                onClick={() => onFollow(company)}
              >
                <Heart className="w-4 h-4 mr-2" />
                Follow Company
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  const companyIdentifier = getCompanyIdentifier()
                  window.open(`/company/${companyIdentifier}`, '_blank')
                }}
                className="button-enhanced"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                View Full Profile
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto min-h-0">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-5 sticky top-0 bg-background z-10 shrink-0">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="jobs">Jobs</TabsTrigger>
                <TabsTrigger value="culture">Culture</TabsTrigger>
                <TabsTrigger value="benefits">Benefits</TabsTrigger>
                <TabsTrigger value="team">Team</TabsTrigger>
              </TabsList>

              <div className="p-6 flex-1 overflow-y-auto">
                <TabsContent value="overview" className="space-y-6">
                  {/* About */}
                  <Card className="card-enhanced">
                    <CardHeader>
                      <CardTitle>About {company.name}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground leading-relaxed">{company.description}</p>
                    </CardContent>
                  </Card>

                  {/* Specialties */}
                  <Card className="card-enhanced">
                    <CardHeader>
                      <CardTitle>Specialties</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {companySpecialties.map((specialty: string) => (
                          <Badge key={specialty} variant="secondary" className="theme-glow">
                            {specialty}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Stats */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <Card className="card-enhanced text-center">
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold text-primary mb-1">
                          {jobsLoading ? '...' : jobStatistics.openJobs}
                        </div>
                        <div className="text-sm text-muted-foreground">Open Positions</div>
                      </CardContent>
                    </Card>
                    <Card className="card-enhanced text-center">
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold text-primary mb-1">
                          {jobStatistics.totalApplications.toLocaleString()}
                        </div>
                        <div className="text-sm text-muted-foreground">Applications</div>
                      </CardContent>
                    </Card>
                    <Card className="card-enhanced text-center">
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold text-primary mb-1">
                          {jobStatistics.followers.toLocaleString()}
                        </div>
                        <div className="text-sm text-muted-foreground">Followers</div>
                      </CardContent>
                    </Card>
                    <Card className="card-enhanced text-center">
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold text-primary mb-1">{company.founded}</div>
                        <div className="text-sm text-muted-foreground">Founded</div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                <TabsContent value="jobs" className="space-y-6">
                  {jobs.map((job: ModalJob) => (
                    <Card key={job.id} className="card-enhanced">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h3 className="text-lg font-bold mb-1">{job.title}</h3>
                            <p className="text-primary font-medium">{job.department}</p>
                          </div>
                          <Badge variant="outline">{job.type}</Badge>
                        </div>
                        <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                          <div>
                            <span className="font-medium">Location:</span> {job.location}
                          </div>
                          <div>
                            <span className="font-medium">Salary:</span> {job.salary}
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Posted {job.posted}</span>
                          <Button variant="outline" size="sm">
                            <ExternalLink className="w-4 h-4 mr-2" />
                            View Job
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </TabsContent>

                <TabsContent value="culture" className="space-y-6">
                  <Card className="card-enhanced">
                    <CardHeader>
                      <CardTitle>Company Culture</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground leading-relaxed">{companyCulture}</p>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="benefits" className="space-y-6">
                  <div className="grid gap-4">
                    {mockBenefits.map((benefit, index) => (
                      <Card key={index} className="card-enhanced">
                        <CardContent className="p-4">
                          <div className="flex items-start space-x-3">
                            <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                              <benefit.icon className="w-5 h-5 text-primary" />
                            </div>
                            <div>
                              <h4 className="font-medium mb-1">{benefit.title}</h4>
                              <p className="text-sm text-muted-foreground">{benefit.description}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="team" className="space-y-6">
                  {team.map((member: TeamMember, index: number) => (
                    <Card key={index} className="card-enhanced">
                      <CardContent className="p-6">
                        <div className="flex items-start space-x-4">
                          <Avatar className="w-16 h-16">
                            <AvatarImage src={member.avatar} alt={member.name} />
                            <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold">
                              {member.name.split(' ').map((n: string) => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <h4 className="font-bold text-lg">{member.name}</h4>
                            <p className="text-primary font-medium mb-2">{member.role}</p>
                            <p className="text-muted-foreground">{member.bio}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

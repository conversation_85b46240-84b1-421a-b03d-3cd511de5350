'use client'

import { useEffect } from 'react'
import { useCompaniesFrontendStore } from '@/stores/companies-frontend.store'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, Building, MapPin, Users, Star } from 'lucide-react'

/**
 * Test component to verify the companies frontend store and service integration
 * This component will be used to test the basic functionality before full implementation
 */
export function CompaniesTest() {
  const {
    companies,
    searchResult,
    isSearching,
    isLoading,
    error,
    searchError,
    searchCompanies,
    updateFilters,
    clearFilters,
    setViewMode,
    viewMode
  } = useCompaniesFrontendStore()

  useEffect(() => {
    // Test initial search on component mount
    console.log('🧪 Testing initial companies search...')
    searchCompanies({
      search: '',
      page: 1,
      limit: 10,
      sortBy: 'relevance',
      sortOrder: 'desc'
    })
  }, [searchCompanies])

  const handleTestSearch = () => {
    console.log('🧪 Testing search with query "tech"...')
    searchCompanies({
      search: 'tech',
      page: 1,
      limit: 5
    })
  }

  const handleTestFilters = () => {
    console.log('🧪 Testing filters - verified companies only...')
    updateFilters({
      isVerified: true,
      hasActiveJobs: true
    })
  }

  const handleTestClearFilters = () => {
    console.log('🧪 Testing clear filters...')
    clearFilters()
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="w-5 h-5" />
            Companies Frontend Store Test
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 mb-4">
            <Button onClick={handleTestSearch} disabled={isSearching}>
              {isSearching && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              Test Search "tech"
            </Button>
            <Button onClick={handleTestFilters} disabled={isSearching} variant="outline">
              Test Filters (Verified + Hiring)
            </Button>
            <Button onClick={handleTestClearFilters} disabled={isSearching} variant="outline">
              Clear Filters
            </Button>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">View:</span>
              <Button
                size="sm"
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                onClick={() => setViewMode('grid')}
              >
                Grid
              </Button>
              <Button
                size="sm"
                variant={viewMode === 'list' ? 'default' : 'outline'}
                onClick={() => setViewMode('list')}
              >
                List
              </Button>
            </div>
          </div>

          {/* Search Results Summary */}
          {searchResult && (
            <div className="mb-4 p-4 bg-muted/50 rounded-lg">
              <h3 className="font-semibold mb-2">Search Results Summary</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Total Companies:</span>
                  <div className="font-semibold">{searchResult.pagination.total}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Current Page:</span>
                  <div className="font-semibold">{searchResult.pagination.page}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Search Time:</span>
                  <div className="font-semibold">{searchResult.searchMeta.searchTime}ms</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Industries:</span>
                  <div className="font-semibold">{searchResult.aggregations.industries.length}</div>
                </div>
              </div>
            </div>
          )}

          {/* Error Display */}
          {(error || searchError) && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <h3 className="font-semibold text-red-800 mb-2">Error</h3>
              <p className="text-red-700">{error || searchError}</p>
            </div>
          )}

          {/* Loading State */}
          {isSearching && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-8 h-8 animate-spin" />
              <span className="ml-2">Searching companies...</span>
            </div>
          )}

          {/* Companies Display */}
          {!isSearching && companies.length > 0 && (
            <div className="space-y-4">
              <h3 className="font-semibold">Companies ({companies.length})</h3>
              <div className={`grid gap-4 ${
                viewMode === 'grid' 
                  ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
                  : 'grid-cols-1'
              }`}>
                {companies.map((company) => (
                  <Card key={company.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-3">
                          {company.logo ? (
                            <img 
                              src={company.logo} 
                              alt={company.name}
                              className="w-12 h-12 rounded-lg object-cover"
                            />
                          ) : (
                            <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                              <Building className="w-6 h-6 text-primary" />
                            </div>
                          )}
                          <div>
                            <h4 className="font-semibold">{company.name}</h4>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <MapPin className="w-3 h-3" />
                              {company.primaryLocation.displayName}
                            </div>
                          </div>
                        </div>
                        {company.verification.isVerified && (
                          <Badge variant="secondary" className="text-xs">
                            Verified
                          </Badge>
                        )}
                      </div>

                      <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                        {company.description}
                      </p>

                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-1">
                            <Users className="w-3 h-3" />
                            <span>{company.stats.activeJobs} jobs</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                            <span>{company.rating}</span>
                          </div>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {company.size}
                        </Badge>
                      </div>

                      {company.industries.length > 0 && (
                        <div className="mt-3 flex flex-wrap gap-1">
                          {company.industries.slice(0, 2).map((industry) => (
                            <Badge key={industry} variant="secondary" className="text-xs">
                              {industry}
                            </Badge>
                          ))}
                          {company.industries.length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +{company.industries.length - 2}
                            </Badge>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* No Results */}
          {!isSearching && companies.length === 0 && !error && !searchError && (
            <div className="text-center py-8">
              <Building className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="font-semibold mb-2">No companies found</h3>
              <p className="text-muted-foreground">Try adjusting your search criteria</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

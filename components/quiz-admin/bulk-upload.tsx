"use client"

import { useState, useRef } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Progress } from "@/components/ui/progress"
import { 
  Upload, 
  Download, 
  FileText, 
  AlertCircle, 
  CheckCircle, 
  X,
  Loader2,
  Info
} from "lucide-react"
import { toast } from "sonner"

interface BulkUploadProps {
  type: 'categories' | 'questions'
  title: string
  description: string
  apiEndpoint: string
  templateEndpoint: string
  onSuccess?: () => void
  categoryId?: string
}

interface UploadResult {
  created: number
  updated: number
  failed: number
  errors: string[]
  warnings?: string[]
}

export default function BulkUpload({ 
  type, 
  title, 
  description, 
  apiEndpoint, 
  templateEndpoint,
  onSuccess,
  categoryId 
}: BulkUploadProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [uploadMode, setUploadMode] = useState<'create' | 'update' | 'upsert'>('create')
  const [uploadMethod, setUploadMethod] = useState<'file' | 'json'>('file')
  const [jsonData, setJsonData] = useState('')
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileUpload = async (file: File) => {
    if (!file) return

    const fileType = file.name.split('.').pop()?.toLowerCase()
    
    if (fileType === 'csv') {
      const text = await file.text()
      const data = parseCSV(text)
      return data
    } else if (fileType === 'json') {
      const text = await file.text()
      try {
        const data = JSON.parse(text)
        return Array.isArray(data) ? data : [data]
      } catch (error) {
        throw new Error('Invalid JSON format')
      }
    } else {
      throw new Error('Only CSV and JSON files are supported')
    }
  }

  const parseCSV = (csvText: string) => {
    const lines = csvText.split('\n').filter(line => line.trim() && !line.startsWith('#'))
    if (lines.length < 2) throw new Error('CSV must have at least a header and one data row')
    
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
    const data = []
    
    for (let i = 1; i < lines.length; i++) {
      const values = parseCSVLine(lines[i])
      if (values.length === 0) continue
      
      const row: any = {}
      headers.forEach((header, index) => {
        row[header] = values[index] || ''
      })
      data.push(row)
    }
    
    return data
  }

  const parseCSVLine = (line: string): string[] => {
    const result = []
    let current = ''
    let inQuotes = false
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i]
      
      if (char === '"') {
        inQuotes = !inQuotes
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim())
        current = ''
      } else {
        current += char
      }
    }
    
    result.push(current.trim())
    return result
  }

  const handleUpload = async () => {
    try {
      setIsUploading(true)
      setUploadProgress(0)
      setUploadResult(null)

      let data: any[] = []

      if (uploadMethod === 'file') {
        const file = fileInputRef.current?.files?.[0]
        if (!file) {
          toast.error('Please select a file')
          return
        }
        data = await handleFileUpload(file)
      } else {
        if (!jsonData.trim()) {
          toast.error('Please enter JSON data')
          return
        }
        try {
          const parsed = JSON.parse(jsonData)
          data = Array.isArray(parsed) ? parsed : [parsed]
        } catch (error) {
          toast.error('Invalid JSON format')
          return
        }
      }

      setUploadProgress(25)

      const payload: any = {
        [type]: data,
        mode: uploadMode
      }

      if (categoryId && type === 'questions') {
        payload.categoryId = categoryId
      }

      setUploadProgress(50)

      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      })

      setUploadProgress(75)

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Upload failed')
      }

      setUploadProgress(100)
      setUploadResult(result.data.results)
      
      toast.success(result.data.summary)
      
      if (onSuccess) {
        onSuccess()
      }

    } catch (error) {
      console.error('Upload error:', error)
      toast.error(error instanceof Error ? error.message : 'Upload failed')
    } finally {
      setIsUploading(false)
    }
  }

  const downloadTemplate = async (format: 'csv' | 'json') => {
    try {
      const response = await fetch(`${templateEndpoint}?type=${type}&format=${format}`)
      
      if (format === 'csv') {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${type}-template.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        const data = await response.json()
        const blob = new Blob([JSON.stringify(data.data.template, null, 2)], { 
          type: 'application/json' 
        })
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${type}-template.json`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
      
      toast.success(`${format.toUpperCase()} template downloaded`)
    } catch (error) {
      toast.error('Failed to download template')
    }
  }

  const resetForm = () => {
    setUploadMethod('file')
    setJsonData('')
    setUploadResult(null)
    setUploadProgress(0)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button onClick={() => setIsOpen(true)}>
          <Upload className="w-4 h-4 mr-2" />
          Bulk Upload
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Templates Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Download className="w-5 h-5" />
                <span>Download Templates</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Download a template file to see the required format and example data.
              </p>
              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  onClick={() => downloadTemplate('csv')}
                  className="flex items-center space-x-2"
                >
                  <FileText className="w-4 h-4" />
                  <span>CSV Template</span>
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => downloadTemplate('json')}
                  className="flex items-center space-x-2"
                >
                  <FileText className="w-4 h-4" />
                  <span>JSON Template</span>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Upload Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Upload Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Upload Mode</Label>
                  <Select value={uploadMode} onValueChange={(value: any) => setUploadMode(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="create">Create Only (Skip Existing)</SelectItem>
                      <SelectItem value="update">Update Only (Skip New)</SelectItem>
                      <SelectItem value="upsert">Create or Update</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Upload Method</Label>
                  <Select value={uploadMethod} onValueChange={(value: any) => setUploadMethod(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="file">File Upload</SelectItem>
                      <SelectItem value="json">JSON Input</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Upload Section */}
          <Card>
            <CardHeader>
              <CardTitle>Upload Data</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {uploadMethod === 'file' ? (
                <div className="space-y-2">
                  <Label htmlFor="file">Select File (CSV or JSON)</Label>
                  <Input
                    id="file"
                    type="file"
                    accept=".csv,.json"
                    ref={fileInputRef}
                    disabled={isUploading}
                  />
                  <p className="text-sm text-muted-foreground">
                    Supported formats: CSV, JSON
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  <Label htmlFor="json">JSON Data</Label>
                  <Textarea
                    id="json"
                    value={jsonData}
                    onChange={(e) => setJsonData(e.target.value)}
                    placeholder="Paste your JSON data here..."
                    rows={10}
                    disabled={isUploading}
                  />
                  <p className="text-sm text-muted-foreground">
                    Enter a JSON array of objects matching the template format
                  </p>
                </div>
              )}

              {isUploading && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Uploading...</span>
                    <span className="text-sm">{uploadProgress}%</span>
                  </div>
                  <Progress value={uploadProgress} />
                </div>
              )}

              <div className="flex justify-between">
                <Button variant="outline" onClick={resetForm} disabled={isUploading}>
                  Reset
                </Button>
                <Button onClick={handleUpload} disabled={isUploading}>
                  {isUploading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="w-4 h-4 mr-2" />
                      Upload {type}
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Results Section */}
          {uploadResult && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>Upload Results</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{uploadResult.created}</div>
                    <div className="text-sm text-muted-foreground">Created</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{uploadResult.updated}</div>
                    <div className="text-sm text-muted-foreground">Updated</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{uploadResult.failed}</div>
                    <div className="text-sm text-muted-foreground">Failed</div>
                  </div>
                </div>

                {uploadResult.errors.length > 0 && (
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <AlertCircle className="w-4 h-4 text-red-500" />
                      <span className="font-medium">Errors ({uploadResult.errors.length})</span>
                    </div>
                    <div className="max-h-40 overflow-y-auto space-y-1">
                      {uploadResult.errors.map((error, index) => (
                        <div key={index} className="text-sm text-red-600 bg-red-50 p-2 rounded">
                          {error}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {uploadResult.warnings && uploadResult.warnings.length > 0 && (
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Info className="w-4 h-4 text-yellow-500" />
                      <span className="font-medium">Warnings ({uploadResult.warnings.length})</span>
                    </div>
                    <div className="max-h-40 overflow-y-auto space-y-1">
                      {uploadResult.warnings.map((warning, index) => (
                        <div key={index} className="text-sm text-yellow-600 bg-yellow-50 p-2 rounded">
                          {warning}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Info className="w-5 h-5" />
                <span>Instructions</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm text-muted-foreground">
                <p><strong>Upload Modes:</strong></p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li><strong>Create Only:</strong> Only creates new items, skips existing ones</li>
                  <li><strong>Update Only:</strong> Only updates existing items, skips new ones</li>
                  <li><strong>Create or Update:</strong> Creates new items and updates existing ones</li>
                </ul>

                <p className="mt-4"><strong>File Formats:</strong></p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li><strong>CSV:</strong> Comma-separated values with headers</li>
                  <li><strong>JSON:</strong> Array of objects matching the template structure</li>
                </ul>

                <p className="mt-4"><strong>Tips:</strong></p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Download the template first to see the required format</li>
                  <li>Remove instruction lines from CSV templates before uploading</li>
                  <li>Use "Create or Update" mode for most bulk operations</li>
                  <li>Check the results section for any errors after upload</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  )
}

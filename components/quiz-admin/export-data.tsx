"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { 
  Download, 
  FileText, 
  Database,
  Loader2
} from "lucide-react"
import { toast } from "sonner"
import { useQuizAdminStore } from "@/stores/quiz-admin.store"

interface ExportDataProps {
  type: 'categories' | 'questions'
  title: string
  description: string
}

export default function ExportData({ type, title, description }: ExportDataProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [format, setFormat] = useState<'json' | 'csv'>('csv')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [isExporting, setIsExporting] = useState(false)
  
  const { 
    categories, 
    exportCategories, 
    exportQuestions,
    fetchCategories 
  } = useQuizAdminStore()

  const handleExport = async () => {
    try {
      setIsExporting(true)
      
      if (type === 'categories') {
        await exportCategories(format)
        toast.success(`Categories exported as ${format.toUpperCase()}`)
      } else {
        const categoryId = categoryFilter === 'all' ? undefined : categoryFilter
        await exportQuestions(format, categoryId)
        toast.success(`Questions exported as ${format.toUpperCase()}`)
      }
      
      setIsOpen(false)
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Failed to export data')
    } finally {
      setIsExporting(false)
    }
  }

  const handleOpen = () => {
    if (type === 'questions' && categories.length === 0) {
      fetchCategories()
    }
    setIsOpen(true)
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" onClick={handleOpen}>
          <Download className="w-4 h-4 mr-2" />
          Export
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Format Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Export Format</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>File Format</Label>
                <Select value={format} onValueChange={(value: any) => setFormat(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="csv">
                      <div className="flex items-center space-x-2">
                        <FileText className="w-4 h-4" />
                        <span>CSV (Comma Separated Values)</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="json">
                      <div className="flex items-center space-x-2">
                        <Database className="w-4 h-4" />
                        <span>JSON (JavaScript Object Notation)</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {type === 'questions' && (
                <div className="space-y-2">
                  <Label>Category Filter</Label>
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          <div className="flex items-center space-x-2">
                            <span>{category.icon}</span>
                            <span>{category.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Format Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Format Information</CardTitle>
            </CardHeader>
            <CardContent>
              {format === 'csv' ? (
                <div className="space-y-2 text-sm text-muted-foreground">
                  <p><strong>CSV Format:</strong></p>
                  <ul className="list-disc list-inside space-y-1 ml-2">
                    <li>Comma-separated values with headers</li>
                    <li>Easy to open in Excel or Google Sheets</li>
                    <li>Human-readable format</li>
                    <li>Good for data analysis and editing</li>
                  </ul>
                </div>
              ) : (
                <div className="space-y-2 text-sm text-muted-foreground">
                  <p><strong>JSON Format:</strong></p>
                  <ul className="list-disc list-inside space-y-1 ml-2">
                    <li>Structured data format</li>
                    <li>Preserves data types and structure</li>
                    <li>Easy to import back into the system</li>
                    <li>Good for backups and data migration</li>
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Export Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Export Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Data Type:</span>
                  <span className="font-medium capitalize">{type}</span>
                </div>
                <div className="flex justify-between">
                  <span>Format:</span>
                  <span className="font-medium">{format.toUpperCase()}</span>
                </div>
                {type === 'questions' && (
                  <div className="flex justify-between">
                    <span>Category:</span>
                    <span className="font-medium">
                      {categoryFilter === 'all' 
                        ? 'All Categories' 
                        : categories.find(c => c.id === categoryFilter)?.name || 'Unknown'
                      }
                    </span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span>File Name:</span>
                  <span className="font-medium text-xs">
                    quiz-{type}.{format}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Export Button */}
          <div className="flex justify-between">
            <Button variant="outline" onClick={() => setIsOpen(false)} disabled={isExporting}>
              Cancel
            </Button>
            <Button onClick={handleExport} disabled={isExporting}>
              {isExporting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  Export {type}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

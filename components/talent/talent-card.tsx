'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  MapPin,
  Star,
  Clock,
  Eye,
  Heart,
  MessageCircle,
  Briefcase,
  CheckCircle,
  Users,
  ExternalLink
} from 'lucide-react'
import { TalentFrontend } from '@/lib/services/talent-frontend.service'

interface TalentCardProps {
  talent: TalentFrontend
  viewMode: 'grid' | 'list'
  onViewProfile: (talent: TalentFrontend) => void
  onContact: (talent: TalentFrontend) => void
  onHire?: (talent: TalentFrontend) => void
  onSave?: (talent: TalentFrontend) => void
}

export function TalentCard({
  talent,
  viewMode,
  onViewProfile,
  onContact,
  onHire,
  onSave
}: TalentCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [isSaved, setIsSaved] = useState(false)

  const getAvailabilityColor = (availability: string | { status: string } | unknown) => {
    let status: string
    if (typeof availability === 'string') {
      status = availability
    } else if (availability && typeof availability === 'object' && 'status' in availability) {
      status = (availability as { status: string }).status
    } else {
      status = 'unavailable'
    }

    switch (status.toLowerCase()) {
      case 'available':
        return 'bg-green-500'
      case 'open_to_opportunities':
        return 'bg-blue-500'
      case 'employed':
        return 'bg-yellow-500'
      case 'not_looking':
        return 'bg-red-500'
      case 'busy':
        return 'bg-yellow-500'
      case 'unavailable':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }



  const handleSave = () => {
    setIsSaved(!isSaved)
    if (onSave) onSave(talent)
  }

  if (viewMode === 'list') {
    return (
      <>
        {/* List View - Hidden on Mobile */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          onHoverStart={() => setIsHovered(true)}
          onHoverEnd={() => setIsHovered(false)}
          className="hidden md:block"
        >
        <Card className={`card-premium transition-all duration-300 ${
          isHovered ? 'scale-[1.02] theme-glow' : ''
        }`}>
          <CardContent className="p-6">
            <div className="flex items-start space-x-6">
              {/* Avatar and Status */}
              <div className="relative">
                <Avatar className="w-20 h-20 border-2 border-primary/20">
                  <AvatarImage src={talent.avatar} alt={talent.name} />
                  <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-lg">
                    {talent.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div className={`absolute -bottom-1 -right-1 w-6 h-6 ${getAvailabilityColor(talent.availability)} rounded-full border-2 border-background flex items-center justify-center`}>
                  <div className="w-2 h-2 bg-white rounded-full" />
                </div>
              </div>

              {/* Main Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h3 className="text-xl font-bold mb-1 truncate">{talent.name}</h3>
                    <p className="text-primary font-medium mb-2">{talent.currentTitle || talent.headline}</p>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <MapPin className="w-4 h-4" />
                        <span>{talent.location}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        <span>{talent.rating?.toFixed(1) || 'N/A'}</span>
                        <span className="text-muted-foreground">({talent.applicationStats.totalApplications} applications)</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Briefcase className="w-4 h-4" />
                        <span>{talent.experience.yearsOfExperience} years • {talent.experience.level}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleSave}
                      className={`p-2 ${isSaved ? 'text-red-500' : 'text-muted-foreground'}`}
                    >
                      <Heart className={`w-4 h-4 ${isSaved ? 'fill-current' : ''}`} />
                    </Button>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-primary">
                        {talent.privacy.showSalaryExpectation ? (
                          talent.jobPreferences.salaryExpectation.min ? 
                            `$${talent.jobPreferences.salaryExpectation.min.toLocaleString()}` :
                            'Negotiable'
                        ) : 'Salary negotiable'}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {talent.privacy.showSalaryExpectation && talent.jobPreferences.salaryExpectation.period ? 
                          `per ${talent.jobPreferences.salaryExpectation.period}` : 'expected'}
                      </div>
                    </div>
                  </div>
                </div>

                <p className="text-muted-foreground mb-4 line-clamp-2">{talent.summary}</p>

                {/* Skills */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {talent.skills.slice(0, 6).map((skill, index) => (
                    <Badge key={index} variant="secondary" className="theme-glow">
                      {skill.name}
                    </Badge>
                  ))}
                  {talent.skills.length > 6 && (
                    <Badge variant="outline">+{talent.skills.length - 6} more</Badge>
                  )}
                </div>

                {/* Stats and Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-6 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span>{Math.round((talent.applicationStats.totalApplications / (talent.applicationStats.totalApplications + 2)) * 100)}% success</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>Responds in 2 hours</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4" />
                      <span>{talent.applicationStats.totalApplications} projects</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onContact(talent)}
                    >
                      <MessageCircle className="w-4 h-4 mr-2" />
                      Contact
                    </Button>
                    <Button
                      size="sm"
                      className="button-premium"
                      onClick={() => onViewProfile(talent)}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Quick View
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        window.open(`/talent/${talent.id}`, '_blank')
                      }}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Full Profile
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        </motion.div>

        {/* Mobile Grid View Fallback - Shown on Mobile when list view is selected */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          onHoverStart={() => setIsHovered(true)}
          onHoverEnd={() => setIsHovered(false)}
          className="block md:hidden"
        >
          <Card className={`card-premium h-full transition-all duration-300 ${
            isHovered ? 'scale-105 theme-glow' : ''
          }`}>
            <CardContent className="p-4">
              {/* Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="relative">
                  <Avatar className="w-12 h-12 border-2 border-primary/20">
                    <AvatarImage src={talent.avatar} alt={talent.name} />
                    <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-sm">
                      {talent.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className={`absolute -bottom-1 -right-1 w-4 h-4 ${getAvailabilityColor(talent.availability)} rounded-full border-2 border-background flex items-center justify-center`}>
                    <div className="w-1 h-1 bg-white rounded-full" />
                  </div>
                </div>

                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleSave}
                    className={`p-1.5 ${isSaved ? 'text-red-500' : 'text-muted-foreground'}`}
                  >
                    <Heart className={`w-3 h-3 ${isSaved ? 'fill-current' : ''}`} />
                  </Button>
                  <div className="text-right">
                    <div className="text-sm font-bold text-primary">
                      {talent.privacy.showSalaryExpectation ? (
                        talent.jobPreferences.salaryExpectation.min ?
                          `$${talent.jobPreferences.salaryExpectation.min.toLocaleString()}` :
                          'Negotiable'
                      ) : 'Salary negotiable'}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {talent.privacy.showSalaryExpectation && talent.jobPreferences.salaryExpectation.period ?
                        `per ${talent.jobPreferences.salaryExpectation.period}` : 'expected'}
                    </div>
                  </div>
                </div>
              </div>

              {/* Name and Title */}
              <div className="mb-3">
                <h3 className="text-base font-bold mb-1 truncate">{talent.name}</h3>
                <p className="text-primary font-medium text-xs mb-2 truncate">{talent.currentTitle || talent.headline}</p>
                <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <MapPin className="w-3 h-3" />
                    <span className="truncate">{talent.location}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                    <span>{talent.rating?.toFixed(1) || 'N/A'}</span>
                  </div>
                </div>
              </div>

              {/* Bio */}
              <p className="text-muted-foreground text-xs mb-3 line-clamp-2">{talent.summary}</p>

              {/* Skills */}
              <div className="flex flex-wrap gap-1 mb-3">
                {talent.skills.slice(0, 3).map((skill, index) => (
                  <Badge key={index} variant="secondary" className="text-xs theme-glow">
                    {skill.name}
                  </Badge>
                ))}
                {talent.skills.length > 3 && (
                  <Badge variant="outline" className="text-xs">+{talent.skills.length - 3}</Badge>
                )}
              </div>

              {/* Actions */}
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  className="button-premium flex-1 text-xs"
                  onClick={() => onContact(talent)}
                >
                  <MessageCircle className="w-3 h-3 mr-1" />
                  Contact
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onViewProfile(talent)}
                  className="flex-1 text-xs"
                >
                  <Eye className="w-3 h-3 mr-1" />
                  View
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </>
    )
  }

  // Grid View
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <Card className={`card-premium h-full transition-all duration-300 ${
        isHovered ? 'scale-105 theme-glow' : ''
      }`}>
        <CardContent className="p-4 md:p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-3 md:mb-4">
            <div className="relative">
              <Avatar className="w-12 h-12 md:w-16 md:h-16 border-2 border-primary/20">
                <AvatarImage src={talent.avatar} alt={talent.name} />
                <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-sm md:text-base">
                  {talent.name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <div className={`absolute -bottom-1 -right-1 w-4 h-4 md:w-5 md:h-5 ${getAvailabilityColor(talent.availability)} rounded-full border-2 border-background flex items-center justify-center`}>
                <div className="w-1 h-1 md:w-1.5 md:h-1.5 bg-white rounded-full" />
              </div>
            </div>

            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSave}
                className={`p-1.5 md:p-2 ${isSaved ? 'text-red-500' : 'text-muted-foreground'}`}
              >
                <Heart className={`w-3 h-3 md:w-4 md:h-4 ${isSaved ? 'fill-current' : ''}`} />
              </Button>
              <div className="text-right">
                <div className="text-base md:text-xl font-bold text-primary">
                  {talent.privacy.showSalaryExpectation ? (
                    talent.jobPreferences.salaryExpectation.min ?
                      `$${talent.jobPreferences.salaryExpectation.min.toLocaleString()}` :
                      'Negotiable'
                  ) : 'Salary negotiable'}
                </div>
                <div className="text-xs text-muted-foreground">
                  {talent.privacy.showSalaryExpectation && talent.jobPreferences.salaryExpectation.period ?
                    `per ${talent.jobPreferences.salaryExpectation.period}` : 'expected'}
                </div>
              </div>
            </div>
          </div>

          {/* Name and Title */}
          <div className="mb-3">
            <h3 className="text-lg font-bold mb-1 truncate">{talent.name}</h3>
            <p className="text-primary font-medium text-sm mb-2">{talent.currentTitle || talent.headline}</p>
            <div className="flex items-center space-x-3 text-xs text-muted-foreground">
              <div className="flex items-center space-x-1">
                <MapPin className="w-3 h-3" />
                <span>{talent.location}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                <span>{talent.rating?.toFixed(1) || 'N/A'}</span>
              </div>
            </div>
          </div>

          {/* Bio */}
          <p className="text-muted-foreground text-sm mb-4 line-clamp-3">{talent.summary}</p>

          {/* Skills - Reduced on mobile */}
          <div className="flex flex-wrap gap-1 mb-3 md:mb-4">
            {talent.skills.slice(0, 3).map((skill, index) => (
              <Badge key={index} variant="secondary" className="text-xs theme-glow">
                {skill.name}
              </Badge>
            ))}
            {talent.skills.length > 3 && (
              <Badge variant="outline" className="text-xs">+{talent.skills.length - 3}</Badge>
            )}
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 gap-2 mb-4 text-xs">
            <div className="flex items-center space-x-1 text-muted-foreground">
              <CheckCircle className="w-3 h-3 text-green-500" />
              <span>{Math.round((talent.applicationStats.totalApplications / (talent.applicationStats.totalApplications + 2)) * 100)}% success</span>
            </div>
            <div className="flex items-center space-x-1 text-muted-foreground">
              <Users className="w-3 h-3" />
              <span>{talent.applicationStats.totalApplications} projects</span>
            </div>
            <div className="flex items-center space-x-1 text-muted-foreground">
              <Clock className="w-3 h-3" />
              <span>2 hours</span>
            </div>
            <div className="flex items-center space-x-1 text-muted-foreground">
              <Briefcase className="w-3 h-3" />
              <span>{talent.experience.yearsOfExperience} years</span>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col space-y-2">
            <Button
              size="sm"
              className="w-full button-premium"
              onClick={() => onViewProfile(talent)}
            >
              <Eye className="w-4 h-4 mr-2" />
              Quick View
            </Button>
            {onHire && (
              <Button
                size="sm"
                className="w-full button-premium"
                onClick={() => onHire(talent)}
              >
                <Briefcase className="w-4 h-4 mr-2" />
                Hire Now
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={() => onContact(talent)}
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              Contact
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={() => {
                window.open(`/talent/${talent.id}`, '_blank')
              }}
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Full Profile
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

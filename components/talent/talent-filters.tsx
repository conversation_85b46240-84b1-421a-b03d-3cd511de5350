'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Separator } from '@/components/ui/separator'
import {
  MapPin,
  DollarSign,
  Clock,
  Star,
  Briefcase,
  Code,
  Palette,
  Database,
  Smartphone,
  Globe,
  Shield,
  ChevronDown,
  ChevronUp,
  X,
  Filter,
  Users,
  Calculator,
  TrendingUp,
  Scale,
  Megaphone,
  Settings,
  Heart,
  Building,
  FileText,
  Zap
} from 'lucide-react'

import { TalentSearchFilters } from '@/stores/talent-frontend.store'

interface TalentFiltersProps {
  filters: Partial<TalentSearchFilters>
  onFiltersChange: (filters: Partial<TalentSearchFilters>) => void
  onClearFilters: () => void
}

export function TalentFilters({
  filters,
  onFiltersChange,
  onClearFilters
}: TalentFiltersProps) {
  const [expandedSections, setExpandedSections] = useState({
    skills: true,
    experience: true,
    salary: true,
    location: true,
    availability: true,
    rating: true,
    industries: true,
    jobTypes: true
  })

  // Helper functions to update filters
  const updateFilter = (key: keyof TalentSearchFilters, value: unknown) => {
    onFiltersChange({ ...filters, [key]: value })
  }

  const toggleArrayFilter = (key: keyof TalentSearchFilters, value: string) => {
    const currentArray = (filters[key] as string[]) || []
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value]
    updateFilter(key, newArray)
  }

  const skillCategories = [
    {
      name: 'Frontend Development',
      icon: Code,
      skills: ['React', 'Vue.js', 'Angular', 'TypeScript', 'JavaScript', 'HTML/CSS', 'Tailwind CSS', 'Next.js']
    },
    {
      name: 'Backend Development',
      icon: Database,
      skills: ['Node.js', 'Python', 'Java', 'C#', 'PHP', 'Ruby', 'Go', 'Rust']
    },
    {
      name: 'Design & UX',
      icon: Palette,
      skills: ['Figma', 'Adobe XD', 'Sketch', 'Photoshop', 'Illustrator', 'User Research', 'Prototyping', 'Design Systems']
    },
    {
      name: 'Mobile Development',
      icon: Smartphone,
      skills: ['React Native', 'Flutter', 'iOS', 'Android', 'Swift', 'Kotlin', 'Xamarin', 'Ionic']
    },
    {
      name: 'Data & AI',
      icon: Database,
      skills: ['Python', 'Machine Learning', 'TensorFlow', 'PyTorch', 'SQL', 'R', 'Data Science', 'AI/ML']
    },
    {
      name: 'DevOps & Cloud',
      icon: Globe,
      skills: ['AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes', 'CI/CD', 'Terraform', 'Jenkins']
    },
    {
      name: 'Blockchain & Web3',
      icon: Shield,
      skills: ['Solidity', 'Web3', 'Ethereum', 'Smart Contracts', 'DeFi', 'NFTs', 'Blockchain', 'Crypto']
    },
    {
      name: 'Human Resources',
      icon: Users,
      skills: ['Recruitment', 'Talent Acquisition', 'HRIS', 'Performance Management', 'Employee Relations', 'Compensation', 'Training & Development', 'HR Analytics']
    },
    {
      name: 'Accounting & Finance',
      icon: Calculator,
      skills: ['Financial Analysis', 'Bookkeeping', 'Tax Preparation', 'Auditing', 'QuickBooks', 'Excel', 'Financial Planning', 'Budget Management']
    },
    {
      name: 'Marketing & Sales',
      icon: Megaphone,
      skills: ['Digital Marketing', 'SEO/SEM', 'Social Media', 'Content Marketing', 'Sales Strategy', 'CRM', 'Email Marketing', 'Analytics']
    },
    {
      name: 'Legal & Compliance',
      icon: Scale,
      skills: ['Contract Law', 'Corporate Law', 'Compliance', 'Legal Research', 'Intellectual Property', 'Employment Law', 'Regulatory Affairs', 'Risk Management']
    },
    {
      name: 'Operations & Management',
      icon: Settings,
      skills: ['Project Management', 'Process Improvement', 'Supply Chain', 'Quality Assurance', 'Lean Six Sigma', 'Operations Strategy', 'Vendor Management', 'KPI Management']
    },
    {
      name: 'Healthcare & Medical',
      icon: Heart,
      skills: ['Clinical Research', 'Medical Writing', 'Healthcare Administration', 'Nursing', 'Telemedicine', 'Medical Coding', 'Patient Care', 'Healthcare IT']
    },
    {
      name: 'Business & Strategy',
      icon: TrendingUp,
      skills: ['Business Analysis', 'Strategic Planning', 'Market Research', 'Consulting', 'Business Development', 'Competitive Analysis', 'Financial Modeling', 'Growth Strategy']
    },
    {
      name: 'Real Estate & Construction',
      icon: Building,
      skills: ['Real Estate Sales', 'Property Management', 'Construction Management', 'Architecture', 'Project Planning', 'Building Inspection', 'Real Estate Law', 'Property Valuation']
    },
    {
      name: 'Content & Writing',
      icon: FileText,
      skills: ['Content Writing', 'Copywriting', 'Technical Writing', 'Journalism', 'Editing', 'Proofreading', 'Grant Writing', 'Creative Writing']
    }
  ]

  const locations = [
    'San Francisco, CA', 'New York, NY', 'Los Angeles, CA', 'Seattle, WA', 'Austin, TX',
    'Boston, MA', 'Chicago, IL', 'Denver, CO', 'Miami, FL', 'Toronto, ON',
    'London, UK', 'Berlin, Germany', 'Amsterdam, Netherlands', 'Remote', 'Anywhere'
  ]

  const availabilityOptions = [
    { value: 'available', label: 'Available Now', color: 'bg-green-500' },
    { value: 'open_to_opportunities', label: 'Open to Opportunities', color: 'bg-blue-500' },
    { value: 'employed', label: 'Currently Employed', color: 'bg-yellow-500' },
    { value: 'not_looking', label: 'Not Looking', color: 'bg-gray-500' }
  ]

  const experienceLevels = [
    'Entry Level', 'Junior Level', 'Mid Level', 'Senior Level', 'Lead Level', 'Executive'
  ]

  const jobTypes = [
    'full-time', 'part-time', 'contract', 'freelance', 'internship', 'temporary'
  ]

  const industries = [
    'Technology', 'Healthcare', 'Finance', 'Education', 'Retail', 'Manufacturing',
    'Real Estate', 'Marketing', 'Legal', 'Consulting', 'Non-profit', 'Government'
  ]

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const clearAllFilters = () => {
    onClearFilters()
  }

  const activeFilterCount =
    (filters.skills?.length || 0) +
    (filters.industries?.length || 0) +
    (filters.locations?.length || 0) +
    (filters.experienceLevels?.length || 0) +
    (filters.jobTypes?.length || 0) +
    (filters.workArrangements?.length || 0) +
    (filters.availability?.length || 0) +
    (filters.salaryRange?.min || filters.salaryRange?.max ? 1 : 0) +
    (filters.remote !== undefined ? 1 : 0) +
    (filters.verified !== undefined ? 1 : 0) +
    (filters.featured !== undefined ? 1 : 0) +
    (filters.minRating !== undefined ? 1 : 0)

  const FilterSection = ({
    title,
    icon: Icon,
    section,
    children
  }: {
    title: string
    icon: React.ComponentType<{ className?: string }>
    section: keyof typeof expandedSections
    children: React.ReactNode
  }) => (
    <div className="space-y-3">
      <Button
        variant="ghost"
        onClick={() => toggleSection(section)}
        className="w-full justify-between p-0 h-auto font-medium text-left hover:bg-transparent"
      >
        <div className="flex items-center space-x-2">
          <Icon className="w-4 h-4 text-primary" />
          <span>{title}</span>
        </div>
        {expandedSections[section] ? (
          <ChevronUp className="w-4 h-4" />
        ) : (
          <ChevronDown className="w-4 h-4" />
        )}
      </Button>
      
      <AnimatePresence>
        {expandedSections[section] && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
      
      <Separator />
    </div>
  )

  return (
    <Card className="card-premium">
      <CardHeader className="pb-4 md:pb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 md:w-5 md:h-5 text-primary" />
            <CardTitle className="text-base md:text-lg">Filters</CardTitle>
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="theme-glow text-xs">
                {activeFilterCount}
              </Badge>
            )}
          </div>
          {activeFilterCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="text-muted-foreground hover:text-foreground text-xs md:text-sm"
            >
              <X className="w-3 h-3 md:w-4 md:h-4 mr-1" />
              <span className="hidden sm:inline">Clear</span>
              <span className="sm:hidden">×</span>
            </Button>
          )}
        </div>
        <CardDescription className="text-xs md:text-sm">
          <span className="hidden md:inline">Find the perfect talent for your project</span>
          <span className="md:hidden">Filter talent</span>
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Skills Filter */}
        <FilterSection title="Skills & Expertise" icon={Code} section="skills">
          <div className="space-y-4">
            {skillCategories.map((category) => (
              <div key={category.name} className="space-y-3">
                <div className="flex items-center space-x-3 p-3 rounded-lg bg-gradient-to-r from-primary/15 to-primary/10 border border-primary/20">
                  <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                    <category.icon className="w-4 h-4 text-primary-foreground" />
                  </div>
                  <span className="font-semibold text-primary text-sm">{category.name}</span>
                </div>
                <div className="flex flex-wrap gap-2 pl-2">
                  {category.skills.map((skill) => (
                    <Badge
                      key={skill}
                      variant={(filters.skills || []).includes(skill) ? "default" : "outline"}
                      className={`cursor-pointer transition-all duration-200 ${
                        (filters.skills || []).includes(skill)
                          ? 'theme-glow bg-primary text-primary-foreground border-primary'
                          : 'hover:border-primary/50 hover:bg-primary/5'
                      }`}
                      onClick={() => toggleArrayFilter('skills', skill)}
                    >
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Experience Filter */}
        <FilterSection title="Experience Level" icon={Briefcase} section="experience">
          <div className="space-y-4">
            <div className="flex flex-wrap gap-2">
              {experienceLevels.map((level) => (
                <Badge
                  key={level}
                  variant={(filters.experienceLevels || []).includes(level) ? "default" : "outline"}
                  className={`cursor-pointer transition-all duration-200 ${
                    (filters.experienceLevels || []).includes(level)
                      ? 'theme-glow bg-primary text-primary-foreground border-primary'
                      : 'hover:border-primary/50 hover:bg-primary/5'
                  }`}
                  onClick={() => toggleArrayFilter('experienceLevels', level)}
                >
                  {level}
                </Badge>
              ))}
            </div>
          </div>
        </FilterSection>

        {/* Salary Filter */}
        <FilterSection title="Salary Expectation" icon={DollarSign} section="salary">
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label htmlFor="salary-min" className="text-xs">Min ($)</Label>
                <Input
                  id="salary-min"
                  type="number"
                  placeholder="30,000"
                  value={filters.salaryRange?.min || ''}
                  onChange={(e) => updateFilter('salaryRange', {
                    ...filters.salaryRange,
                    min: parseInt(e.target.value) || undefined
                  })}
                  className="input-enhanced"
                />
              </div>
              <div>
                <Label htmlFor="salary-max" className="text-xs">Max ($)</Label>
                <Input
                  id="salary-max"
                  type="number"
                  placeholder="200,000"
                  value={filters.salaryRange?.max || ''}
                  onChange={(e) => updateFilter('salaryRange', {
                    ...filters.salaryRange,
                    max: parseInt(e.target.value) || undefined
                  })}
                  className="input-enhanced"
                />
              </div>
            </div>
          </div>
        </FilterSection>

        {/* Location Filter */}
        <FilterSection title="Location" icon={MapPin} section="location">
          <div className="flex flex-wrap gap-2">
            {locations.map((location) => (
              <Badge
                key={location}
                variant={(filters.locations || []).includes(location) ? "default" : "outline"}
                className={`cursor-pointer transition-all duration-200 ${
                  (filters.locations || []).includes(location)
                    ? 'theme-glow bg-primary text-primary-foreground border-primary'
                    : 'hover:border-primary/50 hover:bg-primary/5'
                }`}
                onClick={() => toggleArrayFilter('locations', location)}
              >
                {location}
              </Badge>
            ))}
          </div>
        </FilterSection>

        {/* Availability Filter */}
        <FilterSection title="Availability" icon={Clock} section="availability">
          <div className="flex flex-wrap gap-2">
            {availabilityOptions.map((option) => (
              <Badge
                key={option.value}
                variant={(filters.availability || []).includes(option.value) ? "default" : "outline"}
                className={`cursor-pointer transition-all duration-200 ${
                  (filters.availability || []).includes(option.value)
                    ? 'theme-glow bg-primary text-primary-foreground border-primary'
                    : 'hover:border-primary/50 hover:bg-primary/5'
                }`}
                onClick={() => toggleArrayFilter('availability', option.value)}
              >
                <div className={`w-2 h-2 rounded-full mr-2 ${option.color}`} />
                {option.label}
              </Badge>
            ))}
          </div>
        </FilterSection>

        {/* Industries Filter */}
        <FilterSection title="Industries" icon={Building} section="industries">
          <div className="flex flex-wrap gap-2">
            {industries.map((industry) => (
              <Badge
                key={industry}
                variant={(filters.industries || []).includes(industry) ? "default" : "outline"}
                className={`cursor-pointer transition-all duration-200 ${
                  (filters.industries || []).includes(industry)
                    ? 'theme-glow bg-primary text-primary-foreground border-primary'
                    : 'hover:border-primary/50 hover:bg-primary/5'
                }`}
                onClick={() => toggleArrayFilter('industries', industry)}
              >
                {industry}
              </Badge>
            ))}
          </div>
        </FilterSection>

        {/* Job Types Filter */}
        <FilterSection title="Job Types" icon={Briefcase} section="jobTypes">
          <div className="flex flex-wrap gap-2">
            {jobTypes.map((jobType) => (
              <Badge
                key={jobType}
                variant={(filters.jobTypes || []).includes(jobType) ? "default" : "outline"}
                className={`cursor-pointer transition-all duration-200 ${
                  (filters.jobTypes || []).includes(jobType)
                    ? 'theme-glow bg-primary text-primary-foreground border-primary'
                    : 'hover:border-primary/50 hover:bg-primary/5'
                }`}
                onClick={() => toggleArrayFilter('jobTypes', jobType)}
              >
                {jobType.charAt(0).toUpperCase() + jobType.slice(1)}
              </Badge>
            ))}
          </div>
        </FilterSection>

        {/* Rating Filter */}
        <FilterSection title="Minimum Rating" icon={Star} section="rating">
          <div className="space-y-4">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Rating</span>
              <span className="font-medium">
                {filters.minRating ? `${filters.minRating}+ stars` : 'Any rating'}
              </span>
            </div>
            <Slider
              value={[filters.minRating || 0]}
              onValueChange={(value) => updateFilter('minRating', value[0] || undefined)}
              max={5}
              min={0}
              step={0.5}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Any</span>
              <span>3+</span>
              <span>4+</span>
              <span>5 stars</span>
            </div>
          </div>
        </FilterSection>

        {/* Quick Filters */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Zap className="w-4 h-4 text-primary" />
            <span className="font-medium">Quick Filters</span>
          </div>
          <div className="flex flex-wrap gap-2">
            <Badge
              variant={filters.remote ? "default" : "outline"}
              className={`cursor-pointer transition-all duration-200 ${
                filters.remote
                  ? 'theme-glow bg-primary text-primary-foreground border-primary'
                  : 'hover:border-primary/50 hover:bg-primary/5'
              }`}
              onClick={() => updateFilter('remote', !filters.remote)}
            >
              Remote Only
            </Badge>
            <Badge
              variant={filters.verified ? "default" : "outline"}
              className={`cursor-pointer transition-all duration-200 ${
                filters.verified
                  ? 'theme-glow bg-primary text-primary-foreground border-primary'
                  : 'hover:border-primary/50 hover:bg-primary/5'
              }`}
              onClick={() => updateFilter('verified', !filters.verified)}
            >
              Verified Only
            </Badge>
            <Badge
              variant={filters.featured ? "default" : "outline"}
              className={`cursor-pointer transition-all duration-200 ${
                filters.featured
                  ? 'theme-glow bg-primary text-primary-foreground border-primary'
                  : 'hover:border-primary/50 hover:bg-primary/5'
              }`}
              onClick={() => updateFilter('featured', !filters.featured)}
            >
              Featured
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

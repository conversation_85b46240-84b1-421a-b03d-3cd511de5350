"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Clock, 
  Users, 
  Star, 
  Play,
  BookOpen,
  Award,
  Heart,
  Share2,
  DollarSign,
  TrendingUp
} from "lucide-react"
import { ICourse } from "@/lib/models/course.model"
import { cn } from "@/lib/utils"

interface CourseCardProps {
  course: ICourse
  viewMode?: 'grid' | 'list'
  showEnrollButton?: boolean
  isEnrolled?: boolean
}

export function CourseCard({ 
  course, 
  viewMode = 'grid', 
  showEnrollButton = true,
  isEnrolled = false 
}: CourseCardProps) {
  const [isLiked, setIsLiked] = useState(false)
  const [imageError, setImageError] = useState(false)

  const formatPrice = (price: number) => {
    if (price === 0) return 'Free'
    return `$${price.toFixed(2)}`
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins > 0 ? `${mins}m` : ''}`
    }
    return `${mins}m`
  }

  const getLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'beginner': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'intermediate': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'advanced': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'expert': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const handleShare = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    if (navigator.share) {
      navigator.share({
        title: course.title,
        text: course.shortDescription,
        url: `/courses/${course.slug}`
      })
    } else {
      navigator.clipboard.writeText(`${window.location.origin}/courses/${course.slug}`)
    }
  }

  const handleLike = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsLiked(!isLiked)
  }

  if (viewMode === 'list') {
    return (
      <motion.div
        whileHover={{ y: -2 }}
        transition={{ duration: 0.2 }}
      >
        <Link href={`/courses/${course.slug}`}>
          <Card className="glass hover:shadow-xl transition-all duration-300 border-border/50 hover:border-primary/20">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-6">
                {/* Thumbnail */}
                <div className="relative w-full md:w-64 h-48 md:h-36 rounded-lg overflow-hidden flex-shrink-0">
                  {!imageError ? (
                    <Image
                      src={course.thumbnail}
                      alt={course.title}
                      fill
                      className="object-cover"
                      onError={() => setImageError(true)}
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-primary/10 to-primary/20 flex items-center justify-center">
                      <BookOpen className="w-12 h-12 text-primary/50" />
                    </div>
                  )}
                  
                  {course.isFeatured && (
                    <Badge className="absolute top-2 left-2 bg-yellow-500 text-yellow-900">
                      <Award className="w-3 h-3 mr-1" />
                      Featured
                    </Badge>
                  )}
                  
                  {course.isPremium && (
                    <Badge className="absolute top-2 right-2 bg-purple-500 text-white">
                      Premium
                    </Badge>
                  )}
                </div>

                {/* Content */}
                <div className="flex-1 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold line-clamp-2 mb-2">{course.title}</h3>
                      <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
                        {course.shortDescription}
                      </p>
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleLike}
                        className="p-2"
                      >
                        <Heart className={cn(
                          "w-4 h-4",
                          isLiked ? "fill-red-500 text-red-500" : "text-muted-foreground"
                        )} />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleShare}
                        className="p-2"
                      >
                        <Share2 className="w-4 h-4 text-muted-foreground" />
                      </Button>
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-1">
                    <Badge className={getLevelColor(course.level)}>
                      {course.level}
                    </Badge>
                    {course.tags.slice(0, 3).map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {course.tags.length > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{course.tags.length - 3}
                      </Badge>
                    )}
                  </div>

                  {/* Stats */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4" />
                        <span>{formatDuration(course.duration)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Users className="w-4 h-4" />
                        <span>{course.totalStudents.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        <span>{course.rating.toFixed(1)}</span>
                        <span>({course.totalRatings})</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <div className="text-right">
                        {course.originalPrice && course.originalPrice > course.price && (
                          <p className="text-sm text-muted-foreground line-through">
                            ${course.originalPrice.toFixed(2)}
                          </p>
                        )}
                        <p className="text-lg font-bold text-primary">
                          {formatPrice(course.price)}
                        </p>
                      </div>
                      
                      {showEnrollButton && (
                        <Button size="sm" className="min-w-[100px]">
                          {isEnrolled ? (
                            <>
                              <Play className="w-4 h-4 mr-2" />
                              Continue
                            </>
                          ) : (
                            <>
                              <BookOpen className="w-4 h-4 mr-2" />
                              Enroll
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>
      </motion.div>
    )
  }

  // Grid view
  return (
    <motion.div
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
    >
      <Link href={`/courses/${course.slug}`}>
        <Card className="glass hover:shadow-xl transition-all duration-300 border-border/50 hover:border-primary/20 h-full">
          {/* Thumbnail */}
          <div className="relative h-48 rounded-t-lg overflow-hidden">
            {!imageError ? (
              <Image
                src={course.thumbnail}
                alt={course.title}
                fill
                className="object-cover"
                onError={() => setImageError(true)}
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-primary/10 to-primary/20 flex items-center justify-center">
                <BookOpen className="w-12 h-12 text-primary/50" />
              </div>
            )}
            
            {course.isFeatured && (
              <Badge className="absolute top-2 left-2 bg-yellow-500 text-yellow-900">
                <Award className="w-3 h-3 mr-1" />
                Featured
              </Badge>
            )}
            
            {course.isPremium && (
              <Badge className="absolute top-2 right-2 bg-purple-500 text-white">
                Premium
              </Badge>
            )}

            <div className="absolute top-2 right-2 flex space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleLike}
                className="p-1 bg-black/20 backdrop-blur-sm hover:bg-black/40"
              >
                <Heart className={cn(
                  "w-4 h-4",
                  isLiked ? "fill-red-500 text-red-500" : "text-white"
                )} />
              </Button>
            </div>
          </div>

          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <h3 className="text-lg font-semibold line-clamp-2 flex-1">{course.title}</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleShare}
                className="p-1 ml-2"
              >
                <Share2 className="w-4 h-4 text-muted-foreground" />
              </Button>
            </div>
            <p className="text-sm text-muted-foreground line-clamp-2">
              {course.shortDescription}
            </p>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Tags */}
            <div className="flex flex-wrap gap-1">
              <Badge className={getLevelColor(course.level)}>
                {course.level}
              </Badge>
              {course.tags.slice(0, 2).map((tag) => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {course.tags.length > 2 && (
                <Badge variant="secondary" className="text-xs">
                  +{course.tags.length - 2}
                </Badge>
              )}
            </div>

            {/* Stats */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>{formatDuration(course.duration)}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Users className="w-4 h-4" />
                  <span>{course.totalStudents.toLocaleString()}</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1 text-sm">
                  <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  <span>{course.rating.toFixed(1)}</span>
                  <span className="text-muted-foreground">({course.totalRatings})</span>
                </div>
                
                <div className="text-right">
                  {course.originalPrice && course.originalPrice > course.price && (
                    <p className="text-sm text-muted-foreground line-through">
                      ${course.originalPrice.toFixed(2)}
                    </p>
                  )}
                  <p className="text-lg font-bold text-primary">
                    {formatPrice(course.price)}
                  </p>
                </div>
              </div>
            </div>

            {/* Enroll Button */}
            {showEnrollButton && (
              <Button className="w-full" size="sm">
                {isEnrolled ? (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Continue Learning
                  </>
                ) : (
                  <>
                    <BookOpen className="w-4 h-4 mr-2" />
                    Enroll Now
                  </>
                )}
              </Button>
            )}
          </CardContent>
        </Card>
      </Link>
    </motion.div>
  )
}

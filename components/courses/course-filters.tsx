"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { <PERSON>lider } from "@/components/ui/slider"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  Filter,
  DollarSign,
  Clock,
  Star,
  BookOpen,
  Users,
  Award,
  X
} from "lucide-react"
import { ICourseCategory } from "@/lib/models/course.model"
import { CourseFilters as CourseFiltersType } from "@/lib/services/course.service"

interface CourseFiltersProps {
  categories: ICourseCategory[]
  filters: CourseFiltersType
  onFilterChange: (filters: Partial<CourseFiltersType>) => void
}

export function CourseFilters({ categories, filters, onFilterChange }: CourseFiltersProps) {
  const [priceRange, set<PERSON>riceRange] = useState([0, 500])
  const [selectedTags, setSelectedTags] = useState<string[]>(filters.tags || [])

  const levels = [
    { value: 'beginner', label: 'Beginner', icon: '🌱' },
    { value: 'intermediate', label: 'Intermediate', icon: '🌿' },
    { value: 'advanced', label: 'Advanced', icon: '🌳' },
    { value: 'expert', label: 'Expert', icon: '🏆' }
  ]

  const durations = [
    { value: '0-2', label: 'Under 2 hours' },
    { value: '2-5', label: '2-5 hours' },
    { value: '5-10', label: '5-10 hours' },
    { value: '10+', label: '10+ hours' }
  ]

  const languages = [
    { value: 'english', label: 'English' },
    { value: 'spanish', label: 'Spanish' },
    { value: 'french', label: 'French' },
    { value: 'german', label: 'German' },
    { value: 'chinese', label: 'Chinese' }
  ]

  const popularTags = [
    'JavaScript', 'React', 'Python', 'Node.js', 'TypeScript',
    'Machine Learning', 'Data Science', 'UI/UX', 'Mobile',
    'DevOps', 'Cloud', 'Blockchain', 'AI', 'Cybersecurity'
  ]

  const handleCategoryChange = (categoryId: string, checked: boolean) => {
    const currentCategories = filters.category ? [filters.category] : []
    let newCategories: string[]
    
    if (checked) {
      newCategories = [...currentCategories, categoryId]
    } else {
      newCategories = currentCategories.filter(id => id !== categoryId)
    }
    
    onFilterChange({ category: newCategories[0] || undefined })
  }

  const handleLevelChange = (level: string, checked: boolean) => {
    onFilterChange({ level: checked ? level : undefined })
  }

  const handlePriceTypeChange = (priceType: string) => {
    onFilterChange({ price: priceType as 'free' | 'paid' | 'premium' })
  }

  const handleDurationChange = (duration: string) => {
    onFilterChange({ duration })
  }

  const handleLanguageChange = (language: string) => {
    onFilterChange({ language })
  }

  const handleRatingChange = (rating: number[]) => {
    onFilterChange({ rating: rating[0] })
  }

  const handleTagToggle = (tag: string) => {
    const newTags = selectedTags.includes(tag)
      ? selectedTags.filter(t => t !== tag)
      : [...selectedTags, tag]
    
    setSelectedTags(newTags)
    onFilterChange({ tags: newTags.length > 0 ? newTags : undefined })
  }

  const handleFeaturedChange = (checked: boolean) => {
    onFilterChange({ featured: checked || undefined })
  }

  const clearAllFilters = () => {
    setSelectedTags([])
    setPriceRange([0, 500])
    onFilterChange({
      category: undefined,
      level: undefined,
      price: undefined,
      rating: undefined,
      duration: undefined,
      language: undefined,
      tags: undefined,
      featured: undefined
    })
  }

  return (
    <Card className="glass border-border/50">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center text-lg">
            <Filter className="w-5 h-5 mr-2 text-primary" />
            Filters
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={clearAllFilters}>
            <X className="w-4 h-4 mr-1" />
            Clear All
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Categories */}
        <div>
          <h4 className="font-medium mb-3 flex items-center">
            <BookOpen className="w-4 h-4 mr-2" />
            Categories
          </h4>
          <div className="space-y-2">
            {categories.map((category) => (
              <div key={category._id.toString()} className="flex items-center space-x-2">
                <Checkbox
                  id={`category-${category._id}`}
                  checked={filters.category === category._id.toString()}
                  onCheckedChange={(checked) => 
                    handleCategoryChange(category._id.toString(), checked as boolean)
                  }
                />
                <Label 
                  htmlFor={`category-${category._id}`}
                  className="flex items-center space-x-2 cursor-pointer"
                >
                  <span>{category.icon}</span>
                  <span>{category.name}</span>
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Level */}
        <div>
          <h4 className="font-medium mb-3 flex items-center">
            <Award className="w-4 h-4 mr-2" />
            Level
          </h4>
          <div className="space-y-2">
            {levels.map((level) => (
              <div key={level.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`level-${level.value}`}
                  checked={filters.level === level.value}
                  onCheckedChange={(checked) => 
                    handleLevelChange(level.value, checked as boolean)
                  }
                />
                <Label 
                  htmlFor={`level-${level.value}`}
                  className="flex items-center space-x-2 cursor-pointer"
                >
                  <span>{level.icon}</span>
                  <span>{level.label}</span>
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Price */}
        <div>
          <h4 className="font-medium mb-3 flex items-center">
            <DollarSign className="w-4 h-4 mr-2" />
            Price
          </h4>
          <Select value={filters.price || ''} onValueChange={handlePriceTypeChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select price range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Prices</SelectItem>
              <SelectItem value="free">Free</SelectItem>
              <SelectItem value="paid">Paid</SelectItem>
              <SelectItem value="premium">Premium</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Duration */}
        <div>
          <h4 className="font-medium mb-3 flex items-center">
            <Clock className="w-4 h-4 mr-2" />
            Duration
          </h4>
          <Select value={filters.duration || ''} onValueChange={handleDurationChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select duration" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Any Duration</SelectItem>
              {durations.map((duration) => (
                <SelectItem key={duration.value} value={duration.value}>
                  {duration.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Rating */}
        <div>
          <h4 className="font-medium mb-3 flex items-center">
            <Star className="w-4 h-4 mr-2" />
            Minimum Rating
          </h4>
          <div className="space-y-2">
            <Slider
              value={[filters.rating || 0]}
              onValueChange={handleRatingChange}
              max={5}
              min={0}
              step={0.5}
              className="w-full"
            />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>0</span>
              <span className="font-medium">
                {filters.rating ? `${filters.rating}+ stars` : 'Any rating'}
              </span>
              <span>5</span>
            </div>
          </div>
        </div>

        {/* Language */}
        <div>
          <h4 className="font-medium mb-3 flex items-center">
            <Users className="w-4 h-4 mr-2" />
            Language
          </h4>
          <Select value={filters.language || ''} onValueChange={handleLanguageChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select language" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Any Language</SelectItem>
              {languages.map((language) => (
                <SelectItem key={language.value} value={language.value}>
                  {language.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Tags */}
        <div>
          <h4 className="font-medium mb-3">Popular Skills</h4>
          <div className="flex flex-wrap gap-2">
            {popularTags.map((tag) => (
              <Badge
                key={tag}
                variant={selectedTags.includes(tag) ? "default" : "secondary"}
                className="cursor-pointer hover:bg-primary/80 transition-colors"
                onClick={() => handleTagToggle(tag)}
              >
                {tag}
              </Badge>
            ))}
          </div>
        </div>

        {/* Featured */}
        <div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="featured"
              checked={filters.featured || false}
              onCheckedChange={handleFeaturedChange}
            />
            <Label htmlFor="featured" className="cursor-pointer">
              Featured courses only
            </Label>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

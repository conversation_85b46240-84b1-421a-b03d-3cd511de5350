'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { 
  Play,
  Clock,
  Users,
  Star,
  Award,
  CheckCircle,
  CreditCard,
  Gift,
  Lock,
  Unlock,
  BookOpen,
  TrendingUp
} from 'lucide-react'
import { useAuthStore } from '@/stores/auth.store'
import { useToast } from '@/hooks/use-toast'
import { ICourse } from '@/lib/models/course.model'

interface CourseEnrollmentProps {
  course: ICourse
}

interface EnrollmentData {
  isEnrolled: boolean
  enrollment?: {
    _id: string
    progress: number
    enrolledAt: string
    lastAccessedAt: string
    completedAt?: string
    paymentStatus: string
  }
}

export function CourseEnrollment({ course }: CourseEnrollmentProps) {
  const [enrollmentData, setEnrollmentData] = useState<EnrollmentData>({ isEnrolled: false })
  const [isLoading, setIsLoading] = useState(true)
  const [isEnrolling, setIsEnrolling] = useState(false)
  const [showEnrollDialog, setShowEnrollDialog] = useState(false)
  
  const { user, isAuthenticated } = useAuthStore()
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    if (isAuthenticated && user) {
      checkEnrollmentStatus()
    } else {
      setIsLoading(false)
    }
  }, [isAuthenticated, user, course.slug])

  const checkEnrollmentStatus = async () => {
    try {
      const { token } = useAuthStore.getState()
      const headers: Record<string, string> = {}

      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch(`/api/v1/courses/${course.slug}/enroll`, {
        headers
      })

      if (response.ok) {
        const result = await response.json()
        setEnrollmentData(result.data)
      }
    } catch (error) {
      console.error('Error checking enrollment status:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleEnroll = async (paymentMethod: 'free' | 'card' = 'free') => {
    if (!isAuthenticated) {
      router.push(`/login?redirect=/courses/${course.slug}`)
      return
    }

    setIsEnrolling(true)

    try {
      const { token } = useAuthStore.getState()
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch(`/api/v1/courses/${course.slug}/enroll`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          paymentMethod,
          amount: course.price,
          courseId: course._id
        })
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "Enrollment Successful!",
          description: "You have been enrolled in the course. Start learning now!"
        })
        setEnrollmentData({ isEnrolled: true, enrollment: result.data.enrollment })
        setShowEnrollDialog(false)
      } else {
        toast({
          title: "Enrollment Failed",
          description: result.error || 'Failed to enroll in course',
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: 'An unexpected error occurred',
        variant: "destructive"
      })
    } finally {
      setIsEnrolling(false)
    }
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`
  }

  const formatPrice = (price: number) => {
    return price === 0 ? 'Free' : `$${price.toFixed(2)}`
  }

  const getDiscountPercentage = () => {
    if (course.originalPrice && course.originalPrice > course.price) {
      return Math.round(((course.originalPrice - course.price) / course.originalPrice) * 100)
    }
    return 0
  }

  if (isLoading) {
    return (
      <Card className="w-full max-w-sm">
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-8 bg-muted rounded"></div>
            <div className="h-10 bg-muted rounded"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-sm sticky top-6">
      <CardHeader className="pb-4">
        <div className="aspect-video relative overflow-hidden rounded-lg bg-muted">
          <img 
            src={course.thumbnail} 
            alt={course.title}
            className="object-cover w-full h-full"
          />
          <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
            <Button size="lg" variant="secondary" className="rounded-full">
              <Play className="w-6 h-6 mr-2" />
              Preview
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Pricing */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <span className="text-3xl font-bold">
              {formatPrice(course.price)}
            </span>
            {course.originalPrice && course.originalPrice > course.price && (
              <>
                <span className="text-lg text-muted-foreground line-through">
                  ${course.originalPrice.toFixed(2)}
                </span>
                <Badge variant="destructive">
                  {getDiscountPercentage()}% OFF
                </Badge>
              </>
            )}
          </div>
          {course.price > 0 && (
            <p className="text-sm text-muted-foreground">
              30-day money-back guarantee
            </p>
          )}
        </div>

        {/* Enrollment Status / Action */}
        {enrollmentData.isEnrolled ? (
          <div className="space-y-4">
            <div className="flex items-center space-x-2 text-green-600">
              <CheckCircle className="w-5 h-5" />
              <span className="font-medium">Enrolled</span>
            </div>
            
            {enrollmentData.enrollment && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Progress</span>
                  <span>{enrollmentData.enrollment.progress}%</span>
                </div>
                <Progress value={enrollmentData.enrollment.progress} />
              </div>
            )}

            <Button className="w-full" size="lg">
              <Play className="w-4 h-4 mr-2" />
              Continue Learning
            </Button>
          </div>
        ) : (
          <div className="space-y-3">
            {course.price === 0 ? (
              <Button 
                className="w-full" 
                size="lg"
                onClick={() => handleEnroll('free')}
                disabled={isEnrolling}
              >
                <Gift className="w-4 h-4 mr-2" />
                {isEnrolling ? 'Enrolling...' : 'Enroll for Free'}
              </Button>
            ) : (
              <>
                <Dialog open={showEnrollDialog} onOpenChange={setShowEnrollDialog}>
                  <DialogTrigger asChild>
                    <Button className="w-full" size="lg">
                      <CreditCard className="w-4 h-4 mr-2" />
                      Enroll Now
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Enroll in {course.title}</DialogTitle>
                      <DialogDescription>
                        Choose your enrollment option
                      </DialogDescription>
                    </DialogHeader>
                    
                    <div className="space-y-4 py-4">
                      <div className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium">Full Course Access</span>
                          <span className="text-xl font-bold">{formatPrice(course.price)}</span>
                        </div>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          <li>• Lifetime access to course content</li>
                          <li>• Certificate of completion</li>
                          <li>• 30-day money-back guarantee</li>
                          <li>• Access to course community</li>
                        </ul>
                      </div>
                    </div>

                    <DialogFooter>
                      <Button 
                        variant="outline" 
                        onClick={() => setShowEnrollDialog(false)}
                      >
                        Cancel
                      </Button>
                      <Button 
                        onClick={() => handleEnroll('card')}
                        disabled={isEnrolling}
                      >
                        {isEnrolling ? 'Processing...' : `Pay ${formatPrice(course.price)}`}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>

                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => handleEnroll('free')}
                  disabled={isEnrolling}
                >
                  <BookOpen className="w-4 h-4 mr-2" />
                  {isEnrolling ? 'Enrolling...' : 'Try Free Preview'}
                </Button>
              </>
            )}
          </div>
        )}

        <Separator />

        {/* Course Info */}
        <div className="space-y-3">
          <h4 className="font-medium">This course includes:</h4>
          <div className="space-y-2 text-sm">
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4 text-muted-foreground" />
              <span>{formatDuration(course.duration)} on-demand video</span>
            </div>
            <div className="flex items-center space-x-2">
              <BookOpen className="w-4 h-4 text-muted-foreground" />
              <span>{course.totalLessons} lessons</span>
            </div>
            <div className="flex items-center space-x-2">
              <Award className="w-4 h-4 text-muted-foreground" />
              <span>Certificate of completion</span>
            </div>
            <div className="flex items-center space-x-2">
              <Unlock className="w-4 h-4 text-muted-foreground" />
              <span>Full lifetime access</span>
            </div>
          </div>
        </div>

        <Separator />

        {/* Course Stats */}
        <div className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-1">
              <Users className="w-4 h-4 text-muted-foreground" />
              <span>{course.totalStudents.toLocaleString()} students</span>
            </div>
            <div className="flex items-center space-x-1">
              <Star className="w-4 h-4 text-yellow-500 fill-current" />
              <span>{course.rating.toFixed(1)} ({course.totalRatings} reviews)</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-1 text-sm">
            <TrendingUp className="w-4 h-4 text-muted-foreground" />
            <span>Level: {course.level.charAt(0).toUpperCase() + course.level.slice(1)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

# Companies Frontend Integration with Zustand and Backend Implementation Guide

## Overview

This guide provides a comprehensive implementation plan for integrating the `/companies` page with real backend data using Zustand stores, replacing static placeholder data with dynamic, efficient frontend services.

## Current Frontend Structure Analysis

### Page Structure (`app/(public)/companies/page.tsx`)
- **Hero Section**: Stats display (5,000+ Companies, 50+ Industries, 10,000+ Open Positions)
- **Search & Filters**: Search bar, desktop/mobile filters, sorting, view mode toggle
- **Main Content**: Company grid/list with pagination
- **Components Used**:
  - `CompanyFilters` - Advanced filtering system
  - `CompanyCard` - Company display cards (grid/list views)
  - `CompanyDetailModal` - Quick view modal
  - `TalentPagination` - Pagination controls

### Current Filter Structure
```typescript
// Filter States
selectedIndustries: string[]
companySizeRange: [number, number] // [1, 10000]
foundedRange: [number, number] // [1900, 2024]
selectedLocation: string
selectedBenefits: string[]
minRating: number
```

### Current Company Data Structure (Static)
```typescript
interface Company {
  id: number
  name: string
  industry: string
  location: string
  logo: string
  rating: number
  size: string
  founded: number
  description: string
  specialties: string[]
  openJobs: number
  followers: number
  website: string
  benefits: string[]
  culture: string
  verified: boolean
}
```

## Backend Data Structure Analysis

### Database Company Model (`lib/models/company.model.ts`)
```typescript
interface ICompany {
  _id: ObjectId
  name: string
  slug: string
  description: string
  logo?: string
  website?: string
  industry: string[]
  size: 'startup' | 'small' | 'medium' | 'large' | 'enterprise'
  founded?: number
  locations: Array<{
    city: string
    state?: string
    country: string
    isHeadquarters: boolean
  }>
  contact: {
    email: string
    phone?: string
  }
  culture: {
    values?: string[]
    benefits?: string[]
    workEnvironment?: string
    mission?: string
    vision?: string
    perks?: string[]
  }
  socialLinks: {
    linkedin?: string
    twitter?: string
    facebook?: string
    instagram?: string
    github?: string
  }
  stats: {
    totalJobs: number
    activeJobs: number
    totalApplications: number
    profileViews: number
    followerCount: number
  }
  verification: {
    isVerified: boolean
    verifiedAt?: Date
  }
  specialties?: string[]
  technologies?: string[]
  funding?: {
    stage: string
    amount?: number
    investors?: string[]
  }
  isActive: boolean
  isFeatured: boolean
  createdAt: Date
  updatedAt: Date
}
```

## Implementation Plan

### 1. Enhanced Zustand Store for Companies Frontend

Create `stores/companies-frontend.store.ts`:

```typescript
import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'

// Frontend-optimized types
export interface CompanyFrontend {
  id: string
  name: string
  slug: string
  description: string
  logo?: string
  website?: string
  industries: string[]
  size: CompanySize
  founded?: number
  primaryLocation: {
    city: string
    state?: string
    country: string
    displayName: string
  }
  allLocations: CompanyLocation[]
  rating: number
  stats: {
    activeJobs: number
    totalApplications: number
    followerCount: number
    profileViews: number
  }
  culture: {
    benefits: string[]
    values: string[]
    workEnvironment?: string
  }
  specialties: string[]
  technologies: string[]
  socialLinks: {
    linkedin?: string
    twitter?: string
    github?: string
  }
  verification: {
    isVerified: boolean
    verifiedAt?: string
  }
  funding?: {
    stage: FundingStage
    amount?: number
    investors: string[]
  }
  isActive: boolean
  isFeatured: boolean
  createdAt: string
  updatedAt: string
}

export type CompanySize = 'startup' | 'small' | 'medium' | 'large' | 'enterprise'
export type FundingStage = 'pre_seed' | 'seed' | 'series_a' | 'series_b' | 'series_c' | 'ipo' | 'acquired'

export interface CompanyLocation {
  city: string
  state?: string
  country: string
  isHeadquarters: boolean
  displayName: string
}

export interface CompanySearchFilters {
  industries: string[]
  sizes: CompanySize[]
  locations: string[]
  foundedRange: [number, number]
  employeeRange: [number, number]
  fundingStages: FundingStage[]
  benefits: string[]
  technologies: string[]
  isVerified?: boolean
  isFeatured?: boolean
  hasActiveJobs?: boolean
  minRating?: number
}

export interface CompanySearchQuery {
  search?: string
  filters: Partial<CompanySearchFilters>
  sortBy: 'relevance' | 'name' | 'founded' | 'size' | 'jobs' | 'rating'
  sortOrder: 'asc' | 'desc'
  page: number
  limit: number
}

export interface CompanySearchResult {
  companies: CompanyFrontend[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  aggregations: {
    industries: Array<{ name: string; count: number }>
    sizes: Array<{ size: CompanySize; count: number }>
    locations: Array<{ location: string; count: number }>
    fundingStages: Array<{ stage: FundingStage; count: number }>
  }
  searchMeta: {
    query: string
    totalResults: number
    searchTime: number
  }
}

interface CompaniesState {
  // Data
  companies: CompanyFrontend[]
  currentCompany: CompanyFrontend | null
  searchResult: CompanySearchResult | null
  followedCompanies: Set<string>
  savedSearches: CompanySearchQuery[]
  
  // Search & Filters
  searchQuery: CompanySearchQuery
  activeFilters: Partial<CompanySearchFilters>
  quickFilters: {
    trending: boolean
    hiring: boolean
    verified: boolean
    remote: boolean
  }
  
  // UI State
  viewMode: 'grid' | 'list'
  showFilters: boolean
  selectedCompanyId: string | null
  
  // Loading States
  isLoading: boolean
  isSearching: boolean
  isLoadingMore: boolean
  isFollowing: boolean
  
  // Error States
  error: string | null
  searchError: string | null
  
  // Cache
  lastSearchTime: number
  cacheExpiry: number
}

interface CompaniesActions {
  // Search & Filter Actions
  searchCompanies: (query: Partial<CompanySearchQuery>) => Promise<void>
  updateFilters: (filters: Partial<CompanySearchFilters>) => void
  clearFilters: () => void
  applyQuickFilter: (filter: keyof CompaniesState['quickFilters']) => void
  
  // Company Actions
  getCompanyById: (id: string) => Promise<CompanyFrontend | null>
  followCompany: (companyId: string) => Promise<void>
  unfollowCompany: (companyId: string) => Promise<void>
  
  // UI Actions
  setViewMode: (mode: 'grid' | 'list') => void
  toggleFilters: () => void
  selectCompany: (companyId: string | null) => void
  
  // Pagination
  loadMore: () => Promise<void>
  goToPage: (page: number) => Promise<void>
  
  // Cache Management
  clearCache: () => void
  refreshData: () => Promise<void>
  
  // Saved Searches
  saveSearch: (query: CompanySearchQuery, name: string) => void
  loadSavedSearch: (index: number) => void
  deleteSavedSearch: (index: number) => void
}
```

### 2. Frontend Service Layer

Create `lib/services/companies-frontend.service.ts`:

```typescript
import { CompanySearchQuery, CompanySearchResult, CompanyFrontend } from '@/stores/companies-frontend.store'

export class CompaniesFrontendService {
  private baseUrl = '/api/v1/companies'
  private cache = new Map<string, { data: any; timestamp: number }>()
  private cacheExpiry = 5 * 60 * 1000 // 5 minutes

  async searchCompanies(query: CompanySearchQuery): Promise<CompanySearchResult> {
    const cacheKey = this.generateCacheKey('search', query)
    const cached = this.getFromCache(cacheKey)
    if (cached) return cached

    const params = this.buildSearchParams(query)
    const response = await fetch(`${this.baseUrl}/search?${params}`)
    
    if (!response.ok) {
      throw new Error(`Search failed: ${response.statusText}`)
    }
    
    const data = await response.json()
    const result = this.transformSearchResult(data)
    
    this.setCache(cacheKey, result)
    return result
  }

  async getCompanyById(id: string): Promise<CompanyFrontend> {
    const cacheKey = this.generateCacheKey('company', { id })
    const cached = this.getFromCache(cacheKey)
    if (cached) return cached

    const response = await fetch(`${this.baseUrl}/${id}`)
    
    if (!response.ok) {
      throw new Error(`Failed to fetch company: ${response.statusText}`)
    }
    
    const data = await response.json()
    const company = this.transformCompany(data.company)
    
    this.setCache(cacheKey, company)
    return company
  }

  async followCompany(companyId: string): Promise<void> {
    const token = this.getAuthToken()
    const response = await fetch(`${this.baseUrl}/${companyId}/follow`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`Failed to follow company: ${response.statusText}`)
    }

    // Invalidate relevant caches
    this.invalidateCompanyCache(companyId)
  }

  async unfollowCompany(companyId: string): Promise<void> {
    const token = this.getAuthToken()
    const response = await fetch(`${this.baseUrl}/${companyId}/follow`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`Failed to unfollow company: ${response.statusText}`)
    }

    // Invalidate relevant caches
    this.invalidateCompanyCache(companyId)
  }

  private transformCompany(backendCompany: any): CompanyFrontend {
    const primaryLocation = backendCompany.locations?.find((loc: any) => loc.isHeadquarters) 
      || backendCompany.locations?.[0]

    return {
      id: backendCompany._id,
      name: backendCompany.name,
      slug: backendCompany.slug,
      description: backendCompany.description,
      logo: backendCompany.logo,
      website: backendCompany.website,
      industries: backendCompany.industry || [],
      size: backendCompany.size,
      founded: backendCompany.founded,
      primaryLocation: primaryLocation ? {
        city: primaryLocation.city,
        state: primaryLocation.state,
        country: primaryLocation.country,
        displayName: this.formatLocationDisplay(primaryLocation)
      } : {
        city: 'Unknown',
        country: 'Unknown',
        displayName: 'Location not specified'
      },
      allLocations: (backendCompany.locations || []).map((loc: any) => ({
        city: loc.city,
        state: loc.state,
        country: loc.country,
        isHeadquarters: loc.isHeadquarters,
        displayName: this.formatLocationDisplay(loc)
      })),
      rating: this.calculateCompanyRating(backendCompany),
      stats: {
        activeJobs: backendCompany.stats?.activeJobs || 0,
        totalApplications: backendCompany.stats?.totalApplications || 0,
        followerCount: backendCompany.stats?.followerCount || 0,
        profileViews: backendCompany.stats?.profileViews || 0
      },
      culture: {
        benefits: backendCompany.culture?.benefits || [],
        values: backendCompany.culture?.values || [],
        workEnvironment: backendCompany.culture?.workEnvironment
      },
      specialties: backendCompany.specialties || [],
      technologies: backendCompany.technologies || [],
      socialLinks: {
        linkedin: backendCompany.socialLinks?.linkedin,
        twitter: backendCompany.socialLinks?.twitter,
        github: backendCompany.socialLinks?.github
      },
      verification: {
        isVerified: backendCompany.verification?.isVerified || false,
        verifiedAt: backendCompany.verification?.verifiedAt
      },
      funding: backendCompany.funding ? {
        stage: backendCompany.funding.stage,
        amount: backendCompany.funding.amount,
        investors: backendCompany.funding.investors || []
      } : undefined,
      isActive: backendCompany.isActive,
      isFeatured: backendCompany.isFeatured || false,
      createdAt: backendCompany.createdAt,
      updatedAt: backendCompany.updatedAt
    }
  }

  private transformSearchResult(backendResult: any): CompanySearchResult {
    return {
      companies: (backendResult.companies || []).map((company: any) => this.transformCompany(company)),
      pagination: backendResult.pagination || {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0
      },
      aggregations: {
        industries: backendResult.aggregations?.industries || [],
        sizes: backendResult.aggregations?.sizes || [],
        locations: backendResult.aggregations?.locations || [],
        fundingStages: backendResult.aggregations?.fundingStages || []
      },
      searchMeta: {
        query: backendResult.searchMeta?.query || '',
        totalResults: backendResult.searchMeta?.totalResults || 0,
        searchTime: backendResult.searchMeta?.searchTime || 0
      }
    }
  }

  private calculateCompanyRating(company: any): number {
    // Calculate rating based on various factors
    let rating = 3.0 // Base rating
    
    if (company.verification?.isVerified) rating += 0.5
    if (company.stats?.activeJobs > 0) rating += 0.3
    if (company.culture?.benefits?.length > 5) rating += 0.2
    if (company.funding?.stage) rating += 0.3
    if (company.stats?.followerCount > 100) rating += 0.2
    
    return Math.min(5.0, Math.round(rating * 10) / 10)
  }

  private formatLocationDisplay(location: any): string {
    const parts = [location.city]
    if (location.state) parts.push(location.state)
    parts.push(location.country)
    return parts.join(', ')
  }

  private buildSearchParams(query: CompanySearchQuery): URLSearchParams {
    const params = new URLSearchParams()
    
    if (query.search) params.append('search', query.search)
    params.append('page', query.page.toString())
    params.append('limit', query.limit.toString())
    params.append('sortBy', query.sortBy)
    params.append('sortOrder', query.sortOrder)
    
    // Add filters
    if (query.filters.industries?.length) {
      params.append('industry', query.filters.industries.join(','))
    }
    if (query.filters.sizes?.length) {
      params.append('size', query.filters.sizes.join(','))
    }
    if (query.filters.locations?.length) {
      params.append('location', query.filters.locations.join(','))
    }
    if (query.filters.isVerified !== undefined) {
      params.append('isVerified', query.filters.isVerified.toString())
    }
    if (query.filters.hasActiveJobs) {
      params.append('hasActiveJobs', 'true')
    }
    
    return params
  }

  private generateCacheKey(type: string, data: any): string {
    return `${type}_${JSON.stringify(data)}`
  }

  private getFromCache(key: string): any {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data
    }
    this.cache.delete(key)
    return null
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() })
  }

  private invalidateCompanyCache(companyId: string): void {
    for (const [key] of this.cache) {
      if (key.includes(companyId)) {
        this.cache.delete(key)
      }
    }
  }

  private getAuthToken(): string {
    const token = localStorage.getItem('auth-token')
    if (!token) {
      throw new Error('Authentication required')
    }
    return token
  }
}

export const companiesFrontendService = new CompaniesFrontendService()
```

### 3. Complete Zustand Store Implementation

Create the full store implementation in `stores/companies-frontend.store.ts`:

```typescript
export const useCompaniesFrontendStore = create<CompaniesState & CompaniesActions>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial State
        companies: [],
        currentCompany: null,
        searchResult: null,
        followedCompanies: new Set(),
        savedSearches: [],

        searchQuery: {
          search: '',
          filters: {},
          sortBy: 'relevance',
          sortOrder: 'desc',
          page: 1,
          limit: 20
        },
        activeFilters: {},
        quickFilters: {
          trending: false,
          hiring: false,
          verified: false,
          remote: false
        },

        viewMode: 'list',
        showFilters: true,
        selectedCompanyId: null,

        isLoading: false,
        isSearching: false,
        isLoadingMore: false,
        isFollowing: false,

        error: null,
        searchError: null,

        lastSearchTime: 0,
        cacheExpiry: 5 * 60 * 1000,

        // Actions
        searchCompanies: async (queryUpdate) => {
          const state = get()
          const newQuery = { ...state.searchQuery, ...queryUpdate }

          set({ isSearching: true, searchError: null, searchQuery: newQuery })

          try {
            const result = await companiesFrontendService.searchCompanies(newQuery)

            set({
              searchResult: result,
              companies: result.companies,
              isSearching: false,
              lastSearchTime: Date.now()
            })
          } catch (error) {
            set({
              searchError: error instanceof Error ? error.message : 'Search failed',
              isSearching: false
            })
          }
        },

        updateFilters: (filters) => {
          const state = get()
          const newFilters = { ...state.activeFilters, ...filters }

          set({ activeFilters: newFilters })

          // Auto-search with new filters
          state.searchCompanies({
            filters: newFilters,
            page: 1 // Reset to first page
          })
        },

        clearFilters: () => {
          set({
            activeFilters: {},
            quickFilters: {
              trending: false,
              hiring: false,
              verified: false,
              remote: false
            }
          })

          const state = get()
          state.searchCompanies({
            filters: {},
            page: 1
          })
        },

        applyQuickFilter: (filter) => {
          const state = get()
          const newQuickFilters = {
            ...state.quickFilters,
            [filter]: !state.quickFilters[filter]
          }

          set({ quickFilters: newQuickFilters })

          // Convert quick filters to search filters
          const searchFilters: Partial<CompanySearchFilters> = {}

          if (newQuickFilters.verified) searchFilters.isVerified = true
          if (newQuickFilters.hiring) searchFilters.hasActiveJobs = true
          if (newQuickFilters.trending) {
            // Sort by follower count or recent activity
            state.searchCompanies({
              sortBy: 'rating',
              sortOrder: 'desc'
            })
            return
          }

          state.updateFilters(searchFilters)
        },

        getCompanyById: async (id) => {
          set({ isLoading: true, error: null })

          try {
            const company = await companiesFrontendService.getCompanyById(id)
            set({ currentCompany: company, isLoading: false })
            return company
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to load company',
              isLoading: false
            })
            return null
          }
        },

        followCompany: async (companyId) => {
          set({ isFollowing: true, error: null })

          try {
            await companiesFrontendService.followCompany(companyId)

            const state = get()
            const newFollowed = new Set(state.followedCompanies)
            newFollowed.add(companyId)

            set({
              followedCompanies: newFollowed,
              isFollowing: false
            })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to follow company',
              isFollowing: false
            })
          }
        },

        unfollowCompany: async (companyId) => {
          set({ isFollowing: true, error: null })

          try {
            await companiesFrontendService.unfollowCompany(companyId)

            const state = get()
            const newFollowed = new Set(state.followedCompanies)
            newFollowed.delete(companyId)

            set({
              followedCompanies: newFollowed,
              isFollowing: false
            })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to unfollow company',
              isFollowing: false
            })
          }
        },

        setViewMode: (mode) => {
          set({ viewMode: mode })
        },

        toggleFilters: () => {
          const state = get()
          set({ showFilters: !state.showFilters })
        },

        selectCompany: (companyId) => {
          set({ selectedCompanyId: companyId })
        },

        loadMore: async () => {
          const state = get()
          if (state.isLoadingMore || !state.searchResult) return

          const nextPage = state.searchQuery.page + 1
          if (nextPage > state.searchResult.pagination.totalPages) return

          set({ isLoadingMore: true })

          try {
            const result = await companiesFrontendService.searchCompanies({
              ...state.searchQuery,
              page: nextPage
            })

            set({
              searchResult: {
                ...result,
                companies: [...state.companies, ...result.companies]
              },
              companies: [...state.companies, ...result.companies],
              searchQuery: { ...state.searchQuery, page: nextPage },
              isLoadingMore: false
            })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to load more',
              isLoadingMore: false
            })
          }
        },

        goToPage: async (page) => {
          const state = get()
          await state.searchCompanies({ page })
        },

        clearCache: () => {
          companiesFrontendService.clearCache?.()
          set({ lastSearchTime: 0 })
        },

        refreshData: async () => {
          const state = get()
          state.clearCache()
          await state.searchCompanies({ page: 1 })
        },

        saveSearch: (query, name) => {
          const state = get()
          const savedSearch = { ...query, name }
          const newSavedSearches = [...state.savedSearches, savedSearch]

          set({ savedSearches: newSavedSearches })
        },

        loadSavedSearch: (index) => {
          const state = get()
          const savedSearch = state.savedSearches[index]
          if (savedSearch) {
            state.searchCompanies(savedSearch)
          }
        },

        deleteSavedSearch: (index) => {
          const state = get()
          const newSavedSearches = state.savedSearches.filter((_, i) => i !== index)
          set({ savedSearches: newSavedSearches })
        }
      }),
      {
        name: 'companies-frontend-store',
        partialize: (state) => ({
          followedCompanies: Array.from(state.followedCompanies),
          savedSearches: state.savedSearches,
          viewMode: state.viewMode,
          showFilters: state.showFilters
        }),
        onRehydrateStorage: () => (state) => {
          if (state?.followedCompanies) {
            state.followedCompanies = new Set(state.followedCompanies as string[])
          }
        }
      }
    ),
    { name: 'companies-frontend' }
  )
)
```

### 4. Updated Company Card Component

Update `components/companies/company-card.tsx` to use the new data structure:

```typescript
import { CompanyFrontend } from '@/stores/companies-frontend.store'

interface CompanyCardProps {
  company: CompanyFrontend
  viewMode: 'grid' | 'list'
  onViewProfile: (company: CompanyFrontend) => void
  onFollow: (company: CompanyFrontend) => void
  onViewJobs: (company: CompanyFrontend) => void
  isFollowing?: boolean
}

export function CompanyCard({
  company,
  viewMode,
  onViewProfile,
  onFollow,
  onViewJobs,
  isFollowing = false
}: CompanyCardProps) {
  const getCompanySizeLabel = (size: string) => {
    const sizeLabels = {
      startup: '1-10 employees',
      small: '11-50 employees',
      medium: '51-200 employees',
      large: '201-1000 employees',
      enterprise: '1000+ employees'
    }
    return sizeLabels[size as keyof typeof sizeLabels] || size
  }

  const formatFollowerCount = (count: number) => {
    if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M`
    if (count >= 1000) return `${(count / 1000).toFixed(1)}k`
    return count.toString()
  }

  // Rest of component implementation using company.primaryLocation.displayName,
  // company.stats.activeJobs, company.verification.isVerified, etc.
}
```

### 5. Updated Companies Page Integration

Update `app/(public)/companies/page.tsx`:

```typescript
'use client'

import { useEffect, useState } from 'react'
import { useCompaniesFrontendStore } from '@/stores/companies-frontend.store'
import { CompanyCard } from '@/components/companies/company-card'
import { CompanyFilters } from '@/components/companies/company-filters'
import { CompanyDetailModal } from '@/components/companies/company-detail-modal'

export default function CompaniesPage() {
  const {
    companies,
    searchResult,
    searchQuery,
    activeFilters,
    viewMode,
    showFilters,
    isSearching,
    isLoadingMore,
    error,
    followedCompanies,

    searchCompanies,
    updateFilters,
    setViewMode,
    toggleFilters,
    followCompany,
    unfollowCompany,
    loadMore,
    goToPage
  } = useCompaniesFrontendStore()

  const [selectedCompany, setSelectedCompany] = useState(null)
  const [searchInput, setSearchInput] = useState('')

  useEffect(() => {
    // Initial load
    searchCompanies({
      search: '',
      page: 1,
      limit: 20,
      sortBy: 'relevance',
      sortOrder: 'desc'
    })
  }, [])

  const handleSearch = (query: string) => {
    setSearchInput(query)
    searchCompanies({
      search: query,
      page: 1
    })
  }

  const handleFilterChange = (newFilters: any) => {
    updateFilters(newFilters)
  }

  const handleFollow = async (company: any) => {
    const isCurrentlyFollowing = followedCompanies.has(company.id)

    if (isCurrentlyFollowing) {
      await unfollowCompany(company.id)
    } else {
      await followCompany(company.id)
    }
  }

  const stats = searchResult ? {
    totalCompanies: searchResult.pagination.total,
    totalIndustries: searchResult.aggregations.industries.length,
    totalJobs: companies.reduce((sum, company) => sum + company.stats.activeJobs, 0)
  } : {
    totalCompanies: 0,
    totalIndustries: 0,
    totalJobs: 0
  }

  return (
    <div className="pt-16">
      {/* Hero Section with Real Stats */}
      <section className="relative py-12 md:py-20 overflow-hidden">
        {/* Hero content with dynamic stats */}
        <div className="flex items-center space-x-6 text-sm text-muted-foreground">
          <div className="flex items-center space-x-2">
            <TrendingUp className="w-4 h-4 text-primary" />
            <span>{stats.totalCompanies.toLocaleString()}+ Companies</span>
          </div>
          <div className="flex items-center space-x-2">
            <Award className="w-4 h-4 text-primary" />
            <span>{stats.totalIndustries}+ Industries</span>
          </div>
          <div className="flex items-center space-x-2">
            <Target className="w-4 h-4 text-primary" />
            <span>{stats.totalJobs.toLocaleString()}+ Open Positions</span>
          </div>
        </div>
      </section>

      {/* Search and Filters */}
      <section className="border-b bg-background/95 backdrop-blur sticky top-16 z-40">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search companies..."
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch(searchInput)}
                className="pl-10"
              />
            </div>

            {/* Filter Controls */}
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={toggleFilters}
                className={showFilters ? 'bg-primary/10 border-primary/50' : ''}
              >
                <Filter className="w-4 h-4 mr-2" />
                Filters
                {Object.keys(activeFilters).length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {Object.keys(activeFilters).length}
                  </Badge>
                )}
              </Button>

              {/* View Mode Toggle */}
              <div className="flex items-center space-x-2 bg-muted/50 rounded-lg p-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="h-8 w-8 p-0"
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="h-8 w-8 p-0"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="flex gap-8">
          {/* Filters Sidebar */}
          {showFilters && (
            <aside className="w-80">
              <CompanyFilters
                filters={activeFilters}
                onFiltersChange={handleFilterChange}
                aggregations={searchResult?.aggregations}
              />
            </aside>
          )}

          {/* Companies Grid/List */}
          <div className="flex-1">
            {/* Results Summary */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-lg font-semibold">
                  {searchResult?.pagination.total || 0} Companies Found
                </h2>
                {searchQuery.search && (
                  <p className="text-muted-foreground">
                    Results for "{searchQuery.search}"
                  </p>
                )}
              </div>
            </div>

            {/* Loading State */}
            {isSearching && (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            )}

            {/* Error State */}
            {error && (
              <div className="text-center py-12">
                <p className="text-red-500">{error}</p>
                <Button
                  onClick={() => searchCompanies(searchQuery)}
                  className="mt-4"
                >
                  Try Again
                </Button>
              </div>
            )}

            {/* Companies Grid/List */}
            {!isSearching && !error && (
              <>
                <div className={`
                  grid gap-6
                  ${viewMode === 'grid'
                    ? 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3'
                    : 'grid-cols-1 space-y-4'
                  }
                `}>
                  {companies.map((company) => (
                    <CompanyCard
                      key={company.id}
                      company={company}
                      viewMode={viewMode}
                      onViewProfile={setSelectedCompany}
                      onFollow={handleFollow}
                      onViewJobs={(company) => {
                        // Navigate to jobs page with company filter
                        window.open(`/jobs?company=${company.slug}`, '_blank')
                      }}
                      isFollowing={followedCompanies.has(company.id)}
                    />
                  ))}
                </div>

                {/* Load More / Pagination */}
                {searchResult && searchResult.pagination.totalPages > 1 && (
                  <div className="mt-8 flex justify-center">
                    {searchResult.pagination.page < searchResult.pagination.totalPages && (
                      <Button
                        onClick={loadMore}
                        disabled={isLoadingMore}
                        variant="outline"
                      >
                        {isLoadingMore ? 'Loading...' : 'Load More Companies'}
                      </Button>
                    )}
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </main>

      {/* Company Detail Modal */}
      <CompanyDetailModal
        company={selectedCompany}
        isOpen={!!selectedCompany}
        onClose={() => setSelectedCompany(null)}
        onFollow={handleFollow}
        onViewJobs={(company) => {
          window.open(`/jobs?company=${company.slug}`, '_blank')
          setSelectedCompany(null)
        }}
        isFollowing={selectedCompany ? followedCompanies.has(selectedCompany.id) : false}
      />
    </div>
  )
}
```

## Performance Optimizations

### 1. Caching Strategy
- **Service-level caching**: 5-minute cache for search results and company details
- **Store-level persistence**: Save user preferences and followed companies
- **Smart invalidation**: Clear relevant caches when data changes

### 2. Efficient Data Loading
- **Pagination**: Load 20 companies per page with infinite scroll option
- **Lazy loading**: Load company details only when needed
- **Debounced search**: Prevent excessive API calls during typing

### 3. Optimized Rendering
- **Memoized components**: Use React.memo for company cards
- **Virtual scrolling**: For large lists (future enhancement)
- **Image optimization**: Lazy load company logos and banners

### 4. Error Handling
- **Graceful degradation**: Show cached data when API fails
- **Retry mechanisms**: Automatic retry for failed requests
- **User feedback**: Clear error messages and recovery options

## Migration Steps

### Phase 1: Store Setup
1. Create `stores/companies-frontend.store.ts`
2. Create `lib/services/companies-frontend.service.ts`
3. Test basic search functionality

### Phase 2: Component Updates
1. Update `CompanyCard` component
2. Update `CompanyFilters` component
3. Update `CompanyDetailModal` component

### Phase 3: Page Integration
1. Update companies page to use new store
2. Implement real-time stats in hero section
3. Add loading and error states

### Phase 4: Advanced Features
1. Add follow/unfollow functionality
2. Implement saved searches
3. Add advanced filtering options

### Phase 5: Performance & Polish
1. Implement caching strategies
2. Add performance monitoring
3. Optimize for mobile devices

## Testing Strategy

### Unit Tests
- Test store actions and state updates
- Test service layer data transformations
- Test component rendering with different data states

### Integration Tests
- Test complete search flow
- Test filter combinations
- Test pagination and infinite scroll

### Performance Tests
- Measure search response times
- Test with large datasets
- Monitor memory usage

This implementation provides a robust, scalable, and performant solution for integrating the companies frontend with real backend data while maintaining excellent user experience and code quality.
```

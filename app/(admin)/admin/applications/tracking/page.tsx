'use client'

import React, { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Search, 
  TrendingUp,
  Award,
  Users,
  Building2,
  Calendar,
  Clock,
  Target,
  RefreshCw,
  Download,
  ArrowLeft,
  CheckCircle,
  Star,
  Briefcase,
  DollarSign
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { TableEmptyState, ErrorState, LoadingState } from '@/components/ui/empty-state'
import { AdminPageHeader } from '@/components/admin/admin-page-header'

interface SuccessfulHire {
  _id: string
  applicant: {
    _id: string
    firstName: string
    lastName: string
    email: string
    avatar?: string
    profile?: {
      experience?: string
      skills?: string[]
      location?: string
    }
  }
  job: {
    _id: string
    title: string
    category: string
    company: {
      _id: string
      name: string
      logo?: string
    }
    salary: {
      min: number
      max: number
      currency: string
    }
  }
  hiredAt: Date
  appliedAt: Date
  timeToHire: number // days
  salaryOffered?: number
  startDate?: Date
  rating?: number
  feedback?: string
  source: string // how they found the job
  recruiterNotes?: string
}

interface SuccessMetrics {
  totalHires: number
  averageTimeToHire: number
  averageSalary: number
  topSources: Array<{
    source: string
    count: number
    percentage: number
  }>
  hiringTrends: Array<{
    month: string
    hires: number
    averageTime: number
  }>
}

export default function SuccessTrackingPage() {
  const router = useRouter()
  const { token } = useAuthStore()
  const [hires, setHires] = useState<SuccessfulHire[]>([])
  const [metrics, setMetrics] = useState<SuccessMetrics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [timeRange, setTimeRange] = useState('30d')
  const [error, setError] = useState<string | null>(null)

  const fetchSuccessData = async () => {
    try {
      setIsLoading(true)
      setError(null)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const queryParams = new URLSearchParams({
        timeRange,
        ...(searchQuery && { search: searchQuery })
      })

      const [hiresResponse, metricsResponse] = await Promise.all([
        fetch(`/api/v1/admin/applications/successful-hires?${queryParams}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch(`/api/v1/admin/applications/success-metrics?${queryParams}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })
      ])

      if (!hiresResponse.ok || !metricsResponse.ok) {
        throw new Error('Failed to fetch success tracking data')
      }

      const [hiresData, metricsData] = await Promise.all([
        hiresResponse.json(),
        metricsResponse.json()
      ])

      setHires(hiresData.data.hires || [])
      setMetrics(metricsData.data)
    } catch (error) {
      console.error('Failed to fetch success tracking data:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch success tracking data')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchSuccessData()
  }, [timeRange])

  const handleSearch = () => {
    fetchSuccessData()
  }

  const formatSalary = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const getRatingStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star 
        key={i} 
        className={cn(
          "w-4 h-4",
          i < rating ? "text-yellow-400 fill-current" : "text-gray-300"
        )} 
      />
    ))
  }



  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <AdminPageHeader
          title="Success Tracking"
          description="Track successful hires and hiring performance metrics"
          icon={Award}
          breadcrumbItems={[
            { label: 'Admin', href: '/admin' },
            { label: 'Applications', href: '/admin/applications' },
            { label: 'Success Tracking' }
          ]}
          actions={
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => router.push('/admin/applications')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Applications
              </Button>
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Time Range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" onClick={fetchSuccessData} disabled={isLoading}>
                <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
                Refresh
              </Button>
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          }
        />

        {isLoading ? (
          <LoadingState message="Loading success data..." />
        ) : (
          <>
            {/* Success Metrics */}
            {metrics && (
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Total Successful Hires</p>
                        <p className="text-2xl font-bold">{metrics.totalHires}</p>
                      </div>
                      <CheckCircle className="w-8 h-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Avg. Time to Hire</p>
                        <p className="text-2xl font-bold">{metrics.averageTimeToHire} days</p>
                      </div>
                      <Clock className="w-8 h-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Avg. Salary Offered</p>
                        <p className="text-2xl font-bold">{formatSalary(metrics.averageSalary)}</p>
                      </div>
                      <DollarSign className="w-8 h-8 text-purple-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Top Source</p>
                        <p className="text-lg font-bold">
                          {metrics.topSources[0]?.source || 'N/A'}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {metrics.topSources[0]?.percentage.toFixed(1)}% of hires
                        </p>
                      </div>
                      <Target className="w-8 h-8 text-orange-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Top Sources */}
            {metrics && metrics.topSources.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Top Hiring Sources</CardTitle>
                  <CardDescription>Where successful candidates are coming from</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {metrics.topSources.map((source, index) => (
                      <div key={source.source} className="flex items-center space-x-4">
                        <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                          <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <span className="font-medium">{source.source}</span>
                            <span className="text-sm text-muted-foreground">
                              {source.count} hires ({source.percentage.toFixed(1)}%)
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${source.percentage}%` }}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Successful Hires Table */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Successful Hires</CardTitle>
                <CardDescription>Track individual hiring successes and performance</CardDescription>
              </CardHeader>
              <CardContent>
                {/* Search */}
                <div className="flex gap-4 mb-6">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      <Input
                        placeholder="Search by candidate name, job title, or company..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <Button onClick={handleSearch} disabled={isLoading}>
                    <Search className="w-4 h-4 mr-2" />
                    Search
                  </Button>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Candidate</TableHead>
                        <TableHead>Job & Company</TableHead>
                        <TableHead>Salary Offered</TableHead>
                        <TableHead>Time to Hire</TableHead>
                        <TableHead>Rating</TableHead>
                        <TableHead>Start Date</TableHead>
                        <TableHead>Source</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {error ? (
                        <TableEmptyState
                          title="Failed to load success data"
                          description={error}
                          action={{
                            label: 'Retry',
                            onClick: fetchSuccessData,
                            variant: 'outline'
                          }}
                          colSpan={7}
                        />
                      ) : hires.length === 0 ? (
                        <TableEmptyState
                          icon={Award}
                          title="No successful hires found"
                          description="Successful hires will appear here once candidates are hired."
                          colSpan={7}
                        />
                      ) : (
                        hires.map((hire) => (
                          <TableRow key={hire._id}>
                            <TableCell>
                              <div className="flex items-center space-x-3">
                                <Avatar className="w-10 h-10">
                                  <AvatarImage src={hire.applicant.avatar} />
                                  <AvatarFallback>
                                    {hire.applicant.firstName[0]}{hire.applicant.lastName[0]}
                                  </AvatarFallback>
                                </Avatar>
                                <div>
                                  <p className="font-medium">
                                    {hire.applicant.firstName} {hire.applicant.lastName}
                                  </p>
                                  <p className="text-sm text-muted-foreground">{hire.applicant.email}</p>
                                  {hire.applicant.profile?.location && (
                                    <p className="text-xs text-muted-foreground">{hire.applicant.profile.location}</p>
                                  )}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="space-y-1">
                                <p className="font-medium">{hire.job.title}</p>
                                <div className="flex items-center space-x-2">
                                  <Avatar className="w-6 h-6">
                                    <AvatarImage src={hire.job.company.logo} />
                                    <AvatarFallback>
                                      <Building2 className="w-3 h-3" />
                                    </AvatarFallback>
                                  </Avatar>
                                  <p className="text-sm text-muted-foreground">{hire.job.company.name}</p>
                                </div>
                                <Badge variant="outline" className="text-xs">{hire.job.category}</Badge>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <DollarSign className="w-4 h-4 mr-1 text-green-600" />
                                <span className="font-medium">
                                  {hire.salaryOffered ? 
                                    formatSalary(hire.salaryOffered) : 
                                    'Not disclosed'
                                  }
                                </span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <Clock className="w-4 h-4 mr-1 text-blue-600" />
                                <span>{hire.timeToHire} days</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              {hire.rating ? (
                                <div className="flex items-center space-x-1">
                                  {getRatingStars(hire.rating)}
                                  <span className="text-sm text-muted-foreground ml-2">
                                    {hire.rating}/5
                                  </span>
                                </div>
                              ) : (
                                <span className="text-muted-foreground">Not rated</span>
                              )}
                            </TableCell>
                            <TableCell>
                              {hire.startDate ? (
                                <div className="flex items-center text-sm">
                                  <Calendar className="w-4 h-4 mr-1 text-muted-foreground" />
                                  {new Date(hire.startDate).toLocaleDateString()}
                                </div>
                              ) : (
                                <span className="text-muted-foreground">TBD</span>
                              )}
                            </TableCell>
                            <TableCell>
                              <Badge variant="secondary">{hire.source}</Badge>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </AdminLayout>
  )
}

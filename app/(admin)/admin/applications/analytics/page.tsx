'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  Target,
  Award,
  Clock,
  RefreshCw,
  Download,
  ArrowLeft,
  Building2,
  FileText,
  CheckCircle,
  Eye
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { ErrorState, LoadingState } from '@/components/ui/empty-state'
import { AdminPageHeader } from '@/components/admin/admin-page-header'

interface ApplicationAnalytics {
  totalApplications: number
  applicationsByStatus: {
    pending: number
    reviewing: number
    shortlisted: number
    interviewed: number
    offered: number
    hired: number
    rejected: number
    withdrawn: number
  }
  applicationsByPeriod: Array<{
    period: string
    applications: number
    hires: number
    conversionRate: number
  }>
  topPerformingJobs: Array<{
    jobId: string
    jobTitle: string
    company: string
    applications: number
    hires: number
    conversionRate: number
  }>
  topPerformingCompanies: Array<{
    companyId: string
    companyName: string
    applications: number
    hires: number
    conversionRate: number
    averageTimeToHire: number
  }>
  applicationTrends: {
    thisMonth: number
    lastMonth: number
    growth: number
  }
  conversionFunnel: Array<{
    stage: string
    count: number
    percentage: number
  }>
}

export default function ApplicationAnalyticsPage() {
  const router = useRouter()
  const { token } = useAuthStore()
  const [analytics, setAnalytics] = useState<ApplicationAnalytics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeRange, setTimeRange] = useState('30d')

  const fetchAnalytics = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/v1/admin/applications/analytics?timeRange=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch application analytics')
      }

      const data = await response.json()
      setAnalytics(data.data)
    } catch (error) {
      console.error('Failed to fetch application analytics:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch application analytics')
    } finally {
      setIsLoading(false)
    }
  }, [token, timeRange])

  useEffect(() => {
    fetchAnalytics()
  }, [timeRange, fetchAnalytics])

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      reviewing: 'bg-blue-100 text-blue-800',
      shortlisted: 'bg-purple-100 text-purple-800',
      interviewed: 'bg-indigo-100 text-indigo-800',
      offered: 'bg-green-100 text-green-800',
      hired: 'bg-green-200 text-green-900',
      rejected: 'bg-red-100 text-red-800',
      withdrawn: 'bg-gray-100 text-gray-800'
    }
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="space-y-6 p-6">
          <AdminPageHeader
            title="Application Analytics"
            description="Comprehensive insights into application performance and trends"
            icon={BarChart3}
            breadcrumbItems={[
              { label: 'Admin', href: '/admin' },
              { label: 'Applications', href: '/admin/applications' },
              { label: 'Analytics' }
            ]}
            actions={
              <Button
                variant="outline"
                onClick={() => router.push('/admin/applications')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Applications
              </Button>
            }
          />
          <ErrorState
            title="Failed to load analytics"
            description={error}
            onRetry={fetchAnalytics}
          />
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <AdminPageHeader
          title="Application Analytics"
          description="Comprehensive insights into application performance and trends"
          icon={BarChart3}
          breadcrumbItems={[
            { label: 'Admin', href: '/admin' },
            { label: 'Applications', href: '/admin/applications' },
            { label: 'Analytics' }
          ]}
          actions={
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => router.push('/admin/applications')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Applications
              </Button>
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Time Range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" onClick={fetchAnalytics} disabled={isLoading}>
                <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
                Refresh
              </Button>
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          }
        />

        {isLoading ? (
          <LoadingState message="Loading analytics..." />
        ) : analytics ? (
          <>
            {/* Overview Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Total Applications</p>
                      <p className="text-2xl font-bold">{analytics.totalApplications.toLocaleString()}</p>
                      <div className="flex items-center mt-1">
                        {analytics.applicationTrends.growth >= 0 ? (
                          <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                        ) : (
                          <TrendingDown className="w-4 h-4 text-red-600 mr-1" />
                        )}
                        <span className={cn(
                          "text-sm font-medium",
                          analytics.applicationTrends.growth >= 0 ? "text-green-600" : "text-red-600"
                        )}>
                          {formatPercentage(Math.abs(analytics.applicationTrends.growth))}
                        </span>
                      </div>
                    </div>
                    <FileText className="w-8 h-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Successful Hires</p>
                      <p className="text-2xl font-bold">{analytics.applicationsByStatus.hired}</p>
                      <p className="text-sm text-muted-foreground mt-1">
                        {formatPercentage((analytics.applicationsByStatus.hired / analytics.totalApplications) * 100)} success rate
                      </p>
                    </div>
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Pending Review</p>
                      <p className="text-2xl font-bold">{analytics.applicationsByStatus.pending}</p>
                      <p className="text-sm text-muted-foreground mt-1">
                        Requires attention
                      </p>
                    </div>
                    <Clock className="w-8 h-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Conversion Rate</p>
                      <p className="text-2xl font-bold">
                        {formatPercentage((analytics.applicationsByStatus.hired / analytics.totalApplications) * 100)}
                      </p>
                      <p className="text-sm text-muted-foreground mt-1">
                        Application to hire
                      </p>
                    </div>
                    <Target className="w-8 h-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Application Status Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>Application Status Distribution</CardTitle>
                <CardDescription>Current status of all applications in the system</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {Object.entries(analytics.applicationsByStatus).map(([status, count]) => (
                    <div key={status} className="text-center">
                      <div className={cn(
                        "inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mb-2",
                        getStatusColor(status)
                      )}>
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </div>
                      <p className="text-2xl font-bold">{count}</p>
                      <p className="text-sm text-muted-foreground">
                        {formatPercentage((count / analytics.totalApplications) * 100)}
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Top Performing Jobs */}
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Jobs</CardTitle>
                <CardDescription>Jobs with the highest application volume and conversion rates</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Job Title</TableHead>
                        <TableHead>Company</TableHead>
                        <TableHead>Applications</TableHead>
                        <TableHead>Hires</TableHead>
                        <TableHead>Conversion Rate</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {analytics.topPerformingJobs.map((job) => (
                        <TableRow key={job.jobId}>
                          <TableCell className="font-medium">{job.jobTitle}</TableCell>
                          <TableCell>{job.company}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Users className="w-4 h-4 mr-2 text-muted-foreground" />
                              {job.applications}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Award className="w-4 h-4 mr-2 text-green-600" />
                              {job.hires}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant={job.conversionRate > 10 ? 'default' : 'secondary'}>
                              {formatPercentage(job.conversionRate)}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => router.push(`/admin/jobs/${job.jobId}`)}
                            >
                              <Eye className="w-4 h-4 mr-2" />
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>

            {/* Top Performing Companies */}
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Companies</CardTitle>
                <CardDescription>Companies with the best hiring performance and efficiency</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Company</TableHead>
                        <TableHead>Applications</TableHead>
                        <TableHead>Hires</TableHead>
                        <TableHead>Conversion Rate</TableHead>
                        <TableHead>Avg. Time to Hire</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {analytics.topPerformingCompanies.map((company) => (
                        <TableRow key={company.companyId}>
                          <TableCell className="font-medium">{company.companyName}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Users className="w-4 h-4 mr-2 text-muted-foreground" />
                              {company.applications}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Award className="w-4 h-4 mr-2 text-green-600" />
                              {company.hires}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant={company.conversionRate > 15 ? 'default' : 'secondary'}>
                              {formatPercentage(company.conversionRate)}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Clock className="w-4 h-4 mr-2 text-muted-foreground" />
                              {company.averageTimeToHire} days
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => router.push(`/admin/companies/${company.companyId}`)}
                            >
                              <Building2 className="w-4 h-4 mr-2" />
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>

            {/* Conversion Funnel */}
            <Card>
              <CardHeader>
                <CardTitle>Application Conversion Funnel</CardTitle>
                <CardDescription>Track how applications progress through the hiring process</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.conversionFunnel.map((stage) => (
                    <div key={stage.stage} className="flex items-center space-x-4">
                      <div className="w-32 text-sm font-medium">{stage.stage}</div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <div className="flex-1 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${stage.percentage}%` }}
                            />
                          </div>
                          <div className="text-sm font-medium w-16 text-right">
                            {formatPercentage(stage.percentage)}
                          </div>
                        </div>
                      </div>
                      <div className="w-20 text-right text-sm text-muted-foreground">
                        {stage.count.toLocaleString()}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </>
        ) : null}
      </div>
    </AdminLayout>
  )
}

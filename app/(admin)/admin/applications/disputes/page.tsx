'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Textarea } from '@/components/ui/textarea'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Search,
  MoreHorizontal,
  Eye,
  MessageSquare,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  User,
  Building2,
  Calendar,
  RefreshCw,
  Download,
  ArrowLeft,
  Flag,
  Scale,
  FileText
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { TableEmptyState, ErrorState } from '@/components/ui/empty-state'
import { AdminPageHeader } from '@/components/admin/admin-page-header'

interface Dispute {
  _id: string
  application: {
    _id: string
    applicant: {
      _id: string
      firstName: string
      lastName: string
      email: string
      avatar?: string
    }
    job: {
      _id: string
      title: string
      company: {
        _id: string
        name: string
        logo?: string
      }
    }
  }
  disputeType: 'application_status' | 'discrimination' | 'unfair_process' | 'communication' | 'other'
  status: 'open' | 'investigating' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  reportedBy: 'applicant' | 'company'
  description: string
  evidence?: string[]
  createdAt: Date
  updatedAt: Date
  assignedTo?: {
    _id: string
    firstName: string
    lastName: string
  }
  resolution?: string
  resolutionDate?: Date
  messages: Array<{
    _id: string
    sender: {
      _id: string
      firstName: string
      lastName: string
      role: string
    }
    message: string
    timestamp: Date
    isInternal: boolean
  }>
}

export default function DisputeResolutionPage() {
  const router = useRouter()
  const { token } = useAuthStore()
  const [disputes, setDisputes] = useState<Dispute[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [priorityFilter, setPriorityFilter] = useState<string>('all')
  const [error, setError] = useState<string | null>(null)
  const [selectedDispute, setSelectedDispute] = useState<Dispute | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [resolutionText, setResolutionText] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const fetchDisputes = async () => {
    try {
      setIsLoading(true)
      setError(null)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const queryParams = new URLSearchParams({
        ...(searchQuery && { search: searchQuery }),
        ...(statusFilter && statusFilter !== 'all' && { status: statusFilter }),
        ...(priorityFilter && priorityFilter !== 'all' && { priority: priorityFilter })
      })

      const response = await fetch(`/api/v1/admin/applications/disputes?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch disputes')
      }

      const data = await response.json()
      setDisputes(data.data.disputes || [])
    } catch (error) {
      console.error('Failed to fetch disputes:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch disputes')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchDisputes()
  }, [statusFilter, priorityFilter])

  const handleSearch = () => {
    fetchDisputes()
  }

  const handleResolveDispute = async () => {
    if (!selectedDispute || !resolutionText.trim()) return

    try {
      setIsSubmitting(true)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/v1/admin/applications/disputes/${selectedDispute._id}/resolve`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          resolution: resolutionText,
          status: 'resolved'
        })
      })

      if (!response.ok) {
        throw new Error('Failed to resolve dispute')
      }

      setSelectedDispute(null)
      setResolutionText('')
      setIsDialogOpen(false)
      await fetchDisputes()
    } catch (error) {
      console.error('Failed to resolve dispute:', error)
      setError(error instanceof Error ? error.message : 'Failed to resolve dispute')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleStatusChange = async (disputeId: string, newStatus: string) => {
    try {
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/v1/admin/applications/disputes/${disputeId}/status`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
      })

      if (!response.ok) {
        throw new Error('Failed to update dispute status')
      }

      await fetchDisputes()
    } catch (error) {
      console.error('Failed to update dispute status:', error)
      setError(error instanceof Error ? error.message : 'Failed to update dispute status')
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      open: { variant: 'destructive' as const, label: 'Open', icon: AlertTriangle },
      investigating: { variant: 'default' as const, label: 'Investigating', icon: Eye },
      resolved: { variant: 'default' as const, label: 'Resolved', icon: CheckCircle },
      closed: { variant: 'secondary' as const, label: 'Closed', icon: XCircle }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.open
    const IconComponent = config.icon
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <IconComponent className="w-3 h-3" />
        {config.label}
      </Badge>
    )
  }

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { variant: 'secondary' as const, label: 'Low' },
      medium: { variant: 'default' as const, label: 'Medium' },
      high: { variant: 'destructive' as const, label: 'High' },
      urgent: { variant: 'destructive' as const, label: 'Urgent' }
    }
    
    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  const getDisputeTypeLabel = (type: string) => {
    const typeLabels = {
      application_status: 'Application Status',
      discrimination: 'Discrimination',
      unfair_process: 'Unfair Process',
      communication: 'Communication Issue',
      other: 'Other'
    }
    return typeLabels[type as keyof typeof typeLabels] || type
  }



  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <AdminPageHeader
          title="Dispute Resolution"
          description="Manage and resolve application-related disputes"
          icon={Scale}
          breadcrumbItems={[
            { label: 'Admin', href: '/admin' },
            { label: 'Applications', href: '/admin/applications' },
            { label: 'Disputes' }
          ]}
          actions={
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => router.push('/admin/applications')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Applications
              </Button>
              <Button variant="outline" onClick={fetchDisputes} disabled={isLoading}>
                <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
                Refresh
              </Button>
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          }
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Disputes</p>
                  <p className="text-2xl font-bold">{disputes.length}</p>
                </div>
                <Flag className="w-8 h-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Open Disputes</p>
                  <p className="text-2xl font-bold">{disputes.filter(d => d.status === 'open').length}</p>
                </div>
                <AlertTriangle className="w-8 h-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Under Investigation</p>
                  <p className="text-2xl font-bold">{disputes.filter(d => d.status === 'investigating').length}</p>
                </div>
                <Eye className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Resolved</p>
                  <p className="text-2xl font-bold">{disputes.filter(d => d.status === 'resolved').length}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Disputes Management */}
        <Card>
          <CardHeader>
            <CardTitle>Dispute Management</CardTitle>
            <CardDescription>Review and resolve application-related disputes</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Search and Filters */}
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search disputes by applicant, company, or description..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button onClick={handleSearch} disabled={isLoading}>
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
            </div>

            <div className="flex flex-wrap gap-4 mb-6">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="investigating">Investigating</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                </SelectContent>
              </Select>

              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priority</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Disputes Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Dispute Details</TableHead>
                    <TableHead>Applicant & Job</TableHead>
                    <TableHead>Type & Priority</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {error ? (
                    <TableEmptyState
                      title="Failed to load disputes"
                      description={error}
                      action={{
                        label: 'Retry',
                        onClick: fetchDisputes,
                        variant: 'outline'
                      }}
                      colSpan={6}
                    />
                  ) : isLoading ? (
                    <TableEmptyState
                      title="Loading disputes..."
                      colSpan={6}
                    />
                  ) : disputes.length === 0 ? (
                    <TableEmptyState
                      icon={Scale}
                      title="No disputes found"
                      description="All disputes have been resolved or no disputes exist."
                      colSpan={6}
                    />
                  ) : (
                    disputes.map((dispute) => (
                      <TableRow key={dispute._id}>
                        <TableCell>
                          <div className="space-y-1">
                            <p className="font-medium">Dispute #{dispute._id.slice(-6)}</p>
                            <p className="text-sm text-muted-foreground line-clamp-2">
                              {dispute.description}
                            </p>
                            <Badge variant="outline" className="text-xs">
                              Reported by {dispute.reportedBy}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <Avatar className="w-6 h-6">
                                <AvatarImage src={dispute.application.applicant.avatar} />
                                <AvatarFallback>
                                  <User className="w-3 h-3" />
                                </AvatarFallback>
                              </Avatar>
                              <span className="text-sm font-medium">
                                {dispute.application.applicant.firstName} {dispute.application.applicant.lastName}
                              </span>
                            </div>
                            <div className="text-sm text-muted-foreground">
                              <p>{dispute.application.job.title}</p>
                              <div className="flex items-center space-x-1">
                                <Building2 className="w-3 h-3" />
                                <span>{dispute.application.job.company.name}</span>
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-2">
                            <Badge variant="outline">{getDisputeTypeLabel(dispute.disputeType)}</Badge>
                            {getPriorityBadge(dispute.priority)}
                          </div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(dispute.status)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Calendar className="w-4 h-4 mr-1" />
                            {new Date(dispute.createdAt).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => router.push(`/admin/applications/disputes/${dispute._id}`)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => router.push(`/admin/applications/disputes/${dispute._id}/messages`)}>
                                <MessageSquare className="mr-2 h-4 w-4" />
                                View Messages
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {dispute.status === 'open' && (
                                <DropdownMenuItem 
                                  onClick={() => handleStatusChange(dispute._id, 'investigating')}
                                  className="text-blue-600"
                                >
                                  <Eye className="mr-2 h-4 w-4" />
                                  Start Investigation
                                </DropdownMenuItem>
                              )}
                              {(dispute.status === 'open' || dispute.status === 'investigating') && (
                                <DropdownMenuItem 
                                  onClick={() => {
                                    setSelectedDispute(dispute)
                                    setIsDialogOpen(true)
                                  }}
                                  className="text-green-600"
                                >
                                  <CheckCircle className="mr-2 h-4 w-4" />
                                  Resolve Dispute
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Resolve Dispute Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Resolve Dispute</DialogTitle>
              <DialogDescription>
                Provide a resolution for this dispute. This action will mark the dispute as resolved.
              </DialogDescription>
            </DialogHeader>
            
            {selectedDispute && (
              <div className="space-y-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">Dispute Details</h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    <strong>Type:</strong> {getDisputeTypeLabel(selectedDispute.disputeType)}
                  </p>
                  <p className="text-sm text-muted-foreground mb-2">
                    <strong>Priority:</strong> {selectedDispute.priority}
                  </p>
                  <p className="text-sm">
                    <strong>Description:</strong> {selectedDispute.description}
                  </p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Resolution Details *
                  </label>
                  <Textarea
                    placeholder="Provide a detailed resolution explaining how this dispute was handled and what actions were taken..."
                    value={resolutionText}
                    onChange={(e) => setResolutionText(e.target.value)}
                    rows={4}
                  />
                </div>
              </div>
            )}
            
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => {
                  setIsDialogOpen(false)
                  setSelectedDispute(null)
                  setResolutionText('')
                }}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button 
                onClick={handleResolveDispute}
                disabled={!resolutionText.trim() || isSubmitting}
              >
                {isSubmitting ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <CheckCircle className="w-4 h-4 mr-2" />
                )}
                Resolve Dispute
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AdminLayout>
  )
}

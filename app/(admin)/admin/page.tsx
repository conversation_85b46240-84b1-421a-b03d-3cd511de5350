'use client'

import React from 'react'
import { AdminLayout } from '@/components/admin/admin-layout'
import { AdminDashboard } from '@/components/admin/admin-dashboard'

export default function AdminPage() {
  // The ProtectedRoute wrapper in layout.tsx handles all authentication and role checking
  // This component only renders when the user is authenticated and has admin role

  return (
    <AdminLayout>
      <AdminDashboard />
    </AdminLayout>
  )
}

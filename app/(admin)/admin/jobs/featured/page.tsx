'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Search, 
  MoreHorizontal,
  Eye,
  Edit,
  Star,
  StarOff,
  Building2,
  MapPin,
  DollarSign,
  Calendar,
  Users,
  RefreshCw,
  Download,
  ArrowLeft,
  TrendingUp,
  Crown,
  Zap
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { TableEmptyState, ErrorState } from '@/components/ui/empty-state'
import { AdminPageHeader } from '@/components/admin/admin-page-header'

interface Job {
  _id: string
  title: string
  description: string
  company: {
    _id: string
    name: string
    logo?: string
    isVerified: boolean
  }
  location: {
    city: string
    state: string
    country: string
    remote: boolean
  }
  salary: {
    min: number
    max: number
    currency: string
    period: string
  }
  jobType: string[]
  category: string
  experienceLevel: string
  status: string
  isFeatured: boolean
  isUrgent: boolean
  applicationCount: number
  viewCount: number
  createdAt: Date
  updatedAt: Date
  featuredUntil?: Date
  featuredOrder?: number
}

export default function FeaturedJobsPage() {
  const router = useRouter()
  const { token } = useAuthStore()
  const [jobs, setJobs] = useState<Job[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [error, setError] = useState<string | null>(null)

  const fetchFeaturedJobs = async () => {
    try {
      setIsLoading(true)
      setError(null)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const queryParams = new URLSearchParams({
        featured: 'true',
        ...(searchQuery && { search: searchQuery })
      })

      const response = await fetch(`/api/v1/admin/jobs?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch featured jobs')
      }

      const data = await response.json()
      setJobs(data.data.jobs || [])
    } catch (error) {
      console.error('Failed to fetch featured jobs:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch featured jobs')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchFeaturedJobs()
  }, [])

  const handleSearch = () => {
    fetchFeaturedJobs()
  }

  const handleJobAction = async (action: string, jobId: string) => {
    try {
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/v1/admin/jobs/${jobId}/${action}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to ${action} job`)
      }

      await fetchFeaturedJobs()
    } catch (error) {
      console.error(`Failed to ${action} job:`, error)
      setError(error instanceof Error ? error.message : `Failed to ${action} job`)
    }
  }

  const formatSalary = (salary: Job['salary']) => {
    if (!salary.min && !salary.max) return 'Not specified'
    
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: salary.currency || 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })
    
    if (salary.min && salary.max) {
      return `${formatter.format(salary.min)} - ${formatter.format(salary.max)} / ${salary.period || 'year'}`
    } else if (salary.min) {
      return `${formatter.format(salary.min)}+ / ${salary.period || 'year'}`
    } else {
      return `Up to ${formatter.format(salary.max)} / ${salary.period || 'year'}`
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { variant: 'secondary' as const, label: 'Draft' },
      active: { variant: 'default' as const, label: 'Active' },
      paused: { variant: 'outline' as const, label: 'Paused' },
      expired: { variant: 'destructive' as const, label: 'Expired' },
      closed: { variant: 'secondary' as const, label: 'Closed' }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="space-y-6 p-6">
          <AdminPageHeader
            title="Featured Jobs"
            description="Manage premium job listings with enhanced visibility"
            icon={Star}
            breadcrumbItems={[
              { label: 'Admin', href: '/admin' },
              { label: 'Jobs', href: '/admin/jobs' },
              { label: 'Featured Jobs' }
            ]}
            actions={
              <Button
                variant="outline"
                onClick={() => router.push('/admin/jobs')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Jobs
              </Button>
            }
          />
          <ErrorState
            title="Failed to load featured jobs"
            description={error}
            onRetry={fetchFeaturedJobs}
          />
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <AdminPageHeader
          title="Featured Jobs"
          description="Manage premium job listings with enhanced visibility"
          icon={Star}
          breadcrumbItems={[
            { label: 'Admin', href: '/admin' },
            { label: 'Jobs', href: '/admin/jobs' },
            { label: 'Featured Jobs' }
          ]}
          actions={
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => router.push('/admin/jobs')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Jobs
              </Button>
              <Button variant="outline" onClick={fetchFeaturedJobs} disabled={isLoading}>
                <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
                Refresh
              </Button>
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          }
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Featured Jobs</p>
                  <p className="text-2xl font-bold">{jobs.length}</p>
                </div>
                <Star className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Active Featured</p>
                  <p className="text-2xl font-bold">{jobs.filter(j => j.status === 'active').length}</p>
                </div>
                <Crown className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Applications</p>
                  <p className="text-2xl font-bold">{jobs.reduce((sum, j) => sum + j.applicationCount, 0)}</p>
                </div>
                <Users className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Views</p>
                  <p className="text-2xl font-bold">{jobs.reduce((sum, j) => sum + j.viewCount, 0)}</p>
                </div>
                <TrendingUp className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Jobs Table */}
        <Card>
          <CardHeader>
            <CardTitle>Featured Jobs Management</CardTitle>
            <CardDescription>Manage premium job listings with enhanced visibility and priority placement</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Search */}
            <div className="flex gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search featured jobs..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button onClick={handleSearch} disabled={isLoading}>
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
            </div>

            {/* Jobs Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Job Details</TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Location & Salary</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Performance</TableHead>
                    <TableHead>Featured Until</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableEmptyState
                      title="Loading featured jobs..."
                      colSpan={7}
                    />
                  ) : jobs.length === 0 ? (
                    <TableEmptyState
                      icon={Star}
                      title="No featured jobs"
                      description="No jobs are currently featured. Feature jobs to give them enhanced visibility."
                      action={{
                        label: 'View All Jobs',
                        onClick: () => router.push('/admin/jobs')
                      }}
                      colSpan={7}
                    />
                  ) : (
                    jobs.map((job) => (
                      <TableRow key={job._id} className="bg-gradient-to-r from-yellow-50 to-transparent">
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center space-x-2">
                              <Star className="w-4 h-4 text-yellow-500" />
                              <p className="font-medium">{job.title}</p>
                              {job.isUrgent && <Badge variant="destructive" className="text-xs">Urgent</Badge>}
                              <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-800">
                                Featured
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground">{job.category}</p>
                            <div className="flex flex-wrap gap-1">
                              {job.jobType.map((type, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {type}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <Avatar className="w-8 h-8">
                              <AvatarImage src={job.company.logo} />
                              <AvatarFallback>
                                <Building2 className="w-4 h-4" />
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{job.company.name}</p>
                              {job.company.isVerified && (
                                <Badge variant="default" className="text-xs">Verified</Badge>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center text-sm">
                              <MapPin className="w-4 h-4 mr-1" />
                              {job.location.remote ? 'Remote' : `${job.location.city}, ${job.location.state}`}
                            </div>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <DollarSign className="w-4 h-4 mr-1" />
                              {formatSalary(job.salary)}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(job.status)}
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center text-sm">
                              <Users className="w-4 h-4 mr-1" />
                              {job.applicationCount} applications
                            </div>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <TrendingUp className="w-4 h-4 mr-1" />
                              {job.viewCount} views
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Calendar className="w-4 h-4 mr-1" />
                            {job.featuredUntil ? 
                              new Date(job.featuredUntil).toLocaleDateString() : 
                              'Indefinite'
                            }
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => router.push(`/admin/jobs/${job._id}`)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => router.push(`/admin/jobs/${job._id}/edit`)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Job
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                onClick={() => handleJobAction('unfeature', job._id)}
                                className="text-orange-600"
                              >
                                <StarOff className="mr-2 h-4 w-4" />
                                Remove Featured
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => router.push(`/admin/jobs/${job._id}/featured-settings`)}
                                className="text-blue-600"
                              >
                                <Zap className="mr-2 h-4 w-4" />
                                Featured Settings
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}

'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { AdminPageHeader } from '@/components/admin/admin-page-header'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ArrowLeft, Plus, X, Save, Eye } from 'lucide-react'
import { cn } from '@/lib/utils'
import { ErrorState, LoadingState } from '@/components/ui/empty-state'

interface JobFormData {
  title: string
  description: string
  requirements: string[]
  responsibilities: string[]
  category: string
  jobType: string[]
  experienceLevel: string
  location: {
    city: string
    state: string
    country: string
    remote: boolean
    hybrid: boolean
  }
  salary: {
    min: number
    max: number
    currency: string
    period: string
    negotiable: boolean
  }
  benefits: string[]
  skills: string[]
  applicationDeadline: string
  isFeatured: boolean
  isUrgent: boolean
  status: string
}

export default function EditJobPage() {
  const router = useRouter()
  const params = useParams()
  const { token } = useAuthStore()
  const [formData, setFormData] = useState<JobFormData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (params.id) {
      fetchJob()
    }
  }, [params.id])

  const fetchJob = async () => {
    try {
      setIsLoading(true)
      setError(null)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/v1/admin/jobs/${params.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch job details')
      }

      const data = await response.json()
      const job = data.data.job

      // Transform job data to form format
      setFormData({
        title: job.title,
        description: job.description,
        requirements: job.requirements.length > 0 ? job.requirements : [''],
        responsibilities: job.responsibilities.length > 0 ? job.responsibilities : [''],
        category: job.category,
        jobType: job.jobType,
        experienceLevel: job.experienceLevel,
        location: job.location,
        salary: job.salary,
        benefits: job.benefits.length > 0 ? job.benefits : [''],
        skills: job.skills.length > 0 ? job.skills : [''],
        applicationDeadline: job.applicationDeadline ? job.applicationDeadline.split('T')[0] : '',
        isFeatured: job.isFeatured,
        isUrgent: job.isUrgent,
        status: job.status
      })
    } catch (error) {
      console.error('Error fetching job:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch job details')
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    if (!formData) return
    setFormData(prev => prev ? ({
      ...prev,
      [field]: value
    }) : null)
  }

  const handleNestedInputChange = (parent: string, field: string, value: any) => {
    if (!formData) return
    setFormData(prev => prev ? ({
      ...prev,
      [parent]: {
        ...prev[parent as keyof JobFormData] as any,
        [field]: value
      }
    }) : null)
  }

  const handleArrayInputChange = (field: string, index: number, value: string) => {
    if (!formData) return
    setFormData(prev => prev ? ({
      ...prev,
      [field]: (prev[field as keyof JobFormData] as string[]).map((item, i) => 
        i === index ? value : item
      )
    }) : null)
  }

  const addArrayItem = (field: string) => {
    if (!formData) return
    setFormData(prev => prev ? ({
      ...prev,
      [field]: [...(prev[field as keyof JobFormData] as string[]), '']
    }) : null)
  }

  const removeArrayItem = (field: string, index: number) => {
    if (!formData) return
    setFormData(prev => prev ? ({
      ...prev,
      [field]: (prev[field as keyof JobFormData] as string[]).filter((_, i) => i !== index)
    }) : null)
  }

  const handleJobTypeChange = (type: string, checked: boolean) => {
    if (!formData) return
    setFormData(prev => prev ? ({
      ...prev,
      jobType: checked 
        ? [...prev.jobType, type]
        : prev.jobType.filter(t => t !== type)
    }) : null)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData) return

    setIsSubmitting(true)
    setError(null)

    try {
      if (!token) {
        throw new Error('No authentication token found')
      }

      const jobData = {
        ...formData,
        requirements: formData.requirements.filter(r => r.trim()),
        responsibilities: formData.responsibilities.filter(r => r.trim()),
        benefits: formData.benefits.filter(b => b.trim()),
        skills: formData.skills.filter(s => s.trim())
      }

      const response = await fetch(`/api/v1/admin/jobs/${params.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(jobData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update job')
      }

      // Redirect to job details
      router.push(`/admin/jobs/${params.id}`)
      
    } catch (error) {
      console.error('Error updating job:', error)
      setError(error instanceof Error ? error.message : 'Failed to update job')
    } finally {
      setIsSubmitting(false)
    }
  }

  const jobTypes = ['Full-time', 'Part-time', 'Contract', 'Freelance', 'Internship', 'Temporary']
  const experienceLevels = ['Entry Level', 'Mid Level', 'Senior Level', 'Executive', 'Internship']
  const categories = ['Technology', 'Marketing', 'Sales', 'Design', 'Finance', 'Operations', 'HR', 'Legal', 'Other']

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="p-6">
          <LoadingState message="Loading job details..." />
        </div>
      </AdminLayout>
    )
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="p-6">
          <ErrorState
            title="Failed to load job details"
            description={error}
            onRetry={fetchJob}
          />
        </div>
      </AdminLayout>
    )
  }

  if (!formData) {
    return (
      <AdminLayout>
        <div className="p-6">
          <ErrorState
            title="Job not found"
            description="The requested job could not be found."
          />
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        <AdminPageHeader
          title="Edit Job"
          description="Update job posting details"
          breadcrumbItems={[
            { label: 'Admin', href: '/admin' },
            { label: 'Jobs', href: '/admin/jobs' },
            { label: formData.title, href: `/admin/jobs/${params.id}` },
            { label: 'Edit' }
          ]}
          actions={
            <Button 
              variant="outline" 
              onClick={() => router.push(`/admin/jobs/${params.id}`)}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Job
            </Button>
          }
        />

        {error && (
          <Card className="border-destructive">
            <CardContent className="p-4">
              <p className="text-destructive text-sm">{error}</p>
            </CardContent>
          </Card>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Update the basic details for the job posting
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Job Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="e.g. Senior Software Engineer"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category} value={category.toLowerCase()}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="experienceLevel">Experience Level *</Label>
                  <Select value={formData.experienceLevel} onValueChange={(value) => handleInputChange('experienceLevel', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select experience level" />
                    </SelectTrigger>
                    <SelectContent>
                      {experienceLevels.map((level) => (
                        <SelectItem key={level} value={level.toLowerCase().replace(' ', '-')}>
                          {level}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="paused">Paused</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="closed">Closed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Job Description *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Describe the role, company culture, and what makes this opportunity unique..."
                  rows={6}
                  required
                />
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push(`/admin/jobs/${params.id}`)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              <Save className="w-4 h-4 mr-2" />
              {isSubmitting ? 'Updating...' : 'Update Job'}
            </Button>
          </div>
        </form>
      </div>
    </AdminLayout>
  )
}

'use client'

import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { AdminPageHeader } from '@/components/admin/admin-page-header'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Star, 
  StarOff, 
  Play, 
  Pause, 
  Eye, 
  Users, 
  MapPin, 
  DollarSign, 
  Calendar,
  Building2,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { ErrorState, LoadingState } from '@/components/ui/empty-state'

interface Job {
  _id: string
  title: string
  description: string
  company: {
    _id: string
    name: string
    logo?: string
    isVerified: boolean
  }
  location: {
    city: string
    state: string
    country: string
    remote: boolean
    hybrid: boolean
  }
  salary: {
    min: number
    max: number
    currency: string
    period: string
    negotiable: boolean
  }
  jobType: string[]
  category: string
  experienceLevel: string
  requirements: string[]
  responsibilities: string[]
  benefits: string[]
  skills: string[]
  status: string
  isFeatured: boolean
  isUrgent: boolean
  applicationCount: number
  viewCount: number
  applicationDeadline?: string
  createdAt: string
  updatedAt: string
}

export default function JobDetailsPage() {
  const router = useRouter()
  const params = useParams()
  const { token } = useAuthStore()
  const [job, setJob] = useState<Job | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isUpdating, setIsUpdating] = useState(false)

  useEffect(() => {
    if (params.id) {
      fetchJob()
    }
  }, [params.id])

  const fetchJob = async () => {
    try {
      setIsLoading(true)
      setError(null)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/v1/admin/jobs/${params.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch job details')
      }

      const data = await response.json()
      setJob(data.data.job)
    } catch (error) {
      console.error('Error fetching job:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch job details')
    } finally {
      setIsLoading(false)
    }
  }

  const handleJobAction = async (action: string, data?: any) => {
    try {
      setIsUpdating(true)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/v1/admin/jobs/${params.id}/${action}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data || {})
      })

      if (!response.ok) {
        throw new Error(`Failed to ${action} job`)
      }

      // Refresh job data
      await fetchJob()
    } catch (error) {
      console.error(`Failed to ${action} job:`, error)
      setError(error instanceof Error ? error.message : `Failed to ${action} job`)
    } finally {
      setIsUpdating(false)
    }
  }

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this job? This action cannot be undone.')) {
      return
    }

    try {
      setIsUpdating(true)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/v1/admin/jobs/${params.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to delete job')
      }

      router.push('/admin/jobs')
    } catch (error) {
      console.error('Failed to delete job:', error)
      setError(error instanceof Error ? error.message : 'Failed to delete job')
    } finally {
      setIsUpdating(false)
    }
  }

  const formatSalary = (salary: Job['salary']) => {
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: salary.currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })

    if (salary.min && salary.max) {
      return `${formatter.format(salary.min)} - ${formatter.format(salary.max)} / ${salary.period}`
    } else if (salary.max) {
      return `Up to ${formatter.format(salary.max)} / ${salary.period}`
    } else if (salary.min) {
      return `From ${formatter.format(salary.min)} / ${salary.period}`
    }
    return 'Salary not specified'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'paused': return 'bg-yellow-100 text-yellow-800'
      case 'closed': return 'bg-red-100 text-red-800'
      case 'draft': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4" />
      case 'paused': return <Pause className="w-4 h-4" />
      case 'closed': return <XCircle className="w-4 h-4" />
      case 'draft': return <Clock className="w-4 h-4" />
      default: return <AlertTriangle className="w-4 h-4" />
    }
  }

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="p-6">
          <LoadingState message="Loading job details..." />
        </div>
      </AdminLayout>
    )
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="p-6">
          <ErrorState
            title="Failed to load job details"
            description={error}
            onRetry={fetchJob}
          />
        </div>
      </AdminLayout>
    )
  }

  if (!job) {
    return (
      <AdminLayout>
        <div className="p-6">
          <ErrorState
            title="Job not found"
            description="The requested job could not be found."
          />
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        <AdminPageHeader
          title={job.title}
          description={`Job posted by ${job.company.name}`}
          breadcrumbItems={[
            { label: 'Admin', href: '/admin' },
            { label: 'Jobs', href: '/admin/jobs' },
            { label: job.title }
          ]}
          actions={
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                onClick={() => router.push('/admin/jobs')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Jobs
              </Button>
              <Button 
                variant="outline"
                onClick={() => router.push(`/admin/jobs/${job._id}/edit`)}
                disabled={isUpdating}
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
              <Button 
                variant="destructive"
                onClick={handleDelete}
                disabled={isUpdating}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete
              </Button>
            </div>
          }
        />

        {error && (
          <Card className="border-destructive">
            <CardContent className="p-4">
              <p className="text-destructive text-sm">{error}</p>
            </CardContent>
          </Card>
        )}

        {/* Job Header */}
        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-4">
                <Avatar className="w-16 h-16">
                  <AvatarImage src={job.company.logo} />
                  <AvatarFallback>
                    <Building2 className="w-8 h-8" />
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="flex items-center space-x-2 mb-2">
                    <h1 className="text-2xl font-bold">{job.title}</h1>
                    {job.isFeatured && <Star className="w-5 h-5 text-yellow-500" />}
                    {job.isUrgent && <Badge variant="destructive">Urgent</Badge>}
                  </div>
                  <div className="flex items-center space-x-2 text-muted-foreground">
                    <span>{job.company.name}</span>
                    {job.company.isVerified && (
                      <Badge variant="default" className="text-xs">Verified</Badge>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge className={cn('flex items-center space-x-1', getStatusColor(job.status))}>
                  {getStatusIcon(job.status)}
                  <span className="capitalize">{job.status}</span>
                </Badge>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Job Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Users className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="text-2xl font-bold">{job.applicationCount}</p>
                  <p className="text-sm text-muted-foreground">Applications</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Eye className="w-5 h-5 text-green-600" />
                <div>
                  <p className="text-2xl font-bold">{job.viewCount}</p>
                  <p className="text-sm text-muted-foreground">Views</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <DollarSign className="w-5 h-5 text-yellow-600" />
                <div>
                  <p className="text-sm font-medium">{formatSalary(job.salary)}</p>
                  <p className="text-sm text-muted-foreground">Salary Range</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Calendar className="w-5 h-5 text-purple-600" />
                <div>
                  <p className="text-sm font-medium">
                    {job.applicationDeadline
                      ? new Date(job.applicationDeadline).toLocaleDateString()
                      : 'No deadline'
                    }
                  </p>
                  <p className="text-sm text-muted-foreground">Application Deadline</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleJobAction(job.isFeatured ? 'unfeature' : 'feature')}
                disabled={isUpdating}
              >
                {job.isFeatured ? <StarOff className="w-4 h-4 mr-2" /> : <Star className="w-4 h-4 mr-2" />}
                {job.isFeatured ? 'Remove Featured' : 'Make Featured'}
              </Button>

              {job.status === 'active' ? (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleJobAction('pause')}
                  disabled={isUpdating}
                >
                  <Pause className="w-4 h-4 mr-2" />
                  Pause Job
                </Button>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleJobAction('activate')}
                  disabled={isUpdating}
                >
                  <Play className="w-4 h-4 mr-2" />
                  Activate Job
                </Button>
              )}

              <Button
                variant="outline"
                size="sm"
                onClick={() => handleJobAction('boost')}
                disabled={isUpdating}
              >
                <Eye className="w-4 h-4 mr-2" />
                Boost Visibility
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Job Details Tabs */}
        <Tabs defaultValue="details" className="space-y-4">
          <TabsList>
            <TabsTrigger value="details">Job Details</TabsTrigger>
            <TabsTrigger value="applications">Applications ({job.applicationCount})</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Job Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Job Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Category</Label>
                    <p className="capitalize">{job.category}</p>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Experience Level</Label>
                    <p className="capitalize">{job.experienceLevel.replace('-', ' ')}</p>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Job Type</Label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {job.jobType.map((type, index) => (
                        <Badge key={index} variant="outline">{type}</Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Location</Label>
                    <div className="flex items-center space-x-1 mt-1">
                      <MapPin className="w-4 h-4 text-muted-foreground" />
                      <span>
                        {job.location.city && job.location.state
                          ? `${job.location.city}, ${job.location.state}, ${job.location.country}`
                          : job.location.country
                        }
                      </span>
                    </div>
                    {(job.location.remote || job.location.hybrid) && (
                      <div className="flex space-x-2 mt-1">
                        {job.location.remote && <Badge variant="secondary">Remote</Badge>}
                        {job.location.hybrid && <Badge variant="secondary">Hybrid</Badge>}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Company Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Company Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Avatar className="w-12 h-12">
                      <AvatarImage src={job.company.logo} />
                      <AvatarFallback>
                        <Building2 className="w-6 h-6" />
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{job.company.name}</p>
                      {job.company.isVerified && (
                        <Badge variant="default" className="text-xs">Verified Company</Badge>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Posted</Label>
                    <p>{new Date(job.createdAt).toLocaleDateString()}</p>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Last Updated</Label>
                    <p>{new Date(job.updatedAt).toLocaleDateString()}</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Job Description */}
            <Card>
              <CardHeader>
                <CardTitle>Job Description</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose max-w-none">
                  <p className="whitespace-pre-wrap">{job.description}</p>
                </div>
              </CardContent>
            </Card>

            {/* Requirements and Responsibilities */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Requirements</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {job.requirements.map((requirement, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{requirement}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Responsibilities</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {job.responsibilities.map((responsibility, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{responsibility}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>

            {/* Skills and Benefits */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Required Skills</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {job.skills.map((skill, index) => (
                      <Badge key={index} variant="outline">{skill}</Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Benefits & Perks</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {job.benefits.map((benefit, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <CheckCircle className="w-4 h-4 text-purple-600 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{benefit}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="applications">
            <Card>
              <CardHeader>
                <CardTitle>Job Applications</CardTitle>
                <CardDescription>
                  Manage applications for this job posting
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Application management interface will be implemented here.
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics">
            <Card>
              <CardHeader>
                <CardTitle>Job Analytics</CardTitle>
                <CardDescription>
                  View performance metrics for this job posting
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Analytics dashboard will be implemented here.
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  )
}

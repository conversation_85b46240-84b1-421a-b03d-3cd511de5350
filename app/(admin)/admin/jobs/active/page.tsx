'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Search, 
  MoreHorizontal,
  Eye,
  Edit,
  Clock,
  Star,
  StarOff,
  Building2,
  MapPin,
  DollarSign,
  Calendar,
  Users,
  RefreshCw,
  Download,
  ArrowLeft,
  CheckCircle,
  TrendingUp
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface Job {
  _id: string
  title: string
  description: string
  company: {
    _id: string
    name: string
    logo?: string
    isVerified: boolean
  }
  location: {
    city: string
    state: string
    country: string
    remote: boolean
  }
  salary: {
    min: number
    max: number
    currency: string
    period: string
  }
  jobType: string[]
  category: string
  experienceLevel: string
  status: string
  isFeatured: boolean
  isUrgent: boolean
  applicationCount: number
  viewCount: number
  createdAt: Date
  updatedAt: Date
  expiresAt: Date
}

export default function ActiveJobsPage() {
  const router = useRouter()
  const { token } = useAuthStore()
  const [jobs, setJobs] = useState<Job[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [error, setError] = useState<string | null>(null)

  const fetchActiveJobs = async () => {
    try {
      setIsLoading(true)
      setError(null)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const queryParams = new URLSearchParams({
        status: 'active',
        ...(searchQuery && { search: searchQuery })
      })

      const response = await fetch(`/api/v1/admin/jobs?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch active jobs')
      }

      const data = await response.json()
      setJobs(data.data.jobs || [])
    } catch (error) {
      console.error('Failed to fetch active jobs:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch active jobs')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchActiveJobs()
  }, [])

  const handleSearch = () => {
    fetchActiveJobs()
  }

  const handleJobAction = async (action: string, jobId: string) => {
    try {
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/v1/admin/jobs/${jobId}/${action}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to ${action} job`)
      }

      await fetchActiveJobs()
    } catch (error) {
      console.error(`Failed to ${action} job:`, error)
      setError(error instanceof Error ? error.message : `Failed to ${action} job`)
    }
  }

  const formatSalary = (salary: Job['salary']) => {
    if (!salary.min && !salary.max) return 'Not specified'
    
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: salary.currency || 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })
    
    if (salary.min && salary.max) {
      return `${formatter.format(salary.min)} - ${formatter.format(salary.max)} / ${salary.period || 'year'}`
    } else if (salary.min) {
      return `${formatter.format(salary.min)}+ / ${salary.period || 'year'}`
    } else {
      return `Up to ${formatter.format(salary.max)} / ${salary.period || 'year'}`
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => router.push('/admin/jobs')}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Jobs
            </Button>
            <div>
              <h1 className="text-3xl font-bold flex items-center">
                <CheckCircle className="w-8 h-8 mr-3 text-green-600" />
                Active Jobs
              </h1>
              <p className="text-muted-foreground">
                Currently active job postings accepting applications
              </p>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={fetchActiveJobs} disabled={isLoading}>
              <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
              Refresh
            </Button>
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Active Jobs</p>
                  <p className="text-2xl font-bold">{jobs.length}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Featured Jobs</p>
                  <p className="text-2xl font-bold">{jobs.filter(j => j.isFeatured).length}</p>
                </div>
                <Star className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Applications</p>
                  <p className="text-2xl font-bold">{jobs.reduce((sum, j) => sum + j.applicationCount, 0)}</p>
                </div>
                <Users className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Views</p>
                  <p className="text-2xl font-bold">{jobs.reduce((sum, j) => sum + j.viewCount, 0)}</p>
                </div>
                <TrendingUp className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Jobs Table */}
        <Card>
          <CardHeader>
            <CardTitle>Active Jobs Management</CardTitle>
            <CardDescription>Manage currently active job postings</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Search */}
            <div className="flex gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search active jobs..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button onClick={handleSearch} disabled={isLoading}>
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
            </div>

            {/* Jobs Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Job Details</TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Location & Salary</TableHead>
                    <TableHead>Performance</TableHead>
                    <TableHead>Posted</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
                        Loading active jobs...
                      </TableCell>
                    </TableRow>
                  ) : jobs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        No active jobs found
                      </TableCell>
                    </TableRow>
                  ) : (
                    jobs.map((job) => (
                      <TableRow key={job._id}>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center space-x-2">
                              <p className="font-medium">{job.title}</p>
                              {job.isFeatured && <Star className="w-4 h-4 text-yellow-500" />}
                              {job.isUrgent && <Badge variant="destructive" className="text-xs">Urgent</Badge>}
                            </div>
                            <p className="text-sm text-muted-foreground">{job.category}</p>
                            <div className="flex flex-wrap gap-1">
                              {job.jobType.map((type, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {type}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <Avatar className="w-8 h-8">
                              <AvatarImage src={job.company.logo} />
                              <AvatarFallback>
                                <Building2 className="w-4 h-4" />
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{job.company.name}</p>
                              {job.company.isVerified && (
                                <Badge variant="default" className="text-xs">Verified</Badge>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center text-sm">
                              <MapPin className="w-4 h-4 mr-1" />
                              {job.location.remote ? 'Remote' : `${job.location.city}, ${job.location.state}`}
                            </div>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <DollarSign className="w-4 h-4 mr-1" />
                              {formatSalary(job.salary)}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center text-sm">
                              <Users className="w-4 h-4 mr-1" />
                              {job.applicationCount} applications
                            </div>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <TrendingUp className="w-4 h-4 mr-1" />
                              {job.viewCount} views
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Calendar className="w-4 h-4 mr-1" />
                            {new Date(job.createdAt).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => router.push(`/admin/jobs/${job._id}`)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => router.push(`/admin/jobs/${job._id}/edit`)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Job
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {job.isFeatured ? (
                                <DropdownMenuItem 
                                  onClick={() => handleJobAction('unfeature', job._id)}
                                  className="text-orange-600"
                                >
                                  <StarOff className="mr-2 h-4 w-4" />
                                  Remove Featured
                                </DropdownMenuItem>
                              ) : (
                                <DropdownMenuItem 
                                  onClick={() => handleJobAction('feature', job._id)}
                                  className="text-yellow-600"
                                >
                                  <Star className="mr-2 h-4 w-4" />
                                  Make Featured
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem 
                                onClick={() => handleJobAction('pause', job._id)}
                                className="text-orange-600"
                              >
                                <Clock className="mr-2 h-4 w-4" />
                                Pause Job
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}

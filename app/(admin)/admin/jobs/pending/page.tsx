'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Textarea } from '@/components/ui/textarea'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Search, 
  MoreHorizontal,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  Building2,
  MapPin,
  DollarSign,
  Calendar,
  RefreshCw,
  Download,
  ArrowLeft,
  AlertTriangle,
  FileText
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface Job {
  _id: string
  title: string
  description: string
  company: {
    _id: string
    name: string
    logo?: string
    isVerified: boolean
  }
  location: {
    city: string
    state: string
    country: string
    remote: boolean
  }
  salary: {
    min: number
    max: number
    currency: string
    period: string
  }
  jobType: string[]
  category: string
  experienceLevel: string
  status: string
  createdAt: Date
  postedBy: {
    _id: string
    firstName: string
    lastName: string
    email: string
  }
  moderationNotes?: string
}

export default function PendingJobsPage() {
  const router = useRouter()
  const { token } = useAuthStore()
  const [jobs, setJobs] = useState<Job[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [selectedJob, setSelectedJob] = useState<Job | null>(null)
  const [moderationAction, setModerationAction] = useState<'approve' | 'reject' | null>(null)
  const [moderationNotes, setModerationNotes] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const fetchPendingJobs = async () => {
    try {
      setIsLoading(true)
      setError(null)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const queryParams = new URLSearchParams({
        status: 'pending',
        ...(searchQuery && { search: searchQuery })
      })

      const response = await fetch(`/api/v1/admin/jobs?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch pending jobs')
      }

      const data = await response.json()
      setJobs(data.data.jobs || [])
    } catch (error) {
      console.error('Failed to fetch pending jobs:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch pending jobs')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchPendingJobs()
  }, [])

  const handleSearch = () => {
    fetchPendingJobs()
  }

  const handleModerationSubmit = async () => {
    if (!selectedJob || !moderationAction) return

    try {
      setIsSubmitting(true)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/v1/admin/jobs/${selectedJob._id}/moderate`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: moderationAction,
          notes: moderationNotes
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to ${moderationAction} job`)
      }

      // Reset form and refresh jobs
      setSelectedJob(null)
      setModerationAction(null)
      setModerationNotes('')
      await fetchPendingJobs()
    } catch (error) {
      console.error(`Failed to ${moderationAction} job:`, error)
      setError(error instanceof Error ? error.message : `Failed to ${moderationAction} job`)
    } finally {
      setIsSubmitting(false)
    }
  }

  const formatSalary = (salary: Job['salary']) => {
    if (!salary.min && !salary.max) return 'Not specified'
    
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: salary.currency || 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })
    
    if (salary.min && salary.max) {
      return `${formatter.format(salary.min)} - ${formatter.format(salary.max)} / ${salary.period || 'year'}`
    } else if (salary.min) {
      return `${formatter.format(salary.min)}+ / ${salary.period || 'year'}`
    } else {
      return `Up to ${formatter.format(salary.max)} / ${salary.period || 'year'}`
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => router.push('/admin/jobs')}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Jobs
            </Button>
            <div>
              <h1 className="text-3xl font-bold flex items-center">
                <Clock className="w-8 h-8 mr-3 text-orange-600" />
                Pending Review
              </h1>
              <p className="text-muted-foreground">
                Job postings awaiting moderation approval
              </p>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={fetchPendingJobs} disabled={isLoading}>
              <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
              Refresh
            </Button>
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Stats Card */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Jobs Pending Review</p>
                <p className="text-2xl font-bold">{jobs.length}</p>
                <p className="text-sm text-muted-foreground mt-1">
                  Requires immediate attention
                </p>
              </div>
              <AlertTriangle className="w-12 h-12 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        {/* Search and Jobs Table */}
        <Card>
          <CardHeader>
            <CardTitle>Pending Jobs Review</CardTitle>
            <CardDescription>Review and moderate job postings before they go live</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Search */}
            <div className="flex gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search pending jobs..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button onClick={handleSearch} disabled={isLoading}>
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
            </div>

            {/* Jobs Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Job Details</TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Location & Salary</TableHead>
                    <TableHead>Posted By</TableHead>
                    <TableHead>Submitted</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
                        Loading pending jobs...
                      </TableCell>
                    </TableRow>
                  ) : jobs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-600" />
                        <p className="text-lg font-medium">All caught up!</p>
                        <p className="text-muted-foreground">No jobs pending review at the moment.</p>
                      </TableCell>
                    </TableRow>
                  ) : (
                    jobs.map((job) => (
                      <TableRow key={job._id}>
                        <TableCell>
                          <div className="space-y-1">
                            <p className="font-medium">{job.title}</p>
                            <p className="text-sm text-muted-foreground">{job.category}</p>
                            <div className="flex flex-wrap gap-1">
                              {job.jobType.map((type, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {type}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <Avatar className="w-8 h-8">
                              <AvatarImage src={job.company.logo} />
                              <AvatarFallback>
                                <Building2 className="w-4 h-4" />
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{job.company.name}</p>
                              {job.company.isVerified && (
                                <Badge variant="default" className="text-xs">Verified</Badge>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center text-sm">
                              <MapPin className="w-4 h-4 mr-1" />
                              {job.location.remote ? 'Remote' : `${job.location.city}, ${job.location.state}`}
                            </div>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <DollarSign className="w-4 h-4 mr-1" />
                              {formatSalary(job.salary)}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <p className="font-medium text-sm">
                              {job.postedBy.firstName} {job.postedBy.lastName}
                            </p>
                            <p className="text-sm text-muted-foreground">{job.postedBy.email}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Calendar className="w-4 h-4 mr-1" />
                            {new Date(job.createdAt).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end space-x-2">
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => setSelectedJob(job)}
                                >
                                  <Eye className="w-4 h-4 mr-2" />
                                  Review
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                                <DialogHeader>
                                  <DialogTitle>Review Job Posting</DialogTitle>
                                  <DialogDescription>
                                    Review the job details and approve or reject the posting
                                  </DialogDescription>
                                </DialogHeader>
                                
                                {selectedJob && (
                                  <div className="space-y-6">
                                    {/* Job Details */}
                                    <div className="grid grid-cols-2 gap-6">
                                      <div className="space-y-4">
                                        <div>
                                          <h3 className="font-semibold mb-2">Job Information</h3>
                                          <div className="space-y-2">
                                            <p><strong>Title:</strong> {selectedJob.title}</p>
                                            <p><strong>Category:</strong> {selectedJob.category}</p>
                                            <p><strong>Experience Level:</strong> {selectedJob.experienceLevel}</p>
                                            <p><strong>Job Types:</strong> {selectedJob.jobType.join(', ')}</p>
                                          </div>
                                        </div>
                                        
                                        <div>
                                          <h3 className="font-semibold mb-2">Location & Salary</h3>
                                          <div className="space-y-2">
                                            <p><strong>Location:</strong> {selectedJob.location.remote ? 'Remote' : `${selectedJob.location.city}, ${selectedJob.location.state}, ${selectedJob.location.country}`}</p>
                                            <p><strong>Salary:</strong> {formatSalary(selectedJob.salary)}</p>
                                          </div>
                                        </div>
                                      </div>
                                      
                                      <div className="space-y-4">
                                        <div>
                                          <h3 className="font-semibold mb-2">Company Information</h3>
                                          <div className="flex items-center space-x-3 mb-2">
                                            <Avatar className="w-10 h-10">
                                              <AvatarImage src={selectedJob.company.logo} />
                                              <AvatarFallback>
                                                <Building2 className="w-5 h-5" />
                                              </AvatarFallback>
                                            </Avatar>
                                            <div>
                                              <p className="font-medium">{selectedJob.company.name}</p>
                                              {selectedJob.company.isVerified && (
                                                <Badge variant="default" className="text-xs">Verified</Badge>
                                              )}
                                            </div>
                                          </div>
                                        </div>
                                        
                                        <div>
                                          <h3 className="font-semibold mb-2">Posted By</h3>
                                          <div className="space-y-1">
                                            <p><strong>Name:</strong> {selectedJob.postedBy.firstName} {selectedJob.postedBy.lastName}</p>
                                            <p><strong>Email:</strong> {selectedJob.postedBy.email}</p>
                                            <p><strong>Date:</strong> {new Date(selectedJob.createdAt).toLocaleDateString()}</p>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    
                                    {/* Job Description */}
                                    <div>
                                      <h3 className="font-semibold mb-2">Job Description</h3>
                                      <div className="bg-gray-50 p-4 rounded-lg max-h-40 overflow-y-auto">
                                        <p className="whitespace-pre-wrap">{selectedJob.description}</p>
                                      </div>
                                    </div>
                                    
                                    {/* Moderation Actions */}
                                    <div className="space-y-4">
                                      <h3 className="font-semibold">Moderation Decision</h3>
                                      <div className="flex space-x-4">
                                        <Button
                                          variant={moderationAction === 'approve' ? 'default' : 'outline'}
                                          onClick={() => setModerationAction('approve')}
                                          className="flex-1"
                                        >
                                          <CheckCircle className="w-4 h-4 mr-2" />
                                          Approve Job
                                        </Button>
                                        <Button
                                          variant={moderationAction === 'reject' ? 'destructive' : 'outline'}
                                          onClick={() => setModerationAction('reject')}
                                          className="flex-1"
                                        >
                                          <XCircle className="w-4 h-4 mr-2" />
                                          Reject Job
                                        </Button>
                                      </div>
                                      
                                      <div>
                                        <label className="block text-sm font-medium mb-2">
                                          Moderation Notes {moderationAction === 'reject' && <span className="text-red-500">*</span>}
                                        </label>
                                        <Textarea
                                          placeholder={moderationAction === 'approve' ? 'Optional notes for approval...' : 'Please provide reason for rejection...'}
                                          value={moderationNotes}
                                          onChange={(e) => setModerationNotes(e.target.value)}
                                          rows={3}
                                        />
                                      </div>
                                    </div>
                                  </div>
                                )}
                                
                                <DialogFooter>
                                  <Button 
                                    variant="outline" 
                                    onClick={() => {
                                      setSelectedJob(null)
                                      setModerationAction(null)
                                      setModerationNotes('')
                                    }}
                                  >
                                    Cancel
                                  </Button>
                                  <Button 
                                    onClick={handleModerationSubmit}
                                    disabled={!moderationAction || (moderationAction === 'reject' && !moderationNotes.trim()) || isSubmitting}
                                  >
                                    {isSubmitting ? (
                                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                    ) : moderationAction === 'approve' ? (
                                      <CheckCircle className="w-4 h-4 mr-2" />
                                    ) : (
                                      <XCircle className="w-4 h-4 mr-2" />
                                    )}
                                    {moderationAction === 'approve' ? 'Approve Job' : 'Reject Job'}
                                  </Button>
                                </DialogFooter>
                              </DialogContent>
                            </Dialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}

'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { AdminPageHeader } from '@/components/admin/admin-page-header'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ArrowLeft, Plus, X, Save, Eye } from 'lucide-react'
import { cn } from '@/lib/utils'

interface JobFormData {
  title: string
  description: string
  requirements: string[]
  responsibilities: string[]
  category: string
  jobType: string[]
  experienceLevel: string
  location: {
    city: string
    state: string
    country: string
    remote: boolean
    hybrid: boolean
  }
  salary: {
    min: number
    max: number
    currency: string
    period: string
    negotiable: boolean
  }
  benefits: string[]
  skills: string[]
  applicationDeadline: string
  isFeatured: boolean
  isUrgent: boolean
  status: string
}

const initialFormData: JobFormData = {
  title: '',
  description: '',
  requirements: [''],
  responsibilities: [''],
  category: '',
  jobType: [],
  experienceLevel: '',
  location: {
    city: '',
    state: '',
    country: 'United States',
    remote: false,
    hybrid: false
  },
  salary: {
    min: 0,
    max: 0,
    currency: 'USD',
    period: 'year',
    negotiable: false
  },
  benefits: [''],
  skills: [''],
  applicationDeadline: '',
  isFeatured: false,
  isUrgent: false,
  status: 'active'
}

export default function CreateJobPage() {
  const router = useRouter()
  const { token } = useAuthStore()
  const [formData, setFormData] = useState<JobFormData>(initialFormData)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [companies, setCompanies] = useState<any[]>([])
  const [selectedCompany, setSelectedCompany] = useState<string>('')

  useEffect(() => {
    fetchCompanies()
  }, [])

  const fetchCompanies = async () => {
    try {
      if (!token) return

      const response = await fetch('/api/v1/admin/companies', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setCompanies(data.data.companies || [])
      }
    } catch (error) {
      console.error('Error fetching companies:', error)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleNestedInputChange = (parent: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent as keyof JobFormData] as any,
        [field]: value
      }
    }))
  }

  const handleArrayInputChange = (field: string, index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: (prev[field as keyof JobFormData] as string[]).map((item, i) => 
        i === index ? value : item
      )
    }))
  }

  const addArrayItem = (field: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: [...(prev[field as keyof JobFormData] as string[]), '']
    }))
  }

  const removeArrayItem = (field: string, index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: (prev[field as keyof JobFormData] as string[]).filter((_, i) => i !== index)
    }))
  }

  const handleJobTypeChange = (type: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      jobType: checked 
        ? [...prev.jobType, type]
        : prev.jobType.filter(t => t !== type)
    }))
  }

  const handleSubmit = async (e: React.FormEvent, asDraft = false) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError(null)

    try {
      if (!token) {
        throw new Error('No authentication token found')
      }

      if (!selectedCompany) {
        throw new Error('Please select a company')
      }

      const jobData = {
        ...formData,
        company: selectedCompany,
        status: asDraft ? 'draft' : formData.status,
        requirements: formData.requirements.filter(r => r.trim()),
        responsibilities: formData.responsibilities.filter(r => r.trim()),
        benefits: formData.benefits.filter(b => b.trim()),
        skills: formData.skills.filter(s => s.trim())
      }

      const response = await fetch('/api/v1/admin/jobs', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(jobData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create job')
      }

      const result = await response.json()
      
      // Redirect to job details or jobs list
      router.push(`/admin/jobs/${result.data.job._id}`)
      
    } catch (error) {
      console.error('Error creating job:', error)
      setError(error instanceof Error ? error.message : 'Failed to create job')
    } finally {
      setIsSubmitting(false)
    }
  }

  const jobTypes = ['Full-time', 'Part-time', 'Contract', 'Freelance', 'Internship', 'Temporary']
  const experienceLevels = ['Entry Level', 'Mid Level', 'Senior Level', 'Executive', 'Internship']
  const categories = ['Technology', 'Marketing', 'Sales', 'Design', 'Finance', 'Operations', 'HR', 'Legal', 'Other']

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        <AdminPageHeader
          title="Create New Job"
          description="Post a new job opening to the platform"
          breadcrumbItems={[
            { label: 'Admin', href: '/admin' },
            { label: 'Jobs', href: '/admin/jobs' },
            { label: 'Create Job' }
          ]}
          actions={
            <Button 
              variant="outline" 
              onClick={() => router.push('/admin/jobs')}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Jobs
            </Button>
          }
        />

        {error && (
          <Card className="border-destructive">
            <CardContent className="p-4">
              <p className="text-destructive text-sm">{error}</p>
            </CardContent>
          </Card>
        )}

        <form onSubmit={(e) => handleSubmit(e, false)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Enter the basic details for the job posting
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="company">Company *</Label>
                  <Select value={selectedCompany} onValueChange={setSelectedCompany}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a company" />
                    </SelectTrigger>
                    <SelectContent>
                      {companies.map((company) => (
                        <SelectItem key={company._id} value={company._id}>
                          {company.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="title">Job Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="e.g. Senior Software Engineer"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category} value={category.toLowerCase()}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="experienceLevel">Experience Level *</Label>
                  <Select value={formData.experienceLevel} onValueChange={(value) => handleInputChange('experienceLevel', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select experience level" />
                    </SelectTrigger>
                    <SelectContent>
                      {experienceLevels.map((level) => (
                        <SelectItem key={level} value={level.toLowerCase().replace(' ', '-')}>
                          {level}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Job Description *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Describe the role, company culture, and what makes this opportunity unique..."
                  rows={6}
                  required
                />
              </div>
            </CardContent>
          </Card>

          {/* Job Type and Work Arrangement */}
          <Card>
            <CardHeader>
              <CardTitle>Job Type & Work Arrangement</CardTitle>
              <CardDescription>
                Specify the employment type and work location preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <Label>Job Type *</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {jobTypes.map((type) => (
                    <div key={type} className="flex items-center space-x-2">
                      <Checkbox
                        id={type}
                        checked={formData.jobType.includes(type)}
                        onCheckedChange={(checked) => handleJobTypeChange(type, checked as boolean)}
                      />
                      <Label htmlFor={type} className="text-sm font-normal">
                        {type}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    value={formData.location.city}
                    onChange={(e) => handleNestedInputChange('location', 'city', e.target.value)}
                    placeholder="e.g. San Francisco"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="state">State</Label>
                  <Input
                    id="state"
                    value={formData.location.state}
                    onChange={(e) => handleNestedInputChange('location', 'state', e.target.value)}
                    placeholder="e.g. California"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="country">Country</Label>
                  <Input
                    id="country"
                    value={formData.location.country}
                    onChange={(e) => handleNestedInputChange('location', 'country', e.target.value)}
                    placeholder="e.g. United States"
                  />
                </div>
              </div>

              <div className="flex space-x-6">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="remote"
                    checked={formData.location.remote}
                    onCheckedChange={(checked) => handleNestedInputChange('location', 'remote', checked)}
                  />
                  <Label htmlFor="remote" className="text-sm font-normal">
                    Remote work available
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="hybrid"
                    checked={formData.location.hybrid}
                    onCheckedChange={(checked) => handleNestedInputChange('location', 'hybrid', checked)}
                  />
                  <Label htmlFor="hybrid" className="text-sm font-normal">
                    Hybrid work available
                  </Label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Requirements & Responsibilities */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Requirements</CardTitle>
                <CardDescription>
                  List the key requirements for this position
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {formData.requirements.map((requirement, index) => (
                  <div key={index} className="flex space-x-2">
                    <Input
                      value={requirement}
                      onChange={(e) => handleArrayInputChange('requirements', index, e.target.value)}
                      placeholder="e.g. Bachelor's degree in Computer Science"
                      className="flex-1"
                    />
                    {formData.requirements.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeArrayItem('requirements', index)}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => addArrayItem('requirements')}
                  className="w-full"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Requirement
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Responsibilities</CardTitle>
                <CardDescription>
                  Outline the key responsibilities of this role
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {formData.responsibilities.map((responsibility, index) => (
                  <div key={index} className="flex space-x-2">
                    <Input
                      value={responsibility}
                      onChange={(e) => handleArrayInputChange('responsibilities', index, e.target.value)}
                      placeholder="e.g. Design and develop web applications"
                      className="flex-1"
                    />
                    {formData.responsibilities.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeArrayItem('responsibilities', index)}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => addArrayItem('responsibilities')}
                  className="w-full"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Responsibility
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Skills & Benefits */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Required Skills</CardTitle>
                <CardDescription>
                  List the technical and soft skills needed
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {formData.skills.map((skill, index) => (
                  <div key={index} className="flex space-x-2">
                    <Input
                      value={skill}
                      onChange={(e) => handleArrayInputChange('skills', index, e.target.value)}
                      placeholder="e.g. React, Node.js, TypeScript"
                      className="flex-1"
                    />
                    {formData.skills.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeArrayItem('skills', index)}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => addArrayItem('skills')}
                  className="w-full"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Skill
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Benefits & Perks</CardTitle>
                <CardDescription>
                  Highlight the benefits and perks offered
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {formData.benefits.map((benefit, index) => (
                  <div key={index} className="flex space-x-2">
                    <Input
                      value={benefit}
                      onChange={(e) => handleArrayInputChange('benefits', index, e.target.value)}
                      placeholder="e.g. Health insurance, 401k matching"
                      className="flex-1"
                    />
                    {formData.benefits.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeArrayItem('benefits', index)}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => addArrayItem('benefits')}
                  className="w-full"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Benefit
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Salary Information */}
          <Card>
            <CardHeader>
              <CardTitle>Salary & Compensation</CardTitle>
              <CardDescription>
                Set the salary range and compensation details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="salaryMin">Minimum Salary</Label>
                  <Input
                    id="salaryMin"
                    type="number"
                    value={formData.salary.min || ''}
                    onChange={(e) => handleNestedInputChange('salary', 'min', parseInt(e.target.value) || 0)}
                    placeholder="50000"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="salaryMax">Maximum Salary</Label>
                  <Input
                    id="salaryMax"
                    type="number"
                    value={formData.salary.max || ''}
                    onChange={(e) => handleNestedInputChange('salary', 'max', parseInt(e.target.value) || 0)}
                    placeholder="80000"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="currency">Currency</Label>
                  <Select value={formData.salary.currency} onValueChange={(value) => handleNestedInputChange('salary', 'currency', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                      <SelectItem value="GBP">GBP</SelectItem>
                      <SelectItem value="CAD">CAD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="period">Period</Label>
                  <Select value={formData.salary.period} onValueChange={(value) => handleNestedInputChange('salary', 'period', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hour">Per Hour</SelectItem>
                      <SelectItem value="month">Per Month</SelectItem>
                      <SelectItem value="year">Per Year</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="negotiable"
                  checked={formData.salary.negotiable}
                  onCheckedChange={(checked) => handleNestedInputChange('salary', 'negotiable', checked)}
                />
                <Label htmlFor="negotiable" className="text-sm font-normal">
                  Salary is negotiable
                </Label>
              </div>
            </CardContent>
          </Card>

          {/* Additional Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Additional Settings</CardTitle>
              <CardDescription>
                Configure deadline and special flags for this job
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="deadline">Application Deadline</Label>
                  <Input
                    id="deadline"
                    type="date"
                    value={formData.applicationDeadline}
                    onChange={(e) => handleInputChange('applicationDeadline', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="paused">Paused</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex space-x-6">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="featured"
                    checked={formData.isFeatured}
                    onCheckedChange={(checked) => handleInputChange('isFeatured', checked)}
                  />
                  <Label htmlFor="featured" className="text-sm font-normal">
                    Featured job (appears at top of listings)
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="urgent"
                    checked={formData.isUrgent}
                    onCheckedChange={(checked) => handleInputChange('isUrgent', checked)}
                  />
                  <Label htmlFor="urgent" className="text-sm font-normal">
                    Urgent hiring (shows urgent badge)
                  </Label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/admin/jobs')}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={(e) => handleSubmit(e, true)}
              disabled={isSubmitting}
            >
              <Save className="w-4 h-4 mr-2" />
              Save as Draft
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              <Eye className="w-4 h-4 mr-2" />
              {isSubmitting ? 'Creating...' : 'Publish Job'}
            </Button>
          </div>
        </form>
      </div>
    </AdminLayout>
  )
}

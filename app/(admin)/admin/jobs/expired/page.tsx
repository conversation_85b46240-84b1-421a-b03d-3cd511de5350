'use client'

import React, { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { AdminPageHeader } from '@/components/admin/admin-page-header'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Search, 
  MoreHorizontal,
  Eye,
  Edit,
  RotateCcw,
  Archive,
  Building2,
  MapPin,
  DollarSign,
  Calendar,
  Users,
  RefreshCw,
  Download,
  ArrowLeft,
  TrendingDown,
  Clock,
  AlertTriangle
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { TableEmptyState, ErrorState } from '@/components/ui/empty-state'

interface Job {
  _id: string
  title: string
  description: string
  company: {
    _id: string
    name: string
    logo?: string
    isVerified: boolean
  }
  location: {
    city: string
    state: string
    country: string
    remote: boolean
  }
  salary: {
    min: number
    max: number
    currency: string
    period: string
  }
  jobType: string[]
  category: string
  experienceLevel: string
  status: string
  isFeatured: boolean
  isUrgent: boolean
  applicationCount: number
  viewCount: number
  createdAt: Date
  updatedAt: Date
  applicationDeadline?: Date
  expiredAt?: Date
}

export default function ExpiredJobsPage() {
  const router = useRouter()
  const { token } = useAuthStore()
  const [jobs, setJobs] = useState<Job[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [error, setError] = useState<string | null>(null)

  const fetchExpiredJobs = async () => {
    try {
      setIsLoading(true)
      setError(null)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const queryParams = new URLSearchParams({
        status: 'expired',
        ...(searchQuery && { search: searchQuery })
      })

      const response = await fetch(`/api/v1/admin/jobs?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch expired jobs')
      }

      const data = await response.json()
      setJobs(data.data.jobs || [])
    } catch (error) {
      console.error('Failed to fetch expired jobs:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch expired jobs')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchExpiredJobs()
  }, [])

  const handleSearch = () => {
    fetchExpiredJobs()
  }

  const handleJobAction = async (action: string, jobId: string) => {
    try {
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/v1/admin/jobs/${jobId}/${action}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to ${action} job`)
      }

      await fetchExpiredJobs()
    } catch (error) {
      console.error(`Failed to ${action} job:`, error)
      setError(error instanceof Error ? error.message : `Failed to ${action} job`)
    }
  }

  const formatSalary = (salary: Job['salary']) => {
    if (!salary.min && !salary.max) return 'Not specified'
    
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: salary.currency || 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })
    
    if (salary.min && salary.max) {
      return `${formatter.format(salary.min)} - ${formatter.format(salary.max)} / ${salary.period || 'year'}`
    } else if (salary.min) {
      return `${formatter.format(salary.min)}+ / ${salary.period || 'year'}`
    } else {
      return `Up to ${formatter.format(salary.max)} / ${salary.period || 'year'}`
    }
  }

  const getDaysExpired = (expiredAt: Date) => {
    const now = new Date()
    const expired = new Date(expiredAt)
    const diffTime = Math.abs(now.getTime() - expired.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="space-y-6 p-6">
          <AdminPageHeader
            title="Expired Jobs"
            description="Manage jobs that have reached their expiration date"
            icon={Clock}
            breadcrumbItems={[
              { label: 'Admin', href: '/admin' },
              { label: 'Jobs', href: '/admin/jobs' },
              { label: 'Expired Jobs' }
            ]}
            actions={
              <Button 
                variant="outline" 
                onClick={() => router.push('/admin/jobs')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Jobs
              </Button>
            }
          />
          <ErrorState
            title="Failed to load expired jobs"
            description={error}
            onRetry={fetchExpiredJobs}
          />
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <AdminPageHeader
          title="Expired Jobs"
          description="Manage jobs that have reached their expiration date"
          icon={Clock}
          breadcrumbItems={[
            { label: 'Admin', href: '/admin' },
            { label: 'Jobs', href: '/admin/jobs' },
            { label: 'Expired Jobs' }
          ]}
          actions={
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                onClick={() => router.push('/admin/jobs')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Jobs
              </Button>
              <Button variant="outline" onClick={fetchExpiredJobs} disabled={isLoading}>
                <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
                Refresh
              </Button>
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          }
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Expired Jobs</p>
                  <p className="text-2xl font-bold">{jobs.length}</p>
                </div>
                <Clock className="w-8 h-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Recently Expired</p>
                  <p className="text-2xl font-bold">
                    {jobs.filter(j => j.expiredAt && getDaysExpired(j.expiredAt) <= 7).length}
                  </p>
                </div>
                <AlertTriangle className="w-8 h-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Applications</p>
                  <p className="text-2xl font-bold">{jobs.reduce((sum, j) => sum + j.applicationCount, 0)}</p>
                </div>
                <Users className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Avg. Applications</p>
                  <p className="text-2xl font-bold">
                    {jobs.length > 0 ? Math.round(jobs.reduce((sum, j) => sum + j.applicationCount, 0) / jobs.length) : 0}
                  </p>
                </div>
                <TrendingDown className="w-8 h-8 text-gray-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Jobs Table */}
        <Card>
          <CardHeader>
            <CardTitle>Expired Jobs Management</CardTitle>
            <CardDescription>Review and manage jobs that have reached their expiration date</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Search */}
            <div className="flex gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search expired jobs..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button onClick={handleSearch} disabled={isLoading}>
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
            </div>

            {/* Jobs Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Job Details</TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Location & Salary</TableHead>
                    <TableHead>Expired Date</TableHead>
                    <TableHead>Performance</TableHead>
                    <TableHead>Days Expired</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableEmptyState
                      title="Loading expired jobs..."
                      colSpan={7}
                    />
                  ) : jobs.length === 0 ? (
                    <TableEmptyState
                      icon={Clock}
                      title="No expired jobs"
                      description="No jobs have expired yet. Jobs will appear here when they reach their expiration date."
                      action={{
                        label: 'View All Jobs',
                        onClick: () => router.push('/admin/jobs')
                      }}
                      colSpan={7}
                    />
                  ) : (
                    jobs.map((job) => (
                      <TableRow key={job._id} className="bg-gradient-to-r from-red-50 to-transparent">
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center space-x-2">
                              <Clock className="w-4 h-4 text-red-500" />
                              <p className="font-medium">{job.title}</p>
                              {job.isUrgent && <Badge variant="destructive" className="text-xs">Urgent</Badge>}
                              {job.isFeatured && (
                                <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-800">
                                  Featured
                                </Badge>
                              )}
                              <Badge variant="destructive" className="text-xs">
                                Expired
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground">{job.category}</p>
                            <div className="flex flex-wrap gap-1">
                              {job.jobType.map((type, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {type}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <Avatar className="w-8 h-8">
                              <AvatarImage src={job.company.logo} />
                              <AvatarFallback>
                                <Building2 className="w-4 h-4" />
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{job.company.name}</p>
                              {job.company.isVerified && (
                                <Badge variant="default" className="text-xs">Verified</Badge>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center text-sm">
                              <MapPin className="w-4 h-4 mr-1" />
                              {job.location.remote ? 'Remote' : `${job.location.city}, ${job.location.state}`}
                            </div>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <DollarSign className="w-4 h-4 mr-1" />
                              {formatSalary(job.salary)}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-sm text-red-600">
                            <Calendar className="w-4 h-4 mr-1" />
                            {job.expiredAt ? 
                              new Date(job.expiredAt).toLocaleDateString() : 
                              new Date(job.applicationDeadline || job.updatedAt).toLocaleDateString()
                            }
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center text-sm">
                              <Users className="w-4 h-4 mr-1" />
                              {job.applicationCount} applications
                            </div>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <TrendingDown className="w-4 h-4 mr-1" />
                              {job.viewCount} views
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-sm">
                            <AlertTriangle className="w-4 h-4 mr-1 text-red-500" />
                            <span className="text-red-600 font-medium">
                              {job.expiredAt ? 
                                `${getDaysExpired(job.expiredAt)} days` : 
                                'Recently'
                              }
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => router.push(`/admin/jobs/${job._id}`)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => router.push(`/admin/jobs/${job._id}/edit`)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Job
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                onClick={() => handleJobAction('activate', job._id)}
                                className="text-green-600"
                              >
                                <RotateCcw className="mr-2 h-4 w-4" />
                                Reactivate Job
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => handleJobAction('archive', job._id)}
                                className="text-orange-600"
                              >
                                <Archive className="mr-2 h-4 w-4" />
                                Archive Job
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}

'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Textarea } from '@/components/ui/textarea'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Search, 
  MoreHorizontal,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Eye,
  ArrowLeft,
  RefreshCw,
  Download,
  Building2,
  FileText,
  Calendar,
  MapPin,
  Globe,
  Users,
  Clock
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface PendingCompany {
  _id: string
  name: string
  slug: string
  email: string
  logo?: string
  website?: string
  description: string
  location: {
    city: string
    state: string
    country: string
  }
  industry: string
  size: string
  verificationDocuments: {
    businessLicense?: string
    taxId?: string
    incorporationCertificate?: string
    additionalDocs?: string[]
  }
  submittedAt: Date
  lastUpdated: Date
  adminUsers: number
  contactPerson: {
    name: string
    email: string
    phone?: string
    position: string
  }
  businessDetails: {
    foundedYear: number
    revenue?: string
    employees: number
    businessType: string
  }
}

export default function PendingCompaniesPage() {
  const router = useRouter()
  const [pendingCompanies, setPendingCompanies] = useState<PendingCompany[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCompany, setSelectedCompany] = useState<PendingCompany | null>(null)
  const [verificationAction, setVerificationAction] = useState<'approve' | 'reject' | null>(null)
  const [verificationNotes, setVerificationNotes] = useState('')

  useEffect(() => {
    fetchPendingCompanies()
  }, [])

  const fetchPendingCompanies = async () => {
    try {
      setIsLoading(true)
      // Mock data for now
      const mockPendingCompanies: PendingCompany[] = [
        {
          _id: '1',
          name: 'InnovateTech Solutions',
          slug: 'innovatetech',
          email: '<EMAIL>',
          website: 'https://innovatetech.com',
          description: 'Cutting-edge AI and machine learning solutions for enterprise clients',
          location: {
            city: 'Austin',
            state: 'TX',
            country: 'USA'
          },
          industry: 'Artificial Intelligence',
          size: 'Medium (100-500 employees)',
          verificationDocuments: {
            businessLicense: 'business-license-innovate.pdf',
            taxId: 'tax-id-innovate.pdf',
            incorporationCertificate: 'incorporation-innovate.pdf'
          },
          submittedAt: new Date('2024-03-01'),
          lastUpdated: new Date('2024-03-05'),
          adminUsers: 2,
          contactPerson: {
            name: 'Sarah Chen',
            email: '<EMAIL>',
            phone: '******-555-0123',
            position: 'Chief Operating Officer'
          },
          businessDetails: {
            foundedYear: 2019,
            revenue: '$5M - $10M',
            employees: 150,
            businessType: 'Corporation'
          }
        },
        {
          _id: '2',
          name: 'GreenEnergy Dynamics',
          slug: 'greenenergy',
          email: '<EMAIL>',
          website: 'https://greenenergy.com',
          description: 'Renewable energy solutions and sustainable technology development',
          location: {
            city: 'Portland',
            state: 'OR',
            country: 'USA'
          },
          industry: 'Renewable Energy',
          size: 'Small (50-100 employees)',
          verificationDocuments: {
            businessLicense: 'business-license-green.pdf',
            taxId: 'tax-id-green.pdf'
          },
          submittedAt: new Date('2024-03-03'),
          lastUpdated: new Date('2024-03-03'),
          adminUsers: 1,
          contactPerson: {
            name: 'Michael Rodriguez',
            email: '<EMAIL>',
            phone: '******-555-0456',
            position: 'Founder & CEO'
          },
          businessDetails: {
            foundedYear: 2021,
            revenue: '$1M - $5M',
            employees: 75,
            businessType: 'LLC'
          }
        },
        {
          _id: '3',
          name: 'DataFlow Analytics',
          slug: 'dataflow',
          email: '<EMAIL>',
          website: 'https://dataflow.com',
          description: 'Big data analytics and business intelligence platform',
          location: {
            city: 'Chicago',
            state: 'IL',
            country: 'USA'
          },
          industry: 'Data Analytics',
          size: 'Startup (1-50 employees)',
          verificationDocuments: {
            businessLicense: 'business-license-dataflow.pdf',
            incorporationCertificate: 'incorporation-dataflow.pdf',
            additionalDocs: ['partnership-agreement.pdf', 'investor-docs.pdf']
          },
          submittedAt: new Date('2024-03-06'),
          lastUpdated: new Date('2024-03-06'),
          adminUsers: 3,
          contactPerson: {
            name: 'Jennifer Kim',
            email: '<EMAIL>',
            position: 'Head of Operations'
          },
          businessDetails: {
            foundedYear: 2023,
            employees: 25,
            businessType: 'Corporation'
          }
        }
      ]
      setPendingCompanies(mockPendingCompanies)
    } catch (error) {
      console.error('Failed to fetch pending companies:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredCompanies = pendingCompanies.filter(company => 
    company.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    company.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    company.industry.toLowerCase().includes(searchQuery.toLowerCase()) ||
    company.contactPerson.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleVerificationAction = async (companyId: string, action: 'approve' | 'reject', notes: string) => {
    try {
      console.log(`${action} company verification:`, companyId, notes)
      // Implement verification action API call
      await fetchPendingCompanies() // Refresh data
      setSelectedCompany(null)
      setVerificationAction(null)
      setVerificationNotes('')
    } catch (error) {
      console.error(`Failed to ${action} company verification:`, error)
    }
  }

  const getDaysWaiting = (submittedAt: Date) => {
    const days = Math.floor((Date.now() - submittedAt.getTime()) / (24 * 60 * 60 * 1000))
    return days
  }

  const getUrgencyBadge = (days: number) => {
    if (days > 7) return <Badge variant="destructive">Urgent ({days} days)</Badge>
    if (days > 3) return <Badge className="bg-orange-100 text-orange-800">Review Soon ({days} days)</Badge>
    return <Badge variant="secondary">Recent ({days} days)</Badge>
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => router.push('/admin/companies')}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Companies
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center">
              <AlertTriangle className="w-8 h-8 mr-3 text-orange-600" />
              Pending Verifications
            </h1>
            <p className="text-muted-foreground">
              Review and approve company verification requests
            </p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={fetchPendingCompanies} disabled={isLoading}>
            <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Reviews</p>
                <p className="text-2xl font-bold text-orange-600">{pendingCompanies.length}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Urgent (7+ days)</p>
                <p className="text-2xl font-bold text-red-600">
                  {pendingCompanies.filter(c => getDaysWaiting(c.submittedAt) > 7).length}
                </p>
              </div>
              <Clock className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Complete Documents</p>
                <p className="text-2xl font-bold text-green-600">
                  {pendingCompanies.filter(c => 
                    c.verificationDocuments.businessLicense && 
                    c.verificationDocuments.taxId
                  ).length}
                </p>
              </div>
              <FileText className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Employees</p>
                <p className="text-2xl font-bold">
                  {pendingCompanies.reduce((sum, c) => sum + c.businessDetails.employees, 0)}
                </p>
              </div>
              <Users className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <Card>
        <CardHeader>
          <CardTitle>Verification Queue</CardTitle>
          <CardDescription>Review company verification requests and supporting documents</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search by company name, email, industry, or contact person..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>

          {/* Companies Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Company</TableHead>
                  <TableHead>Contact Person</TableHead>
                  <TableHead>Industry</TableHead>
                  <TableHead>Documents</TableHead>
                  <TableHead>Waiting Time</TableHead>
                  <TableHead>Employees</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
                      Loading pending companies...
                    </TableCell>
                  </TableRow>
                ) : filteredCompanies.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      No pending companies found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredCompanies.map((company) => {
                    const daysWaiting = getDaysWaiting(company.submittedAt)
                    const hasCompleteDocuments = company.verificationDocuments.businessLicense && 
                                               company.verificationDocuments.taxId
                    
                    return (
                      <TableRow key={company._id}>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <Avatar className="w-12 h-12">
                              <AvatarImage src={company.logo} />
                              <AvatarFallback>
                                <Building2 className="w-6 h-6" />
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{company.name}</p>
                              <p className="text-sm text-muted-foreground">{company.email}</p>
                              <div className="flex items-center text-xs text-muted-foreground mt-1">
                                <MapPin className="w-3 h-3 mr-1" />
                                {(() => {
                                  // Handle different location structures
                                  if (company.locations && Array.isArray(company.locations)) {
                                    const hq = company.locations.find(loc => loc.isHeadquarters) || company.locations[0]
                                    return `${hq.city}${hq.state ? `, ${hq.state}` : ''}`
                                  }
                                  if (company.location && typeof company.location === 'object' && 'city' in company.location) {
                                    return `${company.location.city}${company.location.state ? `, ${company.location.state}` : ''}`
                                  }
                                  return company.location || 'Location not specified'
                                })()}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{company.contactPerson.name}</p>
                            <p className="text-sm text-muted-foreground">{company.contactPerson.position}</p>
                            <p className="text-xs text-muted-foreground">{company.contactPerson.email}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{company.industry}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center text-xs">
                              {company.verificationDocuments.businessLicense ? 
                                <CheckCircle className="w-3 h-3 text-green-600 mr-1" /> : 
                                <XCircle className="w-3 h-3 text-red-600 mr-1" />
                              }
                              Business License
                            </div>
                            <div className="flex items-center text-xs">
                              {company.verificationDocuments.taxId ? 
                                <CheckCircle className="w-3 h-3 text-green-600 mr-1" /> : 
                                <XCircle className="w-3 h-3 text-red-600 mr-1" />
                              }
                              Tax ID
                            </div>
                            <div className="flex items-center text-xs">
                              {company.verificationDocuments.incorporationCertificate ? 
                                <CheckCircle className="w-3 h-3 text-green-600 mr-1" /> : 
                                <XCircle className="w-3 h-3 text-gray-400 mr-1" />
                              }
                              Incorporation
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {getUrgencyBadge(daysWaiting)}
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <p className="font-medium">{company.businessDetails.employees}</p>
                            <p className="text-muted-foreground">{company.size.split(' ')[0]}</p>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button variant="outline" size="sm" onClick={() => setSelectedCompany(company)}>
                                  <Eye className="w-4 h-4 mr-1" />
                                  Review
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                                <DialogHeader>
                                  <DialogTitle>Company Verification Review</DialogTitle>
                                  <DialogDescription>
                                    Review company details and verification documents
                                  </DialogDescription>
                                </DialogHeader>
                                
                                {selectedCompany && (
                                  <div className="space-y-6">
                                    {/* Company Info */}
                                    <div className="grid grid-cols-2 gap-4">
                                      <div>
                                        <h4 className="font-semibold mb-2">Company Information</h4>
                                        <div className="space-y-2 text-sm">
                                          <p><strong>Name:</strong> {selectedCompany.name}</p>
                                          <p><strong>Email:</strong> {selectedCompany.email}</p>
                                          <p><strong>Website:</strong> {selectedCompany.website}</p>
                                          <p><strong>Industry:</strong> {selectedCompany.industry}</p>
                                          <p><strong>Size:</strong> {selectedCompany.size}</p>
                                          <p><strong>Founded:</strong> {selectedCompany.businessDetails.foundedYear}</p>
                                        </div>
                                      </div>
                                      <div>
                                        <h4 className="font-semibold mb-2">Contact Person</h4>
                                        <div className="space-y-2 text-sm">
                                          <p><strong>Name:</strong> {selectedCompany.contactPerson.name}</p>
                                          <p><strong>Position:</strong> {selectedCompany.contactPerson.position}</p>
                                          <p><strong>Email:</strong> {selectedCompany.contactPerson.email}</p>
                                          <p><strong>Phone:</strong> {selectedCompany.contactPerson.phone || 'Not provided'}</p>
                                        </div>
                                      </div>
                                    </div>

                                    {/* Description */}
                                    <div>
                                      <h4 className="font-semibold mb-2">Company Description</h4>
                                      <p className="text-sm text-muted-foreground">{selectedCompany.description}</p>
                                    </div>

                                    {/* Documents */}
                                    <div>
                                      <h4 className="font-semibold mb-2">Verification Documents</h4>
                                      <div className="grid grid-cols-2 gap-4">
                                        {Object.entries(selectedCompany.verificationDocuments).map(([key, value]) => (
                                          value && (
                                            <div key={key} className="flex items-center justify-between p-3 border rounded">
                                              <div className="flex items-center">
                                                <FileText className="w-4 h-4 mr-2" />
                                                <span className="text-sm">{key.replace(/([A-Z])/g, ' $1').toLowerCase()}</span>
                                              </div>
                                              <Button variant="outline" size="sm">
                                                <Eye className="w-4 h-4 mr-1" />
                                                View
                                              </Button>
                                            </div>
                                          )
                                        ))}
                                      </div>
                                    </div>

                                    {/* Action Buttons */}
                                    <div className="flex justify-end space-x-2 pt-4 border-t">
                                      <Dialog>
                                        <DialogTrigger asChild>
                                          <Button 
                                            variant="destructive" 
                                            onClick={() => setVerificationAction('reject')}
                                          >
                                            <XCircle className="w-4 h-4 mr-2" />
                                            Reject
                                          </Button>
                                        </DialogTrigger>
                                        <DialogContent>
                                          <DialogHeader>
                                            <DialogTitle>Reject Verification</DialogTitle>
                                            <DialogDescription>
                                              Please provide a reason for rejecting this company's verification.
                                            </DialogDescription>
                                          </DialogHeader>
                                          <Textarea
                                            placeholder="Reason for rejection..."
                                            value={verificationNotes}
                                            onChange={(e) => setVerificationNotes(e.target.value)}
                                          />
                                          <DialogFooter>
                                            <Button variant="outline" onClick={() => setVerificationAction(null)}>
                                              Cancel
                                            </Button>
                                            <Button 
                                              variant="destructive"
                                              onClick={() => handleVerificationAction(selectedCompany._id, 'reject', verificationNotes)}
                                            >
                                              Reject Verification
                                            </Button>
                                          </DialogFooter>
                                        </DialogContent>
                                      </Dialog>

                                      <Dialog>
                                        <DialogTrigger asChild>
                                          <Button 
                                            onClick={() => setVerificationAction('approve')}
                                          >
                                            <CheckCircle className="w-4 h-4 mr-2" />
                                            Approve
                                          </Button>
                                        </DialogTrigger>
                                        <DialogContent>
                                          <DialogHeader>
                                            <DialogTitle>Approve Verification</DialogTitle>
                                            <DialogDescription>
                                              Confirm that you want to approve this company's verification.
                                            </DialogDescription>
                                          </DialogHeader>
                                          <Textarea
                                            placeholder="Optional approval notes..."
                                            value={verificationNotes}
                                            onChange={(e) => setVerificationNotes(e.target.value)}
                                          />
                                          <DialogFooter>
                                            <Button variant="outline" onClick={() => setVerificationAction(null)}>
                                              Cancel
                                            </Button>
                                            <Button 
                                              onClick={() => handleVerificationAction(selectedCompany._id, 'approve', verificationNotes)}
                                            >
                                              Approve Verification
                                            </Button>
                                          </DialogFooter>
                                        </DialogContent>
                                      </Dialog>
                                    </div>
                                  </div>
                                )}
                              </DialogContent>
                            </Dialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    )
                  })
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      </div>
    </AdminLayout>
  )
}

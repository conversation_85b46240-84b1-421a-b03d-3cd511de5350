'use client'

import React, { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { AdminPageHeader } from '@/components/admin/admin-page-header'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Search, 
  MoreHorizontal,
  Eye,
  Edit,
  Pause,
  Play,
  X,
  Building2,
  Calendar,
  DollarSign,
  Users,
  RefreshCw,
  Download,
  ArrowLeft,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { TableEmptyState, ErrorState } from '@/components/ui/empty-state'

interface Subscription {
  _id: string
  company: {
    _id: string
    name: string
    logo?: string
    isVerified: boolean
  }
  plan: {
    _id: string
    name: string
    price: {
      monthly: number
      yearly: number
      currency: string
    }
  }
  status: string
  billingCycle: string
  currentPeriodStart: Date
  currentPeriodEnd: Date
  trialEnd?: Date
  pricePerPeriod: number
  currency: string
  usage: {
    jobPostings: number
    featuredJobs: number
    teamMembers: number
  }
  daysRemaining: number
  trialDaysRemaining: number
  createdAt: Date
}

export default function SubscriptionsPage() {
  const router = useRouter()
  const { token } = useAuthStore()
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [planFilter, setPlanFilter] = useState('all')
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchSubscriptions()
  }, [])

  const fetchSubscriptions = async () => {
    try {
      setIsLoading(true)
      setError(null)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const queryParams = new URLSearchParams({
        ...(searchQuery && { search: searchQuery }),
        ...(statusFilter !== 'all' && { status: statusFilter }),
        ...(planFilter !== 'all' && { plan: planFilter })
      })

      const response = await fetch(`/api/v1/admin/billing/subscriptions?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch subscriptions')
      }

      const data = await response.json()
      setSubscriptions(data.data.subscriptions || [])
    } catch (error) {
      console.error('Failed to fetch subscriptions:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch subscriptions')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = () => {
    fetchSubscriptions()
  }

  const handleSubscriptionAction = async (action: string, subscriptionId: string) => {
    try {
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/v1/admin/billing/subscriptions/${subscriptionId}/${action}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to ${action} subscription`)
      }

      await fetchSubscriptions()
    } catch (error) {
      console.error(`Failed to ${action} subscription:`, error)
      setError(error instanceof Error ? error.message : `Failed to ${action} subscription`)
    }
  }

  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'trialing': return 'bg-blue-100 text-blue-800'
      case 'past_due': return 'bg-yellow-100 text-yellow-800'
      case 'canceled': return 'bg-red-100 text-red-800'
      case 'paused': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4" />
      case 'trialing': return <Clock className="w-4 h-4" />
      case 'past_due': return <AlertTriangle className="w-4 h-4" />
      case 'canceled': return <XCircle className="w-4 h-4" />
      case 'paused': return <Pause className="w-4 h-4" />
      default: return <Clock className="w-4 h-4" />
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <AdminPageHeader
          title="Subscription Management"
          description="Manage company subscriptions and billing plans"
          icon={Users}
          breadcrumbItems={[
            { label: 'Admin', href: '/admin' },
            { label: 'Billing', href: '/admin/billing' },
            { label: 'Subscriptions' }
          ]}
          actions={
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                onClick={() => router.push('/admin/billing')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Billing
              </Button>
              <Button variant="outline" onClick={fetchSubscriptions} disabled={isLoading}>
                <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
                Refresh
              </Button>
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          }
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Subscriptions</p>
                  <p className="text-2xl font-bold">{subscriptions.length}</p>
                </div>
                <Users className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Active</p>
                  <p className="text-2xl font-bold">
                    {subscriptions.filter(s => s.status === 'active').length}
                  </p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Trialing</p>
                  <p className="text-2xl font-bold">
                    {subscriptions.filter(s => s.status === 'trialing').length}
                  </p>
                </div>
                <Clock className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Past Due</p>
                  <p className="text-2xl font-bold">
                    {subscriptions.filter(s => s.status === 'past_due').length}
                  </p>
                </div>
                <AlertTriangle className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Subscriptions Table */}
        <Card>
          <CardHeader>
            <CardTitle>All Subscriptions</CardTitle>
            <CardDescription>Manage and monitor company subscriptions</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Filters */}
            <div className="flex gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search companies..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="trialing">Trialing</SelectItem>
                  <SelectItem value="past_due">Past Due</SelectItem>
                  <SelectItem value="canceled">Canceled</SelectItem>
                  <SelectItem value="paused">Paused</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={handleSearch} disabled={isLoading}>
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
            </div>

            {/* Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Company</TableHead>
                    <TableHead>Plan</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Billing</TableHead>
                    <TableHead>Usage</TableHead>
                    <TableHead>Period</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {error ? (
                    <TableEmptyState
                      title="Failed to load subscriptions"
                      description={error}
                      action={{
                        label: 'Retry',
                        onClick: fetchSubscriptions,
                        variant: 'outline'
                      }}
                      colSpan={7}
                    />
                  ) : isLoading ? (
                    <TableEmptyState
                      title="Loading subscriptions..."
                      colSpan={7}
                    />
                  ) : subscriptions.length === 0 ? (
                    <TableEmptyState
                      icon={Users}
                      title="No subscriptions found"
                      description="No company subscriptions match your current filters."
                      action={{
                        label: 'View All',
                        onClick: () => {
                          setSearchQuery('')
                          setStatusFilter('all')
                          setPlanFilter('all')
                          fetchSubscriptions()
                        }
                      }}
                      colSpan={7}
                    />
                  ) : (
                    subscriptions.map((subscription) => (
                      <TableRow key={subscription._id}>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <Avatar className="w-8 h-8">
                              <AvatarImage src={subscription.company.logo} />
                              <AvatarFallback>
                                <Building2 className="w-4 h-4" />
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{subscription.company.name}</p>
                              {subscription.company.isVerified && (
                                <Badge variant="default" className="text-xs">Verified</Badge>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{subscription.plan.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {formatCurrency(subscription.pricePerPeriod, subscription.currency)} / {subscription.billingCycle}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={cn('flex items-center space-x-1 w-fit', getStatusColor(subscription.status))}>
                            {getStatusIcon(subscription.status)}
                            <span className="capitalize">{subscription.status.replace('_', ' ')}</span>
                          </Badge>
                          {subscription.status === 'trialing' && subscription.trialDaysRemaining > 0 && (
                            <p className="text-xs text-muted-foreground mt-1">
                              {subscription.trialDaysRemaining} days left
                            </p>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-sm">
                            <DollarSign className="w-4 h-4 mr-1" />
                            {formatCurrency(subscription.pricePerPeriod, subscription.currency)}
                          </div>
                          <p className="text-xs text-muted-foreground capitalize">
                            {subscription.billingCycle}
                          </p>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1 text-sm">
                            <div>Jobs: {subscription.usage.jobPostings}</div>
                            <div>Featured: {subscription.usage.featuredJobs}</div>
                            <div>Team: {subscription.usage.teamMembers}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-sm">
                            <Calendar className="w-4 h-4 mr-1" />
                            {subscription.daysRemaining} days left
                          </div>
                          <p className="text-xs text-muted-foreground">
                            Until {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
                          </p>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => router.push(`/admin/billing/subscriptions/${subscription._id}`)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => router.push(`/admin/billing/subscriptions/${subscription._id}/edit`)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Subscription
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {subscription.status === 'active' ? (
                                <DropdownMenuItem 
                                  onClick={() => handleSubscriptionAction('pause', subscription._id)}
                                  className="text-yellow-600"
                                >
                                  <Pause className="mr-2 h-4 w-4" />
                                  Pause Subscription
                                </DropdownMenuItem>
                              ) : subscription.status === 'paused' ? (
                                <DropdownMenuItem 
                                  onClick={() => handleSubscriptionAction('resume', subscription._id)}
                                  className="text-green-600"
                                >
                                  <Play className="mr-2 h-4 w-4" />
                                  Resume Subscription
                                </DropdownMenuItem>
                              ) : null}
                              <DropdownMenuItem 
                                onClick={() => handleSubscriptionAction('cancel', subscription._id)}
                                className="text-red-600"
                              >
                                <X className="mr-2 h-4 w-4" />
                                Cancel Subscription
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}

'use client'

import React, { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { AdminPageHeader } from '@/components/admin/admin-page-header'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Search, 
  MoreHorizontal,
  Eye,
  Edit,
  Send,
  Download,
  RefreshCw,
  ArrowLeft,
  Plus,
  FileText,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Building2,
  Calendar,
  DollarSign,
  Mail
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { TableEmptyState, ErrorState } from '@/components/ui/empty-state'

interface Invoice {
  _id: string
  invoiceNumber: string
  company: {
    _id: string
    name: string
    logo?: string
    isVerified: boolean
  }
  subscription?: {
    _id: string
    plan: {
      name: string
    }
  }
  status: string
  totalAmount: number
  currency: string
  issueDate: Date
  dueDate: Date
  paidAt?: Date
  sentAt?: Date
  daysOverdue: number
  isOverdue: boolean
  items: Array<{
    description: string
    quantity: number
    unitPrice: number
    amount: number
  }>
  createdAt: Date
}

export default function InvoicesPage() {
  const router = useRouter()
  const { token } = useAuthStore()
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchInvoices()
  }, [])

  const fetchInvoices = async () => {
    try {
      setIsLoading(true)
      setError(null)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const queryParams = new URLSearchParams({
        ...(searchQuery && { search: searchQuery }),
        ...(statusFilter !== 'all' && { status: statusFilter })
      })

      const response = await fetch(`/api/v1/admin/billing/invoices?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch invoices')
      }

      const data = await response.json()
      setInvoices(data.data.invoices || [])
    } catch (error) {
      console.error('Failed to fetch invoices:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch invoices')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = () => {
    fetchInvoices()
  }

  const handleInvoiceAction = async (action: string, invoiceId: string) => {
    try {
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/v1/admin/billing/invoices/${invoiceId}/${action}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to ${action} invoice`)
      }

      await fetchInvoices()
    } catch (error) {
      console.error(`Failed to ${action} invoice:`, error)
      setError(error instanceof Error ? error.message : `Failed to ${action} invoice`)
    }
  }

  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount)
  }

  const getStatusColor = (status: string, isOverdue: boolean) => {
    if (isOverdue && status !== 'paid') return 'bg-red-100 text-red-800'
    
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800'
      case 'sent': return 'bg-blue-100 text-blue-800'
      case 'draft': return 'bg-gray-100 text-gray-800'
      case 'overdue': return 'bg-red-100 text-red-800'
      case 'canceled': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string, isOverdue: boolean) => {
    if (isOverdue && status !== 'paid') return <AlertTriangle className="w-4 h-4" />
    
    switch (status) {
      case 'paid': return <CheckCircle className="w-4 h-4" />
      case 'sent': return <Mail className="w-4 h-4" />
      case 'draft': return <FileText className="w-4 h-4" />
      case 'overdue': return <AlertTriangle className="w-4 h-4" />
      case 'canceled': return <XCircle className="w-4 h-4" />
      default: return <Clock className="w-4 h-4" />
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <AdminPageHeader
          title="Invoice Management"
          description="Create, send, and track invoices"
          icon={FileText}
          breadcrumbItems={[
            { label: 'Admin', href: '/admin' },
            { label: 'Billing', href: '/admin/billing' },
            { label: 'Invoices' }
          ]}
          actions={
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                onClick={() => router.push('/admin/billing')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Billing
              </Button>
              <Button variant="outline" onClick={fetchInvoices} disabled={isLoading}>
                <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
                Refresh
              </Button>
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              <Button onClick={() => router.push('/admin/billing/invoices/create')}>
                <Plus className="w-4 h-4 mr-2" />
                Create Invoice
              </Button>
            </div>
          }
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Invoices</p>
                  <p className="text-2xl font-bold">{invoices.length}</p>
                </div>
                <FileText className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Paid</p>
                  <p className="text-2xl font-bold">
                    {invoices.filter(i => i.status === 'paid').length}
                  </p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Overdue</p>
                  <p className="text-2xl font-bold">
                    {invoices.filter(i => i.isOverdue).length}
                  </p>
                </div>
                <AlertTriangle className="w-8 h-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Value</p>
                  <p className="text-2xl font-bold">
                    {formatCurrency(invoices.reduce((sum, i) => sum + i.totalAmount, 0))}
                  </p>
                </div>
                <DollarSign className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Invoices Table */}
        <Card>
          <CardHeader>
            <CardTitle>All Invoices</CardTitle>
            <CardDescription>Manage and track invoice status</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Filters */}
            <div className="flex gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search invoices..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="sent">Sent</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="overdue">Overdue</SelectItem>
                  <SelectItem value="canceled">Canceled</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={handleSearch} disabled={isLoading}>
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
            </div>

            {/* Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Invoice</TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Issue Date</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {error ? (
                    <TableEmptyState
                      title="Failed to load invoices"
                      description={error}
                      action={{
                        label: 'Retry',
                        onClick: fetchInvoices,
                        variant: 'outline'
                      }}
                      colSpan={7}
                    />
                  ) : isLoading ? (
                    <TableEmptyState
                      title="Loading invoices..."
                      colSpan={7}
                    />
                  ) : invoices.length === 0 ? (
                    <TableEmptyState
                      icon={FileText}
                      title="No invoices found"
                      description="No invoices match your current filters."
                      action={{
                        label: 'Create Invoice',
                        onClick: () => router.push('/admin/billing/invoices/create')
                      }}
                      colSpan={7}
                    />
                  ) : (
                    invoices.map((invoice) => (
                      <TableRow key={invoice._id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">#{invoice.invoiceNumber}</p>
                            {invoice.subscription && (
                              <p className="text-sm text-muted-foreground">
                                {invoice.subscription.plan.name}
                              </p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <Avatar className="w-8 h-8">
                              <AvatarImage src={invoice.company.logo} />
                              <AvatarFallback>
                                <Building2 className="w-4 h-4" />
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{invoice.company.name}</p>
                              {invoice.company.isVerified && (
                                <Badge variant="default" className="text-xs">Verified</Badge>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <DollarSign className="w-4 h-4 mr-1" />
                            <span className="font-medium">
                              {formatCurrency(invoice.totalAmount, invoice.currency)}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={cn('flex items-center space-x-1 w-fit', getStatusColor(invoice.status, invoice.isOverdue))}>
                            {getStatusIcon(invoice.status, invoice.isOverdue)}
                            <span className="capitalize">
                              {invoice.isOverdue && invoice.status !== 'paid' ? 'Overdue' : invoice.status}
                            </span>
                          </Badge>
                          {invoice.isOverdue && invoice.daysOverdue > 0 && (
                            <p className="text-xs text-red-600 mt-1">
                              {invoice.daysOverdue} days overdue
                            </p>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-sm">
                            <Calendar className="w-4 h-4 mr-1" />
                            {new Date(invoice.issueDate).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-sm">
                            <Calendar className="w-4 h-4 mr-1" />
                            <span className={cn(
                              invoice.isOverdue && invoice.status !== 'paid' ? 'text-red-600 font-medium' : ''
                            )}>
                              {new Date(invoice.dueDate).toLocaleDateString()}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => router.push(`/admin/billing/invoices/${invoice._id}`)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => router.push(`/admin/billing/invoices/${invoice._id}/edit`)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Invoice
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Download className="mr-2 h-4 w-4" />
                                Download PDF
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {invoice.status === 'draft' && (
                                <DropdownMenuItem 
                                  onClick={() => handleInvoiceAction('send', invoice._id)}
                                  className="text-blue-600"
                                >
                                  <Send className="mr-2 h-4 w-4" />
                                  Send Invoice
                                </DropdownMenuItem>
                              )}
                              {(invoice.status === 'sent' || invoice.status === 'overdue') && (
                                <DropdownMenuItem 
                                  onClick={() => handleInvoiceAction('mark-paid', invoice._id)}
                                  className="text-green-600"
                                >
                                  <CheckCircle className="mr-2 h-4 w-4" />
                                  Mark as Paid
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}

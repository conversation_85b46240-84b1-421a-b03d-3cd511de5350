'use client'

import React, { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { AdminPageHeader } from '@/components/admin/admin-page-header'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  MoreHorizontal,
  Eye,
  Edit,
  Copy,
  Trash2,
  Star,
  StarOff,
  <PERSON><PERSON>s,
  <PERSON>freshC<PERSON>,
  Arrow<PERSON><PERSON><PERSON>,
  Plus,
  DollarSign,
  Users,
  CheckCircle,
  XCircle,
  Crown,
  Zap
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { TableEmptyState, ErrorState } from '@/components/ui/empty-state'

interface BillingPlan {
  _id: string
  name: string
  slug: string
  description: string
  price: {
    monthly: number
    yearly: number
    currency: string
  }
  features: Array<{
    name: string
    description?: string
    included: boolean
    limit?: number
  }>
  limits: {
    jobPostings: number
    featuredJobs: number
    applicationsPerJob: number
    teamMembers: number
    customBranding: boolean
    prioritySupport: boolean
    analytics: boolean
    apiAccess: boolean
  }
  isActive: boolean
  isPopular: boolean
  sortOrder: number
  trialDays: number
  subscriberCount?: number
  createdAt: Date
  updatedAt: Date
}

export default function BillingPlansPage() {
  const router = useRouter()
  const { token } = useAuthStore()
  const [plans, setPlans] = useState<BillingPlan[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchPlans()
  }, [])

  const fetchPlans = async () => {
    try {
      setIsLoading(true)
      setError(null)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch('/api/v1/admin/billing/plans', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch billing plans')
      }

      const data = await response.json()
      setPlans(data.data.plans || [])
    } catch (error) {
      console.error('Failed to fetch billing plans:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch billing plans')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePlanAction = async (action: string, planId: string, data?: any) => {
    try {
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/v1/admin/billing/plans/${planId}/${action}`, {
        method: action === 'delete' ? 'DELETE' : 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: data ? JSON.stringify(data) : undefined
      })

      if (!response.ok) {
        throw new Error(`Failed to ${action} plan`)
      }

      await fetchPlans()
    } catch (error) {
      console.error(`Failed to ${action} plan:`, error)
      setError(error instanceof Error ? error.message : `Failed to ${action} plan`)
    }
  }

  const togglePlanStatus = async (planId: string, isActive: boolean) => {
    await handlePlanAction('toggle-status', planId, { isActive })
  }

  const togglePopularStatus = async (planId: string, isPopular: boolean) => {
    await handlePlanAction('toggle-popular', planId, { isPopular })
  }

  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const calculateYearlySavings = (monthly: number, yearly: number) => {
    const yearlyFromMonthly = monthly * 12
    const savings = yearlyFromMonthly - yearly
    const percentage = (savings / yearlyFromMonthly) * 100
    return { savings, percentage }
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <AdminPageHeader
          title="Billing Plans"
          description="Manage subscription plans and pricing"
          icon={Settings}
          breadcrumbItems={[
            { label: 'Admin', href: '/admin' },
            { label: 'Billing', href: '/admin/billing' },
            { label: 'Plans' }
          ]}
          actions={
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                onClick={() => router.push('/admin/billing')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Billing
              </Button>
              <Button variant="outline" onClick={fetchPlans} disabled={isLoading}>
                <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
                Refresh
              </Button>
              <Button onClick={() => router.push('/admin/billing/plans/create')}>
                <Plus className="w-4 h-4 mr-2" />
                Create Plan
              </Button>
            </div>
          }
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Plans</p>
                  <p className="text-2xl font-bold">{plans.length}</p>
                </div>
                <Settings className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Active Plans</p>
                  <p className="text-2xl font-bold">
                    {plans.filter(p => p.isActive).length}
                  </p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Subscribers</p>
                  <p className="text-2xl font-bold">
                    {plans.reduce((sum, p) => sum + (p.subscriberCount || 0), 0)}
                  </p>
                </div>
                <Users className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Price Range</p>
                  <p className="text-2xl font-bold">
                    {plans.length > 0 ? 
                      `${formatCurrency(Math.min(...plans.map(p => p.price.monthly)))} - ${formatCurrency(Math.max(...plans.map(p => p.price.monthly)))}` :
                      '$0'
                    }
                  </p>
                </div>
                <DollarSign className="w-8 h-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Plans Table */}
        <Card>
          <CardHeader>
            <CardTitle>All Plans</CardTitle>
            <CardDescription>Manage subscription plans and pricing tiers</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Plan Details</TableHead>
                    <TableHead>Pricing</TableHead>
                    <TableHead>Limits</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Subscribers</TableHead>
                    <TableHead>Trial</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {error ? (
                    <TableEmptyState
                      title="Failed to load plans"
                      description={error}
                      action={{
                        label: 'Retry',
                        onClick: fetchPlans,
                        variant: 'outline'
                      }}
                      colSpan={7}
                    />
                  ) : isLoading ? (
                    <TableEmptyState
                      title="Loading plans..."
                      colSpan={7}
                    />
                  ) : plans.length === 0 ? (
                    <TableEmptyState
                      icon={Settings}
                      title="No billing plans found"
                      description="Create your first billing plan to get started."
                      action={{
                        label: 'Create Plan',
                        onClick: () => router.push('/admin/billing/plans/create')
                      }}
                      colSpan={7}
                    />
                  ) : (
                    plans.map((plan) => {
                      const yearlySavings = calculateYearlySavings(plan.price.monthly, plan.price.yearly)
                      return (
                        <TableRow key={plan._id}>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="flex items-center space-x-2">
                                <p className="font-medium">{plan.name}</p>
                                {plan.isPopular && (
                                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                                    <Star className="w-3 h-3 mr-1" />
                                    Popular
                                  </Badge>
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground">{plan.description}</p>
                              <p className="text-xs text-muted-foreground">Slug: {plan.slug}</p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="flex items-center space-x-2">
                                <DollarSign className="w-4 h-4" />
                                <span className="font-medium">
                                  {formatCurrency(plan.price.monthly, plan.price.currency)}/mo
                                </span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm text-muted-foreground">
                                  {formatCurrency(plan.price.yearly, plan.price.currency)}/yr
                                </span>
                                {yearlySavings.percentage > 0 && (
                                  <Badge variant="outline" className="text-xs text-green-600">
                                    Save {yearlySavings.percentage.toFixed(0)}%
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1 text-sm">
                              <div>Jobs: {plan.limits.jobPostings === -1 ? 'Unlimited' : plan.limits.jobPostings}</div>
                              <div>Featured: {plan.limits.featuredJobs}</div>
                              <div>Team: {plan.limits.teamMembers}</div>
                              <div className="flex space-x-2">
                                {plan.limits.analytics && <Badge variant="outline" className="text-xs">Analytics</Badge>}
                                {plan.limits.apiAccess && <Badge variant="outline" className="text-xs">API</Badge>}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Switch
                                checked={plan.isActive}
                                onCheckedChange={(checked) => togglePlanStatus(plan._id, checked)}
                              />
                              <Badge variant={plan.isActive ? 'default' : 'secondary'}>
                                {plan.isActive ? 'Active' : 'Inactive'}
                              </Badge>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Users className="w-4 h-4" />
                              <span className="font-medium">{plan.subscriberCount || 0}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Zap className="w-4 h-4" />
                              <span>{plan.trialDays} days</span>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => router.push(`/admin/billing/plans/${plan._id}`)}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  View Details
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => router.push(`/admin/billing/plans/${plan._id}/edit`)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit Plan
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => router.push(`/admin/billing/plans/${plan._id}/duplicate`)}>
                                  <Copy className="mr-2 h-4 w-4" />
                                  Duplicate Plan
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem 
                                  onClick={() => togglePopularStatus(plan._id, !plan.isPopular)}
                                  className={plan.isPopular ? "text-orange-600" : "text-yellow-600"}
                                >
                                  {plan.isPopular ? (
                                    <>
                                      <StarOff className="mr-2 h-4 w-4" />
                                      Remove Popular
                                    </>
                                  ) : (
                                    <>
                                      <Star className="mr-2 h-4 w-4" />
                                      Mark Popular
                                    </>
                                  )}
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  onClick={() => handlePlanAction('delete', plan._id)}
                                  className="text-red-600"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete Plan
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      )
                    })
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}

'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { AdminPageHeader } from '@/components/admin/admin-page-header'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Search, 
  MoreHorizontal,
  Eye,
  RotateCcw,
  RefreshCw,
  Download,
  ArrowLeft,
  Plus,
  Building2,
  Calendar,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { TableEmptyState, ErrorState } from '@/components/ui/empty-state'

interface Refund {
  _id: string
  payment: {
    _id: string
    amount: number
    currency: string
    description: string
    company: {
      _id: string
      name: string
      logo?: string
      isVerified: boolean
    }
  }
  amount: number
  currency: string
  reason: string
  status: 'pending' | 'succeeded' | 'failed' | 'canceled'
  stripeRefundId?: string
  failureReason?: string
  processedAt?: Date
  createdBy: {
    _id: string
    name: string
    email: string
  }
  createdAt: Date
  updatedAt: Date
}

export default function RefundsPage() {
  const router = useRouter()
  const { token } = useAuthStore()
  const [refunds, setRefunds] = useState<Refund[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [error, setError] = useState<string | null>(null)
  const [isRefundDialogOpen, setIsRefundDialogOpen] = useState(false)
  const [selectedPayment, setSelectedPayment] = useState<any>(null)
  const [refundForm, setRefundForm] = useState({
    amount: '',
    reason: '',
    description: ''
  })

  useEffect(() => {
    fetchRefunds()
  }, [])

  const fetchRefunds = async () => {
    try {
      setIsLoading(true)
      setError(null)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const queryParams = new URLSearchParams({
        ...(searchQuery && { search: searchQuery }),
        ...(statusFilter !== 'all' && { status: statusFilter })
      })

      const response = await fetch(`/api/v1/admin/billing/refunds?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch refunds')
      }

      const data = await response.json()
      setRefunds(data.data?.refunds || [])
    } catch (error) {
      console.error('Failed to fetch refunds:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch refunds')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = () => {
    fetchRefunds()
  }

  const handleCreateRefund = async () => {
    try {
      if (!token || !selectedPayment) {
        throw new Error('Missing required data')
      }

      const response = await fetch('/api/v1/admin/billing/refunds', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          paymentId: selectedPayment._id,
          amount: parseFloat(refundForm.amount) || selectedPayment.amount,
          reason: refundForm.reason,
          description: refundForm.description
        })
      })

      if (!response.ok) {
        throw new Error('Failed to create refund')
      }

      setIsRefundDialogOpen(false)
      setSelectedPayment(null)
      setRefundForm({ amount: '', reason: '', description: '' })
      await fetchRefunds()
    } catch (error) {
      console.error('Failed to create refund:', error)
      setError(error instanceof Error ? error.message : 'Failed to create refund')
    }
  }

  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'succeeded': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'failed': return 'bg-red-100 text-red-800'
      case 'canceled': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'succeeded': return <CheckCircle className="w-4 h-4" />
      case 'pending': return <Clock className="w-4 h-4" />
      case 'failed': return <XCircle className="w-4 h-4" />
      case 'canceled': return <XCircle className="w-4 h-4" />
      default: return <Clock className="w-4 h-4" />
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <AdminPageHeader
          title="Refund Management"
          description="Process and track payment refunds"
          icon={RotateCcw}
          breadcrumbItems={[
            { label: 'Admin', href: '/admin' },
            { label: 'Billing', href: '/admin/billing' },
            { label: 'Refunds' }
          ]}
          actions={
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                onClick={() => router.push('/admin/billing')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Billing
              </Button>
              <Button variant="outline" onClick={fetchRefunds} disabled={isLoading}>
                <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
                Refresh
              </Button>
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              <Dialog open={isRefundDialogOpen} onOpenChange={setIsRefundDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="w-4 h-4 mr-2" />
                    Process Refund
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Process New Refund</DialogTitle>
                    <DialogDescription>
                      Create a refund for a payment. Enter the payment ID or search for the payment.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Payment ID</label>
                      <Input
                        placeholder="Enter payment ID"
                        value={selectedPayment?._id || ''}
                        onChange={(e) => {
                          // In a real implementation, you'd search for the payment
                          console.log('Search payment:', e.target.value)
                        }}
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Refund Amount</label>
                      <Input
                        type="number"
                        placeholder="Enter amount (leave empty for full refund)"
                        value={refundForm.amount}
                        onChange={(e) => setRefundForm(prev => ({ ...prev, amount: e.target.value }))}
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Reason</label>
                      <Select value={refundForm.reason} onValueChange={(value) => setRefundForm(prev => ({ ...prev, reason: value }))}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select refund reason" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="requested_by_customer">Requested by customer</SelectItem>
                          <SelectItem value="duplicate">Duplicate payment</SelectItem>
                          <SelectItem value="fraudulent">Fraudulent transaction</SelectItem>
                          <SelectItem value="subscription_canceled">Subscription canceled</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Description</label>
                      <Textarea
                        placeholder="Additional details about the refund"
                        value={refundForm.description}
                        onChange={(e) => setRefundForm(prev => ({ ...prev, description: e.target.value }))}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsRefundDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCreateRefund}>
                      Process Refund
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          }
        />

        {error ? (
          <ErrorState
            title="Failed to load refunds"
            description={error}
            onRetry={fetchRefunds}
          />
        ) : (
          <>
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Total Refunds</p>
                      <p className="text-2xl font-bold">{refunds.length}</p>
                    </div>
                    <RotateCcw className="w-8 h-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Processed</p>
                      <p className="text-2xl font-bold">
                        {refunds.filter(r => r.status === 'succeeded').length}
                      </p>
                    </div>
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Pending</p>
                      <p className="text-2xl font-bold">
                        {refunds.filter(r => r.status === 'pending').length}
                      </p>
                    </div>
                    <Clock className="w-8 h-8 text-yellow-600" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Total Amount</p>
                      <p className="text-2xl font-bold">
                        {formatCurrency(refunds.reduce((sum, r) => sum + r.amount, 0))}
                      </p>
                    </div>
                    <DollarSign className="w-8 h-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Refunds Table */}
            <Card>
              <CardHeader>
                <CardTitle>All Refunds</CardTitle>
                <CardDescription>Track and manage payment refunds</CardDescription>
              </CardHeader>
              <CardContent>
                {/* Filters */}
                <div className="flex gap-4 mb-6">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      <Input
                        placeholder="Search refunds..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="succeeded">Succeeded</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                      <SelectItem value="canceled">Canceled</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button onClick={handleSearch} disabled={isLoading}>
                    <Search className="w-4 h-4 mr-2" />
                    Search
                  </Button>
                </div>

                {/* Table */}
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Company</TableHead>
                        <TableHead>Original Payment</TableHead>
                        <TableHead>Refund Amount</TableHead>
                        <TableHead>Reason</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableEmptyState
                          title="Loading refunds..."
                          colSpan={7}
                        />
                      ) : refunds.length === 0 ? (
                        <TableEmptyState
                          icon={RotateCcw}
                          title="No refunds found"
                          description="No refunds match your current filters."
                          action={{
                            label: 'Process Refund',
                            onClick: () => setIsRefundDialogOpen(true)
                          }}
                          colSpan={7}
                        />
                      ) : (
                        refunds.map((refund) => (
                          <TableRow key={refund._id}>
                            <TableCell>
                              <div className="flex items-center space-x-3">
                                <Avatar className="w-8 h-8">
                                  <AvatarImage src={refund.payment.company.logo} />
                                  <AvatarFallback>
                                    <Building2 className="w-4 h-4" />
                                  </AvatarFallback>
                                </Avatar>
                                <div>
                                  <p className="font-medium">{refund.payment.company.name}</p>
                                  {refund.payment.company.isVerified && (
                                    <Badge variant="default" className="text-xs">Verified</Badge>
                                  )}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div>
                                <p className="font-medium">
                                  {formatCurrency(refund.payment.amount, refund.payment.currency)}
                                </p>
                                <p className="text-sm text-muted-foreground">
                                  {refund.payment.description}
                                </p>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <DollarSign className="w-4 h-4 mr-1" />
                                <span className="font-medium">
                                  {formatCurrency(refund.amount, refund.currency)}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div>
                                <p className="text-sm capitalize">{refund.reason.replace('_', ' ')}</p>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge className={cn('flex items-center space-x-1 w-fit', getStatusColor(refund.status))}>
                                {getStatusIcon(refund.status)}
                                <span className="capitalize">{refund.status}</span>
                              </Badge>
                              {refund.failureReason && (
                                <p className="text-xs text-red-600 mt-1">{refund.failureReason}</p>
                              )}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center text-sm">
                                <Calendar className="w-4 h-4 mr-1" />
                                {refund.processedAt ? 
                                  new Date(refund.processedAt).toLocaleDateString() : 
                                  new Date(refund.createdAt).toLocaleDateString()
                                }
                              </div>
                              <p className="text-xs text-muted-foreground">
                                by {refund.createdBy.name}
                              </p>
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" className="h-8 w-8 p-0">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => router.push(`/admin/billing/refunds/${refund._id}`)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => router.push(`/admin/billing/payments/${refund.payment._id}`)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Original Payment
                                  </DropdownMenuItem>
                                  {refund.status === 'pending' && (
                                    <>
                                      <DropdownMenuSeparator />
                                      <DropdownMenuItem className="text-red-600">
                                        <XCircle className="mr-2 h-4 w-4" />
                                        Cancel Refund
                                      </DropdownMenuItem>
                                    </>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </AdminLayout>
  )
}

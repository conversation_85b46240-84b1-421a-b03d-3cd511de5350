'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { AdminPageHeader } from '@/components/admin/admin-page-header'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { 
  FileText,
  Download,
  RefreshCw,
  ArrowLeft,
  Plus,
  Calendar as CalendarIcon,
  BarChart3,
  PieChart,
  TrendingUp,
  DollarSign,
  Users,
  CreditCard,
  FileSpreadsheet,
  Filter,
  Eye,
  Trash2
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { format } from 'date-fns'
import { ErrorState, LoadingState } from '@/components/ui/empty-state'

interface Report {
  _id: string
  name: string
  type: 'revenue' | 'subscriptions' | 'payments' | 'customers' | 'custom'
  description: string
  parameters: {
    dateRange: {
      start: Date
      end: Date
    }
    filters: Record<string, any>
    groupBy?: string
    metrics: string[]
  }
  status: 'generating' | 'completed' | 'failed'
  fileUrl?: string
  fileSize?: number
  generatedAt?: Date
  expiresAt?: Date
  createdBy: {
    _id: string
    name: string
    email: string
  }
  createdAt: Date
}

const REPORT_TEMPLATES = [
  {
    id: 'revenue-summary',
    name: 'Revenue Summary',
    description: 'Comprehensive revenue analysis with trends and breakdowns',
    type: 'revenue',
    icon: DollarSign,
    metrics: ['total_revenue', 'mrr', 'arr', 'growth_rate']
  },
  {
    id: 'subscription-analytics',
    name: 'Subscription Analytics',
    description: 'Subscription lifecycle, churn, and retention analysis',
    type: 'subscriptions',
    icon: Users,
    metrics: ['new_subscriptions', 'canceled_subscriptions', 'churn_rate', 'retention_rate']
  },
  {
    id: 'payment-performance',
    name: 'Payment Performance',
    description: 'Payment success rates, failures, and method analysis',
    type: 'payments',
    icon: CreditCard,
    metrics: ['success_rate', 'failure_rate', 'payment_methods', 'transaction_volume']
  },
  {
    id: 'customer-insights',
    name: 'Customer Insights',
    description: 'Customer behavior, lifetime value, and segmentation',
    type: 'customers',
    icon: BarChart3,
    metrics: ['ltv', 'arpu', 'customer_segments', 'acquisition_channels']
  }
]

export default function ReportsPage() {
  const router = useRouter()
  const { token } = useAuthStore()
  const [reports, setReports] = useState<Report[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null)
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({})
  const [reportForm, setReportForm] = useState({
    name: '',
    description: '',
    type: '',
    groupBy: '',
    metrics: [] as string[]
  })

  useEffect(() => {
    fetchReports()
  }, [])

  const fetchReports = async () => {
    try {
      setIsLoading(true)
      setError(null)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch('/api/v1/admin/billing/reports', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch reports')
      }

      const data = await response.json()
      setReports(data.data?.reports || [])
    } catch (error) {
      console.error('Failed to fetch reports:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch reports')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateReport = async () => {
    try {
      if (!token || !selectedTemplate) {
        throw new Error('Missing required data')
      }

      const response = await fetch('/api/v1/admin/billing/reports', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: reportForm.name || selectedTemplate.name,
          description: reportForm.description || selectedTemplate.description,
          type: selectedTemplate.type,
          parameters: {
            dateRange: {
              start: dateRange.from,
              end: dateRange.to
            },
            groupBy: reportForm.groupBy,
            metrics: selectedTemplate.metrics
          }
        })
      })

      if (!response.ok) {
        throw new Error('Failed to create report')
      }

      setIsCreateDialogOpen(false)
      setSelectedTemplate(null)
      setReportForm({ name: '', description: '', type: '', groupBy: '', metrics: [] })
      setDateRange({})
      await fetchReports()
    } catch (error) {
      console.error('Failed to create report:', error)
      setError(error instanceof Error ? error.message : 'Failed to create report')
    }
  }

  const handleDownloadReport = async (reportId: string) => {
    try {
      if (!token) return

      const response = await fetch(`/api/v1/admin/billing/reports/${reportId}/download`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to download report')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `report-${reportId}.xlsx`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Failed to download report:', error)
      setError(error instanceof Error ? error.message : 'Failed to download report')
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800'
      case 'generating': return 'bg-yellow-100 text-yellow-800'
      case 'failed': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <AdminPageHeader
          title="Billing Reports"
          description="Generate and manage comprehensive billing reports"
          icon={FileText}
          breadcrumbItems={[
            { label: 'Admin', href: '/admin' },
            { label: 'Billing', href: '/admin/billing' },
            { label: 'Reports' }
          ]}
          actions={
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                onClick={() => router.push('/admin/billing')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Billing
              </Button>
              <Button variant="outline" onClick={fetchReports} disabled={isLoading}>
                <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
                Refresh
              </Button>
              <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="w-4 h-4 mr-2" />
                    Generate Report
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Generate New Report</DialogTitle>
                    <DialogDescription>
                      Select a report template and configure parameters
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-6">
                    {/* Template Selection */}
                    <div>
                      <label className="text-sm font-medium mb-3 block">Report Template</label>
                      <div className="grid grid-cols-2 gap-3">
                        {REPORT_TEMPLATES.map((template) => (
                          <Card 
                            key={template.id}
                            className={cn(
                              'cursor-pointer transition-colors',
                              selectedTemplate?.id === template.id ? 'ring-2 ring-blue-500' : ''
                            )}
                            onClick={() => setSelectedTemplate(template)}
                          >
                            <CardContent className="p-4">
                              <div className="flex items-start space-x-3">
                                <template.icon className="w-6 h-6 text-blue-600 mt-1" />
                                <div>
                                  <h4 className="font-medium">{template.name}</h4>
                                  <p className="text-sm text-muted-foreground">{template.description}</p>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>

                    {selectedTemplate && (
                      <>
                        {/* Date Range */}
                        <div>
                          <label className="text-sm font-medium mb-3 block">Date Range</label>
                          <div className="flex space-x-2">
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button variant="outline" className="justify-start text-left font-normal">
                                  <CalendarIcon className="mr-2 h-4 w-4" />
                                  {dateRange.from ? format(dateRange.from, 'PPP') : 'Start date'}
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0">
                                <Calendar
                                  mode="single"
                                  selected={dateRange.from}
                                  onSelect={(date) => setDateRange(prev => ({ ...prev, from: date }))}
                                  initialFocus
                                />
                              </PopoverContent>
                            </Popover>
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button variant="outline" className="justify-start text-left font-normal">
                                  <CalendarIcon className="mr-2 h-4 w-4" />
                                  {dateRange.to ? format(dateRange.to, 'PPP') : 'End date'}
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0">
                                <Calendar
                                  mode="single"
                                  selected={dateRange.to}
                                  onSelect={(date) => setDateRange(prev => ({ ...prev, to: date }))}
                                  initialFocus
                                />
                              </PopoverContent>
                            </Popover>
                          </div>
                        </div>

                        {/* Report Configuration */}
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium">Report Name</label>
                            <Input
                              placeholder={selectedTemplate.name}
                              value={reportForm.name}
                              onChange={(e) => setReportForm(prev => ({ ...prev, name: e.target.value }))}
                            />
                          </div>
                          <div>
                            <label className="text-sm font-medium">Group By</label>
                            <Select value={reportForm.groupBy} onValueChange={(value) => setReportForm(prev => ({ ...prev, groupBy: value }))}>
                              <SelectTrigger>
                                <SelectValue placeholder="Select grouping" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="day">Daily</SelectItem>
                                <SelectItem value="week">Weekly</SelectItem>
                                <SelectItem value="month">Monthly</SelectItem>
                                <SelectItem value="quarter">Quarterly</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        <div>
                          <label className="text-sm font-medium">Description</label>
                          <Input
                            placeholder={selectedTemplate.description}
                            value={reportForm.description}
                            onChange={(e) => setReportForm(prev => ({ ...prev, description: e.target.value }))}
                          />
                        </div>
                      </>
                    )}
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCreateReport} disabled={!selectedTemplate || !dateRange.from || !dateRange.to}>
                      Generate Report
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          }
        />

        {error ? (
          <ErrorState
            title="Failed to load reports"
            description={error}
            onRetry={fetchReports}
          />
        ) : (
          <Tabs defaultValue="reports" className="space-y-6">
            <TabsList>
              <TabsTrigger value="reports">Generated Reports</TabsTrigger>
              <TabsTrigger value="templates">Report Templates</TabsTrigger>
              <TabsTrigger value="scheduled">Scheduled Reports</TabsTrigger>
            </TabsList>

            <TabsContent value="reports" className="space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Total Reports</p>
                        <p className="text-2xl font-bold">{reports.length}</p>
                      </div>
                      <FileText className="w-8 h-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Completed</p>
                        <p className="text-2xl font-bold">
                          {reports.filter(r => r.status === 'completed').length}
                        </p>
                      </div>
                      <Download className="w-8 h-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Generating</p>
                        <p className="text-2xl font-bold">
                          {reports.filter(r => r.status === 'generating').length}
                        </p>
                      </div>
                      <RefreshCw className="w-8 h-8 text-yellow-600" />
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">This Month</p>
                        <p className="text-2xl font-bold">
                          {reports.filter(r => {
                            const reportDate = new Date(r.createdAt)
                            const now = new Date()
                            return reportDate.getMonth() === now.getMonth() && 
                                   reportDate.getFullYear() === now.getFullYear()
                          }).length}
                        </p>
                      </div>
                      <TrendingUp className="w-8 h-8 text-purple-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Reports Table */}
              <Card>
                <CardHeader>
                  <CardTitle>Generated Reports</CardTitle>
                  <CardDescription>View and download your generated reports</CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <LoadingState message="Loading reports..." />
                  ) : (
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Report Name</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Date Range</TableHead>
                            <TableHead>Generated</TableHead>
                            <TableHead>Size</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {reports.length === 0 ? (
                            <TableRow>
                              <TableCell colSpan={7} className="text-center py-8">
                                <div className="flex flex-col items-center space-y-2">
                                  <FileText className="w-8 h-8 text-muted-foreground" />
                                  <p className="text-muted-foreground">No reports generated yet</p>
                                  <Button onClick={() => setIsCreateDialogOpen(true)}>
                                    Generate Your First Report
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ) : (
                            reports.map((report) => (
                              <TableRow key={report._id}>
                                <TableCell>
                                  <div>
                                    <p className="font-medium">{report.name}</p>
                                    <p className="text-sm text-muted-foreground">{report.description}</p>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Badge variant="outline" className="capitalize">
                                    {report.type}
                                  </Badge>
                                </TableCell>
                                <TableCell>
                                  <Badge className={getStatusColor(report.status)}>
                                    {report.status}
                                  </Badge>
                                </TableCell>
                                <TableCell>
                                  <div className="text-sm">
                                    {format(new Date(report.parameters.dateRange.start), 'MMM dd')} - {format(new Date(report.parameters.dateRange.end), 'MMM dd, yyyy')}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <div className="text-sm">
                                    {report.generatedAt ? format(new Date(report.generatedAt), 'MMM dd, yyyy') : '-'}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    by {report.createdBy.name}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  {report.fileSize ? formatFileSize(report.fileSize) : '-'}
                                </TableCell>
                                <TableCell className="text-right">
                                  <div className="flex justify-end space-x-2">
                                    {report.status === 'completed' && (
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handleDownloadReport(report._id)}
                                      >
                                        <Download className="w-4 h-4 mr-1" />
                                        Download
                                      </Button>
                                    )}
                                    <Button variant="outline" size="sm">
                                      <Eye className="w-4 h-4" />
                                    </Button>
                                    <Button variant="outline" size="sm">
                                      <Trash2 className="w-4 h-4" />
                                    </Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))
                          )}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="templates" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {REPORT_TEMPLATES.map((template) => (
                  <Card key={template.id}>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <template.icon className="w-5 h-5 mr-2" />
                        {template.name}
                      </CardTitle>
                      <CardDescription>{template.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div>
                          <p className="text-sm font-medium">Included Metrics:</p>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {template.metrics.map((metric) => (
                              <Badge key={metric} variant="secondary" className="text-xs">
                                {metric.replace('_', ' ')}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <Button 
                          className="w-full" 
                          onClick={() => {
                            setSelectedTemplate(template)
                            setIsCreateDialogOpen(true)
                          }}
                        >
                          Use Template
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="scheduled" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Scheduled Reports</CardTitle>
                  <CardDescription>Automatically generated reports on a schedule</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <FileSpreadsheet className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">Scheduled Reports Coming Soon</h3>
                    <p className="text-muted-foreground mb-4">
                      Set up automatic report generation on daily, weekly, or monthly schedules.
                    </p>
                    <Button variant="outline">
                      <Plus className="w-4 h-4 mr-2" />
                      Schedule Report
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </AdminLayout>
  )
}

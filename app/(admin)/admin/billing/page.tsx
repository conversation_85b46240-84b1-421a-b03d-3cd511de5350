'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { AdminPageHeader } from '@/components/admin/admin-page-header'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  DollarSign, 
  CreditCard, 
  Users, 
  TrendingUp, 
  TrendingDown,
  Calendar,
  FileText,
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw,
  Download,
  Plus,
  Eye,
  Settings,
  RotateCcw,
  BarChart3
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { ErrorState, LoadingState } from '@/components/ui/empty-state'

interface BillingStats {
  revenue: {
    total: number
    thisMonth: number
    lastMonth: number
    growth: number
  }
  subscriptions: {
    total: number
    active: number
    trialing: number
    canceled: number
  }
  payments: {
    total: number
    succeeded: number
    failed: number
    pending: number
  }
  invoices: {
    total: number
    paid: number
    overdue: number
    draft: number
  }
  mrr: number
  arr: number
  churnRate: number
  averageRevenuePerUser: number
}

interface RecentActivity {
  _id: string
  type: 'payment' | 'subscription' | 'invoice' | 'refund'
  description: string
  amount?: number
  currency?: string
  company: {
    name: string
    logo?: string
  }
  status: string
  createdAt: Date
}

export default function BillingDashboardPage() {
  const router = useRouter()
  const { token } = useAuthStore()
  const [stats, setStats] = useState<BillingStats | null>(null)
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchBillingData()
  }, [])

  const fetchBillingData = async () => {
    try {
      setIsLoading(true)
      setError(null)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const [statsResponse, activityResponse] = await Promise.all([
        fetch('/api/v1/admin/billing/stats', {
          headers: { 'Authorization': `Bearer ${token}` }
        }),
        fetch('/api/v1/admin/billing/activity', {
          headers: { 'Authorization': `Bearer ${token}` }
        })
      ])

      if (!statsResponse.ok || !activityResponse.ok) {
        throw new Error('Failed to fetch billing data')
      }

      const [statsData, activityData] = await Promise.all([
        statsResponse.json(),
        activityResponse.json()
      ])

      setStats(statsData.data?.stats || {
        revenue: { total: 0, thisMonth: 0, lastMonth: 0, growth: 0 },
        subscriptions: { total: 0, active: 0, trialing: 0, canceled: 0 },
        payments: { total: 0, succeeded: 0, failed: 0, pending: 0 },
        invoices: { total: 0, paid: 0, overdue: 0, draft: 0 },
        mrr: 0,
        arr: 0,
        churnRate: 0,
        averageRevenuePerUser: 0
      })
      setRecentActivity(activityData.data?.activity || [])
    } catch (error) {
      console.error('Error fetching billing data:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch billing data')
    } finally {
      setIsLoading(false)
    }
  }

  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'payment': return <CreditCard className="w-4 h-4" />
      case 'subscription': return <Users className="w-4 h-4" />
      case 'invoice': return <FileText className="w-4 h-4" />
      case 'refund': return <TrendingDown className="w-4 h-4" />
      default: return <DollarSign className="w-4 h-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'succeeded':
      case 'paid':
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'failed':
      case 'overdue':
      case 'canceled':
        return 'bg-red-100 text-red-800'
      case 'pending':
      case 'trialing':
      case 'draft':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="p-6">
          <LoadingState message="Loading billing dashboard..." />
        </div>
      </AdminLayout>
    )
  }



  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <AdminPageHeader
          title="Billing & Revenue"
          description="Manage subscriptions, payments, and revenue analytics"
          icon={DollarSign}
          breadcrumbItems={[
            { label: 'Admin', href: '/admin' },
            { label: 'Billing & Revenue' }
          ]}
          actions={
            <div className="flex space-x-2">
              <Button variant="outline" onClick={fetchBillingData} disabled={isLoading}>
                <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
                Refresh
              </Button>
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              <Button onClick={() => router.push('/admin/billing/plans')}>
                <Plus className="w-4 h-4 mr-2" />
                Manage Plans
              </Button>
            </div>
          }
        />

        {error ? (
          <ErrorState
            title="Failed to load billing data"
            description={error}
            onRetry={fetchBillingData}
          />
        ) : (
          <>
            {/* Revenue Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                  <p className="text-2xl font-bold">{formatCurrency(stats?.revenue.total || 0)}</p>
                  <div className="flex items-center mt-1">
                    {(stats?.revenue.growth || 0) >= 0 ? (
                      <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                    ) : (
                      <TrendingDown className="w-4 h-4 text-red-600 mr-1" />
                    )}
                    <span className={cn(
                      'text-sm font-medium',
                      (stats?.revenue.growth || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                    )}>
                      {formatPercentage(stats?.revenue.growth || 0)}
                    </span>
                  </div>
                </div>
                <DollarSign className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Active Subscriptions</p>
                  <p className="text-2xl font-bold">{stats?.subscriptions.active || 0}</p>
                  <p className="text-sm text-muted-foreground">
                    {stats?.subscriptions.trialing || 0} trialing
                  </p>
                </div>
                <Users className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Monthly Recurring Revenue</p>
                  <p className="text-2xl font-bold">{formatCurrency(stats?.mrr || 0)}</p>
                  <p className="text-sm text-muted-foreground">
                    ARR: {formatCurrency(stats?.arr || 0)}
                  </p>
                </div>
                <TrendingUp className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Payment Success Rate</p>
                  <p className="text-2xl font-bold">
                    {stats?.payments.total ? 
                      Math.round((stats.payments.succeeded / stats.payments.total) * 100) : 0
                    }%
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {stats?.payments.failed || 0} failed
                  </p>
                </div>
                <CreditCard className="w-8 h-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          <Button
            variant="outline"
            className="h-20 flex-col space-y-2"
            onClick={() => router.push('/admin/billing/subscriptions')}
          >
            <Users className="w-6 h-6" />
            <span>Manage Subscriptions</span>
          </Button>
          <Button
            variant="outline"
            className="h-20 flex-col space-y-2"
            onClick={() => router.push('/admin/billing/payments')}
          >
            <CreditCard className="w-6 h-6" />
            <span>View Payments</span>
          </Button>
          <Button
            variant="outline"
            className="h-20 flex-col space-y-2"
            onClick={() => router.push('/admin/billing/invoices')}
          >
            <FileText className="w-6 h-6" />
            <span>Manage Invoices</span>
          </Button>
          <Button
            variant="outline"
            className="h-20 flex-col space-y-2"
            onClick={() => router.push('/admin/billing/refunds')}
          >
            <RotateCcw className="w-6 h-6" />
            <span>Process Refunds</span>
          </Button>
          <Button
            variant="outline"
            className="h-20 flex-col space-y-2"
            onClick={() => router.push('/admin/billing/reports')}
          >
            <BarChart3 className="w-6 h-6" />
            <span>Generate Reports</span>
          </Button>
          <Button
            variant="outline"
            className="h-20 flex-col space-y-2"
            onClick={() => router.push('/admin/billing/plans')}
          >
            <Settings className="w-6 h-6" />
            <span>Billing Plans</span>
          </Button>
        </div>

        {/* Detailed Stats and Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Detailed Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>Revenue Breakdown</CardTitle>
              <CardDescription>Monthly revenue and growth metrics</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">This Month</span>
                <span className="font-bold">{formatCurrency(stats?.revenue.thisMonth || 0)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Last Month</span>
                <span className="font-bold">{formatCurrency(stats?.revenue.lastMonth || 0)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Average Revenue Per User</span>
                <span className="font-bold">{formatCurrency(stats?.averageRevenuePerUser || 0)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Churn Rate</span>
                <span className="font-bold">{(stats?.churnRate || 0).toFixed(1)}%</span>
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest billing and payment activities</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.length === 0 ? (
                  <p className="text-muted-foreground text-center py-4">No recent activity</p>
                ) : (
                  recentActivity.slice(0, 5).map((activity) => (
                    <div key={activity._id} className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        {getActivityIcon(activity.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {activity.description}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {activity.company.name} • {new Date(activity.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        {activity.amount && (
                          <span className="text-sm font-medium">
                            {formatCurrency(activity.amount, activity.currency)}
                          </span>
                        )}
                        <Badge className={cn('text-xs', getStatusColor(activity.status))}>
                          {activity.status}
                        </Badge>
                      </div>
                    </div>
                  ))
                )}
              </div>
              {recentActivity.length > 5 && (
                <div className="mt-4 text-center">
                  <Button variant="outline" size="sm">
                    <Eye className="w-4 h-4 mr-2" />
                    View All Activity
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
          </>
        )}
      </div>
    </AdminLayout>
  )
}

'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { AdminPageHeader } from '@/components/admin/admin-page-header'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  Calendar,
  RefreshCw,
  Download,
  ArrowLeft,
  PieChart,
  LineChart,
  Target
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { ErrorState, LoadingState } from '@/components/ui/empty-state'

interface AnalyticsData {
  revenue?: {
    current: number
    previous: number
    growth: number
    transactions: number
    dailyRevenue?: Array<{
      _id: { year: number, month: number, day: number }
      revenue: number
      transactions: number
    }>
    revenueByMethod?: Array<{
      _id: string
      revenue: number
      transactions: number
    }>
    revenueByPlan?: Array<{
      _id: string
      revenue: number
      transactions: number
    }>
  }
  subscriptions?: {
    new: number
    canceled: number
    active: number
    churnRate: number
    statusDistribution?: Array<{
      _id: string
      count: number
    }>
    lifecycleMetrics?: Array<{
      _id: { year: number, month: number, day: number }
      newSubscriptions: number
      trialSubscriptions: number
    }>
  }
  mrr?: {
    current: number
    arr: number
  }
  kpis?: {
    arpu: number
    ltv: number
  }
  planMetrics?: Array<{
    name: string
    totalSubscriptions: number
    activeSubscriptions: number
    monthlyRevenue: number
  }>
  cohortAnalysis?: Array<{
    cohort: string
    size: number
    retention: Array<{
      period: number
      retained: number
      rate: number
    }>
  }>
}

export default function BillingAnalyticsPage() {
  const router = useRouter()
  const { token } = useAuthStore()
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeRange, setTimeRange] = useState('30d')
  const [reportType, setReportType] = useState('overview')

  const fetchAnalytics = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const queryParams = new URLSearchParams({
        timeRange,
        type: reportType
      })

      const response = await fetch(`/api/v1/admin/billing/analytics?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }

      const data = await response.json()
      setAnalytics(data.data?.analytics || {})
    } catch (error) {
      console.error('Error fetching analytics:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch analytics')
    } finally {
      setIsLoading(false)
    }
  }, [token, timeRange, reportType])

  useEffect(() => {
    fetchAnalytics()
  }, [fetchAnalytics])

  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`
  }



  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <AdminPageHeader
          title="Billing Analytics"
          description="Comprehensive billing and revenue analytics"
          icon={BarChart3}
          breadcrumbItems={[
            { label: 'Admin', href: '/admin' },
            { label: 'Billing', href: '/admin/billing' },
            { label: 'Analytics' }
          ]}
          actions={
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                onClick={() => router.push('/admin/billing')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Billing
              </Button>
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Time Range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" onClick={fetchAnalytics} disabled={isLoading}>
                <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
                Refresh
              </Button>
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          }
        />

        {error ? (
          <ErrorState
            title="Failed to load analytics"
            description={error}
            onRetry={fetchAnalytics}
          />
        ) : isLoading ? (
          <LoadingState message="Loading analytics..." />
        ) : (
          <Tabs value={reportType} onValueChange={setReportType} className="space-y-6">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="revenue">Revenue</TabsTrigger>
              <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
              <TabsTrigger value="plans">Plans</TabsTrigger>
              <TabsTrigger value="cohort">Cohort</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Key Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Revenue</p>
                        <p className="text-2xl font-bold">{formatCurrency(analytics?.revenue?.current || 0)}</p>
                        <div className="flex items-center mt-1">
                          {(analytics?.revenue?.growth || 0) >= 0 ? (
                            <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                          ) : (
                            <TrendingDown className="w-4 h-4 text-red-600 mr-1" />
                          )}
                          <span className={cn(
                            'text-sm font-medium',
                            (analytics?.revenue?.growth || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                          )}>
                            {formatPercentage(analytics?.revenue?.growth || 0)}
                          </span>
                        </div>
                      </div>
                      <DollarSign className="w-8 h-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Active Subscriptions</p>
                        <p className="text-2xl font-bold">{analytics?.subscriptions?.active || 0}</p>
                        <p className="text-sm text-muted-foreground">
                          {analytics?.subscriptions?.new || 0} new this period
                        </p>
                      </div>
                      <Users className="w-8 h-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Monthly Recurring Revenue</p>
                        <p className="text-2xl font-bold">{formatCurrency(analytics?.mrr?.current || 0)}</p>
                        <p className="text-sm text-muted-foreground">
                          ARR: {formatCurrency(analytics?.mrr?.arr || 0)}
                        </p>
                      </div>
                      <TrendingUp className="w-8 h-8 text-purple-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Churn Rate</p>
                        <p className="text-2xl font-bold">{(analytics?.subscriptions?.churnRate || 0).toFixed(1)}%</p>
                        <p className="text-sm text-muted-foreground">
                          {analytics?.subscriptions?.canceled || 0} canceled
                        </p>
                      </div>
                      <Target className="w-8 h-8 text-orange-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* KPIs */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Key Performance Indicators</CardTitle>
                    <CardDescription>Important business metrics</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Average Revenue Per User (ARPU)</span>
                      <span className="font-bold">{formatCurrency(analytics?.kpis?.arpu || 0)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Customer Lifetime Value (LTV)</span>
                      <span className="font-bold">{formatCurrency(analytics?.kpis?.ltv || 0)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">LTV/CAC Ratio</span>
                      <span className="font-bold">
                        {analytics?.kpis?.ltv && analytics?.kpis?.arpu ? 
                          (analytics.kpis.ltv / analytics.kpis.arpu).toFixed(1) : 'N/A'
                        }
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Revenue Breakdown</CardTitle>
                    <CardDescription>Revenue composition</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Current Period</span>
                      <span className="font-bold">{formatCurrency(analytics?.revenue?.current || 0)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Previous Period</span>
                      <span className="font-bold">{formatCurrency(analytics?.revenue?.previous || 0)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Total Transactions</span>
                      <span className="font-bold">{analytics?.revenue?.transactions || 0}</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="revenue" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <LineChart className="w-5 h-5 mr-2" />
                      Daily Revenue Trend
                    </CardTitle>
                    <CardDescription>Revenue performance over time</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {analytics?.revenue?.dailyRevenue ? (
                      <div className="space-y-2">
                        {analytics.revenue.dailyRevenue.slice(-7).map((day, index) => (
                          <div key={index} className="flex justify-between items-center">
                            <span className="text-sm">
                              {day._id.month}/{day._id.day}
                            </span>
                            <span className="font-medium">
                              {formatCurrency(day.revenue)}
                            </span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No revenue data available</p>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <PieChart className="w-5 h-5 mr-2" />
                      Revenue by Payment Method
                    </CardTitle>
                    <CardDescription>Payment method breakdown</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {analytics?.revenue?.revenueByMethod ? (
                      <div className="space-y-2">
                        {analytics.revenue.revenueByMethod.map((method, index) => (
                          <div key={index} className="flex justify-between items-center">
                            <span className="text-sm capitalize">{method._id}</span>
                            <span className="font-medium">
                              {formatCurrency(method.revenue)}
                            </span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No payment method data available</p>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="subscriptions" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Subscription Status Distribution</CardTitle>
                    <CardDescription>Current subscription statuses</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {analytics?.subscriptions?.statusDistribution ? (
                      <div className="space-y-2">
                        {analytics.subscriptions.statusDistribution.map((status, index) => (
                          <div key={index} className="flex justify-between items-center">
                            <span className="text-sm capitalize">{status._id}</span>
                            <span className="font-medium">{status.count}</span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No subscription data available</p>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Subscription Metrics</CardTitle>
                    <CardDescription>Key subscription KPIs</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">New Subscriptions</span>
                      <span className="font-bold">{analytics?.subscriptions?.new || 0}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Canceled Subscriptions</span>
                      <span className="font-bold">{analytics?.subscriptions?.canceled || 0}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Churn Rate</span>
                      <span className="font-bold">{(analytics?.subscriptions?.churnRate || 0).toFixed(1)}%</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="plans" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Plan Performance</CardTitle>
                  <CardDescription>Subscription plan metrics and revenue</CardDescription>
                </CardHeader>
                <CardContent>
                  {analytics?.planMetrics ? (
                    <div className="space-y-4">
                      {analytics.planMetrics.map((plan, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex justify-between items-start mb-2">
                            <h4 className="font-medium">{plan.name}</h4>
                            <span className="text-sm text-muted-foreground">
                              {formatCurrency(plan.monthlyRevenue)}/mo
                            </span>
                          </div>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="text-muted-foreground">Total Subscriptions:</span>
                              <span className="ml-2 font-medium">{plan.totalSubscriptions}</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Active:</span>
                              <span className="ml-2 font-medium">{plan.activeSubscriptions}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No plan data available</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="cohort" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Cohort Analysis</CardTitle>
                  <CardDescription>Customer retention by signup month</CardDescription>
                </CardHeader>
                <CardContent>
                  {analytics?.cohortAnalysis ? (
                    <div className="space-y-4">
                      {analytics.cohortAnalysis.map((cohort, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex justify-between items-center mb-2">
                            <h4 className="font-medium">Cohort {cohort.cohort}</h4>
                            <span className="text-sm text-muted-foreground">
                              {cohort.size} customers
                            </span>
                          </div>
                          <div className="grid grid-cols-4 gap-4 text-sm">
                            {cohort.retention.map((period, periodIndex) => (
                              <div key={periodIndex}>
                                <span className="text-muted-foreground">Month {period.period}:</span>
                                <span className="ml-2 font-medium">{period.rate.toFixed(1)}%</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No cohort data available</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </AdminLayout>
  )
}

'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Search, 
  MoreHorizontal,
  UserCheck,
  UserX,
  Mail,
  Eye,
  ArrowLeft,
  RefreshCw,
  Download,
  AlertTriangle,
  Calendar,
  Clock,
  FileText
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface SuspendedUser {
  _id: string
  email: string
  role: 'job_seeker' | 'company_admin' | 'recruiter'
  profile: {
    firstName: string
    lastName: string
    avatar?: string
  }
  isActive: boolean
  suspensionInfo: {
    reason: string
    suspendedAt: Date
    suspendedBy: string
    duration?: string
    notes?: string
  }
  createdAt: Date
  lastLogin?: Date
}

export default function SuspendedUsersPage() {
  const router = useRouter()
  const [suspendedUsers, setSuspendedUsers] = useState<SuspendedUser[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterReason, setFilterReason] = useState<'all' | 'violation' | 'spam' | 'fraud' | 'other'>('all')

  useEffect(() => {
    fetchSuspendedUsers()
  }, [])

  const fetchSuspendedUsers = async () => {
    try {
      setIsLoading(true)
      // Mock data for now
      const mockSuspendedUsers: SuspendedUser[] = [
        {
          _id: '1',
          email: '<EMAIL>',
          role: 'job_seeker',
          profile: {
            firstName: 'Suspended',
            lastName: 'User'
          },
          isActive: false,
          suspensionInfo: {
            reason: 'Terms of Service Violation',
            suspendedAt: new Date('2024-03-01'),
            suspendedBy: '<EMAIL>',
            duration: '30 days',
            notes: 'Multiple reports of inappropriate behavior in messages'
          },
          createdAt: new Date('2024-01-15'),
          lastLogin: new Date('2024-02-28')
        },
        {
          _id: '2',
          email: '<EMAIL>',
          role: 'company_admin',
          profile: {
            firstName: 'Spam',
            lastName: 'Account'
          },
          isActive: false,
          suspensionInfo: {
            reason: 'Spam Activity',
            suspendedAt: new Date('2024-03-05'),
            suspendedBy: '<EMAIL>',
            duration: 'Permanent',
            notes: 'Posting fake job listings and sending spam messages'
          },
          createdAt: new Date('2024-02-20'),
          lastLogin: new Date('2024-03-04')
        }
      ]
      setSuspendedUsers(mockSuspendedUsers)
    } catch (error) {
      console.error('Failed to fetch suspended users:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredUsers = suspendedUsers.filter(user => {
    const matchesSearch = 
      user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      `${user.profile.firstName} ${user.profile.lastName}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.suspensionInfo.reason.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesReason = 
      filterReason === 'all' ||
      user.suspensionInfo.reason.toLowerCase().includes(filterReason)
    
    return matchesSearch && matchesReason
  })

  const handleUserAction = async (userId: string, action: 'reactivate' | 'extend_suspension' | 'permanent_ban') => {
    try {
      console.log(`${action} user:`, userId)
      // Implement user action API call
      await fetchSuspendedUsers() // Refresh data
    } catch (error) {
      console.error(`Failed to ${action} user:`, error)
    }
  }

  const getSuspensionBadgeColor = (reason: string) => {
    if (reason.toLowerCase().includes('violation')) return 'bg-red-100 text-red-800'
    if (reason.toLowerCase().includes('spam')) return 'bg-orange-100 text-orange-800'
    if (reason.toLowerCase().includes('fraud')) return 'bg-purple-100 text-purple-800'
    return 'bg-gray-100 text-gray-800'
  }

  const getDaysRemaining = (suspendedAt: Date, duration: string) => {
    if (duration === 'Permanent') return 'Permanent'
    const days = parseInt(duration.split(' ')[0])
    const endDate = new Date(suspendedAt.getTime() + days * 24 * 60 * 60 * 1000)
    const remaining = Math.ceil((endDate.getTime() - Date.now()) / (24 * 60 * 60 * 1000))
    return remaining > 0 ? `${remaining} days left` : 'Expired'
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => router.push('/admin/users')}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Users
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center">
              <UserX className="w-8 h-8 mr-3 text-red-600" />
              Suspended Users
            </h1>
            <p className="text-muted-foreground">
              Manage suspended user accounts and review suspension details
            </p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={fetchSuspendedUsers} disabled={isLoading}>
            <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Suspended</p>
                <p className="text-2xl font-bold text-red-600">{suspendedUsers.length}</p>
              </div>
              <UserX className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Temporary Suspensions</p>
                <p className="text-2xl font-bold">
                  {suspendedUsers.filter(u => u.suspensionInfo.duration !== 'Permanent').length}
                </p>
              </div>
              <Clock className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Permanent Bans</p>
                <p className="text-2xl font-bold">
                  {suspendedUsers.filter(u => u.suspensionInfo.duration === 'Permanent').length}
                </p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">This Month</p>
                <p className="text-2xl font-bold">
                  {suspendedUsers.filter(u => 
                    new Date(u.suspensionInfo.suspendedAt).getMonth() === new Date().getMonth()
                  ).length}
                </p>
              </div>
              <Calendar className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Suspended Users Management</CardTitle>
          <CardDescription>Review and manage suspended user accounts</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search by name, email, or reason..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant={filterReason === 'all' ? 'default' : 'outline'}
                onClick={() => setFilterReason('all')}
                size="sm"
              >
                All Reasons
              </Button>
              <Button
                variant={filterReason === 'violation' ? 'default' : 'outline'}
                onClick={() => setFilterReason('violation')}
                size="sm"
              >
                Violations
              </Button>
              <Button
                variant={filterReason === 'spam' ? 'default' : 'outline'}
                onClick={() => setFilterReason('spam')}
                size="sm"
              >
                Spam
              </Button>
              <Button
                variant={filterReason === 'fraud' ? 'default' : 'outline'}
                onClick={() => setFilterReason('fraud')}
                size="sm"
              >
                Fraud
              </Button>
            </div>
          </div>

          {/* Users Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Suspension Reason</TableHead>
                  <TableHead>Duration</TableHead>
                  <TableHead>Suspended By</TableHead>
                  <TableHead>Suspended Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
                      Loading suspended users...
                    </TableCell>
                  </TableRow>
                ) : filteredUsers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      No suspended users found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredUsers.map((user) => (
                    <TableRow key={user._id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar className="w-10 h-10 opacity-60">
                            <AvatarImage src={user.profile.avatar} />
                            <AvatarFallback>
                              {user.profile.firstName[0]}{user.profile.lastName[0]}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-muted-foreground">
                              {user.profile.firstName} {user.profile.lastName}
                            </p>
                            <p className="text-sm text-muted-foreground">{user.email}</p>
                            <Badge variant="outline" className="text-xs mt-1">
                              {user.role.replace('_', ' ')}
                            </Badge>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getSuspensionBadgeColor(user.suspensionInfo.reason)}>
                          {user.suspensionInfo.reason}
                        </Badge>
                        {user.suspensionInfo.notes && (
                          <p className="text-xs text-muted-foreground mt-1">
                            {user.suspensionInfo.notes.substring(0, 50)}...
                          </p>
                        )}
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{user.suspensionInfo.duration}</p>
                          <p className="text-xs text-muted-foreground">
                            {getDaysRemaining(user.suspensionInfo.suspendedAt, user.suspensionInfo.duration || '')}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <p className="text-sm">{user.suspensionInfo.suspendedBy}</p>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Calendar className="w-4 h-4 mr-1" />
                          {new Date(user.suspensionInfo.suspendedAt).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="destructive">
                          Suspended
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => router.push(`/admin/users/${user._id}`)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Profile
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <FileText className="mr-2 h-4 w-4" />
                              View Suspension Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Mail className="mr-2 h-4 w-4" />
                              Contact User
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleUserAction(user._id, 'reactivate')}
                              className="text-green-600"
                            >
                              <UserCheck className="mr-2 h-4 w-4" />
                              Reactivate Account
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleUserAction(user._id, 'extend_suspension')}
                              className="text-orange-600"
                            >
                              <Clock className="mr-2 h-4 w-4" />
                              Extend Suspension
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleUserAction(user._id, 'permanent_ban')}
                              className="text-red-600"
                            >
                              <AlertTriangle className="mr-2 h-4 w-4" />
                              Permanent Ban
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      </div>
    </AdminLayout>
  )
}

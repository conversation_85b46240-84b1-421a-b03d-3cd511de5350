'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Search, 
  MoreHorizontal,
  UserCheck,
  UserX,
  Mail,
  Eye,
  ArrowLeft,
  RefreshCw,
  Download,
  Users,
  Activity,
  Calendar,
  Clock
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ActiveUser {
  _id: string
  email: string
  role: 'job_seeker' | 'company_admin' | 'recruiter'
  profile: {
    firstName: string
    lastName: string
    avatar?: string
  }
  isActive: boolean
  isEmailVerified: boolean
  lastLogin: Date
  lastActivity: Date
  createdAt: Date
  activityScore: number
  sessionsThisWeek: number
}

export default function ActiveUsersPage() {
  const router = useRouter()
  const [activeUsers, setActiveUsers] = useState<ActiveUser[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterRole, setFilterRole] = useState<'all' | 'job_seeker' | 'company_admin' | 'recruiter'>('all')

  useEffect(() => {
    fetchActiveUsers()
  }, [])

  const fetchActiveUsers = async () => {
    try {
      setIsLoading(true)
      // Mock data for now
      const mockActiveUsers: ActiveUser[] = [
        {
          _id: '1',
          email: '<EMAIL>',
          role: 'job_seeker',
          profile: {
            firstName: 'John',
            lastName: 'Doe'
          },
          isActive: true,
          isEmailVerified: true,
          lastLogin: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
          lastActivity: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
          createdAt: new Date('2024-01-15'),
          activityScore: 85,
          sessionsThisWeek: 12
        },
        {
          _id: '2',
          email: '<EMAIL>',
          role: 'company_admin',
          profile: {
            firstName: 'Alice',
            lastName: 'Johnson'
          },
          isActive: true,
          isEmailVerified: true,
          lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
          lastActivity: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
          createdAt: new Date('2024-01-10'),
          activityScore: 92,
          sessionsThisWeek: 8
        },
        {
          _id: '3',
          email: '<EMAIL>',
          role: 'recruiter',
          profile: {
            firstName: 'Bob',
            lastName: 'Smith'
          },
          isActive: true,
          isEmailVerified: true,
          lastLogin: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
          lastActivity: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
          createdAt: new Date('2024-02-01'),
          activityScore: 78,
          sessionsThisWeek: 15
        }
      ]
      setActiveUsers(mockActiveUsers)
    } catch (error) {
      console.error('Failed to fetch active users:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredUsers = activeUsers.filter(user => {
    const matchesSearch = 
      user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      `${user.profile.firstName} ${user.profile.lastName}`.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesRole = filterRole === 'all' || user.role === filterRole
    
    return matchesSearch && matchesRole
  })

  const getActivityStatus = (lastActivity: Date) => {
    const minutesAgo = Math.floor((Date.now() - lastActivity.getTime()) / (1000 * 60))
    if (minutesAgo < 5) return { status: 'online', text: 'Online now', color: 'text-green-600' }
    if (minutesAgo < 30) return { status: 'recent', text: `${minutesAgo}m ago`, color: 'text-yellow-600' }
    if (minutesAgo < 60) return { status: 'away', text: `${minutesAgo}m ago`, color: 'text-orange-600' }
    const hoursAgo = Math.floor(minutesAgo / 60)
    return { status: 'away', text: `${hoursAgo}h ago`, color: 'text-gray-600' }
  }

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'job_seeker': return 'bg-blue-100 text-blue-800'
      case 'company_admin': return 'bg-purple-100 text-purple-800'
      case 'recruiter': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => router.push('/admin/users')}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Users
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center">
              <Activity className="w-8 h-8 mr-3 text-green-600" />
              Active Users
            </h1>
            <p className="text-muted-foreground">
              Monitor currently active and recently active users
            </p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={fetchActiveUsers} disabled={isLoading}>
            <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Online Now</p>
                <p className="text-2xl font-bold text-green-600">
                  {activeUsers.filter(u => getActivityStatus(u.lastActivity).status === 'online').length}
                </p>
              </div>
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Today</p>
                <p className="text-2xl font-bold">{activeUsers.length}</p>
              </div>
              <Users className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Activity Score</p>
                <p className="text-2xl font-bold">
                  {Math.round(activeUsers.reduce((sum, u) => sum + u.activityScore, 0) / activeUsers.length || 0)}
                </p>
              </div>
              <Activity className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Sessions</p>
                <p className="text-2xl font-bold">
                  {activeUsers.reduce((sum, u) => sum + u.sessionsThisWeek, 0)}
                </p>
              </div>
              <Clock className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Active Users Monitoring</CardTitle>
          <CardDescription>Real-time view of active users and their activity levels</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search by name or email..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant={filterRole === 'all' ? 'default' : 'outline'}
                onClick={() => setFilterRole('all')}
                size="sm"
              >
                All Roles
              </Button>
              <Button
                variant={filterRole === 'job_seeker' ? 'default' : 'outline'}
                onClick={() => setFilterRole('job_seeker')}
                size="sm"
              >
                Job Seekers
              </Button>
              <Button
                variant={filterRole === 'company_admin' ? 'default' : 'outline'}
                onClick={() => setFilterRole('company_admin')}
                size="sm"
              >
                Company Admins
              </Button>
              <Button
                variant={filterRole === 'recruiter' ? 'default' : 'outline'}
                onClick={() => setFilterRole('recruiter')}
                size="sm"
              >
                Recruiters
              </Button>
            </div>
          </div>

          {/* Users Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Activity Status</TableHead>
                  <TableHead>Activity Score</TableHead>
                  <TableHead>Sessions This Week</TableHead>
                  <TableHead>Last Login</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
                      Loading active users...
                    </TableCell>
                  </TableRow>
                ) : filteredUsers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      No active users found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredUsers.map((user) => {
                    const activityStatus = getActivityStatus(user.lastActivity)
                    return (
                      <TableRow key={user._id}>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <div className="relative">
                              <Avatar className="w-10 h-10">
                                <AvatarImage src={user.profile.avatar} />
                                <AvatarFallback>
                                  {user.profile.firstName[0]}{user.profile.lastName[0]}
                                </AvatarFallback>
                              </Avatar>
                              {activityStatus.status === 'online' && (
                                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
                              )}
                            </div>
                            <div>
                              <p className="font-medium">{user.profile.firstName} {user.profile.lastName}</p>
                              <p className="text-sm text-muted-foreground">{user.email}</p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getRoleBadgeColor(user.role)}>
                            {user.role.replace('_', ' ')}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <span className={cn('text-sm font-medium', activityStatus.color)}>
                            {activityStatus.text}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <div className="w-16 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-600 h-2 rounded-full" 
                                style={{ width: `${user.activityScore}%` }}
                              ></div>
                            </div>
                            <span className="text-sm font-medium">{user.activityScore}%</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary">
                            {user.sessionsThisWeek} sessions
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Calendar className="w-4 h-4 mr-1" />
                            {new Date(user.lastLogin).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => router.push(`/admin/users/${user._id}`)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Profile
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Mail className="mr-2 h-4 w-4" />
                                Send Message
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Activity className="mr-2 h-4 w-4" />
                                View Activity Log
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-orange-600">
                                <UserX className="mr-2 h-4 w-4" />
                                Suspend User
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    )
                  })
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      </div>
    </AdminLayout>
  )
}

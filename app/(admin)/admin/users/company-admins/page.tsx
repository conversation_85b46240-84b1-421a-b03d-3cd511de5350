'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Search, 
  MoreHorizontal,
  UserCheck,
  UserX,
  Mail,
  Eye,
  ArrowLeft,
  RefreshCw,
  Download,
  Building2,
  Briefcase,
  Users,
  Calendar,
  Shield
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface CompanyAdmin {
  _id: string
  email: string
  profile: {
    firstName: string
    lastName: string
    avatar?: string
    phone?: string
  }
  company: {
    _id: string
    name: string
    slug: string
    isVerified: boolean
    logo?: string
  }
  isActive: boolean
  isEmailVerified: boolean
  lastLogin?: Date
  createdAt: Date
  jobsPosted?: number
  permissions: string[]
}

export default function CompanyAdminsPage() {
  const router = useRouter()
  const [companyAdmins, setCompanyAdmins] = useState<CompanyAdmin[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive' | 'verified' | 'unverified'>('all')

  useEffect(() => {
    fetchCompanyAdmins()
  }, [])

  const fetchCompanyAdmins = async () => {
    try {
      setIsLoading(true)
      // Mock data for now
      const mockCompanyAdmins: CompanyAdmin[] = [
        {
          _id: '1',
          email: '<EMAIL>',
          profile: {
            firstName: 'Alice',
            lastName: 'Johnson',
            phone: '******-0123'
          },
          company: {
            _id: 'comp1',
            name: 'TechCorp Inc.',
            slug: 'techcorp',
            isVerified: true,
            logo: '/company-logos/techcorp.png'
          },
          isActive: true,
          isEmailVerified: true,
          lastLogin: new Date(),
          createdAt: new Date('2024-01-10'),
          jobsPosted: 15,
          permissions: ['post_jobs', 'manage_applications', 'view_analytics']
        },
        {
          _id: '2',
          email: '<EMAIL>',
          profile: {
            firstName: 'Bob',
            lastName: 'Smith',
            phone: '******-0456'
          },
          company: {
            _id: 'comp2',
            name: 'StartupXYZ',
            slug: 'startupxyz',
            isVerified: false,
            logo: '/company-logos/startupxyz.png'
          },
          isActive: true,
          isEmailVerified: true,
          lastLogin: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          createdAt: new Date('2024-02-15'),
          jobsPosted: 3,
          permissions: ['post_jobs', 'manage_applications']
        }
      ]
      setCompanyAdmins(mockCompanyAdmins)
    } catch (error) {
      console.error('Failed to fetch company admins:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredCompanyAdmins = companyAdmins.filter(admin => {
    const matchesSearch = 
      admin.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      `${admin.profile.firstName} ${admin.profile.lastName}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
      admin.company.name.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesFilter = 
      filterStatus === 'all' ||
      (filterStatus === 'active' && admin.isActive) ||
      (filterStatus === 'inactive' && !admin.isActive) ||
      (filterStatus === 'verified' && admin.company.isVerified) ||
      (filterStatus === 'unverified' && !admin.company.isVerified)
    
    return matchesSearch && matchesFilter
  })

  const handleAdminAction = async (adminId: string, action: 'activate' | 'deactivate' | 'reset_password') => {
    try {
      console.log(`${action} admin:`, adminId)
      // Implement admin action API call
      await fetchCompanyAdmins() // Refresh data
    } catch (error) {
      console.error(`Failed to ${action} admin:`, error)
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => router.push('/admin/users')}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Users
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center">
              <Shield className="w-8 h-8 mr-3 text-purple-600" />
              Company Admins
            </h1>
            <p className="text-muted-foreground">
              Manage company administrator accounts and permissions
            </p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={fetchCompanyAdmins} disabled={isLoading}>
            <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Admins</p>
                <p className="text-2xl font-bold">{companyAdmins.length}</p>
              </div>
              <Shield className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Admins</p>
                <p className="text-2xl font-bold">{companyAdmins.filter(a => a.isActive).length}</p>
              </div>
              <UserCheck className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Verified Companies</p>
                <p className="text-2xl font-bold">{companyAdmins.filter(a => a.company.isVerified).length}</p>
              </div>
              <Building2 className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Jobs Posted</p>
                <p className="text-2xl font-bold">{companyAdmins.reduce((sum, a) => sum + (a.jobsPosted || 0), 0)}</p>
              </div>
              <Briefcase className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Company Admins Management</CardTitle>
          <CardDescription>Search, filter, and manage company administrator accounts</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search by name, email, or company..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant={filterStatus === 'all' ? 'default' : 'outline'}
                onClick={() => setFilterStatus('all')}
                size="sm"
              >
                All
              </Button>
              <Button
                variant={filterStatus === 'active' ? 'default' : 'outline'}
                onClick={() => setFilterStatus('active')}
                size="sm"
              >
                Active
              </Button>
              <Button
                variant={filterStatus === 'verified' ? 'default' : 'outline'}
                onClick={() => setFilterStatus('verified')}
                size="sm"
              >
                Verified
              </Button>
              <Button
                variant={filterStatus === 'unverified' ? 'default' : 'outline'}
                onClick={() => setFilterStatus('unverified')}
                size="sm"
              >
                Unverified
              </Button>
            </div>
          </div>

          {/* Admins Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Admin</TableHead>
                  <TableHead>Company</TableHead>
                  <TableHead>Jobs Posted</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Company Status</TableHead>
                  <TableHead>Last Login</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
                      Loading company admins...
                    </TableCell>
                  </TableRow>
                ) : filteredCompanyAdmins.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      No company admins found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredCompanyAdmins.map((admin) => (
                    <TableRow key={admin._id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar className="w-10 h-10">
                            <AvatarImage src={admin.profile.avatar} />
                            <AvatarFallback>
                              {admin.profile.firstName[0]}{admin.profile.lastName[0]}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{admin.profile.firstName} {admin.profile.lastName}</p>
                            <p className="text-sm text-muted-foreground">{admin.email}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Avatar className="w-8 h-8">
                            <AvatarImage src={admin.company.logo} />
                            <AvatarFallback>
                              <Building2 className="w-4 h-4" />
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{admin.company.name}</p>
                            <p className="text-sm text-muted-foreground">@{admin.company.slug}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">
                          {admin.jobsPosted || 0} jobs
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={admin.isActive ? 'default' : 'secondary'}>
                          {admin.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={admin.company.isVerified ? 'default' : 'destructive'}>
                          {admin.company.isVerified ? 'Verified' : 'Unverified'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Calendar className="w-4 h-4 mr-1" />
                          {admin.lastLogin ? 
                            new Date(admin.lastLogin).toLocaleDateString() : 
                            'Never'
                          }
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => router.push(`/admin/users/${admin._id}`)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Profile
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => router.push(`/admin/companies/${admin.company._id}`)}>
                              <Building2 className="mr-2 h-4 w-4" />
                              View Company
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Mail className="mr-2 h-4 w-4" />
                              Send Message
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {admin.isActive ? (
                              <DropdownMenuItem 
                                onClick={() => handleAdminAction(admin._id, 'deactivate')}
                                className="text-orange-600"
                              >
                                <UserX className="mr-2 h-4 w-4" />
                                Deactivate
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem 
                                onClick={() => handleAdminAction(admin._id, 'activate')}
                                className="text-green-600"
                              >
                                <UserCheck className="mr-2 h-4 w-4" />
                                Activate
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem 
                              onClick={() => handleAdminAction(admin._id, 'reset_password')}
                              className="text-blue-600"
                            >
                              <Shield className="mr-2 h-4 w-4" />
                              Reset Password
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      </div>
    </AdminLayout>
  )
}

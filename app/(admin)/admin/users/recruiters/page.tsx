'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Search, 
  MoreHorizontal,
  UserCheck,
  UserX,
  Mail,
  Eye,
  ArrowLeft,
  RefreshCw,
  Download,
  Users,
  Briefcase,
  Building2,
  Calendar,
  Target
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface Recruiter {
  _id: string
  email: string
  profile: {
    firstName: string
    lastName: string
    avatar?: string
    phone?: string
  }
  company: {
    _id: string
    name: string
    slug: string
    isVerified: boolean
    logo?: string
  }
  isActive: boolean
  isEmailVerified: boolean
  lastLogin?: Date
  createdAt: Date
  jobsManaged?: number
  candidatesHired?: number
  specializations: string[]
}

export default function RecruitersPage() {
  const router = useRouter()
  const [recruiters, setRecruiters] = useState<Recruiter[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive' | 'verified' | 'unverified'>('all')

  useEffect(() => {
    fetchRecruiters()
  }, [])

  const fetchRecruiters = async () => {
    try {
      setIsLoading(true)
      // Mock data for now
      const mockRecruiters: Recruiter[] = [
        {
          _id: '1',
          email: '<EMAIL>',
          profile: {
            firstName: 'Sarah',
            lastName: 'Wilson',
            phone: '******-0789'
          },
          company: {
            _id: 'comp1',
            name: 'TechCorp Inc.',
            slug: 'techcorp',
            isVerified: true,
            logo: '/company-logos/techcorp.png'
          },
          isActive: true,
          isEmailVerified: true,
          lastLogin: new Date(),
          createdAt: new Date('2024-01-15'),
          jobsManaged: 25,
          candidatesHired: 12,
          specializations: ['Software Engineering', 'Data Science', 'DevOps']
        },
        {
          _id: '2',
          email: '<EMAIL>',
          profile: {
            firstName: 'Mike',
            lastName: 'Chen',
            phone: '******-0456'
          },
          company: {
            _id: 'comp2',
            name: 'StartupXYZ',
            slug: 'startupxyz',
            isVerified: false,
            logo: '/company-logos/startupxyz.png'
          },
          isActive: true,
          isEmailVerified: true,
          lastLogin: new Date(Date.now() - 24 * 60 * 60 * 1000),
          createdAt: new Date('2024-02-01'),
          jobsManaged: 8,
          candidatesHired: 3,
          specializations: ['Product Management', 'Marketing', 'Sales']
        }
      ]
      setRecruiters(mockRecruiters)
    } catch (error) {
      console.error('Failed to fetch recruiters:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredRecruiters = recruiters.filter(recruiter => {
    const matchesSearch = 
      recruiter.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      `${recruiter.profile.firstName} ${recruiter.profile.lastName}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
      recruiter.company.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      recruiter.specializations.some(spec => spec.toLowerCase().includes(searchQuery.toLowerCase()))
    
    const matchesFilter = 
      filterStatus === 'all' ||
      (filterStatus === 'active' && recruiter.isActive) ||
      (filterStatus === 'inactive' && !recruiter.isActive) ||
      (filterStatus === 'verified' && recruiter.company.isVerified) ||
      (filterStatus === 'unverified' && !recruiter.company.isVerified)
    
    return matchesSearch && matchesFilter
  })

  const handleRecruiterAction = async (recruiterId: string, action: 'activate' | 'deactivate' | 'reset_password') => {
    try {
      console.log(`${action} recruiter:`, recruiterId)
      // Implement recruiter action API call
      await fetchRecruiters() // Refresh data
    } catch (error) {
      console.error(`Failed to ${action} recruiter:`, error)
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => router.push('/admin/users')}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Users
            </Button>
            <div>
              <h1 className="text-3xl font-bold flex items-center">
                <Target className="w-8 h-8 mr-3 text-green-600" />
                Recruiters
              </h1>
              <p className="text-muted-foreground">
                Manage recruiter accounts and hiring performance
              </p>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={fetchRecruiters} disabled={isLoading}>
              <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
              Refresh
            </Button>
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Recruiters</p>
                  <p className="text-2xl font-bold">{recruiters.length}</p>
                </div>
                <Target className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Active Recruiters</p>
                  <p className="text-2xl font-bold">{recruiters.filter(r => r.isActive).length}</p>
                </div>
                <UserCheck className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Jobs Managed</p>
                  <p className="text-2xl font-bold">{recruiters.reduce((sum, r) => sum + (r.jobsManaged || 0), 0)}</p>
                </div>
                <Briefcase className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Candidates Hired</p>
                  <p className="text-2xl font-bold">{recruiters.reduce((sum, r) => sum + (r.candidatesHired || 0), 0)}</p>
                </div>
                <Users className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle>Recruiters Management</CardTitle>
            <CardDescription>Search, filter, and manage recruiter accounts</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search by name, email, company, or specialization..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={filterStatus === 'all' ? 'default' : 'outline'}
                  onClick={() => setFilterStatus('all')}
                  size="sm"
                >
                  All
                </Button>
                <Button
                  variant={filterStatus === 'active' ? 'default' : 'outline'}
                  onClick={() => setFilterStatus('active')}
                  size="sm"
                >
                  Active
                </Button>
                <Button
                  variant={filterStatus === 'verified' ? 'default' : 'outline'}
                  onClick={() => setFilterStatus('verified')}
                  size="sm"
                >
                  Verified Companies
                </Button>
              </div>
            </div>

            {/* Recruiters Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Recruiter</TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Specializations</TableHead>
                    <TableHead>Performance</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Login</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
                        Loading recruiters...
                      </TableCell>
                    </TableRow>
                  ) : filteredRecruiters.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        No recruiters found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredRecruiters.map((recruiter) => (
                      <TableRow key={recruiter._id}>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <Avatar className="w-10 h-10">
                              <AvatarImage src={recruiter.profile.avatar} />
                              <AvatarFallback>
                                {recruiter.profile.firstName[0]}{recruiter.profile.lastName[0]}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{recruiter.profile.firstName} {recruiter.profile.lastName}</p>
                              <p className="text-sm text-muted-foreground">{recruiter.email}</p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Avatar className="w-8 h-8">
                              <AvatarImage src={recruiter.company.logo} />
                              <AvatarFallback>
                                <Building2 className="w-4 h-4" />
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{recruiter.company.name}</p>
                              <Badge variant={recruiter.company.isVerified ? 'default' : 'secondary'} className="text-xs">
                                {recruiter.company.isVerified ? 'Verified' : 'Unverified'}
                              </Badge>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {recruiter.specializations.slice(0, 2).map((spec, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {spec}
                              </Badge>
                            ))}
                            {recruiter.specializations.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{recruiter.specializations.length - 2}
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <p className="font-medium">{recruiter.jobsManaged || 0} jobs</p>
                            <p className="text-muted-foreground">{recruiter.candidatesHired || 0} hires</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={recruiter.isActive ? 'default' : 'secondary'}>
                            {recruiter.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Calendar className="w-4 h-4 mr-1" />
                            {recruiter.lastLogin ? 
                              new Date(recruiter.lastLogin).toLocaleDateString() : 
                              'Never'
                            }
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => router.push(`/admin/users/${recruiter._id}`)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Profile
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => router.push(`/admin/companies/${recruiter.company._id}`)}>
                                <Building2 className="mr-2 h-4 w-4" />
                                View Company
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Mail className="mr-2 h-4 w-4" />
                                Send Message
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {recruiter.isActive ? (
                                <DropdownMenuItem 
                                  onClick={() => handleRecruiterAction(recruiter._id, 'deactivate')}
                                  className="text-orange-600"
                                >
                                  <UserX className="mr-2 h-4 w-4" />
                                  Deactivate
                                </DropdownMenuItem>
                              ) : (
                                <DropdownMenuItem 
                                  onClick={() => handleRecruiterAction(recruiter._id, 'activate')}
                                  className="text-green-600"
                                >
                                  <UserCheck className="mr-2 h-4 w-4" />
                                  Activate
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}

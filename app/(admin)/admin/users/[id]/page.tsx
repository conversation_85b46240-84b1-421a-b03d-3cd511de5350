'use client'

import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { AdminPageHeader } from '@/components/admin/admin-page-header'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  UserCheck, 
  UserX, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  Shield,
  Building2,
  Briefcase,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { ErrorState, LoadingState } from '@/components/ui/empty-state'

interface User {
  _id: string
  email: string
  role: string
  profile: {
    firstName: string
    lastName: string
    avatar?: string
    phone?: string
    location?: {
      city?: string
      state?: string
      country?: string
    }
  }
  isActive: boolean
  isEmailVerified: boolean
  lastLoginAt?: string
  createdAt: string
  updatedAt: string
  stats?: {
    applicationsCount: number
    jobsPosted: number
    companiesManaged: number
  }
}

export default function UserDetailsPage() {
  const router = useRouter()
  const params = useParams()
  const { token } = useAuthStore()
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isUpdating, setIsUpdating] = useState(false)

  useEffect(() => {
    if (params.id) {
      fetchUser()
    }
  }, [params.id])

  const fetchUser = async () => {
    try {
      setIsLoading(true)
      setError(null)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/v1/admin/users/${params.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch user details')
      }

      const data = await response.json()
      setUser(data.data.user)
    } catch (error) {
      console.error('Error fetching user:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch user details')
    } finally {
      setIsLoading(false)
    }
  }

  const handleUserAction = async (action: string, data?: any) => {
    try {
      setIsUpdating(true)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/v1/admin/users/${params.id}/${action}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data || {})
      })

      if (!response.ok) {
        throw new Error(`Failed to ${action} user`)
      }

      // Refresh user data
      await fetchUser()
    } catch (error) {
      console.error(`Failed to ${action} user:`, error)
      setError(error instanceof Error ? error.message : `Failed to ${action} user`)
    } finally {
      setIsUpdating(false)
    }
  }

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      return
    }

    try {
      setIsUpdating(true)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/v1/admin/users/${params.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to delete user')
      }

      router.push('/admin/users')
    } catch (error) {
      console.error('Failed to delete user:', error)
      setError(error instanceof Error ? error.message : 'Failed to delete user')
    } finally {
      setIsUpdating(false)
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800'
      case 'company_admin': return 'bg-blue-100 text-blue-800'
      case 'recruiter': return 'bg-green-100 text-green-800'
      case 'job_seeker': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin': return <Shield className="w-4 h-4" />
      case 'company_admin': return <Building2 className="w-4 h-4" />
      case 'recruiter': return <Briefcase className="w-4 h-4" />
      case 'job_seeker': return <UserCheck className="w-4 h-4" />
      default: return <UserCheck className="w-4 h-4" />
    }
  }

  const formatRoleName = (role: string) => {
    return role.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="p-6">
          <LoadingState message="Loading user details..." />
        </div>
      </AdminLayout>
    )
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="p-6">
          <ErrorState
            title="Failed to load user details"
            description={error}
            onRetry={fetchUser}
          />
        </div>
      </AdminLayout>
    )
  }

  if (!user) {
    return (
      <AdminLayout>
        <div className="p-6">
          <ErrorState
            title="User not found"
            description="The requested user could not be found."
          />
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        <AdminPageHeader
          title={`${user.profile.firstName} ${user.profile.lastName}`}
          description={`User profile and account management`}
          breadcrumbItems={[
            { label: 'Admin', href: '/admin' },
            { label: 'Users', href: '/admin/users' },
            { label: `${user.profile.firstName} ${user.profile.lastName}` }
          ]}
          actions={
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                onClick={() => router.push('/admin/users')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Users
              </Button>
              <Button 
                variant="outline"
                onClick={() => router.push(`/admin/users/${user._id}/edit`)}
                disabled={isUpdating}
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
              <Button 
                variant="destructive"
                onClick={handleDelete}
                disabled={isUpdating}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete
              </Button>
            </div>
          }
        />

        {error && (
          <Card className="border-destructive">
            <CardContent className="p-4">
              <p className="text-destructive text-sm">{error}</p>
            </CardContent>
          </Card>
        )}

        {/* User Header */}
        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-4">
                <Avatar className="w-20 h-20">
                  <AvatarImage src={user.profile.avatar} />
                  <AvatarFallback className="text-lg">
                    {user.profile.firstName.charAt(0)}{user.profile.lastName.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h1 className="text-2xl font-bold">
                    {user.profile.firstName} {user.profile.lastName}
                  </h1>
                  <div className="flex items-center space-x-2 mt-1">
                    <Mail className="w-4 h-4 text-muted-foreground" />
                    <span className="text-muted-foreground">{user.email}</span>
                  </div>
                  {user.profile.phone && (
                    <div className="flex items-center space-x-2 mt-1">
                      <Phone className="w-4 h-4 text-muted-foreground" />
                      <span className="text-muted-foreground">{user.profile.phone}</span>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex flex-col items-end space-y-2">
                <Badge className={cn('flex items-center space-x-1', getRoleColor(user.role))}>
                  {getRoleIcon(user.role)}
                  <span>{formatRoleName(user.role)}</span>
                </Badge>
                <div className="flex space-x-2">
                  <Badge variant={user.isActive ? 'default' : 'secondary'}>
                    {user.isActive ? <CheckCircle className="w-3 h-3 mr-1" /> : <XCircle className="w-3 h-3 mr-1" />}
                    {user.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                  <Badge variant={user.isEmailVerified ? 'default' : 'outline'}>
                    {user.isEmailVerified ? <CheckCircle className="w-3 h-3 mr-1" /> : <Clock className="w-3 h-3 mr-1" />}
                    {user.isEmailVerified ? 'Verified' : 'Unverified'}
                  </Badge>
                </div>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleUserAction(user.isActive ? 'deactivate' : 'activate')}
                disabled={isUpdating}
              >
                {user.isActive ? <UserX className="w-4 h-4 mr-2" /> : <UserCheck className="w-4 h-4 mr-2" />}
                {user.isActive ? 'Deactivate' : 'Activate'}
              </Button>
              
              {!user.isEmailVerified && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleUserAction('verify-email')}
                  disabled={isUpdating}
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Verify Email
                </Button>
              )}
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleUserAction('reset-password')}
                disabled={isUpdating}
              >
                <Shield className="w-4 h-4 mr-2" />
                Reset Password
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* User Details Tabs */}
        <Tabs defaultValue="profile" className="space-y-4">
          <TabsList>
            <TabsTrigger value="profile">Profile</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Personal Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Full Name</Label>
                    <p>{user.profile.firstName} {user.profile.lastName}</p>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Email</Label>
                    <p>{user.email}</p>
                  </div>
                  
                  {user.profile.phone && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">Phone</Label>
                      <p>{user.profile.phone}</p>
                    </div>
                  )}
                  
                  {user.profile.location && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">Location</Label>
                      <div className="flex items-center space-x-1">
                        <MapPin className="w-4 h-4 text-muted-foreground" />
                        <span>
                          {[
                            user.profile.location.city,
                            user.profile.location.state,
                            user.profile.location.country
                          ].filter(Boolean).join(', ')}
                        </span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Account Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Account Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Role</Label>
                    <div className="flex items-center space-x-2 mt-1">
                      {getRoleIcon(user.role)}
                      <span>{formatRoleName(user.role)}</span>
                    </div>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Account Status</Label>
                    <div className="flex space-x-2 mt-1">
                      <Badge variant={user.isActive ? 'default' : 'secondary'}>
                        {user.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                      <Badge variant={user.isEmailVerified ? 'default' : 'outline'}>
                        {user.isEmailVerified ? 'Email Verified' : 'Email Unverified'}
                      </Badge>
                    </div>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Member Since</Label>
                    <div className="flex items-center space-x-1 mt-1">
                      <Calendar className="w-4 h-4 text-muted-foreground" />
                      <span>{new Date(user.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                  
                  {user.lastLoginAt && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">Last Login</Label>
                      <p>{new Date(user.lastLoginAt).toLocaleString()}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Statistics */}
            {user.stats && (
              <Card>
                <CardHeader>
                  <CardTitle>User Statistics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-muted/50 rounded-lg">
                      <p className="text-2xl font-bold">{user.stats.applicationsCount}</p>
                      <p className="text-sm text-muted-foreground">Applications</p>
                    </div>
                    <div className="text-center p-4 bg-muted/50 rounded-lg">
                      <p className="text-2xl font-bold">{user.stats.jobsPosted}</p>
                      <p className="text-sm text-muted-foreground">Jobs Posted</p>
                    </div>
                    <div className="text-center p-4 bg-muted/50 rounded-lg">
                      <p className="text-2xl font-bold">{user.stats.companiesManaged}</p>
                      <p className="text-sm text-muted-foreground">Companies Managed</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="activity">
            <Card>
              <CardHeader>
                <CardTitle>User Activity</CardTitle>
                <CardDescription>
                  Recent activity and engagement history
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Activity tracking interface will be implemented here.
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security">
            <Card>
              <CardHeader>
                <CardTitle>Security Settings</CardTitle>
                <CardDescription>
                  Manage user security and authentication settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Security management interface will be implemented here.
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  )
}

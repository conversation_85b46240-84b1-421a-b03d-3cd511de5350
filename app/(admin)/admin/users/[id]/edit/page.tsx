'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { AdminLayout } from '@/components/admin/admin-layout'
import { AdminPageHeader } from '@/components/admin/admin-page-header'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Separator } from '@/components/ui/separator'
import { ArrowLeft, Save } from 'lucide-react'
import { cn } from '@/lib/utils'
import { ErrorState, LoadingState } from '@/components/ui/empty-state'

interface UserFormData {
  email: string
  role: string
  profile: {
    firstName: string
    lastName: string
    phone: string
    location: {
      city: string
      state: string
      country: string
    }
  }
  isActive: boolean
  isEmailVerified: boolean
}

export default function EditUserPage() {
  const router = useRouter()
  const params = useParams()
  const { token } = useAuthStore()
  const [formData, setFormData] = useState<UserFormData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (params.id) {
      fetchUser()
    }
  }, [params.id])

  const fetchUser = async () => {
    try {
      setIsLoading(true)
      setError(null)

      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/v1/admin/users/${params.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch user details')
      }

      const data = await response.json()
      const user = data.data.user

      // Transform user data to form format
      setFormData({
        email: user.email,
        role: user.role,
        profile: {
          firstName: user.profile.firstName,
          lastName: user.profile.lastName,
          phone: user.profile.phone || '',
          location: {
            city: user.profile.location?.city || '',
            state: user.profile.location?.state || '',
            country: user.profile.location?.country || 'United States'
          }
        },
        isActive: user.isActive,
        isEmailVerified: user.isEmailVerified
      })
    } catch (error) {
      console.error('Error fetching user:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch user details')
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    if (!formData) return
    setFormData(prev => prev ? ({
      ...prev,
      [field]: value
    }) : null)
  }

  const handleNestedInputChange = (parent: string, field: string, value: any) => {
    if (!formData) return
    setFormData(prev => prev ? ({
      ...prev,
      [parent]: {
        ...prev[parent as keyof UserFormData] as any,
        [field]: value
      }
    }) : null)
  }

  const handleLocationChange = (field: string, value: string) => {
    if (!formData) return
    setFormData(prev => prev ? ({
      ...prev,
      profile: {
        ...prev.profile,
        location: {
          ...prev.profile.location,
          [field]: value
        }
      }
    }) : null)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData) return

    setIsSubmitting(true)
    setError(null)

    try {
      if (!token) {
        throw new Error('No authentication token found')
      }

      // Validate required fields
      if (!formData.email || !formData.profile.firstName || !formData.profile.lastName) {
        throw new Error('Please fill in all required fields')
      }

      const userData = {
        email: formData.email,
        role: formData.role,
        profile: {
          firstName: formData.profile.firstName,
          lastName: formData.profile.lastName,
          phone: formData.profile.phone,
          location: formData.profile.location
        },
        isActive: formData.isActive,
        isEmailVerified: formData.isEmailVerified
      }

      const response = await fetch(`/api/v1/admin/users/${params.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update user')
      }

      // Redirect to user details
      router.push(`/admin/users/${params.id}`)
      
    } catch (error) {
      console.error('Error updating user:', error)
      setError(error instanceof Error ? error.message : 'Failed to update user')
    } finally {
      setIsSubmitting(false)
    }
  }

  const roles = [
    { value: 'job_seeker', label: 'Job Seeker' },
    { value: 'company_admin', label: 'Company Admin' },
    { value: 'recruiter', label: 'Recruiter' },
    { value: 'admin', label: 'Admin' }
  ]

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="p-6">
          <LoadingState message="Loading user details..." />
        </div>
      </AdminLayout>
    )
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="p-6">
          <ErrorState
            title="Failed to load user details"
            description={error}
            onRetry={fetchUser}
          />
        </div>
      </AdminLayout>
    )
  }

  if (!formData) {
    return (
      <AdminLayout>
        <div className="p-6">
          <ErrorState
            title="User not found"
            description="The requested user could not be found."
          />
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6">
        <AdminPageHeader
          title="Edit User"
          description="Update user account and profile information"
          breadcrumbItems={[
            { label: 'Admin', href: '/admin' },
            { label: 'Users', href: '/admin/users' },
            { label: `${formData.profile.firstName} ${formData.profile.lastName}`, href: `/admin/users/${params.id}` },
            { label: 'Edit' }
          ]}
          actions={
            <Button 
              variant="outline" 
              onClick={() => router.push(`/admin/users/${params.id}`)}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to User
            </Button>
          }
        />

        {error && (
          <Card className="border-destructive">
            <CardContent className="p-4">
              <p className="text-destructive text-sm">{error}</p>
            </CardContent>
          </Card>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Account Information */}
          <Card>
            <CardHeader>
              <CardTitle>Account Information</CardTitle>
              <CardDescription>
                Update the user's account credentials and role
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role">Role *</Label>
                  <Select value={formData.role} onValueChange={(value) => handleInputChange('role', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select user role" />
                    </SelectTrigger>
                    <SelectContent>
                      {roles.map((role) => (
                        <SelectItem key={role.value} value={role.value}>
                          {role.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Personal Information */}
          <Card>
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
              <CardDescription>
                Update the user's personal details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name *</Label>
                  <Input
                    id="firstName"
                    value={formData.profile.firstName}
                    onChange={(e) => handleNestedInputChange('profile', 'firstName', e.target.value)}
                    placeholder="John"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name *</Label>
                  <Input
                    id="lastName"
                    value={formData.profile.lastName}
                    onChange={(e) => handleNestedInputChange('profile', 'lastName', e.target.value)}
                    placeholder="Doe"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.profile.phone}
                  onChange={(e) => handleNestedInputChange('profile', 'phone', e.target.value)}
                  placeholder="+****************"
                />
              </div>

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    value={formData.profile.location.city}
                    onChange={(e) => handleLocationChange('city', e.target.value)}
                    placeholder="San Francisco"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="state">State</Label>
                  <Input
                    id="state"
                    value={formData.profile.location.state}
                    onChange={(e) => handleLocationChange('state', e.target.value)}
                    placeholder="California"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="country">Country</Label>
                  <Input
                    id="country"
                    value={formData.profile.location.country}
                    onChange={(e) => handleLocationChange('country', e.target.value)}
                    placeholder="United States"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Account Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Account Settings</CardTitle>
              <CardDescription>
                Configure account status and permissions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex space-x-6">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => handleInputChange('isActive', checked)}
                  />
                  <Label htmlFor="isActive" className="text-sm font-normal">
                    Account is active
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isEmailVerified"
                    checked={formData.isEmailVerified}
                    onCheckedChange={(checked) => handleInputChange('isEmailVerified', checked)}
                  />
                  <Label htmlFor="isEmailVerified" className="text-sm font-normal">
                    Email is verified
                  </Label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push(`/admin/users/${params.id}`)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              <Save className="w-4 h-4 mr-2" />
              {isSubmitting ? 'Updating...' : 'Update User'}
            </Button>
          </div>
        </form>
      </div>
    </AdminLayout>
  )
}

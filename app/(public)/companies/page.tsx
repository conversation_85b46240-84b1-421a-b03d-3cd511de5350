// app/(public)/companies/page.tsx
'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON>, <PERSON>et<PERSON>ontent, SheetHeader, <PERSON>etTitle, <PERSON>etTrigger } from '@/components/ui/sheet'
import { CompanyFilters } from '@/components/companies/company-filters'
import { CompanyCard } from '@/components/companies/company-card'
import { CompanyDetailModal } from '@/components/companies/company-detail-modal'
import { TalentPagination, TalentSorting, TalentResultsSummary } from '@/components/talent/talent-pagination'
import { companies } from '@/lib/company-data'
import { useCompaniesFrontendStore, CompanyFrontend } from '@/stores/companies-frontend.store'

// Extended company interface for modal
interface ExtendedCompany {
  id?: string | number
  _id?: string
  name?: string
  slug?: string
  description?: string
  logo?: string
  industry?: string | string[]
  industries?: string[]
  location?: unknown
  size?: string
  founded?: number | string
  followers?: number
  openJobs?: number
  totalApplications?: number
  views?: number
  profileViews?: number
  rating?: number
  [key: string]: unknown
}

// Legacy interface for backward compatibility (matches CompanyCard)
interface LegacyCompany {
  id: number
  name: string
  industry: string
  location: string
  logo: string
  rating: number
  size: string
  founded: number
  description: string
  specialties: string[]
  openJobs: number
  followers: number
  website: string
  benefits: string[]
  culture: string
  verified: boolean
}

// Union type for companies that can be either legacy or new format
type AnyCompany = CompanyFrontend | LegacyCompany
import {
  Search,
  Filter,
  Grid3X3,
  List,
  Building,
  TrendingUp,
  Award,
  Target
} from 'lucide-react'

export default function CompaniesPage() {
  // Store state
  const {
    companies: storeCompanies,
    searchResult,
    activeFilters,
    viewMode: storeViewMode,
    isSearching,
    error,
    searchError,
    followedCompanies,


    searchCompanies,
    setViewMode,
    followCompany,
    unfollowCompany,
    goToPage
  } = useCompaniesFrontendStore()

  // Local UI state
  const [searchQuery, setSearchQuery] = useState('')
  const [showMobileFilters, setShowMobileFilters] = useState(false)
  const [showDesktopFilters, setShowDesktopFilters] = useState(true)
  const [sortBy, setSortBy] = useState('relevance')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(12)
  const [selectedCompany, setSelectedCompany] = useState<AnyCompany | null>(null)

  // Legacy filter states for backward compatibility
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>([])
  const [companySizeRange, setCompanySizeRange] = useState([1, 10000])
  const [foundedRange, setFoundedRange] = useState([1900, 2024])

  // ALWAYS prioritize backend data - only use static data if there's an error
  const usingBackendData = !error && !searchError
  const displayCompanies = usingBackendData ? storeCompanies : companies
  const totalCompanies = searchResult?.pagination.total || displayCompanies.length
  const totalPages = searchResult?.pagination.totalPages || Math.ceil(displayCompanies.length / itemsPerPage)



  // For static data pagination (only when backend data not available)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentCompanies = usingBackendData ? storeCompanies : companies.slice(startIndex, endIndex)

  // Calculate active filters for UI
  const activeFilterCount = selectedIndustries.length +
    (companySizeRange[0] > 1 || companySizeRange[1] < 10000 ? 1 : 0) +
    (foundedRange[0] > 1900 || foundedRange[1] < 2024 ? 1 : 0) +
    Object.keys(activeFilters).length

  // Initialize backend data on mount (prioritize over static data)
  useEffect(() => {
    // Always try to load backend data on mount
    searchCompanies({
      search: '',
      page: 1,
      limit: itemsPerPage,
      sortBy: 'relevance',
      sortOrder: 'desc'
    })
  }, [searchCompanies, itemsPerPage])

  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setSortBy(newSortBy)
    setSortOrder(newSortOrder)
    setCurrentPage(1)

    // Update store search with new sorting (prioritize backend)
    if (usingBackendData) {
      searchCompanies({
        sortBy: newSortBy as 'relevance' | 'name' | 'founded' | 'size' | 'jobs' | 'rating',
        sortOrder: newSortOrder,
        page: 1
      })
    }
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)

    // Update store if using backend data
    if (usingBackendData) {
      goToPage(page)
    }

    document.querySelector('#companies-results')?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage)
    setCurrentPage(1)

    // Update store search with new limit (prioritize backend)
    if (usingBackendData) {
      searchCompanies({
        limit: newItemsPerPage,
        page: 1
      })
    }
  }

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1)

    // Update store search (prioritize backend)
    if (usingBackendData) {
      searchCompanies({
        search: query,
        page: 1
      })
    }
  }

  const handleFollow = async (company: AnyCompany) => {
    if (usingBackendData) {
      const companyId = getCompanyId(company)
      const isCurrentlyFollowing = followedCompanies.has(companyId)

      if (isCurrentlyFollowing) {
        await unfollowCompany(companyId)
      } else {
        await followCompany(companyId)
      }
    } else {
      console.log('Follow company (static mode):', company.name)
    }
  }

  // Use store view mode if available
  const [viewMode, setLocalViewMode] = useState<'grid' | 'list'>('list')

  const handleViewModeChange = (mode: 'grid' | 'list') => {
    if (usingBackendData) {
      setViewMode(mode)
    } else {
      setLocalViewMode(mode)
    }
  }

  const displayViewMode = usingBackendData ? storeViewMode : viewMode

  // Type-safe helper functions
  const getCompanyId = (company: AnyCompany): string => {
    return 'id' in company && typeof company.id === 'string' ? company.id : company.id.toString()
  }

  const getCompanySlug = (company: AnyCompany): string => {
    return 'slug' in company ? company.slug : company.id.toString()
  }

  const isNewFormatCompany = (company: AnyCompany): company is CompanyFrontend => {
    return 'slug' in company && 'primaryLocation' in company
  }

  // Convert AnyCompany to full Company interface for modal
  const convertToFullCompany = (company: AnyCompany): ExtendedCompany => {
    if (isNewFormatCompany(company)) {
      // Convert CompanyFrontend to Company
      return {
        id: parseInt(company.id),
        name: company.name,
        industry: company.industries[0] || 'Technology',
        location: company.primaryLocation.displayName,
        logo: company.logo || '',
        rating: company.rating,
        size: company.size,
        founded: company.founded || new Date().getFullYear(),
        description: company.description,
        specialties: company.specialties,
        openJobs: company.stats.activeJobs,
        followers: company.stats.followerCount,
        website: company.website || '',
        benefits: company.culture.benefits || [],
        culture: company.culture.workEnvironment || '',
        verified: company.verification.isVerified,
        employees: company.size,
        revenue: '',
        funding: company.funding?.stage || '',
        headquarters: company.primaryLocation.displayName,
        offices: company.allLocations.map(loc => loc.displayName),
        techStack: company.technologies || [],
        awards: [],
        socialImpact: ''
      }
    } else {
      // Already a LegacyCompany, convert to full Company
      const legacyCompany = company as LegacyCompany
      return {
        ...legacyCompany,
        employees: legacyCompany.size,
        revenue: '',
        funding: '',
        headquarters: legacyCompany.location,
        offices: [legacyCompany.location],
        techStack: [],
        awards: [],
        socialImpact: ''
      }
    }
  }

  // Handler for viewing company profile
  const handleViewProfile = (company: AnyCompany) => {
    setSelectedCompany(company)
  }



  return (
    <div className="pt-16">
      {/* Hero Section - Mobile Optimized */}
      <section className="relative py-12 md:py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/30 via-background to-primary/10" />
        {/* Hide pattern on mobile for better performance */}
        <div className="absolute inset-0 opacity-20 bg-[radial-gradient(circle_at_20%_30%,_hsl(var(--primary))_3px,_transparent_3px),_radial-gradient(circle_at_80%_70%,_hsl(var(--primary))_2px,_transparent_2px)] bg-[length:80px_80px,_120px_120px] hidden md:block" />

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-2xl sm:text-3xl md:text-4xl lg:text-6xl font-bold mb-4 md:mb-6 bg-gradient-to-r from-foreground via-primary to-foreground bg-clip-text text-transparent"
            >
              Discover Amazing Companies
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-base sm:text-lg md:text-xl text-muted-foreground mb-6 md:mb-8 max-w-2xl mx-auto px-4 md:px-0"
            >
              <span className="hidden md:inline">
                Explore innovative companies across all industries. Find your next career opportunity with organizations that match your values and goals.
              </span>
              <span className="md:hidden">
                Find companies that match your career goals.
              </span>
            </motion.p>

            {/* Stats - Real data from store or fallback */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex flex-wrap items-center justify-center gap-4 md:gap-6 text-xs md:text-sm text-muted-foreground"
            >
              <div className="flex items-center space-x-1.5 md:space-x-2">
                <TrendingUp className="w-3 h-3 md:w-4 md:h-4 text-primary" />
                <span>
                  <span className="hidden sm:inline">{totalCompanies.toLocaleString()}+ Companies</span>
                  <span className="sm:hidden">{totalCompanies > 1000 ? `${Math.round(totalCompanies/1000)}k+` : totalCompanies}+ Co.</span>
                </span>
              </div>
              <div className="flex items-center space-x-1.5 md:space-x-2">
                <Award className="w-3 h-3 md:w-4 md:h-4 text-primary" />
                <span>
                  <span className="hidden sm:inline">{searchResult?.aggregations.industries.length || 50}+ Industries</span>
                  <span className="sm:hidden">{searchResult?.aggregations.industries.length || 50}+ Ind.</span>
                </span>
              </div>
              {/* Hide on mobile */}
              <div className="hidden sm:flex items-center space-x-1.5 md:space-x-2">
                <Target className="w-3 h-3 md:w-4 md:h-4 text-primary" />
                <span>{currentCompanies.reduce((sum, company) => {
                  // Handle both legacy and new data structures safely
                  const jobs = isNewFormatCompany(company)
                    ? company.stats.activeJobs
                    : (company as LegacyCompany).openJobs
                  return sum + jobs
                }, 0).toLocaleString()}+ Open Positions</span>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Search and Filters - Mobile Optimized */}
      <section className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-16 z-40">
        <div className="container mx-auto px-4 py-4 md:py-6">
          <div className="flex flex-col lg:flex-row gap-3 md:gap-4">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search companies..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch(searchQuery)}
                className="pl-10 input-enhanced h-10 md:h-auto"
              />
            </div>

            {/* Desktop Controls */}
            <div className="hidden lg:flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => setShowDesktopFilters(!showDesktopFilters)}
                className={showDesktopFilters ? 'bg-primary/10 border-primary/50' : ''}
              >
                <Filter className="w-4 h-4 mr-2" />
                Filters
                {activeFilterCount > 0 && (
                  <Badge variant="secondary" className="ml-2 theme-glow">
                    {activeFilterCount}
                  </Badge>
                )}
              </Button>

              <TalentSorting
                sortBy={sortBy}
                sortOrder={sortOrder}
                onSortChange={handleSortChange}
              />

              {/* View Mode Toggle - Hidden on Mobile */}
              <div className="hidden md:flex items-center space-x-2 bg-muted/50 rounded-lg p-1">
                <Button
                  variant={displayViewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleViewModeChange('grid')}
                  className="h-8 w-8 p-0"
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={displayViewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleViewModeChange('list')}
                  className="h-8 w-8 p-0"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Mobile Controls - Optimized */}
            <div className="flex lg:hidden items-center space-x-2">
              <Sheet open={showMobileFilters} onOpenChange={setShowMobileFilters}>
                <SheetTrigger asChild>
                  <Button variant="outline" className="flex-1 h-10">
                    <Filter className="w-4 h-4 mr-2" />
                    <span className="hidden sm:inline">Filters</span>
                    <span className="sm:hidden">Filter</span>
                    {activeFilterCount > 0 && (
                      <Badge variant="secondary" className="ml-2 theme-glow text-xs">
                        {activeFilterCount}
                      </Badge>
                    )}
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="w-[90vw] sm:w-80 overflow-y-auto">
                  <SheetHeader>
                    <SheetTitle>Company Filters</SheetTitle>
                  </SheetHeader>
                  <div className="mt-6">
                    <CompanyFilters
                      selectedIndustries={selectedIndustries}
                      setSelectedIndustries={setSelectedIndustries}
                      companySizeRange={companySizeRange}
                      setCompanySizeRange={setCompanySizeRange}
                      foundedRange={foundedRange}
                      setFoundedRange={setFoundedRange}
                      onFiltersChange={() => setShowMobileFilters(false)}
                    />
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content - Mobile Optimized */}
      <main className="container mx-auto px-4 py-6 md:py-8">
        <div className="flex gap-6 md:gap-8">
          {/* Desktop Filters Sidebar */}
          <aside className={`hidden lg:block transition-all duration-300 ${
            showDesktopFilters ? 'w-80' : 'w-0 overflow-hidden'
          }`}>
            {showDesktopFilters && (
              <div className="sticky top-40">
                <CompanyFilters
                  selectedIndustries={selectedIndustries}
                  setSelectedIndustries={setSelectedIndustries}
                  companySizeRange={companySizeRange}
                  setCompanySizeRange={setCompanySizeRange}
                  foundedRange={foundedRange}
                  setFoundedRange={setFoundedRange}
                  showApplyButton={false}
                />
              </div>
            )}
          </aside>

          {/* Companies Grid/List */}
          <div className="flex-1" id="companies-results">
            <TalentResultsSummary
              totalResults={totalCompanies}
              searchQuery={searchQuery}
              activeFilters={activeFilterCount}
              viewMode={displayViewMode}
              onViewModeChange={handleViewModeChange}
            />

            {/* Loading State */}
            {isSearching && (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <span className="ml-2">Searching companies...</span>
              </div>
            )}

            {/* Error State */}
            {(error || searchError) && (
              <div className="text-center py-12">
                <p className="text-red-500 mb-4">{error || searchError}</p>
                <Button onClick={() => searchCompanies({ page: 1 })}>
                  Try Again
                </Button>
              </div>
            )}

            {/* Companies Grid/List - Responsive */}
            {!isSearching && !error && !searchError && (
              <AnimatePresence mode="wait">
                <motion.div
                  key={displayViewMode}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className={`
                    grid grid-cols-1 gap-4 md:gap-6
                    ${displayViewMode === 'grid' ? 'md:grid-cols-2 xl:grid-cols-3' : 'md:space-y-4 md:block'}
                  `}
                >
                  {currentCompanies.map((company, index) => (
                    <motion.div
                      key={company.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className={displayViewMode === 'list' ? 'md:mb-4' : ''}
                    >
                      <CompanyCard
                        company={company}
                        viewMode={displayViewMode}
                        onViewProfile={handleViewProfile}
                        onFollow={handleFollow}
                        onViewJobs={(company) => {
                          const companySlug = getCompanySlug(company)
                          window.open(`/jobs?company=${companySlug}`, '_blank')
                        }}
                        isFollowing={usingBackendData ? followedCompanies.has(getCompanyId(company)) : false}
                      />
                    </motion.div>
                  ))}
                </motion.div>
              </AnimatePresence>
            )}

            {/* No Results */}
            {!isSearching && !error && !searchError && currentCompanies.length === 0 && (
              <div className="text-center py-12">
                <Building className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="font-semibold mb-2">No companies found</h3>
                <p className="text-muted-foreground">Try adjusting your search criteria</p>
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <TalentPagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalCompanies}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
              />
            )}
          </div>
        </div>
      </main>

      {/* Company Detail Modal */}
      <CompanyDetailModal
        company={selectedCompany ? convertToFullCompany(selectedCompany) : null}
        originalCompany={selectedCompany ? convertToFullCompany(selectedCompany) : undefined}
        isOpen={!!selectedCompany}
        onClose={() => setSelectedCompany(null)}
        onFollow={async () => {
          if (selectedCompany) {
            await handleFollow(selectedCompany)
          }
          setSelectedCompany(null)
        }}
        onViewJobs={() => {
          if (selectedCompany) {
            const companySlug = getCompanySlug(selectedCompany)
            window.open(`/jobs?company=${companySlug}`, '_blank')
          }
          setSelectedCompany(null)
        }}
      />
    </div>
  )
}

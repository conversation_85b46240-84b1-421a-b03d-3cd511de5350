// app/(public)/talent/page.tsx
'use client'

import React, { useState, useEffect } from 'react'

import { useSearchParams, useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON>, <PERSON>et<PERSON>ontent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet'
import { TalentFilters } from '@/components/talent/talent-filters'
import { TalentCard } from '@/components/talent/talent-card'
import { TalentDetailModal } from '@/components/talent/talent-detail-modal'
import { HireModal } from '@/components/hire/hire-modal'
import { TalentPagination, TalentSorting, TalentResultsSummary } from '@/components/talent/talent-pagination'
import { useTalentFrontendStore } from '@/stores/talent-frontend.store'
import { useLocationStore } from '@/stores/location-store'
import { TalentFrontend } from '@/lib/services/talent-frontend.service'
import { HireFrontend } from '@/types/hire.types'
import { useStatsStore } from '@/stores/stats.store'
import { useAuthStore } from '@/stores/auth.store'
import { useHireStore } from '@/stores/hire.store'
import {
  Search,
  Filter,
  Grid3X3,
  List,
  X,
  Users,
  TrendingUp,
  Award,
  Clock
} from 'lucide-react'

export default function TalentPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { currentLocation } = useLocationStore()
  const { user } = useAuthStore()
  const { openHireModal, closeHireModal, showHireModal } = useHireStore()

  const {
    talent,
    searchResult,
    isSearching,
    error,
    searchQuery,
    activeFilters,
    viewMode,
    showFilters,
    searchTalent,
    updateFilters,
    clearFilters,
    setViewMode,
    toggleFilters,
    selectTalent,
    goToPage,
    followTalent
  } = useTalentFrontendStore()

  const {
    stats,
    loading: statsLoading,
    getStats
  } = useStatsStore()

  // Local state for modal
  const [selectedTalent, setSelectedTalent] = useState<TalentFrontend | null>(null)
  const [talentToHire, setTalentToHire] = useState<TalentFrontend | null>(null)

  const [showMobileFilters, setShowMobileFilters] = useState(false)

  // Initialize search on component mount
  useEffect(() => {
    const initialQuery = searchParams.get('q') || ''
    const initialLocation = searchParams.get('location') || currentLocation?.city || ''

    searchTalent({
      query: initialQuery,
      location: initialLocation,
      page: 1,
      limit: 20
    })
  }, [searchParams, currentLocation, searchTalent])

  // Fetch stats on mount
  useEffect(() => {
    getStats()
  }, [getStats])

  // Handle search input
  const handleSearch = (query: string) => {
    searchTalent({ query, page: 1 })

    // Update URL
    const params = new URLSearchParams(searchParams.toString())
    if (query) {
      params.set('q', query)
    } else {
      params.delete('q')
    }
    router.push(`/talent?${params.toString()}`)
  }

  // Handle talent actions
  const handleViewTalent = (talent: TalentFrontend) => {
    setSelectedTalent(talent)
    selectTalent(talent.id)
  }

  const handleContactTalent = (talent: TalentFrontend) => {
    // TODO: Implement contact functionality
    console.log('Contact talent:', talent.name)
    setSelectedTalent(null) // Close modal after action
  }

  const handleHireTalent = (talent: TalentFrontend) => {
    console.log('🔍 Hire button clicked for talent:', talent.name)
    console.log('🔍 Current user:', user)
    console.log('🔍 User role:', user?.role)

    // Check if user is authenticated and has permission to hire
    if (!user || !['company_admin', 'recruiter'].includes(user.role)) {
      console.log('❌ User needs to be authenticated as company admin or recruiter to hire')
      alert('You need to be logged in as a company administrator or recruiter to hire talent. Please log in with a company account.')
      return
    }

    console.log('✅ User authorized, opening hire modal')
    setTalentToHire(talent)
    setSelectedTalent(null) // Close talent detail modal
    openHireModal(talent.id)
    console.log('🔍 Hire modal should be open now, showHireModal:', showHireModal)
  }

  const handleFollowTalent = async (talent: TalentFrontend) => {
    try {
      await followTalent(talent.id)
      console.log('Following talent:', talent.name)
      setSelectedTalent(null) // Close modal after action
    } catch (error) {
      console.error('Failed to follow talent:', error)
    }
  }

  const handleHireSuccess = (hireRequest: HireFrontend) => {
    console.log('Hire request created successfully:', hireRequest)
    setTalentToHire(null)
  }

  const handleCloseHireModal = () => {
    closeHireModal()
    setTalentToHire(null)
  }

  // Handle pagination
  const handlePageChange = (page: number) => {
    goToPage(page)

    // Update URL
    const params = new URLSearchParams(searchParams.toString())
    params.set('page', page.toString())
    router.push(`/talent?${params.toString()}`)
  }

  // Handle sorting
  const handleSortChange = (sortBy: 'relevance' | 'rating' | 'experience' | 'lastActive' | 'joinedDate', sortOrder: 'asc' | 'desc') => {
    searchTalent({ sortBy, sortOrder, page: 1 })
  }

  // Handle view mode change
  const handleViewModeChange = (mode: 'grid' | 'list') => {
    setViewMode(mode)
  }

  // Handle filters
  const handleFiltersChange = (newFilters: Record<string, unknown>) => {
    updateFilters(newFilters)
  }

  const handleClearFilters = () => {
    clearFilters()
  }

  // Use real data from store
  const displayTalents = talent || []
  const pagination = searchResult?.pagination || {
    page: 1,
    limit: 20,
    total: 0, // Don't show count until we have real data
    totalPages: 0
  }

  // Calculate active filter count
  const activeFilterCount =
    (activeFilters.skills?.length || 0) +
    (activeFilters.industries?.length || 0) +
    (activeFilters.locations?.length || 0) +
    (activeFilters.experienceLevels?.length || 0) +
    (activeFilters.jobTypes?.length || 0) +
    (activeFilters.workArrangements?.length || 0) +
    (activeFilters.availability?.length || 0) +
    (activeFilters.salaryRange?.min || activeFilters.salaryRange?.max ? 1 : 0) +
    (activeFilters.remote !== undefined ? 1 : 0) +
    (activeFilters.verified !== undefined ? 1 : 0) +
    (activeFilters.featured !== undefined ? 1 : 0) +
    (activeFilters.minRating !== undefined ? 1 : 0)

  // Calculate dynamic stats based on actual data
  const getTalentStats = () => {
    // Use stats from API first, then search results, then pagination
    const actualTalentCount = stats?.clients?.active || searchResult?.pagination?.total || pagination.total || 0

    // Use skill categories from stats API or fallback to filter categories
    const skillCategoriesCount = stats?.skills?.total || 16 // 16 categories from filters

    // Calculate success rate based on actual data (placeholder calculation)
    const successRate = actualTalentCount > 0 ? Math.min(95, Math.max(85, 90 + (actualTalentCount * 2))) : 95

    return {
      activeTalent: actualTalentCount,
      skillCategories: skillCategoriesCount,
      successRate: Math.round(successRate),
      globalCoverage: '24/7' // Always available
    }
  }

  const dynamicStats = getTalentStats()

  return (
    <div className="pt-16">
      {/* Premium Hero Section - Mobile Optimized */}
      <section className="relative py-12 md:py-16 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-background to-primary/10" />
        {/* Hide pattern on mobile for better performance */}
        <div className="absolute inset-0 opacity-10 bg-[radial-gradient(circle_at_20%_30%,_hsl(var(--primary))_3px,_transparent_3px),_radial-gradient(circle_at_80%_70%,_hsl(var(--primary))_2px,_transparent_2px)] bg-[length:80px_80px,_120px_120px] hidden md:block" />

        <div className="container mx-auto px-4 relative">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 rounded-full bg-card/95 backdrop-blur-sm border border-border/50 text-primary text-xs md:text-sm font-medium shadow-lg mb-4 md:mb-6">
              <Users className="w-3 h-3 md:w-4 md:h-4 mr-1.5 md:mr-2" />
              <span>Discover Top Talent</span>
            </div>

            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-6xl font-bold mb-4 md:mb-6 bg-gradient-to-r from-foreground via-foreground/90 to-foreground/70 bg-clip-text text-transparent">
              Find Exceptional
              <span className="block bg-gradient-to-r from-primary via-primary/90 to-primary/70 bg-clip-text text-transparent">
                Talent
              </span>
            </h1>

            <p className="text-base sm:text-lg md:text-xl text-muted-foreground mb-6 md:mb-8 max-w-2xl mx-auto px-4 md:px-0">
              <span className="hidden md:inline">
                Connect with world-class professionals across technology, business, healthcare, legal, finance, and more. Find the perfect expert for your project.
              </span>
              <span className="md:hidden">
                Find world-class professionals for your projects.
              </span>
            </p>

            {/* Stats - Dynamic from API */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="flex flex-wrap items-center justify-center gap-4 md:gap-6 text-xs md:text-sm text-muted-foreground">
                <div className="flex items-center space-x-1.5 md:space-x-2">
                  <TrendingUp className="w-3 h-3 md:w-4 md:h-4 text-primary" />
                  <span>
                    <span className="hidden sm:inline">
                      {statsLoading ? 'Loading...' :
                       dynamicStats.activeTalent > 0 ? `${dynamicStats.activeTalent.toLocaleString()}+ Active Talent` :
                       'No Active Talent'}
                    </span>
                    <span className="sm:hidden">
                      {statsLoading ? '...' :
                       dynamicStats.activeTalent > 0 ? `${dynamicStats.activeTalent}+` :
                       '0'} Talent
                    </span>
                  </span>
                </div>
                <div className="flex items-center space-x-1.5 md:space-x-2">
                  <Award className="w-3 h-3 md:w-4 md:h-4 text-primary" />
                  <span>
                    <span className="hidden sm:inline">
                      {dynamicStats.skillCategories}+ Skill Categories
                    </span>
                    <span className="sm:hidden">
                      {dynamicStats.skillCategories}+ Skills
                    </span>
                  </span>
                </div>
                {/* Hide on mobile */}
                <div className="hidden sm:flex items-center space-x-1.5 md:space-x-2">
                  <Clock className="w-3 h-3 md:w-4 md:h-4 text-primary" />
                  <span>{dynamicStats.successRate}% Match Success Rate</span>
                </div>
                <div className="hidden md:flex items-center space-x-1.5 md:space-x-2">
                  <Users className="w-3 h-3 md:w-4 md:h-4 text-primary" />
                  <span>{dynamicStats.globalCoverage} Global Coverage</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Search and Filters Header - Mobile Optimized */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-16 z-40">
        <div className="container mx-auto px-4 py-3 md:py-4">
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-3 md:mb-4 space-y-3 md:space-y-0">
            <div className="flex items-center space-x-2 md:space-x-4">
              <h2 className="text-lg md:text-2xl font-bold">Browse Talent</h2>
              {activeFilterCount > 0 && (
                <Badge variant="secondary" className="theme-glow text-xs">
                  <span className="hidden sm:inline">{activeFilterCount} filter{activeFilterCount !== 1 ? 's' : ''} applied</span>
                  <span className="sm:hidden">{activeFilterCount}</span>
                </Badge>
              )}
            </div>

            <div className="flex items-center space-x-2 md:space-x-4">
              {/* Mobile Filter Toggle */}
              <div className="lg:hidden flex-1">
                <Sheet open={showMobileFilters} onOpenChange={setShowMobileFilters}>
                  <SheetTrigger asChild>
                    <Button variant="outline" size="sm" className="button-enhanced w-full md:w-auto">
                      <Filter className="w-4 h-4 mr-2" />
                      Filters
                      {activeFilterCount > 0 && (
                        <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
                          {activeFilterCount}
                        </Badge>
                      )}
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="left" className="w-80 overflow-y-auto">
                    <SheetHeader>
                      <SheetTitle>Talent Filters</SheetTitle>
                    </SheetHeader>
                    <div className="mt-6">
                      <TalentFilters
                        filters={activeFilters}
                        onFiltersChange={(filters) => {
                          handleFiltersChange(filters)
                          setShowMobileFilters(false)
                        }}
                        onClearFilters={() => {
                          handleClearFilters()
                          setShowMobileFilters(false)
                        }}
                      />
                    </div>
                  </SheetContent>
                </Sheet>
              </div>

              {/* Desktop Filter Toggle */}
              <div className="hidden lg:block">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleFilters}
                  className="button-enhanced"
                >
                  {showFilters ? (
                    <>
                      <X className="w-4 h-4 mr-2" />
                      Hide Filters
                    </>
                  ) : (
                    <>
                      <Filter className="w-4 h-4 mr-2" />
                      Show Filters
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* Search Bar */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search by skills, title, or name..."
                value={searchQuery.query || ''}
                onChange={(e) => handleSearch(e.target.value)}
                className="input-enhanced pl-10"
              />
            </div>
            <TalentSorting
              sortBy={searchQuery.sortBy || 'relevance'}
              sortOrder={searchQuery.sortOrder || 'desc'}
              onSortChange={handleSortChange}
            />

            {/* View Mode Toggle - Hidden on Mobile */}
            <div className="hidden md:flex items-center space-x-2 bg-muted/50 rounded-lg p-1">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => handleViewModeChange('grid')}
                className="h-8 w-8 p-0"
              >
                <Grid3X3 className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => handleViewModeChange('list')}
                className="h-8 w-8 p-0"
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="flex gap-8">
          {/* Desktop Filters Sidebar */}
          <aside className={`hidden lg:block transition-all duration-300 ${
            showFilters ? 'w-80' : 'w-0 overflow-hidden'
          }`}>
            {showFilters && (
              <div className="sticky top-40">
                <TalentFilters
                  filters={activeFilters}
                  onFiltersChange={handleFiltersChange}
                  onClearFilters={handleClearFilters}
                />
              </div>
            )}
          </aside>

          {/* Talent Grid/List */}
          <div className="flex-1" id="talent-results">
            <TalentResultsSummary
              totalResults={pagination.total}
              searchQuery={searchQuery.query || ''}
              activeFilters={Object.keys(activeFilters).length}
              viewMode={viewMode}
              onViewModeChange={handleViewModeChange}
            />

            {/* Talent Grid/List - Responsive */}
            <AnimatePresence mode="wait">
              <motion.div
                key={viewMode}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className={`
                  grid grid-cols-1 gap-4 md:gap-6
                  ${viewMode === 'grid' ? 'md:grid-cols-2 xl:grid-cols-3' : 'md:space-y-4 md:block'}
                `}
              >
                {isSearching ? (
                  // Loading skeleton
                  Array.from({ length: 6 }).map((_, index) => (
                    <div key={index} className="animate-pulse">
                      <div className="bg-muted rounded-lg h-64"></div>
                    </div>
                  ))
                ) : displayTalents.length > 0 ? (
                  displayTalents.map((talent, index) => (
                    <motion.div
                      key={talent.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className={viewMode === 'list' ? 'md:mb-4' : ''}
                  >
                    <TalentCard
                      talent={talent}
                      viewMode={viewMode}
                      onViewProfile={handleViewTalent}
                      onContact={handleContactTalent}
                      onHire={handleHireTalent}
                      onSave={handleFollowTalent}
                    />
                  </motion.div>
                  ))
                ) : (
                  // Empty state
                  <div className="col-span-full flex flex-col items-center justify-center py-16">
                    <div className="text-center">
                      <Users className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-xl font-semibold mb-2">No talent found</h3>
                      <p className="text-muted-foreground mb-4">
                        Try adjusting your search criteria or filters to find more talent.
                      </p>
                      <Button onClick={handleClearFilters} variant="outline">
                        Clear Filters
                      </Button>
                    </div>
                  </div>
                )}
              </motion.div>
            </AnimatePresence>

            {/* Error State */}
            {error && (
              <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4 mb-6">
                <p className="text-destructive">{error}</p>
              </div>
            )}

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <TalentPagination
                currentPage={pagination.page}
                totalPages={pagination.totalPages}
                totalItems={pagination.total}
                itemsPerPage={pagination.limit}
                onPageChange={handlePageChange}
                onItemsPerPageChange={(limit) => searchTalent({ limit, page: 1 })}
              />
            )}
          </div>
        </div>
      </main>

      {/* Talent Detail Modal */}
      <TalentDetailModal
        talent={selectedTalent}
        isOpen={!!selectedTalent}
        onClose={() => setSelectedTalent(null)}
        onContact={handleContactTalent}
        onHire={handleHireTalent}
      />

      {/* Hire Modal */}
      <HireModal
        talent={talentToHire}
        isOpen={showHireModal && !!talentToHire}
        onClose={handleCloseHireModal}
        onSuccess={handleHireSuccess}
      />


    </div>
  )
}

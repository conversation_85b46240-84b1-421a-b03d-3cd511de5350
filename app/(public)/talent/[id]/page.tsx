'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useClientDetailStore } from '@/stores/client-detail.store'
import { ClientDetailProfile } from '@/types/client-detail.types'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { 
  ArrowLeft,
  MapPin, 
  Star, 
  DollarSign, 
  Clock, 
  Heart, 
  MessageCircle,
  Briefcase,
  CheckCircle,
  TrendingUp,
  Award,
  Users,
  Calendar,
  Globe,
  Mail,
  Phone,
  ExternalLink,
  Download,
  Share2,
  Flag,
  Building,
  GraduationCap,
  Code,
  Palette,
  Database,
  Shield,
  Zap,
  Eye,
  ThumbsUp,
  MessageSquare,
  FileText,
  Camera,
  Video,
  Link as LinkIcon
} from 'lucide-react'

export default function ClientDetailPage() {
  const params = useParams()
  const router = useRouter()
  const clientId = params.id as string

  const {
    currentClient,
    analytics,
    isLoading,
    isLoadingAnalytics,
    error,
    showAnalytics,
    fetchClientDetail,
    fetchAnalytics,
    toggleAnalytics,
    clearErrors,
    getProfileSuggestions,
    getProfileStrength
  } = useClientDetailStore()

  const [activeTab, setActiveTab] = useState('overview')
  const [isFollowing, setIsFollowing] = useState(false)

  // Fetch client data on component mount
  useEffect(() => {
    if (clientId) {
      fetchClientDetail(clientId)
    }
  }, [clientId, fetchClientDetail])

  // Fetch analytics if user owns the profile
  useEffect(() => {
    if (clientId && showAnalytics) {
      fetchAnalytics(clientId)
    }
  }, [clientId, showAnalytics, fetchAnalytics])

  // Handle follow/unfollow
  const handleFollow = async () => {
    if (!currentClient) return
    // TODO: Implement follow functionality
    setIsFollowing(!isFollowing)
  }

  // Handle contact
  const handleContact = () => {
    if (!currentClient) return
    // TODO: Implement contact functionality
    console.log('Contact client:', currentClient.headline)
  }

  // Handle hire
  const handleHire = () => {
    if (!currentClient) return
    // TODO: Implement hire functionality
    console.log('Hire client:', currentClient.headline)
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-64 bg-muted rounded-lg mb-8"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2 space-y-6">
                <div className="h-32 bg-muted rounded-lg"></div>
                <div className="h-48 bg-muted rounded-lg"></div>
              </div>
              <div className="space-y-6">
                <div className="h-24 bg-muted rounded-lg"></div>
                <div className="h-32 bg-muted rounded-lg"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Profile Not Found</h1>
            <p className="text-muted-foreground mb-6">{error}</p>
            <Button onClick={() => router.push('/talent')}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Talent
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Use current client data
  const client = currentClient
  
  if (!client) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Profile Not Available</h1>
            <p className="text-muted-foreground mb-6">This profile is not available or does not exist.</p>
            <Button onClick={() => router.push('/talent')}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Talent
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Calculate profile strength and suggestions
  const profileStrength = getProfileStrength()
  const suggestions = getProfileSuggestions()

  // Helper functions
  const formatSalaryRange = (salaryExpectation: ClientDetailProfile['jobPreferences']['salaryExpectation']) => {
    if (!salaryExpectation.min && !salaryExpectation.max) {
      return 'Salary negotiable'
    }

    const { min, max, currency, period } = salaryExpectation
    const periodText = period === 'yearly' ? '/year' : period === 'monthly' ? '/month' : '/hour'
    
    if (min && max) {
      return `${currency}${min.toLocaleString()} - ${currency}${max.toLocaleString()}${periodText}`
    } else if (min) {
      return `${currency}${min.toLocaleString()}+${periodText}`
    } else if (max) {
      return `Up to ${currency}${max.toLocaleString()}${periodText}`
    }

    return 'Salary negotiable'
  }

  // Helper functions
  const getSkillLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'expert':
        return 'bg-green-500'
      case 'advanced':
        return 'bg-blue-500'
      case 'intermediate':
        return 'bg-yellow-500'
      case 'beginner':
        return 'bg-orange-500'
      default:
        return 'bg-gray-500'
    }
  }

  const formatExperienceLevel = (level: string, years: number) => {
    const levelMap: Record<string, string> = {
      entry: 'Entry Level',
      junior: 'Junior',
      mid: 'Mid Level',
      senior: 'Senior',
      lead: 'Lead',
      executive: 'Executive'
    }

    const levelText = levelMap[level] || level
    return years > 0 ? `${levelText} (${years} years)` : levelText
  }

  return (
    <div className="pt-16">
      {/* Hero Section with Cover Image */}
      <section className="relative h-80 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/30 via-background to-primary/10" />
        <div className="absolute inset-0 opacity-20 bg-[radial-gradient(circle_at_20%_30%,_hsl(var(--primary))_3px,_transparent_3px),_radial-gradient(circle_at_80%_70%,_hsl(var(--primary))_2px,_transparent_2px)] bg-[length:80px_80px,_120px_120px]" />
        
        {/* Navigation */}
        <div className="absolute top-6 left-6 z-10">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => router.push('/talent')}
            className="bg-background/80 backdrop-blur-sm"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Talent
          </Button>
        </div>

        {/* Profile Header */}
        <div className="absolute bottom-0 left-0 right-0 p-6">
          <div className="container mx-auto">
            <div className="flex flex-col md:flex-row items-start md:items-end gap-6">
              {/* Avatar */}
              <div className="relative">
                <Avatar className="w-32 h-32 border-4 border-background shadow-xl">
                  <AvatarImage 
                    src={client.user.profile.avatar || "/api/placeholder/128/128"} 
                    alt={`${client.user.profile.firstName} ${client.user.profile.lastName}`} 
                  />
                  <AvatarFallback className="text-2xl font-bold">
                    {client.user.profile.firstName?.[0]}{client.user.profile.lastName?.[0]}
                  </AvatarFallback>
                </Avatar>
                {client.verification.emailVerified && (
                  <div className="absolute -bottom-2 -right-2 bg-green-500 text-white rounded-full p-1">
                    <CheckCircle className="w-4 h-4" />
                  </div>
                )}
              </div>

              {/* Basic Info */}
              <div className="flex-1 text-white">
                <h1 className="text-3xl md:text-4xl font-bold mb-2">
                  {client.user.profile.firstName} {client.user.profile.lastName}
                </h1>
                <p className="text-xl text-white/90 mb-3">{client.headline}</p>
                
                <div className="flex flex-wrap items-center gap-4 text-white/80">
                  {client.jobPreferences.locations?.[0] && (
                    <div className="flex items-center gap-1">
                      <MapPin className="w-4 h-4" />
                      <span>
                        {client.jobPreferences.locations[0].city}, {client.jobPreferences.locations[0].country}
                      </span>
                    </div>
                  )}
                  
                  <div className="flex items-center gap-1">
                    <Briefcase className="w-4 h-4" />
                    <span>{formatExperienceLevel(client.experience.level, client.experience.yearsOfExperience)}</span>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    <span>Available {client.jobPreferences.availability}</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3">
                <Button onClick={handleContact} size="lg" className="bg-primary hover:bg-primary/90">
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Contact
                </Button>
                <Button onClick={handleHire} variant="outline" size="lg" className="bg-background/80 backdrop-blur-sm">
                  <Briefcase className="w-4 h-4 mr-2" />
                  Hire
                </Button>
                <Button 
                  onClick={handleFollow} 
                  variant="outline" 
                  size="lg"
                  className="bg-background/80 backdrop-blur-sm"
                >
                  <Heart className={`w-4 h-4 mr-2 ${isFollowing ? 'fill-current text-red-500' : ''}`} />
                  {isFollowing ? 'Following' : 'Follow'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Tabs Navigation */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="experience">Experience</TabsTrigger>
                <TabsTrigger value="skills">Skills</TabsTrigger>
                <TabsTrigger value="portfolio">Portfolio</TabsTrigger>
                <TabsTrigger value="reviews">Reviews</TabsTrigger>
              </TabsList>

              {/* Overview Tab */}
              <TabsContent value="overview" className="space-y-6">
                {/* About Section */}
                <Card>
                  <CardHeader>
                    <CardTitle>About</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground leading-relaxed">
                      {client.summary || 'No summary provided.'}
                    </p>
                  </CardContent>
                </Card>

                {/* Job Preferences */}
                <Card>
                  <CardHeader>
                    <CardTitle>Job Preferences</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Desired Roles</h4>
                      <div className="flex flex-wrap gap-2">
                        {client.jobPreferences.desiredRoles.map((role, index) => (
                          <Badge key={index} variant="secondary">{role}</Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Industries</h4>
                      <div className="flex flex-wrap gap-2">
                        {client.jobPreferences.industries.map((industry, index) => (
                          <Badge key={index} variant="outline">{industry}</Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Work Arrangement</h4>
                      <div className="flex flex-wrap gap-2">
                        {client.jobPreferences.workArrangement.map((arrangement, index) => (
                          <Badge key={index} variant="secondary">{arrangement}</Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Salary Expectation</h4>
                      <p className="text-lg font-semibold text-green-600">
                        {formatSalaryRange(client.jobPreferences.salaryExpectation)}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Experience Tab */}
              <TabsContent value="experience" className="space-y-6">
                {/* Work History */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Briefcase className="w-5 h-5" />
                      Work Experience
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {client.workHistory.length > 0 ? (
                      <div className="space-y-6">
                        {client.workHistory.map((work, index) => (
                          <div key={work.id || index} className="border-l-2 border-primary/20 pl-4 pb-6 last:pb-0">
                            <div className="flex items-start justify-between mb-2">
                              <div>
                                <h3 className="font-semibold">{work.position}</h3>
                                <p className="text-muted-foreground">{work.company}</p>
                              </div>
                              <Badge variant={work.current ? "default" : "secondary"}>
                                {work.current ? "Current" : "Past"}
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground mb-2">
                              {work.startDate} - {work.current ? "Present" : work.endDate}
                            </p>
                            <p className="text-sm mb-3">{work.description}</p>
                            {work.skills.length > 0 && (
                              <div className="flex flex-wrap gap-1">
                                {work.skills.map((skill, skillIndex) => (
                                  <Badge key={skillIndex} variant="outline" className="text-xs">
                                    {skill}
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No work experience added yet.</p>
                    )}
                  </CardContent>
                </Card>

                {/* Education */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <GraduationCap className="w-5 h-5" />
                      Education
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {client.education.length > 0 ? (
                      <div className="space-y-4">
                        {client.education.map((edu, index) => (
                          <div key={edu.id || index} className="border-l-2 border-primary/20 pl-4">
                            <h3 className="font-semibold">{edu.degree}</h3>
                            <p className="text-muted-foreground">{edu.institution}</p>
                            <p className="text-sm text-muted-foreground">
                              {edu.fieldOfStudy} • {edu.startDate} - {edu.current ? "Present" : edu.endDate}
                            </p>
                            {edu.gpa && (
                              <p className="text-sm">GPA: {edu.gpa}</p>
                            )}
                            {edu.description && (
                              <p className="text-sm mt-2">{edu.description}</p>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No education information added yet.</p>
                    )}
                  </CardContent>
                </Card>

                {/* Certifications */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Award className="w-5 h-5" />
                      Certifications
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {client.certifications.length > 0 ? (
                      <div className="space-y-4">
                        {client.certifications.map((cert, index) => (
                          <div key={cert.id || index} className="border rounded-lg p-4">
                            <div className="flex items-start justify-between">
                              <div>
                                <h3 className="font-semibold">{cert.name}</h3>
                                <p className="text-muted-foreground">{cert.issuer}</p>
                                <p className="text-sm text-muted-foreground">
                                  Issued: {new Date(cert.issueDate).toLocaleDateString()}
                                  {cert.expiryDate && ` • Expires: ${new Date(cert.expiryDate).toLocaleDateString()}`}
                                </p>
                              </div>
                              {cert.credentialUrl && (
                                <Button variant="outline" size="sm" asChild>
                                  <a href={cert.credentialUrl} target="_blank" rel="noopener noreferrer">
                                    <ExternalLink className="w-4 h-4" />
                                  </a>
                                </Button>
                              )}
                            </div>
                            {cert.description && (
                              <p className="text-sm mt-2">{cert.description}</p>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No certifications added yet.</p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Skills Tab */}
              <TabsContent value="skills" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Code className="w-5 h-5" />
                      Skills & Expertise
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {client.skills.length > 0 ? (
                      <div className="space-y-6">
                        {/* Group skills by category */}
                        {Object.entries(
                          client.skills.reduce((acc, skill) => {
                            const category = skill.category || 'Other'
                            if (!acc[category]) acc[category] = []
                            acc[category].push(skill)
                            return acc
                          }, {} as Record<string, typeof client.skills>)
                        ).map(([category, skills]) => (
                          <div key={category}>
                            <h3 className="font-semibold mb-3">{category}</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {skills.map((skill, index) => (
                                <div key={index} className="border rounded-lg p-4">
                                  <div className="flex items-center justify-between mb-2">
                                    <h4 className="font-medium">{skill.name}</h4>
                                    <Badge className={getSkillLevelColor(skill.level)}>
                                      {skill.level}
                                    </Badge>
                                  </div>
                                  <p className="text-sm text-muted-foreground">
                                    {skill.yearsOfExperience} years experience
                                  </p>
                                  {skill.endorsed && (
                                    <div className="flex items-center gap-1 mt-2">
                                      <ThumbsUp className="w-3 h-3 text-green-500" />
                                      <span className="text-xs text-green-600">
                                        {skill.endorsements} endorsements
                                      </span>
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No skills added yet.</p>
                    )}
                  </CardContent>
                </Card>

                {/* Languages */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Globe className="w-5 h-5" />
                      Languages
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {client.languages.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {client.languages.map((language, index) => (
                          <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                            <span className="font-medium">{language.name}</span>
                            <Badge variant="outline">{language.proficiency}</Badge>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No languages added yet.</p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Portfolio Tab */}
              <TabsContent value="portfolio" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Palette className="w-5 h-5" />
                      Portfolio
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {client.portfolio.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {client.portfolio.map((project, index) => (
                          <div key={project.id || index} className="border rounded-lg overflow-hidden">
                            {project.images.length > 0 && (
                              <div className="aspect-video bg-muted relative">
                                <img
                                  src={project.images[0]}
                                  alt={project.title}
                                  className="w-full h-full object-cover"
                                />
                                {project.featured && (
                                  <Badge className="absolute top-2 right-2">Featured</Badge>
                                )}
                              </div>
                            )}
                            <div className="p-4">
                              <div className="flex items-start justify-between mb-2">
                                <h3 className="font-semibold">{project.title}</h3>
                                {project.url && (
                                  <Button variant="outline" size="sm" asChild>
                                    <a href={project.url} target="_blank" rel="noopener noreferrer">
                                      <ExternalLink className="w-4 h-4" />
                                    </a>
                                  </Button>
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground mb-3">
                                {project.description}
                              </p>
                              <div className="flex flex-wrap gap-1 mb-2">
                                {project.technologies.map((tech, techIndex) => (
                                  <Badge key={techIndex} variant="outline" className="text-xs">
                                    {tech}
                                  </Badge>
                                ))}
                              </div>
                              <p className="text-xs text-muted-foreground">
                                {project.category} • {new Date(project.completionDate).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No portfolio items added yet.</p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Reviews Tab */}
              <TabsContent value="reviews" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Star className="w-5 h-5" />
                      Reviews & Ratings
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8">
                      <MessageSquare className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="font-semibold mb-2">No Reviews Yet</h3>
                      <p className="text-muted-foreground">
                        This professional hasn't received any reviews yet.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Right Column - Sidebar */}
          <div className="space-y-6">
            {/* Profile Strength */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  Profile Strength
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium">Completeness</span>
                      <span className="text-sm text-muted-foreground">{profileStrength}%</span>
                    </div>
                    <Progress value={profileStrength} className="h-2" />
                  </div>
                  
                  {suggestions.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">Suggestions</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        {suggestions.slice(0, 3).map((suggestion, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="text-primary">•</span>
                            {suggestion}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="w-5 h-5" />
                  Profile Stats
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Profile Views</span>
                    <span className="font-medium">{client.activity.profileViews}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Search Appearances</span>
                    <span className="font-medium">{client.activity.searchAppearances}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Applications</span>
                    <span className="font-medium">{client.applicationStats.totalApplications}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Success Rate</span>
                    <span className="font-medium">{client.applicationStats.successRate}%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Verification Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  Verification
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Email</span>
                    {client.verification.emailVerified ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <span className="text-xs text-muted-foreground">Not verified</span>
                    )}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Phone</span>
                    {client.verification.phoneVerified ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <span className="text-xs text-muted-foreground">Not verified</span>
                    )}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Identity</span>
                    {client.verification.identityVerified ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <span className="text-xs text-muted-foreground">Not verified</span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

"use client"

import { use<PERSON>ffe<PERSON>, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useR<PERSON>er, useSearchParams } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { 
  ArrowLeft, 
  Clock, 
  Target, 
  Users, 
  Trophy, 
  Brain,
  Star,
  Zap,
  CheckCircle,
  AlertCircle,
  Loader2,
  Award,
  BookOpen,
  TrendingUp,
  Shield
} from "lucide-react"
import { useQuizCategories, useQuizSession } from "@/stores/quiz.store"
import { QuizInterface } from "@/components/quiz/quiz-interface"
import { PhaseTransition } from "@/components/quiz/phase-transition"
import { EnhancedResultsModal } from "@/components/quiz/enhanced-results-modal"

interface QuizCategory {
  id: string
  name: string
  description: string
  icon: string
  color: string
  difficulty: string
  estimatedTime: number
  passingScore: number
  skills: string[]
  totalQuestions: number
  availableQuestions: number
}

export default function QuizDetailPage() {
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams()
  const categoryId = params.categoryId as string
  
  const { categories, isLoading, error, fetchCategories } = useQuizCategories()
  const { session, isActive, startQuiz } = useQuizSession()
  
  const [category, setCategory] = useState<QuizCategory | null>(null)
  const [isStarting, setIsStarting] = useState(false)

  // Load categories and find the specific category
  useEffect(() => {
    fetchCategories()
  }, [fetchCategories])

  useEffect(() => {
    if (categories.length > 0 && categoryId) {
      const foundCategory = categories.find(cat => cat.id === categoryId)
      setCategory(foundCategory || null)
    }
  }, [categories, categoryId])

  // Auto-start quiz if specified in URL params
  useEffect(() => {
    const autoStart = searchParams.get('start')
    if (autoStart === 'true' && category && !session && !isActive) {
      handleStartQuiz()
    }
  }, [category, session, isActive, searchParams])

  const handleStartQuiz = async () => {
    if (!category) return
    
    setIsStarting(true)
    try {
      await startQuiz(category.id)
    } catch (error) {
      console.error('Failed to start quiz:', error)
    } finally {
      setIsStarting(false)
    }
  }

  const handleBackToCategories = () => {
    router.push('/quiz')
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'expert': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'advanced': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'intermediate': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'beginner': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getDifficultyIcon = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'expert': return <Shield className="w-4 h-4" />
      case 'advanced': return <TrendingUp className="w-4 h-4" />
      case 'intermediate': return <Target className="w-4 h-4" />
      case 'beginner': return <BookOpen className="w-4 h-4" />
      default: return <Brain className="w-4 h-4" />
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center py-20">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
              <p className="text-muted-foreground">Loading quiz details...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center py-20">
              <AlertCircle className="w-12 h-12 mx-auto mb-4 text-red-500" />
              <h2 className="text-2xl font-bold mb-2">Failed to Load Quiz</h2>
              <p className="text-muted-foreground mb-6">{error}</p>
              <div className="space-x-4">
                <Button onClick={fetchCategories} variant="outline">
                  <Loader2 className="w-4 h-4 mr-2" />
                  Try Again
                </Button>
                <Button onClick={handleBackToCategories}>
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Categories
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Category not found
  if (!category && !isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center py-20">
              <AlertCircle className="w-12 h-12 mx-auto mb-4 text-orange-500" />
              <h2 className="text-2xl font-bold mb-2">Quiz Category Not Found</h2>
              <p className="text-muted-foreground mb-6">
                The quiz category you're looking for doesn't exist or has been removed.
              </p>
              <Button onClick={handleBackToCategories}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Categories
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // If quiz is active, show the quiz interface
  if (session && isActive) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <QuizInterface />
          </div>
        </div>
      </div>
    )
  }

  // Main quiz detail page
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Back Navigation */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="mb-6"
          >
            <Button 
              variant="ghost" 
              onClick={handleBackToCategories}
              className="text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to All Assessments
            </Button>
          </motion.div>

          {category && (
            <>
              {/* Header Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center mb-8"
              >
                <div className="flex items-center justify-center mb-4">
                  <div className="text-6xl mr-4">{category.icon}</div>
                  <div className="text-left">
                    <h1 className="text-3xl md:text-4xl font-bold mb-2">{category.name}</h1>
                    <div className="flex items-center space-x-3">
                      <Badge className={getDifficultyColor(category.difficulty)}>
                        {getDifficultyIcon(category.difficulty)}
                        <span className="ml-1 capitalize">{category.difficulty}</span>
                      </Badge>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Clock className="w-4 h-4 mr-1" />
                        {category.estimatedTime} minutes
                      </div>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Target className="w-4 h-4 mr-1" />
                        Pass: {category.passingScore}%
                      </div>
                    </div>
                  </div>
                </div>
                <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                  {category.description}
                </p>
              </motion.div>

              {/* Stats Cards */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
              >
                <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-primary mb-2">
                      {category.availableQuestions}
                    </div>
                    <p className="text-sm text-muted-foreground">Questions Available</p>
                  </CardContent>
                </Card>
                
                <Card className="bg-gradient-to-br from-green-500/5 to-green-500/10 border-green-500/20">
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-green-600 mb-2">
                      {category.estimatedTime}
                    </div>
                    <p className="text-sm text-muted-foreground">Minutes to Complete</p>
                  </CardContent>
                </Card>
                
                <Card className="bg-gradient-to-br from-orange-500/5 to-orange-500/10 border-orange-500/20">
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-orange-600 mb-2">
                      {category.passingScore}%
                    </div>
                    <p className="text-sm text-muted-foreground">Passing Score</p>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Skills Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="mb-8"
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Brain className="w-5 h-5 mr-2 text-primary" />
                      Skills Covered
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {category.skills.map((skill, index) => (
                        <Badge key={index} variant="secondary" className="text-sm">
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Start Quiz Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="text-center"
              >
                <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
                  <CardContent className="p-8">
                    <div className="mb-6">
                      <Trophy className="w-12 h-12 mx-auto mb-4 text-primary" />
                      <h3 className="text-2xl font-bold mb-2">Ready to Test Your Skills?</h3>
                      <p className="text-muted-foreground">
                        This assessment will evaluate your knowledge in {category.name.toLowerCase()} 
                        through {category.availableQuestions} carefully selected questions.
                      </p>
                    </div>
                    
                    <div className="space-y-4">
                      <Button
                        size="lg"
                        onClick={handleStartQuiz}
                        disabled={isStarting}
                        className="w-full sm:w-auto px-8 py-3 text-lg"
                      >
                        {isStarting ? (
                          <>
                            <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                            Starting Assessment...
                          </>
                        ) : (
                          <>
                            <Zap className="w-5 h-5 mr-2" />
                            Start Assessment
                          </>
                        )}
                      </Button>
                      
                      <div className="text-sm text-muted-foreground">
                        <p>• {category.availableQuestions} questions</p>
                        <p>• {category.estimatedTime} minutes time limit</p>
                        <p>• {category.passingScore}% required to pass</p>
                        <p>• Instant results and feedback</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

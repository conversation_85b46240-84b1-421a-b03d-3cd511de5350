"use client"

import { useEffect, useState } from "react"
import { motion } from "framer-motion"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { 
  Search, 
  Clock, 
  Target, 
  Users, 
  Trophy, 
  Brain,
  Star,
  Filter,
  ArrowRight,
  CheckCircle,
  HelpCircle,
  Zap
} from "lucide-react"
import { useQuizCategories } from "@/stores/quiz.store"
import { cn } from "@/lib/utils"

export default function QuizCategoriesPage() {
  const {
    categories,
    isLoading: isLoadingCategories,
    error: categoriesError,
    fetchCategories
  } = useQuizCategories()

  const [filteredCategories, setFilteredCategories] = useState(categories)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>("all")

  // Calculate dynamic stats based on current filters
  const currentStats = {
    totalCategories: filteredCategories.length,
    totalQuestions: filteredCategories.reduce((sum, cat) => sum + (cat.availableQuestions || 0), 0),
    averageDuration: filteredCategories.length > 0 
      ? Math.round(filteredCategories.reduce((sum, cat) => sum + (cat.estimatedTime || 0), 0) / filteredCategories.length)
      : 0,
    difficultyDistribution: {
      beginner: filteredCategories.filter(c => c.difficulty === 'beginner').length,
      intermediate: filteredCategories.filter(c => c.difficulty === 'intermediate').length,
      advanced: filteredCategories.filter(c => c.difficulty === 'advanced').length,
      expert: filteredCategories.filter(c => c.difficulty === 'expert').length,
    },
    averageQuestionsPerCategory: filteredCategories.length > 0
      ? Math.round(filteredCategories.reduce((sum, cat) => sum + (cat.availableQuestions || 0), 0) / filteredCategories.length)
      : 0
  }

  useEffect(() => {
    fetchCategories()
  }, [fetchCategories])

  useEffect(() => {
    let filtered = categories

    if (searchTerm) {
      filtered = filtered.filter(category =>
        category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        category.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        category.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    if (selectedDifficulty !== "all") {
      filtered = filtered.filter(category => category.difficulty === selectedDifficulty)
    }

    setFilteredCategories(filtered)
  }, [categories, searchTerm, selectedDifficulty])

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'intermediate': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'advanced': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      case 'expert': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getDifficultyIcon = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return '🌱'
      case 'intermediate': return '🚀'
      case 'advanced': return '⚡'
      case 'expert': return '🏆'
      default: return '📚'
    }
  }

  if (isLoadingCategories) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
        <div className="container mx-auto px-4 py-8">
          <div className="space-y-6">
            <div className="text-center space-y-4">
              <div className="h-12 bg-muted rounded-lg animate-pulse mx-auto max-w-md"></div>
              <div className="h-6 bg-muted rounded animate-pulse mx-auto max-w-lg"></div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-24 bg-muted rounded-lg animate-pulse"></div>
              ))}
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-64 bg-muted rounded-lg animate-pulse"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (categoriesError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center space-y-4">
            <div className="text-6xl">😕</div>
            <h1 className="text-2xl font-bold text-foreground">Oops! Something went wrong</h1>
            <p className="text-muted-foreground">{categoriesError}</p>
            <Button onClick={fetchCategories} className="mt-4">
              Try Again
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-8">
          {/* Header */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center space-y-4"
          >
            <div className="flex items-center justify-center space-x-2 mb-4">
              <Brain className="w-8 h-8 text-primary" />
              <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                Skills Assessment Center
              </h1>
            </div>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Test your knowledge, earn certificates, and showcase your skills to potential employers. 
              Choose from {categories.length} professional skill assessments.
            </p>
          </motion.div>

          {/* Dynamic Stats Cards */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-4 gap-4"
          >
            <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <HelpCircle className="w-4 h-4 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">
                      {searchTerm || selectedDifficulty !== "all" ? "Filtered Categories" : "Total Categories"}
                    </p>
                    <p className="text-2xl font-bold text-foreground">{currentStats.totalCategories}</p>
                    {(searchTerm || selectedDifficulty !== "all") && (
                      <p className="text-xs text-muted-foreground">of {categories.length} total</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-blue-500/5 to-blue-500/10 border-blue-500/20">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="p-2 bg-blue-500/10 rounded-lg">
                    <Target className="w-4 h-4 text-blue-500" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Total Questions</p>
                    <p className="text-2xl font-bold text-foreground">{currentStats.totalQuestions}</p>
                    <p className="text-xs text-muted-foreground">
                      avg {currentStats.averageQuestionsPerCategory} per category
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-500/5 to-green-500/10 border-green-500/20">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="p-2 bg-green-500/10 rounded-lg">
                    <Clock className="w-4 h-4 text-green-500" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Avg. Duration</p>
                    <p className="text-2xl font-bold text-foreground">{currentStats.averageDuration}m</p>
                    <p className="text-xs text-muted-foreground">per assessment</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-500/5 to-purple-500/10 border-purple-500/20">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="p-2 bg-purple-500/10 rounded-lg">
                    <Trophy className="w-4 h-4 text-purple-500" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Difficulty Mix</p>
                    <div className="flex space-x-1 mt-1">
                      {Object.entries(currentStats.difficultyDistribution).map(([difficulty, count]) => (
                        count > 0 && (
                          <span key={difficulty} className="text-xs px-1 py-0.5 bg-muted rounded">
                            {difficulty.charAt(0).toUpperCase()}: {count}
                          </span>
                        )
                      ))}
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {selectedDifficulty === "all" ? "All levels" : `${selectedDifficulty} only`}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Search and Filters */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="flex flex-col md:flex-row gap-4 items-center justify-between"
          >
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search categories, skills, or topics..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-background/50 backdrop-blur-sm border-muted-foreground/20"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-muted-foreground" />
              <div className="flex space-x-2">
                {['all', 'beginner', 'intermediate', 'advanced', 'expert'].map((difficulty) => (
                  <Button
                    key={difficulty}
                    variant={selectedDifficulty === difficulty ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedDifficulty(difficulty)}
                    className={cn(
                      "capitalize",
                      selectedDifficulty === difficulty && "bg-primary text-primary-foreground"
                    )}
                  >
                    {difficulty === 'all' ? 'All Levels' : difficulty}
                  </Button>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Categories Grid */}
          {filteredCategories.length === 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-12"
            >
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold text-foreground mb-2">No categories found</h3>
              <p className="text-muted-foreground">
                Try adjusting your search terms or difficulty filter.
              </p>
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm("")
                  setSelectedDifficulty("all")
                }}
                className="mt-4"
              >
                Clear Filters
              </Button>
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            >
              {filteredCategories.map((category, index) => (
                <motion.div
                  key={category.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 * index }}
                  whileHover={{ y: -5 }}
                  className="group"
                >
                  <Card className="h-full bg-gradient-to-br from-background to-muted/20 border-muted-foreground/20 hover:border-primary/30 transition-all duration-300 hover:shadow-lg">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="text-3xl">{category.icon}</div>
                          <div>
                            <CardTitle className="text-lg font-bold text-foreground group-hover:text-primary transition-colors">
                              {category.name}
                            </CardTitle>
                            <div className="flex items-center space-x-2 mt-1">
                              <Badge className={getDifficultyColor(category.difficulty)}>
                                {getDifficultyIcon(category.difficulty)} {category.difficulty}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="space-y-4">
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {category.description}
                      </p>

                      {/* Stats */}
                      <div className="grid grid-cols-3 gap-2 text-center">
                        <div className="space-y-1">
                          <div className="flex items-center justify-center space-x-1">
                            <HelpCircle className="w-3 h-3 text-muted-foreground" />
                            <span className="text-xs text-muted-foreground">Questions</span>
                          </div>
                          <p className="text-sm font-semibold text-foreground">{category.availableQuestions || 0}</p>
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center justify-center space-x-1">
                            <Clock className="w-3 h-3 text-muted-foreground" />
                            <span className="text-xs text-muted-foreground">Duration</span>
                          </div>
                          <p className="text-sm font-semibold text-foreground">{category.estimatedTime}m</p>
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center justify-center space-x-1">
                            <Target className="w-3 h-3 text-muted-foreground" />
                            <span className="text-xs text-muted-foreground">Pass</span>
                          </div>
                          <p className="text-sm font-semibold text-foreground">{category.passingScore}%</p>
                        </div>
                      </div>

                      {/* Skills */}
                      {category.skills && category.skills.length > 0 && (
                        <div className="space-y-2">
                          <p className="text-xs text-muted-foreground font-medium">Skills Covered:</p>
                          <div className="flex flex-wrap gap-1">
                            {category.skills.slice(0, 3).map((skill, skillIndex) => (
                              <Badge key={skillIndex} variant="secondary" className="text-xs">
                                {skill}
                              </Badge>
                            ))}
                            {category.skills.length > 3 && (
                              <Badge variant="secondary" className="text-xs">
                                +{category.skills.length - 3} more
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Action Button */}
                      <Button
                        className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-all duration-300"
                        onClick={() => {
                          // Navigate to dedicated quiz detail page
                          window.location.href = `/quiz/${category.id}`
                        }}
                      >
                        <Zap className="w-4 h-4 mr-2" />
                        Start Assessment
                        <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          )}

          {/* Call to Action */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="text-center space-y-4 py-8"
          >
            <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-6 border border-primary/20">
              <h3 className="text-xl font-bold text-foreground mb-2">Ready to showcase your skills?</h3>
              <p className="text-muted-foreground mb-4">
                Complete assessments to earn certificates and stand out to employers.
              </p>
              <div className="flex items-center justify-center space-x-4">
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>Instant Results</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <Trophy className="w-4 h-4 text-yellow-500" />
                  <span>Digital Certificates</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <Star className="w-4 h-4 text-blue-500" />
                  <span>Skill Verification</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { 
  Trophy, 
  Star, 
  Target, 
  Clock, 
  CheckCircle, 
  XCircle,
  ArrowLeft,
  Download,
  Share2,
  RotateCcw,
  TrendingUp,
  Award,
  Brain,
  Loader2,
  AlertCircle
} from "lucide-react"

interface QuizResult {
  sessionId: string
  category: {
    id: string
    name: string
    icon: string
    difficulty: string
  }
  score: number
  totalQuestions: number
  correctAnswers: number
  totalPoints: number
  maxPoints: number
  timeSpent: number
  completedAt: string
  phase: 'first' | 'second' | 'completed'
  earnedAward: boolean
  skillLevel: string
  answers: Array<{
    questionId: string
    question: string
    selectedAnswer: number
    correctAnswer: number
    isCorrect: boolean
    points: number
    timeSpent: number
  }>
}

export default function QuizResultsPage() {
  const params = useParams()
  const router = useRouter()
  const sessionId = params.sessionId as string
  
  const [result, setResult] = useState<QuizResult | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (sessionId) {
      fetchQuizResult()
    }
  }, [sessionId])

  const fetchQuizResult = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/v1/quiz/results/${sessionId}`)
      const data = await response.json()
      
      if (data.success) {
        setResult(data.data)
      } else {
        setError(data.error || 'Failed to load quiz results')
      }
    } catch (error) {
      setError('Failed to load quiz results')
    } finally {
      setIsLoading(false)
    }
  }

  const getPerformanceMessage = (score: number) => {
    if (score >= 90) return { message: "Outstanding performance! 🌟", color: "text-yellow-600" }
    if (score >= 70) return { message: "Great job! Well done! 👏", color: "text-green-600" }
    if (score >= 60) return { message: "Good effort! Keep practicing! 💪", color: "text-blue-600" }
    return { message: "Keep learning and try again! 📚", color: "text-orange-600" }
  }

  const getSkillLevel = (score: number) => {
    if (score >= 90) return { level: "Expert", color: "text-green-600", icon: Trophy }
    if (score >= 75) return { level: "Advanced", color: "text-blue-600", icon: Star }
    if (score >= 60) return { level: "Intermediate", color: "text-orange-600", icon: Target }
    return { level: "Beginner", color: "text-gray-600", icon: Award }
  }

  const handleRetakeQuiz = () => {
    if (result) {
      router.push(`/quiz/${result.category.id}`)
    }
  }

  const handleBackToCategories = () => {
    router.push('/quiz')
  }

  const handleShareResults = () => {
    if (result) {
      const shareText = `I just scored ${result.score}% on the ${result.category.name} assessment! 🎯`
      if (navigator.share) {
        navigator.share({
          title: 'Quiz Results',
          text: shareText,
          url: window.location.href
        })
      } else {
        navigator.clipboard.writeText(shareText)
        // Could show toast here
      }
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center py-20">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
              <p className="text-muted-foreground">Loading quiz results...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !result) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center py-20">
              <AlertCircle className="w-12 h-12 mx-auto mb-4 text-red-500" />
              <h2 className="text-2xl font-bold mb-2">Results Not Found</h2>
              <p className="text-muted-foreground mb-6">
                {error || "The quiz results you're looking for don't exist or have expired."}
              </p>
              <div className="space-x-4">
                <Button onClick={fetchQuizResult} variant="outline">
                  <RotateCcw className="w-4 h-4 mr-2" />
                  Try Again
                </Button>
                <Button onClick={handleBackToCategories}>
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Assessments
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const performance = getPerformanceMessage(result.score)
  const skillLevel = getSkillLevel(result.score)
  const SkillIcon = skillLevel.icon

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Back Navigation */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="mb-6"
          >
            <Button 
              variant="ghost" 
              onClick={handleBackToCategories}
              className="text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to All Assessments
            </Button>
          </motion.div>

          {/* Results Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-8"
          >
            <div className="flex items-center justify-center mb-4">
              <div className="text-6xl mr-4">{result.category.icon}</div>
              <div className="text-left">
                <h1 className="text-3xl md:text-4xl font-bold mb-2">Assessment Complete!</h1>
                <p className="text-lg text-muted-foreground">{result.category.name}</p>
              </div>
            </div>
          </motion.div>

          {/* Score Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="mb-8"
          >
            <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
              <CardContent className="p-8 text-center">
                <div className="text-6xl font-bold text-primary mb-4">
                  {result.score}%
                </div>
                <p className={`text-xl font-semibold mb-4 ${performance.color}`}>
                  {performance.message}
                </p>
                
                <Progress value={result.score} className="h-4 mb-6" />
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-foreground">
                      {result.correctAnswers}/{result.totalQuestions}
                    </div>
                    <p className="text-sm text-muted-foreground">Correct</p>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-foreground">
                      {result.totalPoints}
                    </div>
                    <p className="text-sm text-muted-foreground">Points</p>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-foreground">
                      {Math.round(result.timeSpent / 60)}m
                    </div>
                    <p className="text-sm text-muted-foreground">Time</p>
                  </div>
                  <div>
                    <div className="flex items-center justify-center mb-1">
                      <SkillIcon className={`w-6 h-6 ${skillLevel.color}`} />
                    </div>
                    <div className={`text-lg font-bold ${skillLevel.color}`}>
                      {skillLevel.level}
                    </div>
                    <p className="text-sm text-muted-foreground">Level</p>
                  </div>
                </div>

                {result.earnedAward && (
                  <div className="mt-6 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                    <div className="flex items-center justify-center space-x-2">
                      <Award className="w-5 h-5 text-yellow-500" />
                      <span className="font-semibold text-yellow-600">Certificate Earned!</span>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      You've earned a digital certificate for this assessment
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="flex flex-wrap gap-4 justify-center mb-8"
          >
            <Button onClick={handleRetakeQuiz} size="lg">
              <RotateCcw className="w-4 h-4 mr-2" />
              Retake Assessment
            </Button>
            
            <Button variant="outline" onClick={handleShareResults} size="lg">
              <Share2 className="w-4 h-4 mr-2" />
              Share Results
            </Button>
            
            {result.earnedAward && (
              <Button variant="outline" size="lg">
                <Download className="w-4 h-4 mr-2" />
                Download Certificate
              </Button>
            )}
          </motion.div>

          {/* Detailed Results */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2 text-primary" />
                  Detailed Results
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {result.answers.map((answer, index) => (
                  <div key={answer.questionId} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-sm">
                        Question {index + 1}: {answer.question}
                      </h4>
                      <div className="flex items-center space-x-2">
                        {answer.isCorrect ? (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        ) : (
                          <XCircle className="w-4 h-4 text-red-500" />
                        )}
                        <Badge variant={answer.isCorrect ? "default" : "destructive"}>
                          {answer.points} pts
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="text-sm text-muted-foreground">
                      <p>Your answer: Option {String.fromCharCode(65 + answer.selectedAnswer)}</p>
                      {!answer.isCorrect && (
                        <p>Correct answer: Option {String.fromCharCode(65 + answer.correctAnswer)}</p>
                      )}
                      <p>Time spent: {answer.timeSpent}s</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

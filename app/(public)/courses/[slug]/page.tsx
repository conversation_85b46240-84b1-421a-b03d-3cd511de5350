"use client"

import { useEffect, useState } from "react"
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation"
import Image from "next/image"
import { motion } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { 
  Play,
  Clock, 
  Users, 
  Star, 
  BookOpen,
  Award,
  Heart,
  Share2,
  Download,
  CheckCircle,
  ArrowLeft,
  Globe,
  Calendar,
  Target,
  Loader2,
  AlertCircle
} from "lucide-react"
import { useCurrentCourse, useCourseEnrollments } from "@/stores/course.store"
import { ICourse } from "@/lib/models/course.model"

export default function CourseDetailPage() {
  const params = useParams()
  const router = useRouter()
  const slug = params.slug as string
  
  const { course, isLoading, error, fetchCourse } = useCurrentCourse()
  const { enrollments, enrollInCourse } = useCourseEnrollments()
  
  const [isEnrolling, setIsEnrolling] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [isLiked, setIsLiked] = useState(false)

  useEffect(() => {
    if (slug) {
      fetchCourse(slug)
    }
  }, [slug, fetchCourse])

  const isEnrolled = course && enrollments.some(e => e.courseId.toString() === course._id.toString())

  const handleEnroll = async () => {
    if (!course) return
    
    setIsEnrolling(true)
    try {
      const result = await enrollInCourse(course._id.toString())
      
      if (result.success) {
        if (result.paymentUrl) {
          // Redirect to payment
          window.location.href = result.paymentUrl
        } else {
          // Free course - redirect to course content
          router.push(`/courses/${course.slug}/learn`)
        }
      } else {
        console.error('Enrollment failed:', result.error)
      }
    } catch (error) {
      console.error('Enrollment error:', error)
    } finally {
      setIsEnrolling(false)
    }
  }

  const handleShare = () => {
    if (navigator.share && course) {
      navigator.share({
        title: course.title,
        text: course.shortDescription,
        url: window.location.href
      })
    } else if (course) {
      navigator.clipboard.writeText(window.location.href)
    }
  }

  const formatPrice = (price: number) => {
    if (price === 0) return 'Free'
    return `$${price.toFixed(2)}`
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins > 0 ? `${mins}m` : ''}`
    }
    return `${mins}m`
  }

  const getLevelColor = (level: string) => {
    switch (level?.toLowerCase()) {
      case 'beginner': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'intermediate': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'advanced': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'expert': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            <div className="text-center py-20">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
              <p className="text-muted-foreground">Loading course details...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !course) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            <div className="text-center py-20">
              <AlertCircle className="w-12 h-12 mx-auto mb-4 text-red-500" />
              <h2 className="text-2xl font-bold mb-2">Course Not Found</h2>
              <p className="text-muted-foreground mb-6">
                {error || "The course you're looking for doesn't exist or has been removed."}
              </p>
              <div className="space-x-4">
                <Button onClick={() => fetchCourse(slug)} variant="outline">
                  <Loader2 className="w-4 h-4 mr-2" />
                  Try Again
                </Button>
                <Button onClick={() => router.push('/courses')}>
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Courses
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Back Navigation */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="mb-6"
          >
            <Button 
              variant="ghost" 
              onClick={() => router.push('/courses')}
              className="text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Courses
            </Button>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-8">
              {/* Course Header */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <div className="relative h-64 md:h-80 rounded-lg overflow-hidden mb-6">
                  <Image
                    src={course.thumbnail}
                    alt={course.title}
                    fill
                    className="object-cover"
                  />
                  
                  {course.isFeatured && (
                    <Badge className="absolute top-4 left-4 bg-yellow-500 text-yellow-900">
                      <Award className="w-3 h-3 mr-1" />
                      Featured
                    </Badge>
                  )}
                  
                  {course.isPremium && (
                    <Badge className="absolute top-4 right-4 bg-purple-500 text-white">
                      Premium
                    </Badge>
                  )}

                  {course.previewVideo && (
                    <Button
                      className="absolute inset-0 m-auto w-16 h-16 rounded-full bg-black/50 backdrop-blur-sm hover:bg-black/70"
                      onClick={() => setShowPreview(true)}
                    >
                      <Play className="w-6 h-6 text-white ml-1" />
                    </Button>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h1 className="text-3xl md:text-4xl font-bold mb-2">{course.title}</h1>
                      <p className="text-lg text-muted-foreground">{course.shortDescription}</p>
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsLiked(!isLiked)}
                      >
                        <Heart className={`w-5 h-5 ${isLiked ? 'fill-red-500 text-red-500' : 'text-muted-foreground'}`} />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={handleShare}>
                        <Share2 className="w-5 h-5 text-muted-foreground" />
                      </Button>
                    </div>
                  </div>

                  {/* Course Meta */}
                  <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span className="font-medium">{course.rating.toFixed(1)}</span>
                      <span>({course.totalRatings.toLocaleString()} ratings)</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4" />
                      <span>{course.totalStudents.toLocaleString()} students</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>{formatDuration(course.duration)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <BookOpen className="w-4 h-4" />
                      <span>{course.totalLessons} lessons</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Globe className="w-4 h-4" />
                      <span>{course.language}</span>
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2">
                    <Badge className={getLevelColor(course.level)}>
                      {course.level}
                    </Badge>
                    {course.tags.map((tag) => (
                      <Badge key={tag} variant="secondary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </motion.div>

              {/* Course Content Tabs */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Tabs defaultValue="overview" className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="curriculum">Curriculum</TabsTrigger>
                    <TabsTrigger value="instructor">Instructor</TabsTrigger>
                    <TabsTrigger value="reviews">Reviews</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="overview" className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle>Course Description</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-muted-foreground leading-relaxed">
                          {course.description}
                        </p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>What You'll Learn</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {course.whatYouWillLearn.map((item, index) => (
                            <div key={index} className="flex items-start space-x-2">
                              <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                              <span className="text-sm">{item}</span>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <Card>
                        <CardHeader>
                          <CardTitle>Requirements</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ul className="space-y-2">
                            {course.requirements.map((req, index) => (
                              <li key={index} className="text-sm text-muted-foreground flex items-start space-x-2">
                                <span className="w-1.5 h-1.5 bg-muted-foreground rounded-full mt-2 flex-shrink-0" />
                                <span>{req}</span>
                              </li>
                            ))}
                          </ul>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle>Target Audience</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ul className="space-y-2">
                            {course.targetAudience.map((audience, index) => (
                              <li key={index} className="text-sm text-muted-foreground flex items-start space-x-2">
                                <Target className="w-4 h-4 mt-0.5 flex-shrink-0" />
                                <span>{audience}</span>
                              </li>
                            ))}
                          </ul>
                        </CardContent>
                      </Card>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="curriculum">
                    <Card>
                      <CardHeader>
                        <CardTitle>Course Curriculum</CardTitle>
                        <p className="text-sm text-muted-foreground">
                          {course.totalLessons} lessons • {formatDuration(course.duration)} total length
                        </p>
                      </CardHeader>
                      <CardContent>
                        <div className="text-center py-8 text-muted-foreground">
                          <BookOpen className="w-12 h-12 mx-auto mb-4" />
                          <p>Detailed curriculum will be available after enrollment</p>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                  
                  <TabsContent value="instructor">
                    <Card>
                      <CardHeader>
                        <CardTitle>Meet Your Instructor</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-center py-8 text-muted-foreground">
                          <Users className="w-12 h-12 mx-auto mb-4" />
                          <p>Instructor information will be displayed here</p>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                  
                  <TabsContent value="reviews">
                    <Card>
                      <CardHeader>
                        <CardTitle>Student Reviews</CardTitle>
                        <div className="flex items-center space-x-2">
                          <Star className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                          <span className="text-lg font-semibold">{course.rating.toFixed(1)}</span>
                          <span className="text-muted-foreground">({course.totalRatings} reviews)</span>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-center py-8 text-muted-foreground">
                          <Star className="w-12 h-12 mx-auto mb-4" />
                          <p>Course reviews will be displayed here</p>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </motion.div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="sticky top-8"
              >
                <Card className="glass border-primary/20">
                  <CardContent className="p-6 space-y-6">
                    {/* Price */}
                    <div className="text-center">
                      <div className="space-y-2">
                        {course.originalPrice && course.originalPrice > course.price && (
                          <p className="text-lg text-muted-foreground line-through">
                            ${course.originalPrice.toFixed(2)}
                          </p>
                        )}
                        <p className="text-3xl font-bold text-primary">
                          {formatPrice(course.price)}
                        </p>
                        {course.originalPrice && course.originalPrice > course.price && (
                          <Badge className="bg-green-100 text-green-800">
                            Save ${(course.originalPrice - course.price).toFixed(2)}
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Enroll Button */}
                    <Button
                      className="w-full"
                      size="lg"
                      onClick={handleEnroll}
                      disabled={isEnrolling || isEnrolled}
                    >
                      {isEnrolling ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Enrolling...
                        </>
                      ) : isEnrolled ? (
                        <>
                          <Play className="w-4 h-4 mr-2" />
                          Continue Learning
                        </>
                      ) : (
                        <>
                          <BookOpen className="w-4 h-4 mr-2" />
                          Enroll Now
                        </>
                      )}
                    </Button>

                    {/* Course Info */}
                    <div className="space-y-4 pt-4 border-t">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Duration</span>
                        <span className="font-medium">{formatDuration(course.duration)}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Lessons</span>
                        <span className="font-medium">{course.totalLessons}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Level</span>
                        <Badge className={getLevelColor(course.level)} variant="secondary">
                          {course.level}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Language</span>
                        <span className="font-medium">{course.language}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Students</span>
                        <span className="font-medium">{course.totalStudents.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Last Updated</span>
                        <span className="font-medium">
                          {new Date(course.lastUpdated).toLocaleDateString()}
                        </span>
                      </div>
                    </div>

                    {/* Skills */}
                    <div className="pt-4 border-t">
                      <h4 className="font-medium mb-3">Skills You'll Gain</h4>
                      <div className="flex flex-wrap gap-2">
                        {course.skills.slice(0, 6).map((skill) => (
                          <Badge key={skill} variant="outline" className="text-xs">
                            {skill}
                          </Badge>
                        ))}
                        {course.skills.length > 6 && (
                          <Badge variant="outline" className="text-xs">
                            +{course.skills.length - 6} more
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

'use client'

// Debug Admin Dashboard - No authentication required for testing purposes
import { DebugNavigation } from '@/components/debug/debug-navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Database, 
  Users, 
  Building2, 
  Briefcase, 
  FileText,
  Settings,
  BarChart3,
  Shield,
  ArrowRight
} from 'lucide-react'
import Link from 'next/link'

export default function DebugAdminDashboard() {
  const adminTools = [
    {
      title: 'Database Admin',
      description: 'Monitor and manage your database collections, view statistics, and perform data operations',
      icon: Database,
      href: '/debugadmin/database',
      color: 'bg-blue-500',
      features: [
        'Real-time database statistics',
        'Collection data browsing',
        'Data export and cleanup',
        'Relationship analysis'
      ]
    },
    {
      title: 'User Management',
      description: 'Manage user accounts, roles, and permissions across the platform',
      icon: Users,
      href: '/debugadmin/users',
      color: 'bg-green-500',
      features: [
        'User account management',
        'Role and permission control',
        'Account verification',
        'Activity monitoring'
      ],
      comingSoon: true
    },
    {
      title: 'Company Management',
      description: 'Oversee company profiles, verification status, and subscription management',
      icon: Building2,
      href: '/debugadmin/companies',
      color: 'bg-purple-500',
      features: [
        'Company profile oversight',
        'Verification management',
        'Subscription tracking',
        'Performance analytics'
      ]
    },
    {
      title: 'Job Management',
      description: 'Monitor job postings, manage categories, and track application metrics',
      icon: Briefcase,
      href: '/debugadmin/jobs',
      color: 'bg-orange-500',
      features: [
        'Job posting oversight',
        'Category management',
        'Application tracking',
        'Performance metrics'
      ],
      comingSoon: true
    },
    {
      title: 'System Analytics',
      description: 'View comprehensive analytics, reports, and system performance metrics',
      icon: BarChart3,
      href: '/debugadmin/analytics',
      color: 'bg-red-500',
      features: [
        'Platform analytics',
        'Performance reports',
        'User engagement metrics',
        'Revenue tracking'
      ],
      comingSoon: true
    },
    {
      title: 'System Logs',
      description: 'Monitor application logs, error tracking, and system events',
      icon: FileText,
      href: '/debugadmin/logs',
      color: 'bg-orange-500',
      features: [
        'Error log monitoring',
        'System event tracking',
        'Log filtering and search',
        'Real-time log streaming'
      ]
    },
    {
      title: 'System Settings',
      description: 'Configure platform settings, integrations, and system-wide preferences',
      icon: Settings,
      href: '/debugadmin/settings',
      color: 'bg-gray-500',
      features: [
        'Platform configuration',
        'Integration management',
        'Security settings',
        'System preferences'
      ],
      comingSoon: true
    }
  ]

  return (
    <div className="min-h-screen bg-background">
      <DebugNavigation />
      <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Shield className="h-8 w-8" />
            Debug Admin Dashboard
          </h1>
          <p className="text-muted-foreground">
            Debug and monitor your job portal platform
          </p>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Platform Status</CardTitle>
            <div className="h-2 w-2 bg-green-500 rounded-full"></div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">Online</div>
            <p className="text-xs text-muted-foreground">All systems operational</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Database</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">Connected</div>
            <p className="text-xs text-muted-foreground">MongoDB Atlas</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Debug Tools</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">6</div>
            <p className="text-xs text-muted-foreground">Available modules</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Features</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">1</div>
            <p className="text-xs text-muted-foreground">Database Admin ready</p>
          </CardContent>
        </Card>
      </div>

      {/* Admin Tools Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {adminTools.map((tool) => {
          const Icon = tool.icon
          return (
            <Card key={tool.title} className="relative overflow-hidden">
              {tool.comingSoon && (
                <div className="absolute top-2 right-2 z-10">
                  <span className="bg-yellow-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full">
                    Coming Soon
                  </span>
                </div>
              )}
              
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${tool.color}`}>
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{tool.title}</CardTitle>
                  </div>
                </div>
                <CardDescription className="text-sm">
                  {tool.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  {tool.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="h-1.5 w-1.5 bg-current rounded-full"></div>
                      {feature}
                    </div>
                  ))}
                </div>
                
                {tool.comingSoon ? (
                  <Button disabled className="w-full">
                    Coming Soon
                  </Button>
                ) : (
                  <Link href={tool.href}>
                    <Button className="w-full group">
                      Access {tool.title}
                      <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                )}
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common debug and administrative tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link href="/debugadmin/database">
              <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
                <Database className="h-6 w-6" />
                <div className="text-center">
                  <div className="font-medium">View Database</div>
                  <div className="text-xs text-muted-foreground">Monitor collections and data</div>
                </div>
              </Button>
            </Link>

            <Button variant="outline" disabled className="w-full h-auto p-4 flex flex-col items-center gap-2">
              <Users className="h-6 w-6" />
              <div className="text-center">
                <div className="font-medium">Manage Users</div>
                <div className="text-xs text-muted-foreground">Coming soon</div>
              </div>
            </Button>

            <Button variant="outline" disabled className="w-full h-auto p-4 flex flex-col items-center gap-2">
              <BarChart3 className="h-6 w-6" />
              <div className="text-center">
                <div className="font-medium">View Analytics</div>
                <div className="text-xs text-muted-foreground">Coming soon</div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
      </div>
    </div>
  )
}

// app/debugadmin/database/page.tsx
'use client'

// Database Admin - No authentication required for testing purposes
import React, { useState, useEffect } from 'react'
import { DebugNavigation } from '@/components/debug/debug-navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Database,
  Users,
  Building2,
  UserCheck,
  Briefcase,
  FileText,
  Search,
  RefreshCw,
  Eye,
  Calendar,
  TrendingUp,
  Activity,
  Download,
  Trash2,
  Settings,
  AlertTriangle
} from 'lucide-react'

interface DatabaseStats {
  collections: {
    users: {
      total: number
      byRole: Record<string, number>
      active: number
      verified: number
      recent: number
    }
    companies: {
      total: number
      bySize: Record<string, number>
      active: number
      verified: number
      recent: number
    }
    clients: {
      total: number
      active: number
      public: number
      recent: number
    }
    jobs: {
      total: number
      active: number
      byType: Record<string, number>
      byLevel: Record<string, number>
      recent: number
    }
    applications: {
      total: number
      byStatus: Record<string, number>
      recent: number
    }
  }
  relationships: {
    usersWithCompanies: number
    usersWithClients: number
    companiesWithJobs: number
    jobsWithApplications: number
  }
  systemHealth: {
    totalDocuments: number
    averageResponseTime: number
    lastUpdated: string
  }
}

interface CollectionData {
  data: any[]
  total: number
  page: number
  limit: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export default function DatabaseAdminPage() {
  // Simple test version
  return (
    <div className="min-h-screen bg-background">
      <DebugNavigation />
      <div className="container mx-auto p-6 space-y-6">
        <h1 className="text-3xl font-bold">Database Admin</h1>
        <p>Database administration interface</p>
      </div>
    </div>
  )
}

// Complex version (temporarily disabled)
function DatabaseAdminPageComplex() {
  const [stats, setStats] = useState<DatabaseStats | null>(null)
  const [collectionData, setCollectionData] = useState<Record<string, CollectionData>>({})
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [activeCollection, setActiveCollection] = useState('users')
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)

  const collections = [
    { id: 'users', name: 'Users', icon: Users, color: 'bg-blue-500' },
    { id: 'companies', name: 'Companies', icon: Building2, color: 'bg-green-500' },
    { id: 'clients', name: 'Clients', icon: UserCheck, color: 'bg-purple-500' },
    { id: 'jobs', name: 'Jobs', icon: Briefcase, color: 'bg-orange-500' },
    { id: 'applications', name: 'Applications', icon: FileText, color: 'bg-red-500' }
  ]

  useEffect(() => {
    fetchDatabaseStats()
    fetchCollectionData('users')
  }, [])

  const fetchDatabaseStats = async () => {
    try {
      setRefreshing(true)
      const response = await fetch('/api/v1/admin/database')
      if (response.ok) {
        const result = await response.json()
        setStats(result.data)
      } else {
        console.error('Failed to fetch database stats')
      }
    } catch (error) {
      console.error('Error fetching database stats:', error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const fetchCollectionData = async (collection: string, page = 1, search = '') => {
    try {
      const response = await fetch('/api/v1/admin/database', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          collection,
          page,
          limit: 20,
          search: search || undefined
        })
      })

      if (response.ok) {
        const result = await response.json()
        setCollectionData(prev => ({
          ...prev,
          [collection]: result.data
        }))
      } else {
        console.error(`Failed to fetch ${collection} data`)
      }
    } catch (error) {
      console.error(`Error fetching ${collection} data:`, error)
    }
  }

  const handleCollectionChange = (collection: string) => {
    setActiveCollection(collection)
    setCurrentPage(1)
    setSearchTerm('')
    if (!collectionData[collection]) {
      fetchCollectionData(collection)
    }
  }

  const handleSearch = () => {
    setCurrentPage(1)
    fetchCollectionData(activeCollection, 1, searchTerm)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchCollectionData(activeCollection, page, searchTerm)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const renderStatsCard = (title: string, value: number, icon: React.ComponentType<any>, color: string, subtitle?: string) => {
    const Icon = icon
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
          <div className={`p-2 rounded-md ${color}`}>
            <Icon className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{value.toLocaleString()}</div>
          {subtitle && <p className="text-xs text-muted-foreground">{subtitle}</p>}
        </CardContent>
      </Card>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-6 w-6 animate-spin" />
          <span>Loading database information...</span>
        </div>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Database className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-xl font-semibold mb-2">Unable to load database information</h2>
          <p className="text-muted-foreground mb-4">There was an error connecting to the database.</p>
          <Button onClick={fetchDatabaseStats}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <DebugNavigation />
      <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Database className="h-8 w-8" />
            Database Admin
          </h1>
          <p className="text-muted-foreground">
            Monitor and manage your application database
          </p>
        </div>
        <Button 
          onClick={fetchDatabaseStats} 
          disabled={refreshing}
          variant="outline"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* System Health */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            System Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {stats.systemHealth.totalDocuments.toLocaleString()}
              </div>
              <div className="text-sm text-muted-foreground">Total Documents</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {stats.systemHealth.averageResponseTime}ms
              </div>
              <div className="text-sm text-muted-foreground">Response Time</div>
            </div>
            <div className="text-center">
              <div className="text-sm font-medium">Last Updated</div>
              <div className="text-xs text-muted-foreground">
                {formatDate(stats.systemHealth.lastUpdated)}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Collection Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {renderStatsCard(
          'Users', 
          stats.collections.users.total, 
          Users, 
          'bg-blue-500',
          `${stats.collections.users.active} active`
        )}
        {renderStatsCard(
          'Companies', 
          stats.collections.companies.total, 
          Building2, 
          'bg-green-500',
          `${stats.collections.companies.active} active`
        )}
        {renderStatsCard(
          'Clients', 
          stats.collections.clients.total, 
          UserCheck, 
          'bg-purple-500',
          `${stats.collections.clients.active} active`
        )}
        {renderStatsCard(
          'Jobs', 
          stats.collections.jobs.total, 
          Briefcase, 
          'bg-orange-500',
          `${stats.collections.jobs.active} active`
        )}
        {renderStatsCard(
          'Applications', 
          stats.collections.applications.total, 
          FileText, 
          'bg-red-500',
          `${stats.collections.applications.recent} recent`
        )}
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              User Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>By Role</span>
                </div>
                {Object.entries(stats.collections.users.byRole).map(([role, count]) => (
                  <div key={role} className="flex justify-between items-center py-1">
                    <span className="capitalize">{role.replace('_', ' ')}</span>
                    <Badge variant="outline">{count}</Badge>
                  </div>
                ))}
              </div>
              <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                <div className="text-center">
                  <div className="text-lg font-semibold text-green-600">
                    {stats.collections.users.verified}
                  </div>
                  <div className="text-xs text-muted-foreground">Email Verified</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-blue-600">
                    {stats.collections.users.recent}
                  </div>
                  <div className="text-xs text-muted-foreground">Recent (7 days)</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Company Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Company Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>By Size</span>
                </div>
                {Object.entries(stats.collections.companies.bySize).map(([size, count]) => (
                  <div key={size} className="flex justify-between items-center py-1">
                    <span className="capitalize">{size}</span>
                    <Badge variant="outline">{count}</Badge>
                  </div>
                ))}
              </div>
              <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                <div className="text-center">
                  <div className="text-lg font-semibold text-green-600">
                    {stats.collections.companies.verified}
                  </div>
                  <div className="text-xs text-muted-foreground">Verified</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-blue-600">
                    {stats.collections.companies.recent}
                  </div>
                  <div className="text-xs text-muted-foreground">Recent (7 days)</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Job Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Briefcase className="h-5 w-5" />
              Job Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>By Type</span>
                </div>
                {Object.entries(stats.collections.jobs.byType).map(([type, count]) => (
                  <div key={type} className="flex justify-between items-center py-1">
                    <span className="capitalize">{type.replace('-', ' ')}</span>
                    <Badge variant="outline">{count}</Badge>
                  </div>
                ))}
              </div>
              <div>
                <div className="flex justify-between text-sm mb-1 mt-4">
                  <span>By Level</span>
                </div>
                {Object.entries(stats.collections.jobs.byLevel).map(([level, count]) => (
                  <div key={level} className="flex justify-between items-center py-1">
                    <span className="capitalize">{level}</span>
                    <Badge variant="secondary">{count}</Badge>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Application Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Application Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>By Status</span>
                </div>
                {Object.entries(stats.collections.applications.byStatus).map(([status, count]) => (
                  <div key={status} className="flex justify-between items-center py-1">
                    <span className="capitalize">{status.replace('_', ' ')}</span>
                    <Badge
                      variant={
                        status === 'offer_received' ? 'default' :
                        status === 'rejected' ? 'destructive' :
                        status === 'withdrawn' ? 'secondary' :
                        'outline'
                      }
                    >
                      {count}
                    </Badge>
                  </div>
                ))}
              </div>
              <div className="text-center pt-4 border-t">
                <div className="text-lg font-semibold text-blue-600">
                  {stats.collections.applications.recent}
                </div>
                <div className="text-xs text-muted-foreground">Recent Applications (7 days)</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Relationships Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Data Relationships
          </CardTitle>
          <CardDescription>
            Overview of how your data is connected
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {stats.relationships.usersWithCompanies}
              </div>
              <div className="text-sm text-muted-foreground">Users with Companies</div>
              <div className="text-xs text-muted-foreground mt-1">
                {((stats.relationships.usersWithCompanies / stats.collections.users.total) * 100).toFixed(1)}% of users
              </div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {stats.relationships.usersWithClients}
              </div>
              <div className="text-sm text-muted-foreground">Users with Client Profiles</div>
              <div className="text-xs text-muted-foreground mt-1">
                {((stats.relationships.usersWithClients / stats.collections.users.total) * 100).toFixed(1)}% of users
              </div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {stats.relationships.companiesWithJobs}
              </div>
              <div className="text-sm text-muted-foreground">Companies with Jobs</div>
              <div className="text-xs text-muted-foreground mt-1">
                {stats.collections.companies.total > 0 ? ((stats.relationships.companiesWithJobs / stats.collections.companies.total) * 100).toFixed(1) : 0}% of companies
              </div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {stats.relationships.jobsWithApplications}
              </div>
              <div className="text-sm text-muted-foreground">Jobs with Applications</div>
              <div className="text-xs text-muted-foreground mt-1">
                {stats.collections.jobs.total > 0 ? ((stats.relationships.jobsWithApplications / stats.collections.jobs.total) * 100).toFixed(1) : 0}% of jobs
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Collection Data Viewer */}
      <Card>
        <CardHeader>
          <CardTitle>Collection Data Viewer</CardTitle>
          <CardDescription>
            Browse and search through your database collections
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeCollection} onValueChange={handleCollectionChange}>
            <TabsList className="grid w-full grid-cols-5">
              {collections.map((collection) => {
                const Icon = collection.icon
                return (
                  <TabsTrigger key={collection.id} value={collection.id} className="flex items-center gap-2">
                    <Icon className="h-4 w-4" />
                    {collection.name}
                  </TabsTrigger>
                )
              })}
            </TabsList>

            {/* Search Bar */}
            <div className="flex items-center space-x-2 mt-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={`Search ${activeCollection}...`}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="pl-10"
                />
              </div>
              <Button onClick={handleSearch}>
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            </div>

            {/* Collection Content */}
            {collections.map((collection) => (
              <TabsContent key={collection.id} value={collection.id} className="mt-6">
                {collectionData[collection.id] ? (
                  <CollectionDataTable
                    collection={collection.id}
                    data={collectionData[collection.id]}
                    onPageChange={handlePageChange}
                    currentPage={currentPage}
                  />
                ) : (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                    Loading {collection.name} data...
                  </div>
                )}
              </TabsContent>
            ))}
          </Tabs>
        </CardContent>
      </Card>

      {/* Data Management Tools */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Data Management Tools
          </CardTitle>
          <CardDescription>
            Export, cleanup, and manage your database data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataManagementTools onRefresh={fetchDatabaseStats} />
        </CardContent>
      </Card>
      </div>
    </div>
  )
}

// Data Management Tools Component
function DataManagementTools({ onRefresh }: { onRefresh: () => void }) {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)

  const handleManagementAction = async (action: string, collection?: string, options: any = {}) => {
    try {
      setLoading(true)
      setResult(null)

      const response = await fetch('/api/v1/admin/database/manage', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action,
          collection,
          options
        })
      })

      if (response.ok) {
        const data = await response.json()
        setResult(data.data)

        // Refresh stats after cleanup operations
        if (action === 'cleanup' && !options.dryRun) {
          setTimeout(onRefresh, 1000)
        }
      } else {
        const error = await response.json()
        setResult({ error: error.error?.message || 'Operation failed' })
      }
    } catch (error) {
      console.error('Management action failed:', error)
      setResult({ error: 'Operation failed' })
    } finally {
      setLoading(false)
    }
  }

  const exportData = (collection?: string) => {
    handleManagementAction('export', collection, { format: 'json', limit: 1000 })
  }

  const cleanupTestData = (dryRun = true) => {
    handleManagementAction('cleanup', undefined, { dryRun, olderThanDays: 0 })
  }

  const backupMetadata = () => {
    handleManagementAction('backup', undefined, { includeIndexes: false })
  }

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Button
          onClick={() => exportData()}
          disabled={loading}
          variant="outline"
          className="h-auto p-4 flex flex-col items-center gap-2"
        >
          <Download className="h-6 w-6" />
          <div className="text-center">
            <div className="font-medium">Export All Data</div>
            <div className="text-xs text-muted-foreground">Download data summary</div>
          </div>
        </Button>

        <Button
          onClick={() => cleanupTestData(true)}
          disabled={loading}
          variant="outline"
          className="h-auto p-4 flex flex-col items-center gap-2"
        >
          <Eye className="h-6 w-6" />
          <div className="text-center">
            <div className="font-medium">Preview Cleanup</div>
            <div className="text-xs text-muted-foreground">See what would be cleaned</div>
          </div>
        </Button>

        <Button
          onClick={() => backupMetadata()}
          disabled={loading}
          variant="outline"
          className="h-auto p-4 flex flex-col items-center gap-2"
        >
          <Database className="h-6 w-6" />
          <div className="text-center">
            <div className="font-medium">Backup Metadata</div>
            <div className="text-xs text-muted-foreground">Get collection info</div>
          </div>
        </Button>
      </div>

      {/* Cleanup Actions */}
      <div className="border rounded-lg p-4 bg-orange-50 dark:bg-orange-950/20">
        <div className="flex items-center gap-2 mb-3">
          <AlertTriangle className="h-5 w-5 text-orange-600" />
          <h3 className="font-medium text-orange-800 dark:text-orange-200">Cleanup Operations</h3>
        </div>
        <p className="text-sm text-orange-700 dark:text-orange-300 mb-4">
          These operations will permanently delete test data. Use with caution.
        </p>
        <div className="flex gap-2">
          <Button
            onClick={() => cleanupTestData(false)}
            disabled={loading}
            variant="destructive"
            size="sm"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Clean Test Data
          </Button>
        </div>
      </div>

      {/* Collection-specific exports */}
      <div>
        <h3 className="font-medium mb-3">Export by Collection</h3>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
          {['users', 'companies', 'clients', 'jobs', 'applications'].map((collection) => (
            <Button
              key={collection}
              onClick={() => exportData(collection)}
              disabled={loading}
              variant="outline"
              size="sm"
            >
              <Download className="h-3 w-3 mr-1" />
              {collection}
            </Button>
          ))}
        </div>
      </div>

      {/* Results Display */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
          Processing...
        </div>
      )}

      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Operation Result</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-muted p-4 rounded-lg text-sm overflow-auto max-h-96">
              {JSON.stringify(result, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Collection Data Table Component
function CollectionDataTable({
  collection,
  data,
  onPageChange,
  currentPage
}: {
  collection: string
  data: CollectionData
  onPageChange: (page: number) => void
  currentPage: number
}) {
  const renderUserRow = (user: any) => (
    <tr key={user._id} className="border-b hover:bg-muted/50">
      <td className="px-4 py-3 font-mono text-xs">{user._id}</td>
      <td className="px-4 py-3">{user.email}</td>
      <td className="px-4 py-3">
        <Badge variant={user.role === 'company_admin' ? 'default' : 'secondary'}>
          {user.role}
        </Badge>
      </td>
      <td className="px-4 py-3">{user.profile?.firstName} {user.profile?.lastName}</td>
      <td className="px-4 py-3">
        <Badge variant={user.isActive ? 'default' : 'destructive'}>
          {user.isActive ? 'Active' : 'Inactive'}
        </Badge>
      </td>
      <td className="px-4 py-3">
        <Badge variant={user.isEmailVerified ? 'default' : 'secondary'}>
          {user.isEmailVerified ? 'Verified' : 'Unverified'}
        </Badge>
      </td>
      <td className="px-4 py-3 text-sm text-muted-foreground">
        {new Date(user.createdAt).toLocaleDateString()}
      </td>
    </tr>
  )

  const renderCompanyRow = (company: any) => (
    <tr key={company._id} className="border-b hover:bg-muted/50">
      <td className="px-4 py-3 font-mono text-xs">{company._id}</td>
      <td className="px-4 py-3 font-medium">{company.name}</td>
      <td className="px-4 py-3">{company.slug}</td>
      <td className="px-4 py-3">
        <Badge variant="outline">{company.size}</Badge>
      </td>
      <td className="px-4 py-3">{company.industry?.join(', ')}</td>
      <td className="px-4 py-3">
        <Badge variant={company.isActive ? 'default' : 'destructive'}>
          {company.isActive ? 'Active' : 'Inactive'}
        </Badge>
      </td>
      <td className="px-4 py-3 text-sm text-muted-foreground">
        {new Date(company.createdAt).toLocaleDateString()}
      </td>
    </tr>
  )

  const renderClientRow = (client: any) => (
    <tr key={client._id} className="border-b hover:bg-muted/50">
      <td className="px-4 py-3 font-mono text-xs">{client._id}</td>
      <td className="px-4 py-3">{client.user?.email || 'N/A'}</td>
      <td className="px-4 py-3">{client.headline || 'No headline'}</td>
      <td className="px-4 py-3">
        <Badge variant={client.isActive ? 'default' : 'destructive'}>
          {client.isActive ? 'Active' : 'Inactive'}
        </Badge>
      </td>
      <td className="px-4 py-3">
        <Badge variant={client.isPublic ? 'default' : 'secondary'}>
          {client.isPublic ? 'Public' : 'Private'}
        </Badge>
      </td>
      <td className="px-4 py-3 text-sm text-muted-foreground">
        {new Date(client.createdAt).toLocaleDateString()}
      </td>
    </tr>
  )

  const renderJobRow = (job: any) => (
    <tr key={job._id} className="border-b hover:bg-muted/50">
      <td className="px-4 py-3 font-mono text-xs">{job._id}</td>
      <td className="px-4 py-3 font-medium">{job.title}</td>
      <td className="px-4 py-3">{job.companyId?.name || 'N/A'}</td>
      <td className="px-4 py-3">
        <Badge variant="outline">{job.type}</Badge>
      </td>
      <td className="px-4 py-3">
        <Badge variant="secondary">{job.level}</Badge>
      </td>
      <td className="px-4 py-3">
        <Badge variant={job.isActive ? 'default' : 'destructive'}>
          {job.isActive ? 'Active' : 'Inactive'}
        </Badge>
      </td>
      <td className="px-4 py-3 text-sm text-muted-foreground">
        {new Date(job.createdAt).toLocaleDateString()}
      </td>
    </tr>
  )

  const renderApplicationRow = (application: any) => (
    <tr key={application._id} className="border-b hover:bg-muted/50">
      <td className="px-4 py-3 font-mono text-xs">{application._id}</td>
      <td className="px-4 py-3">{application.client?.user?.email || 'N/A'}</td>
      <td className="px-4 py-3">{application.job?.title || 'N/A'}</td>
      <td className="px-4 py-3">
        <Badge
          variant={
            application.status === 'offer_received' ? 'default' :
            application.status === 'rejected' ? 'destructive' :
            application.status === 'withdrawn' ? 'secondary' :
            'outline'
          }
        >
          {application.status.replace('_', ' ')}
        </Badge>
      </td>
      <td className="px-4 py-3 text-sm text-muted-foreground">
        {new Date(application.createdAt).toLocaleDateString()}
      </td>
    </tr>
  )

  const getTableHeaders = () => {
    switch (collection) {
      case 'users':
        return ['ID', 'Email', 'Role', 'Name', 'Status', 'Verified', 'Created']
      case 'companies':
        return ['ID', 'Name', 'Slug', 'Size', 'Industry', 'Status', 'Created']
      case 'clients':
        return ['ID', 'User Email', 'Headline', 'Active', 'Public', 'Created']
      case 'jobs':
        return ['ID', 'Title', 'Company', 'Type', 'Level', 'Status', 'Created']
      case 'applications':
        return ['ID', 'Applicant', 'Job', 'Status', 'Applied']
      default:
        return []
    }
  }

  const renderTableRow = (item: any) => {
    switch (collection) {
      case 'users':
        return renderUserRow(item)
      case 'companies':
        return renderCompanyRow(item)
      case 'clients':
        return renderClientRow(item)
      case 'jobs':
        return renderJobRow(item)
      case 'applications':
        return renderApplicationRow(item)
      default:
        return null
    }
  }

  return (
    <div className="space-y-4">
      {/* Data Summary */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Showing {data.data.length} of {data.total.toLocaleString()} {collection}
        </div>
        <div className="text-sm text-muted-foreground">
          Page {data.page} of {data.totalPages}
        </div>
      </div>

      {/* Data Table */}
      <div className="border rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-muted">
              <tr>
                {getTableHeaders().map((header) => (
                  <th key={header} className="px-4 py-3 text-left text-sm font-medium">
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {data.data.length > 0 ? (
                data.data.map(renderTableRow)
              ) : (
                <tr>
                  <td
                    colSpan={getTableHeaders().length}
                    className="px-4 py-8 text-center text-muted-foreground"
                  >
                    No {collection} found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {data.totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={!data.hasPrev}
          >
            Previous
          </Button>

          <div className="flex items-center space-x-1">
            {Array.from({ length: Math.min(5, data.totalPages) }, (_, i) => {
              const page = i + 1
              return (
                <Button
                  key={page}
                  variant={page === currentPage ? "default" : "outline"}
                  size="sm"
                  onClick={() => onPageChange(page)}
                >
                  {page}
                </Button>
              )
            })}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={!data.hasNext}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}

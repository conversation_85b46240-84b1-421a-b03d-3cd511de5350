import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { Course, CourseCategory } from '@/lib/models/course.model'
import { Types } from 'mongoose'

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()

    // Find or create categories
    let webDevCategory = await CourseCategory.findOne({ slug: 'web-development' })
    let dataScienceCategory = await CourseCategory.findOne({ slug: 'data-science' })

    if (!webDevCategory) {
      webDevCategory = await CourseCategory.create({
        name: 'Web Development',
        description: 'Learn modern web development technologies and frameworks',
        icon: '💻',
        color: '#3B82F6',
        slug: 'web-development',
        isActive: true,
        sortOrder: 1
      })
    }

    if (!dataScienceCategory) {
      dataScienceCategory = await CourseCategory.create({
        name: 'Data Science',
        description: 'Master data analysis, machine learning, and AI',
        icon: '📊',
        color: '#10B981',
        slug: 'data-science',
        isActive: true,
        sortOrder: 2
      })
    }

    // Create a dummy instructor ID
    const instructorId = new Types.ObjectId()

    // Check if courses already exist
    const existingPython = await Course.findOne({ slug: 'basics-of-python-for-beginners' })
    const existingReact = await Course.findOne({ slug: 'react-from-the-start' })

    const coursesToAdd = []

    if (!existingPython) {
      coursesToAdd.push({
        title: 'Basics of Python for Beginners',
        description: 'Start your programming journey with Python! This comprehensive beginner-friendly course covers Python fundamentals, data types, control structures, functions, and object-oriented programming. Perfect for complete beginners who want to learn programming from scratch.',
        shortDescription: 'Learn Python programming from scratch with hands-on exercises and real-world examples',
        slug: 'basics-of-python-for-beginners',
        thumbnail: 'https://images.unsplash.com/photo-1526379879527-8559ecfcaec0?w=800&h=600&fit=crop',
        previewVideo: 'https://example.com/preview-python-basics.mp4',
        categoryId: dataScienceCategory._id,
        instructorId: instructorId,
        level: 'beginner',
        duration: 420, // 7 hours
        price: 49.99,
        originalPrice: 79.99,
        currency: 'USD',
        language: 'English',
        tags: ['Python', 'Programming', 'Beginner', 'Fundamentals'],
        skills: ['Python Syntax', 'Variables and Data Types', 'Control Flow', 'Functions', 'Object-Oriented Programming'],
        requirements: ['No programming experience required', 'Computer with internet access', 'Willingness to learn'],
        whatYouWillLearn: [
          'Understand Python syntax and basic programming concepts',
          'Work with variables, data types, and operators',
          'Use control structures like loops and conditionals',
          'Create and use functions effectively',
          'Understand object-oriented programming basics',
          'Handle files and exceptions',
          'Build simple Python applications',
          'Debug and troubleshoot Python code'
        ],
        targetAudience: ['Complete programming beginners', 'Students wanting to learn Python', 'Career changers entering tech', 'Anyone interested in data science or web development'],
        isPublished: true,
        isFeatured: true,
        isPremium: false,
        rating: 4.6,
        totalRatings: 892,
        totalStudents: 5420,
        totalLessons: 35,
        lastUpdated: new Date()
      })
    }

    if (!existingReact) {
      coursesToAdd.push({
        title: 'React from the Start',
        description: 'Master React.js from the ground up! This comprehensive course takes you from React basics to building complex, interactive web applications. Learn modern React patterns, hooks, state management, and best practices used by professional developers.',
        shortDescription: 'Build modern web applications with React.js from beginner to advanced level',
        slug: 'react-from-the-start',
        thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800&h=600&fit=crop',
        previewVideo: 'https://example.com/preview-react-start.mp4',
        categoryId: webDevCategory._id,
        instructorId: instructorId,
        level: 'beginner',
        duration: 540, // 9 hours
        price: 69.99,
        originalPrice: 99.99,
        currency: 'USD',
        language: 'English',
        tags: ['React', 'JavaScript', 'Frontend', 'Web Development', 'Hooks'],
        skills: ['React Components', 'JSX', 'State Management', 'Event Handling', 'React Hooks', 'API Integration'],
        requirements: ['Basic HTML and CSS knowledge', 'JavaScript fundamentals', 'Node.js installed on your computer'],
        whatYouWillLearn: [
          'Build React applications from scratch',
          'Understand React components and JSX',
          'Master React hooks (useState, useEffect, useContext)',
          'Handle events and user interactions',
          'Manage application state effectively',
          'Connect to APIs and handle data',
          'Implement routing with React Router',
          'Deploy React applications to production',
          'Follow React best practices and patterns',
          'Debug React applications efficiently'
        ],
        targetAudience: ['Web developers new to React', 'JavaScript developers', 'Frontend development students', 'Developers wanting to modernize their skills'],
        isPublished: true,
        isFeatured: true,
        isPremium: false,
        rating: 4.8,
        totalRatings: 1156,
        totalStudents: 7230,
        totalLessons: 42,
        lastUpdated: new Date()
      })
    }

    let message = ''
    let addedCourses = []

    if (coursesToAdd.length > 0) {
      const newCourses = await Course.insertMany(coursesToAdd)
      addedCourses = newCourses.map(course => ({
        id: course._id,
        title: course.title,
        slug: course.slug
      }))
      message = `Added ${coursesToAdd.length} new courses`
    } else {
      message = 'Both courses already exist in the database'
    }

    return NextResponse.json({
      success: true,
      data: {
        message,
        addedCourses,
        categories: [
          {
            id: webDevCategory._id,
            name: webDevCategory.name,
            slug: webDevCategory.slug
          },
          {
            id: dataScienceCategory._id,
            name: dataScienceCategory.name,
            slug: dataScienceCategory.slug
          }
        ]
      }
    })

  } catch (error) {
    console.error('Error seeding courses:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to seed courses' 
      },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { Course, CourseCategory } from '@/lib/models/course.model'

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    await connectToDatabase()

    const { slug } = params

    if (!slug) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Course slug is required' 
        },
        { status: 400 }
      )
    }

    // Find course by slug
    const course = await Course.findOne({ slug, isPublished: true })
      .populate('categoryId', 'name icon color slug')
      .lean()

    if (!course) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Course not found' 
        },
        { status: 404 }
      )
    }

    // Format course for frontend
    const formattedCourse = {
      _id: course._id,
      title: course.title,
      description: course.description,
      shortDescription: course.shortDescription,
      slug: course.slug,
      thumbnail: course.thumbnail,
      previewVideo: course.previewVideo,
      categoryId: course.categoryId,
      instructorId: course.instructorId,
      level: course.level,
      duration: course.duration,
      price: course.price,
      originalPrice: course.originalPrice,
      currency: course.currency,
      language: course.language,
      tags: course.tags,
      skills: course.skills,
      requirements: course.requirements,
      whatYouWillLearn: course.whatYouWillLearn,
      targetAudience: course.targetAudience,
      isPublished: course.isPublished,
      isFeatured: course.isFeatured,
      isPremium: course.isPremium,
      rating: course.rating,
      totalRatings: course.totalRatings,
      totalStudents: course.totalStudents,
      totalLessons: course.totalLessons,
      lastUpdated: course.lastUpdated,
      createdAt: course.createdAt,
      updatedAt: course.updatedAt
    }

    return NextResponse.json({
      success: true,
      data: {
        course: formattedCourse
      }
    })

  } catch (error) {
    console.error('Error fetching course:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch course' 
      },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    await connectToDatabase()

    const { slug } = params
    const body = await request.json()

    if (!slug) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Course slug is required' 
        },
        { status: 400 }
      )
    }

    // Find and update course
    const course = await Course.findOneAndUpdate(
      { slug },
      {
        ...body,
        lastUpdated: new Date()
      },
      {
        new: true,
        runValidators: true
      }
    )
    .populate('categoryId', 'name icon color slug')

    if (!course) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Course not found' 
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        course: {
          _id: course._id,
          title: course.title,
          description: course.description,
          shortDescription: course.shortDescription,
          slug: course.slug,
          thumbnail: course.thumbnail,
          previewVideo: course.previewVideo,
          categoryId: course.categoryId,
          instructorId: course.instructorId,
          level: course.level,
          duration: course.duration,
          price: course.price,
          originalPrice: course.originalPrice,
          currency: course.currency,
          language: course.language,
          tags: course.tags,
          skills: course.skills,
          requirements: course.requirements,
          whatYouWillLearn: course.whatYouWillLearn,
          targetAudience: course.targetAudience,
          isPublished: course.isPublished,
          isFeatured: course.isFeatured,
          isPremium: course.isPremium,
          rating: course.rating,
          totalRatings: course.totalRatings,
          totalStudents: course.totalStudents,
          totalLessons: course.totalLessons,
          lastUpdated: course.lastUpdated,
          createdAt: course.createdAt,
          updatedAt: course.updatedAt
        }
      }
    })

  } catch (error) {
    console.error('Error updating course:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to update course' 
      },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    await connectToDatabase()

    const { slug } = params

    if (!slug) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Course slug is required' 
        },
        { status: 400 }
      )
    }

    // Find and delete course
    const course = await Course.findOneAndDelete({ slug })

    if (!course) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Course not found' 
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        message: 'Course deleted successfully'
      }
    })

  } catch (error) {
    console.error('Error deleting course:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to delete course' 
      },
      { status: 500 }
    )
  }
}

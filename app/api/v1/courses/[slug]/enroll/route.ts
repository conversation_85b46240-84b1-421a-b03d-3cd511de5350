import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { Course, CourseEnrollment } from '@/lib/models/course.model'
import { User } from '@/lib/models/user.model'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function POST(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    await connectDB()

    // Get user session
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Find user
    const user = await User.findOne({ email: session.user.email })
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    // Find course
    const course = await Course.findOne({ slug: params.slug, isPublished: true })
    if (!course) {
      return NextResponse.json(
        { success: false, error: 'Course not found or not published' },
        { status: 404 }
      )
    }

    // Check if already enrolled
    const existingEnrollment = await CourseEnrollment.findOne({
      userId: user._id,
      courseId: course._id
    })

    if (existingEnrollment) {
      return NextResponse.json(
        { success: false, error: 'Already enrolled in this course' },
        { status: 400 }
      )
    }

    // Parse request body for payment information
    const body = await request.json()
    const { paymentMethod, paymentIntentId, amount } = body

    // Create enrollment
    const enrollment = new CourseEnrollment({
      userId: user._id,
      courseId: course._id,
      enrolledAt: new Date(),
      paymentStatus: amount > 0 ? 'completed' : 'free', // For now, assume payment is completed
      paymentMethod: paymentMethod || 'free',
      paymentIntentId,
      amountPaid: amount || 0,
      progress: 0,
      isActive: true
    })

    await enrollment.save()

    // Update course enrollment count
    await Course.findByIdAndUpdate(course._id, {
      $inc: { totalStudents: 1 }
    })

    // Populate enrollment data for response
    const populatedEnrollment = await CourseEnrollment.findById(enrollment._id)
      .populate('courseId', 'title slug thumbnail price')
      .populate('userId', 'name email')

    return NextResponse.json({
      success: true,
      data: {
        enrollment: populatedEnrollment,
        message: 'Successfully enrolled in course'
      }
    })

  } catch (error) {
    console.error('Course enrollment error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to enroll in course' },
      { status: 500 }
    )
  }
}

// Get enrollment status for a course
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    await connectDB()

    // Get user session
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Find user
    const user = await User.findOne({ email: session.user.email })
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    // Find course
    const course = await Course.findOne({ slug: params.slug })
    if (!course) {
      return NextResponse.json(
        { success: false, error: 'Course not found' },
        { status: 404 }
      )
    }

    // Check enrollment status
    const enrollment = await CourseEnrollment.findOne({
      userId: user._id,
      courseId: course._id
    }).populate('courseId', 'title slug thumbnail price')

    return NextResponse.json({
      success: true,
      data: {
        isEnrolled: !!enrollment,
        enrollment: enrollment || null
      }
    })

  } catch (error) {
    console.error('Get enrollment status error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get enrollment status' },
      { status: 500 }
    )
  }
}

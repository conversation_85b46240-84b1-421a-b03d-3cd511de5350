import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { Course, CourseSection } from '@/lib/models/course.model'
import { authMiddleware } from '@/lib/middleware/auth.middleware'

// Get all sections for a course
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    await connectDB()

    // Find course
    const course = await Course.findOne({ slug: params.slug })
    if (!course) {
      return NextResponse.json(
        { success: false, error: 'Course not found' },
        { status: 404 }
      )
    }

    // Get sections with lessons
    const sections = await CourseSection.find({ courseId: course._id })
      .populate({
        path: 'lessons',
        options: { sort: { sortOrder: 1 } }
      })
      .sort({ sortOrder: 1 })
      .lean()

    return NextResponse.json({
      success: true,
      data: {
        sections,
        courseId: course._id,
        courseTitle: course.title
      }
    })

  } catch (error) {
    console.error('Get course sections error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get course sections' },
      { status: 500 }
    )
  }
}

// Create a new section
export async function POST(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    await connectDB()

    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication required' },
        { status: authResult.status || 401 }
      )
    }

    // Check if user is admin or instructor
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'instructor') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Find course
    const course = await Course.findOne({ slug: params.slug })
    if (!course) {
      return NextResponse.json(
        { success: false, error: 'Course not found' },
        { status: 404 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { title, description, sortOrder } = body

    if (!title) {
      return NextResponse.json(
        { success: false, error: 'Section title is required' },
        { status: 400 }
      )
    }

    // Get next sort order if not provided
    let finalSortOrder = sortOrder
    if (finalSortOrder === undefined) {
      const lastSection = await CourseSection.findOne({ courseId: course._id })
        .sort({ sortOrder: -1 })
      finalSortOrder = lastSection ? lastSection.sortOrder + 1 : 1
    }

    // Create section
    const section = new CourseSection({
      courseId: course._id,
      title,
      description: description || '',
      sortOrder: finalSortOrder,
      isPublished: true
    })

    await section.save()

    // Populate the section with lessons
    const populatedSection = await CourseSection.findById(section._id)
      .populate({
        path: 'lessons',
        options: { sort: { sortOrder: 1 } }
      })

    return NextResponse.json({
      success: true,
      data: {
        section: populatedSection,
        message: 'Section created successfully'
      }
    })

  } catch (error) {
    console.error('Create course section error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create course section' },
      { status: 500 }
    )
  }
}

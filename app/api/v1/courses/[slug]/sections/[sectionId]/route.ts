import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { Course, CourseSection } from '@/lib/models/course.model'
import { authMiddleware } from '@/lib/middleware/auth.middleware'

// Get a specific section
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string; sectionId: string } }
) {
  try {
    await connectToDatabase()

    // Find course
    const course = await Course.findOne({ slug: params.slug })
    if (!course) {
      return NextResponse.json(
        { success: false, error: 'Course not found' },
        { status: 404 }
      )
    }

    // Get section with lessons
    const section = await CourseSection.findOne({ 
      _id: params.sectionId, 
      courseId: course._id 
    })
    .populate({
      path: 'lessons',
      options: { sort: { sortOrder: 1 } }
    })

    if (!section) {
      return NextResponse.json(
        { success: false, error: 'Section not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: { section }
    })

  } catch (error) {
    console.error('Get course section error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get course section' },
      { status: 500 }
    )
  }
}

// Update a section
export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string; sectionId: string } }
) {
  try {
    await connectToDatabase()

    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication required' },
        { status: authResult.status || 401 }
      )
    }

    // Check permissions
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'instructor') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Find course
    const course = await Course.findOne({ slug: params.slug })
    if (!course) {
      return NextResponse.json(
        { success: false, error: 'Course not found' },
        { status: 404 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { title, description, sortOrder, isPublished } = body

    // Update section
    const section = await CourseSection.findOneAndUpdate(
      { _id: params.sectionId, courseId: course._id },
      {
        ...(title && { title }),
        ...(description !== undefined && { description }),
        ...(sortOrder !== undefined && { sortOrder }),
        ...(isPublished !== undefined && { isPublished }),
        lastUpdated: new Date()
      },
      { new: true, runValidators: true }
    )
    .populate({
      path: 'lessons',
      options: { sort: { sortOrder: 1 } }
    })

    if (!section) {
      return NextResponse.json(
        { success: false, error: 'Section not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        section,
        message: 'Section updated successfully'
      }
    })

  } catch (error) {
    console.error('Update course section error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update course section' },
      { status: 500 }
    )
  }
}

// Delete a section
export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string; sectionId: string } }
) {
  try {
    await connectToDatabase()

    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication required' },
        { status: authResult.status || 401 }
      )
    }

    // Check permissions
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'instructor') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Find course
    const course = await Course.findOne({ slug: params.slug })
    if (!course) {
      return NextResponse.json(
        { success: false, error: 'Course not found' },
        { status: 404 }
      )
    }

    // Check if section has lessons
    const section = await CourseSection.findOne({ 
      _id: params.sectionId, 
      courseId: course._id 
    }).populate('lessons')

    if (!section) {
      return NextResponse.json(
        { success: false, error: 'Section not found' },
        { status: 404 }
      )
    }

    if (section.lessons && section.lessons.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete section with lessons. Please delete lessons first.' },
        { status: 400 }
      )
    }

    // Delete section
    await CourseSection.findByIdAndDelete(params.sectionId)

    return NextResponse.json({
      success: true,
      data: { message: 'Section deleted successfully' }
    })

  } catch (error) {
    console.error('Delete course section error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete course section' },
      { status: 500 }
    )
  }
}

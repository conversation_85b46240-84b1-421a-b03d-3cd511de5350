import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { Course, CourseSection, CourseLesson } from '@/lib/models/course.model'
import { authMiddleware } from '@/lib/middleware/auth.middleware'

// Get all lessons for a section
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string; sectionId: string } }
) {
  try {
    await connectToDatabase()

    // Find course
    const course = await Course.findOne({ slug: params.slug })
    if (!course) {
      return NextResponse.json(
        { success: false, error: 'Course not found' },
        { status: 404 }
      )
    }

    // Find section
    const section = await CourseSection.findOne({ 
      _id: params.sectionId, 
      courseId: course._id 
    })
    if (!section) {
      return NextResponse.json(
        { success: false, error: 'Section not found' },
        { status: 404 }
      )
    }

    // Get lessons
    const lessons = await CourseLesson.find({ 
      courseId: course._id, 
      sectionId: params.sectionId 
    })
    .sort({ sortOrder: 1 })
    .lean()

    return NextResponse.json({
      success: true,
      data: {
        lessons,
        sectionId: params.sectionId,
        sectionTitle: section.title,
        courseId: course._id
      }
    })

  } catch (error) {
    console.error('Get section lessons error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get section lessons' },
      { status: 500 }
    )
  }
}

// Create a new lesson
export async function POST(
  request: NextRequest,
  { params }: { params: { slug: string; sectionId: string } }
) {
  try {
    await connectToDatabase()

    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication required' },
        { status: authResult.status || 401 }
      )
    }

    // Check permissions
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'instructor') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Find course
    const course = await Course.findOne({ slug: params.slug })
    if (!course) {
      return NextResponse.json(
        { success: false, error: 'Course not found' },
        { status: 404 }
      )
    }

    // Find section
    const section = await CourseSection.findOne({ 
      _id: params.sectionId, 
      courseId: course._id 
    })
    if (!section) {
      return NextResponse.json(
        { success: false, error: 'Section not found' },
        { status: 404 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { 
      title, 
      description, 
      content, 
      videoUrl, 
      duration, 
      sortOrder,
      lessonType = 'video',
      isPreview = false,
      resources = []
    } = body

    if (!title) {
      return NextResponse.json(
        { success: false, error: 'Lesson title is required' },
        { status: 400 }
      )
    }

    // Get next sort order if not provided
    let finalSortOrder = sortOrder
    if (finalSortOrder === undefined) {
      const lastLesson = await CourseLesson.findOne({ 
        courseId: course._id, 
        sectionId: params.sectionId 
      }).sort({ sortOrder: -1 })
      finalSortOrder = lastLesson ? lastLesson.sortOrder + 1 : 1
    }

    // Create lesson
    const lesson = new CourseLesson({
      courseId: course._id,
      sectionId: params.sectionId,
      title,
      description: description || '',
      content: content || '',
      videoUrl: videoUrl || '',
      duration: duration || 0,
      lessonType,
      isPreview,
      resources,
      sortOrder: finalSortOrder,
      isPublished: true
    })

    await lesson.save()

    // Update course total lessons count
    await Course.findByIdAndUpdate(course._id, {
      $inc: { totalLessons: 1 }
    })

    return NextResponse.json({
      success: true,
      data: {
        lesson,
        message: 'Lesson created successfully'
      }
    })

  } catch (error) {
    console.error('Create lesson error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create lesson' },
      { status: 500 }
    )
  }
}

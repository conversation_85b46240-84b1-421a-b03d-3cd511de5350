import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { Course, CourseLesson } from '@/lib/models/course.model'
import { Quiz } from '@/lib/models/quiz.model'
import { authMiddleware } from '@/lib/middleware/auth.middleware'

// Get quiz for a lesson
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string; sectionId: string; lessonId: string } }
) {
  try {
    await connectDB()

    // Find course
    const course = await Course.findOne({ slug: params.slug })
    if (!course) {
      return NextResponse.json(
        { success: false, error: 'Course not found' },
        { status: 404 }
      )
    }

    // Find lesson
    const lesson = await CourseLesson.findOne({ 
      _id: params.lessonId,
      courseId: course._id,
      sectionId: params.sectionId
    })

    if (!lesson) {
      return NextResponse.json(
        { success: false, error: 'Lesson not found' },
        { status: 404 }
      )
    }

    // Get quiz if exists
    let quiz = null
    if (lesson.quiz && lesson.quiz.quizId) {
      quiz = await Quiz.findById(lesson.quiz.quizId)
        .populate('categoryId', 'name')
        .populate('questions')
    }

    return NextResponse.json({
      success: true,
      data: {
        quiz,
        lessonQuiz: lesson.quiz,
        hasQuiz: !!quiz
      }
    })

  } catch (error) {
    console.error('Get lesson quiz error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get lesson quiz' },
      { status: 500 }
    )
  }
}

// Attach quiz to lesson
export async function POST(
  request: NextRequest,
  { params }: { params: { slug: string; sectionId: string; lessonId: string } }
) {
  try {
    await connectDB()

    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication required' },
        { status: authResult.status || 401 }
      )
    }

    // Check permissions
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'instructor') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Find course
    const course = await Course.findOne({ slug: params.slug })
    if (!course) {
      return NextResponse.json(
        { success: false, error: 'Course not found' },
        { status: 404 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { quizId, isRequired = false, passingScore = 70, maxAttempts = 3 } = body

    if (!quizId) {
      return NextResponse.json(
        { success: false, error: 'Quiz ID is required' },
        { status: 400 }
      )
    }

    // Verify quiz exists
    const quiz = await Quiz.findById(quizId)
    if (!quiz) {
      return NextResponse.json(
        { success: false, error: 'Quiz not found' },
        { status: 404 }
      )
    }

    // Update lesson with quiz
    const lesson = await CourseLesson.findOneAndUpdate(
      { 
        _id: params.lessonId,
        courseId: course._id,
        sectionId: params.sectionId
      },
      {
        quiz: {
          quizId,
          isRequired,
          passingScore,
          maxAttempts,
          attachedAt: new Date()
        },
        lastUpdated: new Date()
      },
      { new: true, runValidators: true }
    )

    if (!lesson) {
      return NextResponse.json(
        { success: false, error: 'Lesson not found' },
        { status: 404 }
      )
    }

    // Get populated quiz data
    const populatedQuiz = await Quiz.findById(quizId)
      .populate('categoryId', 'name')
      .populate('questions')

    return NextResponse.json({
      success: true,
      data: {
        lesson,
        quiz: populatedQuiz,
        message: 'Quiz attached to lesson successfully'
      }
    })

  } catch (error) {
    console.error('Attach quiz to lesson error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to attach quiz to lesson' },
      { status: 500 }
    )
  }
}

// Update quiz settings for lesson
export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string; sectionId: string; lessonId: string } }
) {
  try {
    await connectDB()

    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication required' },
        { status: authResult.status || 401 }
      )
    }

    // Check permissions
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'instructor') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Find course
    const course = await Course.findOne({ slug: params.slug })
    if (!course) {
      return NextResponse.json(
        { success: false, error: 'Course not found' },
        { status: 404 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { isRequired, passingScore, maxAttempts } = body

    // Update lesson quiz settings
    const lesson = await CourseLesson.findOneAndUpdate(
      { 
        _id: params.lessonId,
        courseId: course._id,
        sectionId: params.sectionId
      },
      {
        $set: {
          ...(isRequired !== undefined && { 'quiz.isRequired': isRequired }),
          ...(passingScore !== undefined && { 'quiz.passingScore': passingScore }),
          ...(maxAttempts !== undefined && { 'quiz.maxAttempts': maxAttempts }),
          lastUpdated: new Date()
        }
      },
      { new: true, runValidators: true }
    )

    if (!lesson) {
      return NextResponse.json(
        { success: false, error: 'Lesson not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        lesson,
        message: 'Quiz settings updated successfully'
      }
    })

  } catch (error) {
    console.error('Update lesson quiz settings error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update quiz settings' },
      { status: 500 }
    )
  }
}

// Remove quiz from lesson
export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string; sectionId: string; lessonId: string } }
) {
  try {
    await connectDB()

    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication required' },
        { status: authResult.status || 401 }
      )
    }

    // Check permissions
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'instructor') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Find course
    const course = await Course.findOne({ slug: params.slug })
    if (!course) {
      return NextResponse.json(
        { success: false, error: 'Course not found' },
        { status: 404 }
      )
    }

    // Remove quiz from lesson
    const lesson = await CourseLesson.findOneAndUpdate(
      { 
        _id: params.lessonId,
        courseId: course._id,
        sectionId: params.sectionId
      },
      {
        $unset: { quiz: 1 },
        lastUpdated: new Date()
      },
      { new: true, runValidators: true }
    )

    if (!lesson) {
      return NextResponse.json(
        { success: false, error: 'Lesson not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        lesson,
        message: 'Quiz removed from lesson successfully'
      }
    })

  } catch (error) {
    console.error('Remove quiz from lesson error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to remove quiz from lesson' },
      { status: 500 }
    )
  }
}

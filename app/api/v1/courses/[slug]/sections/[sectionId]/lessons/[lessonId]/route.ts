import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { Course, CourseSection, CourseLesson } from '@/lib/models/course.model'
import { authMiddleware } from '@/lib/middleware/auth.middleware'

// Get a specific lesson
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string; sectionId: string; lessonId: string } }
) {
  try {
    await connectToDatabase()

    // Find course
    const course = await Course.findOne({ slug: params.slug })
    if (!course) {
      return NextResponse.json(
        { success: false, error: 'Course not found' },
        { status: 404 }
      )
    }

    // Get lesson
    const lesson = await CourseLesson.findOne({ 
      _id: params.lessonId,
      courseId: course._id,
      sectionId: params.sectionId
    }).lean()

    if (!lesson) {
      return NextResponse.json(
        { success: false, error: 'Lesson not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: { lesson }
    })

  } catch (error) {
    console.error('Get lesson error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get lesson' },
      { status: 500 }
    )
  }
}

// Update a lesson
export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string; sectionId: string; lessonId: string } }
) {
  try {
    await connectToDatabase()

    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication required' },
        { status: authResult.status || 401 }
      )
    }

    // Check permissions
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'instructor') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Find course
    const course = await Course.findOne({ slug: params.slug })
    if (!course) {
      return NextResponse.json(
        { success: false, error: 'Course not found' },
        { status: 404 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { 
      title, 
      description, 
      content, 
      videoUrl, 
      duration, 
      sortOrder,
      lessonType,
      isPreview,
      isPublished,
      resources,
      quiz
    } = body

    // Update lesson
    const lesson = await CourseLesson.findOneAndUpdate(
      { 
        _id: params.lessonId,
        courseId: course._id,
        sectionId: params.sectionId
      },
      {
        ...(title && { title }),
        ...(description !== undefined && { description }),
        ...(content !== undefined && { content }),
        ...(videoUrl !== undefined && { videoUrl }),
        ...(duration !== undefined && { duration }),
        ...(sortOrder !== undefined && { sortOrder }),
        ...(lessonType && { lessonType }),
        ...(isPreview !== undefined && { isPreview }),
        ...(isPublished !== undefined && { isPublished }),
        ...(resources && { resources }),
        ...(quiz !== undefined && { quiz }),
        lastUpdated: new Date()
      },
      { new: true, runValidators: true }
    )

    if (!lesson) {
      return NextResponse.json(
        { success: false, error: 'Lesson not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        lesson,
        message: 'Lesson updated successfully'
      }
    })

  } catch (error) {
    console.error('Update lesson error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update lesson' },
      { status: 500 }
    )
  }
}

// Delete a lesson
export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string; sectionId: string; lessonId: string } }
) {
  try {
    await connectToDatabase()

    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication required' },
        { status: authResult.status || 401 }
      )
    }

    // Check permissions
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'instructor') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Find course
    const course = await Course.findOne({ slug: params.slug })
    if (!course) {
      return NextResponse.json(
        { success: false, error: 'Course not found' },
        { status: 404 }
      )
    }

    // Find and delete lesson
    const lesson = await CourseLesson.findOneAndDelete({ 
      _id: params.lessonId,
      courseId: course._id,
      sectionId: params.sectionId
    })

    if (!lesson) {
      return NextResponse.json(
        { success: false, error: 'Lesson not found' },
        { status: 404 }
      )
    }

    // Update course total lessons count
    await Course.findByIdAndUpdate(course._id, {
      $inc: { totalLessons: -1 }
    })

    return NextResponse.json({
      success: true,
      data: { message: 'Lesson deleted successfully' }
    })

  } catch (error) {
    console.error('Delete lesson error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete lesson' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { Course, CourseEnrollment, CourseSection, CourseLesson, CourseProgress } from '@/lib/models/course.model'
import { authMiddleware } from '@/lib/middleware/auth.middleware'

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    await connectDB()

    // Find course
    const course = await Course.findOne({ slug: params.slug })
    if (!course) {
      return NextResponse.json(
        { success: false, error: 'Course not found' },
        { status: 404 }
      )
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '30' // days

    // Calculate date range
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(endDate.getDate() - parseInt(timeRange))

    // Get course structure statistics
    const [
      totalSections,
      totalLessons,
      publishedSections,
      publishedLessons,
      totalEnrollments,
      activeEnrollments,
      completedEnrollments,
      totalRevenue,
      averageRating,
      totalRatings
    ] = await Promise.all([
      // Total sections
      CourseSection.countDocuments({ courseId: course._id }),
      
      // Total lessons
      CourseLesson.countDocuments({ courseId: course._id }),
      
      // Published sections
      CourseSection.countDocuments({ courseId: course._id, isPublished: true }),
      
      // Published lessons
      CourseLesson.countDocuments({ courseId: course._id, isPublished: true }),
      
      // Total enrollments
      CourseEnrollment.countDocuments({ courseId: course._id }),
      
      // Active enrollments (not completed)
      CourseEnrollment.countDocuments({ 
        courseId: course._id, 
        progress: { $lt: 100 },
        isActive: true 
      }),
      
      // Completed enrollments
      CourseEnrollment.countDocuments({ 
        courseId: course._id, 
        progress: 100 
      }),
      
      // Total revenue
      CourseEnrollment.aggregate([
        {
          $match: {
            courseId: course._id,
            paymentStatus: 'completed'
          }
        },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: '$amountPaid' }
          }
        }
      ]).then(result => result[0]?.totalRevenue || 0),
      
      // Average rating (from course model)
      Promise.resolve(course.rating || 0),
      
      // Total ratings (from course model)
      Promise.resolve(course.totalRatings || 0)
    ])

    // Get enrollment trends (last 30 days)
    const enrollmentTrends = await CourseEnrollment.aggregate([
      {
        $match: {
          courseId: course._id,
          enrolledAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$enrolledAt'
            }
          },
          enrollments: { $sum: 1 },
          revenue: { $sum: '$amountPaid' }
        }
      },
      {
        $sort: { '_id': 1 }
      }
    ])

    // Get completion rates by section
    const sectionStats = await CourseSection.aggregate([
      {
        $match: { courseId: course._id }
      },
      {
        $lookup: {
          from: 'courselessons',
          localField: '_id',
          foreignField: 'sectionId',
          as: 'lessons'
        }
      },
      {
        $addFields: {
          totalLessons: { $size: '$lessons' }
        }
      },
      {
        $lookup: {
          from: 'courseprogresses',
          let: { sectionId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$courseId', course._id] },
                    { $eq: ['$isCompleted', true] }
                  ]
                }
              }
            },
            {
              $lookup: {
                from: 'courselessons',
                localField: 'lessonId',
                foreignField: '_id',
                as: 'lesson'
              }
            },
            {
              $match: {
                'lesson.sectionId': { $eq: '$$sectionId' }
              }
            }
          ],
          as: 'completedLessons'
        }
      },
      {
        $addFields: {
          completedLessons: { $size: '$completedLessons' },
          completionRate: {
            $cond: {
              if: { $gt: ['$totalLessons', 0] },
              then: {
                $multiply: [
                  { $divide: [{ $size: '$completedLessons' }, '$totalLessons'] },
                  100
                ]
              },
              else: 0
            }
          }
        }
      },
      {
        $project: {
          title: 1,
          totalLessons: 1,
          completedLessons: 1,
          completionRate: 1,
          isPublished: 1
        }
      }
    ])

    // Get student progress distribution
    const progressDistribution = await CourseEnrollment.aggregate([
      {
        $match: { courseId: course._id }
      },
      {
        $bucket: {
          groupBy: '$progress',
          boundaries: [0, 25, 50, 75, 100, 101],
          default: 'other',
          output: {
            count: { $sum: 1 },
            students: { $push: '$userId' }
          }
        }
      }
    ])

    // Get recent student activity
    const recentActivity = await CourseProgress.aggregate([
      {
        $match: {
          courseId: course._id,
          lastAccessedAt: { $gte: startDate }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $lookup: {
          from: 'courselessons',
          localField: 'lessonId',
          foreignField: '_id',
          as: 'lesson'
        }
      },
      {
        $project: {
          userId: 1,
          lessonId: 1,
          isCompleted: 1,
          timeSpent: 1,
          lastAccessedAt: 1,
          'user.name': 1,
          'user.email': 1,
          'lesson.title': 1
        }
      },
      {
        $sort: { lastAccessedAt: -1 }
      },
      {
        $limit: 20
      }
    ])

    // Calculate completion rate
    const completionRate = totalEnrollments > 0 
      ? (completedEnrollments / totalEnrollments) * 100 
      : 0

    // Calculate average progress
    const averageProgress = await CourseEnrollment.aggregate([
      {
        $match: { courseId: course._id }
      },
      {
        $group: {
          _id: null,
          averageProgress: { $avg: '$progress' }
        }
      }
    ]).then(result => result[0]?.averageProgress || 0)

    // Get lesson performance
    const lessonPerformance = await CourseLesson.aggregate([
      {
        $match: { courseId: course._id }
      },
      {
        $lookup: {
          from: 'courseprogresses',
          localField: '_id',
          foreignField: 'lessonId',
          as: 'progress'
        }
      },
      {
        $addFields: {
          totalViews: { $size: '$progress' },
          completions: {
            $size: {
              $filter: {
                input: '$progress',
                cond: { $eq: ['$$this.isCompleted', true] }
              }
            }
          },
          averageTimeSpent: { $avg: '$progress.timeSpent' }
        }
      },
      {
        $addFields: {
          completionRate: {
            $cond: {
              if: { $gt: ['$totalViews', 0] },
              then: {
                $multiply: [
                  { $divide: ['$completions', '$totalViews'] },
                  100
                ]
              },
              else: 0
            }
          }
        }
      },
      {
        $project: {
          title: 1,
          lessonType: 1,
          duration: 1,
          totalViews: 1,
          completions: 1,
          completionRate: 1,
          averageTimeSpent: 1,
          isPublished: 1
        }
      },
      {
        $sort: { completionRate: -1 }
      }
    ])

    return NextResponse.json({
      success: true,
      data: {
        course: {
          _id: course._id,
          title: course.title,
          slug: course.slug,
          isPublished: course.isPublished,
          isFeatured: course.isFeatured,
          price: course.price,
          originalPrice: course.originalPrice
        },
        overview: {
          totalSections,
          totalLessons,
          publishedSections,
          publishedLessons,
          totalEnrollments,
          activeEnrollments,
          completedEnrollments,
          totalRevenue,
          averageRating,
          totalRatings,
          completionRate: Math.round(completionRate * 100) / 100,
          averageProgress: Math.round(averageProgress * 100) / 100
        },
        enrollmentTrends,
        sectionStats,
        progressDistribution,
        recentActivity,
        lessonPerformance,
        timeRange: parseInt(timeRange)
      }
    })

  } catch (error) {
    console.error('Get course stats error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get course statistics' },
      { status: 500 }
    )
  }
}

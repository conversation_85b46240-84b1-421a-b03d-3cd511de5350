import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { CourseCategory } from '@/lib/models/course.model'

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()

    // Get all active categories, sorted by sortOrder
    const categories = await CourseCategory.find({ isActive: true })
      .sort({ sortOrder: 1, name: 1 })
      .lean()

    // Format categories for frontend
    const formattedCategories = categories.map(category => ({
      id: category._id,
      name: category.name,
      description: category.description,
      icon: category.icon,
      color: category.color,
      slug: category.slug,
      isActive: category.isActive,
      sortOrder: category.sortOrder,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt
    }))

    return NextResponse.json({
      success: true,
      data: {
        categories: formattedCategories,
        total: formattedCategories.length
      }
    })

  } catch (error) {
    console.error('Error fetching course categories:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch course categories' 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()

    const body = await request.json()
    const { name, description, icon, color, slug } = body

    // Validate required fields
    if (!name || !description || !icon || !color || !slug) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Missing required fields: name, description, icon, color, slug' 
        },
        { status: 400 }
      )
    }

    // Check if slug already exists
    const existingCategory = await CourseCategory.findOne({ slug })
    if (existingCategory) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Category with this slug already exists' 
        },
        { status: 400 }
      )
    }

    // Get the next sort order
    const lastCategory = await CourseCategory.findOne({}, {}, { sort: { sortOrder: -1 } })
    const sortOrder = lastCategory ? lastCategory.sortOrder + 1 : 1

    // Create new category
    const category = new CourseCategory({
      name,
      description,
      icon,
      color,
      slug,
      sortOrder,
      isActive: true
    })

    await category.save()

    return NextResponse.json({
      success: true,
      data: {
        category: {
          id: category._id,
          name: category.name,
          description: category.description,
          icon: category.icon,
          color: category.color,
          slug: category.slug,
          isActive: category.isActive,
          sortOrder: category.sortOrder,
          createdAt: category.createdAt,
          updatedAt: category.updatedAt
        }
      }
    })

  } catch (error) {
    console.error('Error creating course category:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to create course category' 
      },
      { status: 500 }
    )
  }
}

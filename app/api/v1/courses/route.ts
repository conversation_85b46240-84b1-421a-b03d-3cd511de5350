import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { Course, CourseCategory } from '@/lib/models/course.model'

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()

    const { searchParams } = new URL(request.url)
    
    // Pagination
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '12')
    const skip = (page - 1) * limit

    // Sorting
    const sort = searchParams.get('sort') || 'createdAt'
    const order = searchParams.get('order') === 'asc' ? 1 : -1

    // Filters
    const category = searchParams.get('category')
    const level = searchParams.get('level')
    const price = searchParams.get('price')
    const rating = searchParams.get('rating')
    const duration = searchParams.get('duration')
    const language = searchParams.get('language')
    const search = searchParams.get('search')
    const featured = searchParams.get('featured') === 'true'
    const tags = searchParams.getAll('tags')

    // Build query
    const query: any = { isPublished: true }

    if (category) {
      query.categoryId = category
    }

    if (level) {
      query.level = level
    }

    if (price) {
      switch (price) {
        case 'free':
          query.price = 0
          break
        case 'paid':
          query.price = { $gt: 0, $lt: 100 }
          break
        case 'premium':
          query.price = { $gte: 100 }
          break
      }
    }

    if (rating) {
      query.rating = { $gte: parseFloat(rating) }
    }

    if (duration) {
      if (duration === '10+') {
        query.duration = { $gte: 10 * 60 } // 10+ hours in minutes
      } else {
        const [min, max] = duration.split('-').map(d => parseInt(d))
        if (max) {
          query.duration = { $gte: min * 60, $lte: max * 60 } // Convert hours to minutes
        } else {
          query.duration = { $gte: min * 60 }
        }
      }
    }

    if (language) {
      query.language = new RegExp(language, 'i')
    }

    if (search) {
      query.$or = [
        { title: new RegExp(search, 'i') },
        { description: new RegExp(search, 'i') },
        { shortDescription: new RegExp(search, 'i') },
        { tags: { $in: [new RegExp(search, 'i')] } },
        { skills: { $in: [new RegExp(search, 'i')] } }
      ]
    }

    if (featured) {
      query.isFeatured = true
    }

    if (tags.length > 0) {
      query.tags = { $in: tags }
    }

    // Execute query
    const [courses, total] = await Promise.all([
      Course.find(query)
        .populate('categoryId', 'name icon color slug')
        .sort({ [sort]: order })
        .skip(skip)
        .limit(limit)
        .lean(),
      Course.countDocuments(query)
    ])

    // Format courses for frontend
    const formattedCourses = courses.map(course => ({
      _id: course._id,
      title: course.title,
      description: course.description,
      shortDescription: course.shortDescription,
      slug: course.slug,
      thumbnail: course.thumbnail,
      previewVideo: course.previewVideo,
      categoryId: course.categoryId,
      instructorId: course.instructorId,
      level: course.level,
      duration: course.duration,
      price: course.price,
      originalPrice: course.originalPrice,
      currency: course.currency,
      language: course.language,
      tags: course.tags,
      skills: course.skills,
      requirements: course.requirements,
      whatYouWillLearn: course.whatYouWillLearn,
      targetAudience: course.targetAudience,
      isPublished: course.isPublished,
      isFeatured: course.isFeatured,
      isPremium: course.isPremium,
      rating: course.rating,
      totalRatings: course.totalRatings,
      totalStudents: course.totalStudents,
      totalLessons: course.totalLessons,
      lastUpdated: course.lastUpdated,
      createdAt: course.createdAt,
      updatedAt: course.updatedAt
    }))

    const pages = Math.ceil(total / limit)

    return NextResponse.json({
      success: true,
      data: {
        courses: formattedCourses,
        pagination: {
          page,
          limit,
          total,
          pages
        }
      }
    })

  } catch (error) {
    console.error('Error fetching courses:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch courses' 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()

    const body = await request.json()
    const {
      title,
      description,
      shortDescription,
      slug,
      thumbnail,
      previewVideo,
      categoryId,
      instructorId,
      level,
      duration,
      price,
      originalPrice,
      currency = 'USD',
      language = 'English',
      tags = [],
      skills = [],
      requirements = [],
      whatYouWillLearn = [],
      targetAudience = [],
      isPremium = false
    } = body

    // Validate required fields
    if (!title || !description || !shortDescription || !slug || !thumbnail || 
        !categoryId || !instructorId || !level || !duration || price === undefined) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Missing required fields' 
        },
        { status: 400 }
      )
    }

    // Check if slug already exists
    const existingCourse = await Course.findOne({ slug })
    if (existingCourse) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Course with this slug already exists' 
        },
        { status: 400 }
      )
    }

    // Verify category exists
    const category = await CourseCategory.findById(categoryId)
    if (!category) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid category ID' 
        },
        { status: 400 }
      )
    }

    // Create new course
    const course = new Course({
      title,
      description,
      shortDescription,
      slug,
      thumbnail,
      previewVideo,
      categoryId,
      instructorId,
      level,
      duration,
      price,
      originalPrice,
      currency,
      language,
      tags,
      skills,
      requirements,
      whatYouWillLearn,
      targetAudience,
      isPremium,
      isPublished: false, // New courses start as unpublished
      isFeatured: false,
      rating: 0,
      totalRatings: 0,
      totalStudents: 0,
      totalLessons: 0,
      lastUpdated: new Date()
    })

    await course.save()

    // Populate the course with category and instructor info
    await course.populate('categoryId', 'name icon color slug')
    await course.populate('instructorId', 'name email avatar')

    return NextResponse.json({
      success: true,
      data: {
        course: {
          _id: course._id,
          title: course.title,
          description: course.description,
          shortDescription: course.shortDescription,
          slug: course.slug,
          thumbnail: course.thumbnail,
          previewVideo: course.previewVideo,
          categoryId: course.categoryId,
          instructorId: course.instructorId,
          level: course.level,
          duration: course.duration,
          price: course.price,
          originalPrice: course.originalPrice,
          currency: course.currency,
          language: course.language,
          tags: course.tags,
          skills: course.skills,
          requirements: course.requirements,
          whatYouWillLearn: course.whatYouWillLearn,
          targetAudience: course.targetAudience,
          isPublished: course.isPublished,
          isFeatured: course.isFeatured,
          isPremium: course.isPremium,
          rating: course.rating,
          totalRatings: course.totalRatings,
          totalStudents: course.totalStudents,
          totalLessons: course.totalLessons,
          lastUpdated: course.lastUpdated,
          createdAt: course.createdAt,
          updatedAt: course.updatedAt
        }
      }
    })

  } catch (error) {
    console.error('Error creating course:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to create course' 
      },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { QuizQuestion } from '@/lib/models/quiz.model'
import { Types } from 'mongoose'

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    const body = await request.json()
    const { categoryId, count, excludeIds = [] } = body

    if (!categoryId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Category ID is required' 
        },
        { status: 400 }
      )
    }

    if (!count || count <= 0) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Valid count is required' 
        },
        { status: 400 }
      )
    }

    // Convert excludeIds to ObjectIds for MongoDB query
    const excludeObjectIds = excludeIds
      .filter(id => Types.ObjectId.isValid(id))
      .map(id => new Types.ObjectId(id))

    // Get additional random questions from the category, excluding already used ones
    const additionalQuestions = await QuizQuestion.aggregate([
      {
        $match: {
          categoryId: new Types.ObjectId(categoryId),
          isActive: true,
          _id: { $nin: excludeObjectIds }
        }
      },
      {
        $sample: {
          size: count
        }
      },
      {
        $project: {
          _id: 1,
          question: 1,
          options: 1,
          difficulty: 1,
          points: 1,
          tags: 1
          // Note: We don't include correctAnswer or explanation in the response
        }
      }
    ])

    // Format questions for frontend
    const formattedQuestions = additionalQuestions.map((q, index) => ({
      id: q._id,
      questionNumber: index + 1,
      question: q.question,
      options: q.options,
      difficulty: q.difficulty,
      points: q.points,
      tags: q.tags,
      isRetry: false // Mark as new question
    }))

    return NextResponse.json({
      success: true,
      data: {
        questions: formattedQuestions,
        count: formattedQuestions.length,
        requested: count
      }
    })

  } catch (error) {
    console.error('Error getting additional questions:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to get additional questions' 
      },
      { status: 500 }
    )
  }
}

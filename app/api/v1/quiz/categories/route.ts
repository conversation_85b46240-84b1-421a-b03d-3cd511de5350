import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { QuizCategory, QuizQuestion } from '@/lib/models/quiz.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const { searchParams } = new URL(request.url)
    const includeStats = searchParams.get('includeStats') === 'true'

    // Get all active quiz categories
    const categories = await QuizCategory.find({ isActive: true })
      .sort({ createdAt: -1 })
      .lean()

    // If stats are requested, get question counts for each category
    let categoriesWithStats = categories
    if (includeStats) {
      categoriesWithStats = await Promise.all(
        categories.map(async (category) => {
          const questionCount = await QuizQuestion.countDocuments({
            categoryId: category._id,
            isActive: true
          })

          return {
            ...category,
            availableQuestions: questionCount,
            totalQuestions: Math.min(category.totalQuestions, questionCount)
          }
        })
      )
    }

    // Format for frontend
    const formattedCategories = categoriesWithStats.map(category => ({
      id: category._id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      icon: category.icon,
      color: category.color,
      skills: category.skills,
      difficulty: category.difficulty,
      estimatedTime: category.estimatedTime,
      totalQuestions: category.totalQuestions,
      passingScore: category.passingScore,
      availableQuestions: category.availableQuestions || category.totalQuestions,
      isActive: category.isActive
    }))

    return NextResponse.json({
      success: true,
      data: {
        categories: formattedCategories,
        total: formattedCategories.length
      }
    })

  } catch (error) {
    console.error('Error fetching quiz categories:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch quiz categories' 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    const body = await request.json()
    const {
      name,
      description,
      icon,
      color,
      skills,
      difficulty,
      estimatedTime,
      totalQuestions,
      passingScore
    } = body

    // Validate required fields
    if (!name || !description || !icon || !color || !skills || !difficulty || !estimatedTime || !totalQuestions) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Missing required fields' 
        },
        { status: 400 }
      )
    }

    // Generate slug from name
    const slug = name.toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .trim()

    // Check if slug already exists
    const existingCategory = await QuizCategory.findOne({ slug })
    if (existingCategory) {
      return NextResponse.json(
        { 
          success: false,
          error: 'A category with this name already exists' 
        },
        { status: 400 }
      )
    }

    // Create new category
    const category = new QuizCategory({
      name,
      slug,
      description,
      icon,
      color,
      skills: Array.isArray(skills) ? skills : [skills],
      difficulty,
      estimatedTime: parseInt(estimatedTime),
      totalQuestions: parseInt(totalQuestions),
      passingScore: passingScore || 70
    })

    await category.save()

    return NextResponse.json({
      success: true,
      data: {
        category: {
          id: category._id,
          name: category.name,
          slug: category.slug,
          description: category.description,
          icon: category.icon,
          color: category.color,
          skills: category.skills,
          difficulty: category.difficulty,
          estimatedTime: category.estimatedTime,
          totalQuestions: category.totalQuestions,
          passingScore: category.passingScore,
          isActive: category.isActive
        }
      }
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating quiz category:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to create quiz category' 
      },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { QuizQuestion } from '@/lib/models/quiz.model'

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    const body = await request.json()
    const { answers } = body

    if (!Array.isArray(answers) || answers.length === 0) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Answers array is required' 
        },
        { status: 400 }
      )
    }

    // Get all questions for the provided answers
    const questionIds = answers.map(answer => answer.questionId)
    const questions = await QuizQuestion.find({
      _id: { $in: questionIds }
    }).lean()

    // Create a map for quick lookup
    const questionMap = new Map(questions.map(q => [q._id.toString(), q]))

    // Check each answer and identify wrong ones
    const wrongQuestions = []
    const results = []

    for (const answer of answers) {
      const question = questionMap.get(answer.questionId)
      if (!question) continue

      const isCorrect = answer.selectedAnswer === question.correctAnswer
      
      results.push({
        questionId: answer.questionId,
        selectedAnswer: answer.selectedAnswer,
        correctAnswer: question.correctAnswer,
        isCorrect,
        points: isCorrect ? question.points : 0
      })

      // If wrong, add the full question for second phase
      if (!isCorrect) {
        wrongQuestions.push({
          id: question._id,
          questionNumber: wrongQuestions.length + 1,
          question: question.question,
          options: question.options,
          difficulty: question.difficulty,
          points: question.points,
          tags: question.tags,
          isRetry: true // Mark as retry question
        })
      }
    }

    // Calculate score
    const totalPoints = results.reduce((sum, r) => sum + r.points, 0)
    const maxPoints = questions.reduce((sum, q) => sum + q.points, 0)
    const percentage = maxPoints > 0 ? Math.round((totalPoints / maxPoints) * 100) : 0

    return NextResponse.json({
      success: true,
      data: {
        results,
        wrongQuestions,
        score: {
          totalPoints,
          maxPoints,
          percentage,
          correctAnswers: results.filter(r => r.isCorrect).length,
          totalQuestions: results.length
        }
      }
    })

  } catch (error) {
    console.error('Error checking answers:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to check answers' 
      },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { QuizCategory, QuizQuestion } from '@/lib/models/quiz.model'

// Simple UUID generator
function generateUUID() {
  return 'quiz_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    const body = await request.json()
    const { categoryId, sessionId } = body

    // Validate required fields
    if (!categoryId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Category ID is required' 
        },
        { status: 400 }
      )
    }

    // Find the category
    const category = await QuizCategory.findById(categoryId)
    if (!category || !category.isActive) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Quiz category not found or inactive' 
        },
        { status: 404 }
      )
    }

    // Get random questions for this category
    const questions = await QuizQuestion.aggregate([
      {
        $match: {
          categoryId: category._id,
          isActive: true
        }
      },
      {
        $sample: {
          size: Math.min(category.totalQuestions, 15) // Max 15 questions per quiz
        }
      },
      {
        $project: {
          _id: 1,
          question: 1,
          options: 1,
          difficulty: 1,
          points: 1,
          tags: 1
          // Note: We don't include correctAnswer or explanation in the response
        }
      }
    ])

    if (questions.length === 0) {
      return NextResponse.json(
        { 
          success: false,
          error: 'No questions available for this category' 
        },
        { status: 404 }
      )
    }

    // Generate session ID if not provided
    const quizSessionId = sessionId || generateUUID()

    // Calculate max possible points
    const maxPoints = questions.reduce((total, q) => total + q.points, 0)

    return NextResponse.json({
      success: true,
      data: {
        sessionId: quizSessionId,
        category: {
          id: category._id,
          name: category.name,
          description: category.description,
          icon: category.icon,
          color: category.color,
          difficulty: category.difficulty,
          estimatedTime: category.estimatedTime,
          passingScore: category.passingScore
        },
        questions: questions.map((q, index) => ({
          id: q._id,
          questionNumber: index + 1,
          question: q.question,
          options: q.options,
          difficulty: q.difficulty,
          points: q.points,
          tags: q.tags
        })),
        totalQuestions: questions.length,
        maxPoints,
        timeLimit: category.estimatedTime * 60 // Convert minutes to seconds
      }
    })

  } catch (error) {
    console.error('Error starting quiz:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to start quiz' 
      },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { QuizCategory, QuizQuestion, QuizAttempt } from '@/lib/models/quiz.model'
import { AwardTemplate, UserAward } from '@/lib/models/award.model'

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    const body = await request.json()
    const { 
      sessionId, 
      categoryId, 
      answers, 
      timeSpent, 
      userId = null 
    } = body

    // Validate required fields
    if (!sessionId || !categoryId || !answers || !Array.isArray(answers)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Missing required fields' 
        },
        { status: 400 }
      )
    }

    // Find the category
    const category = await QuizCategory.findById(categoryId)
    if (!category) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Quiz category not found' 
        },
        { status: 404 }
      )
    }

    // Get the questions with correct answers
    const questionIds = answers.map(a => a.questionId)
    const questions = await QuizQuestion.find({
      _id: { $in: questionIds },
      categoryId: category._id
    })

    if (questions.length !== answers.length) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid questions provided' 
        },
        { status: 400 }
      )
    }

    // Calculate results
    let totalPoints = 0
    let maxPoints = 0
    const processedAnswers = []

    for (const answer of answers) {
      const question = questions.find(q => q._id.toString() === answer.questionId)
      if (!question) continue

      const isCorrect = answer.selectedAnswer === question.correctAnswer
      const points = isCorrect ? question.points : 0

      totalPoints += points
      maxPoints += question.points

      processedAnswers.push({
        questionId: question._id,
        selectedAnswer: answer.selectedAnswer,
        isCorrect,
        timeSpent: answer.timeSpent || 0,
        points
      })
    }

    // Calculate score percentage
    const score = maxPoints > 0 ? Math.round((totalPoints / maxPoints) * 100) : 0

    // Get client info
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // Create quiz attempt record
    const attempt = new QuizAttempt({
      userId,
      sessionId,
      categoryId: category._id,
      questions: processedAnswers,
      score,
      totalPoints,
      maxPoints,
      timeSpent: timeSpent || 0,
      ipAddress,
      userAgent,
      isAnonymous: !userId
    })

    await attempt.save()

    // Check for awards if user is registered and passed
    let earnedAwards = []
    if (userId && score >= category.passingScore) {
      try {
        const availableAwards = await AwardTemplate.find({
          categoryId: category._id,
          minimumScore: { $lte: score },
          isActive: true
        }).sort({ minimumScore: -1 }) // Get highest tier award first

        for (const awardTemplate of availableAwards) {
          // Check if user already has this award
          const existingAward = await UserAward.findOne({
            userId,
            awardTemplateId: awardTemplate._id
          })

          if (!existingAward) {
            // Create new award
            const userAward = new UserAward({
              userId,
              awardTemplateId: awardTemplate._id,
              quizAttemptId: attempt._id,
              score
            })

            await userAward.save()
            earnedAwards.push({
              id: awardTemplate._id,
              name: awardTemplate.name,
              description: awardTemplate.description,
              badgeIcon: awardTemplate.badgeIcon,
              badgeColor: awardTemplate.badgeColor,
              rarity: awardTemplate.rarity,
              benefits: awardTemplate.benefits
            })
            
            // Only award the highest tier
            break
          }
        }
      } catch (awardError) {
        console.error('Error processing awards:', awardError)
        // Don't fail the quiz submission if award processing fails
      }
    }

    // Determine skill level
    const getSkillLevel = (score: number) => {
      if (score >= 90) return { level: "Expert", color: "text-green-600", tier: "expert" }
      if (score >= 75) return { level: "Advanced", color: "text-blue-600", tier: "advanced" }
      if (score >= 60) return { level: "Intermediate", color: "text-orange-600", tier: "intermediate" }
      return { level: "Beginner", color: "text-gray-600", tier: "beginner" }
    }

    const skillLevel = getSkillLevel(score)

    return NextResponse.json({
      success: true,
      data: {
        attemptId: attempt._id,
        score,
        totalPoints,
        maxPoints,
        correctAnswers: processedAnswers.filter(a => a.isCorrect).length,
        totalQuestions: processedAnswers.length,
        timeSpent,
        skillLevel,
        passed: score >= category.passingScore,
        passingScore: category.passingScore,
        earnedAwards,
        category: {
          id: category._id,
          name: category.name,
          icon: category.icon
        },
        recommendations: {
          jobMatches: score >= 75 ? "senior" : score >= 60 ? "mid-level" : "entry-level",
          nextSteps: score >= category.passingScore 
            ? ["Explore matching job opportunities", "Consider advanced certifications"]
            : ["Review study materials", "Retake assessment", "Practice with tutorials"]
        }
      }
    })

  } catch (error) {
    console.error('Error submitting quiz:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to submit quiz' 
      },
      { status: 500 }
    )
  }
}

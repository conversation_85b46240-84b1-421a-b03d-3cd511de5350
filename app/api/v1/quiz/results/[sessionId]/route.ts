import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { QuizAttempt, QuizCategory, QuizQuestion } from '@/lib/models/quiz.model'

export async function GET(
  request: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    await connectDB()

    const { sessionId } = params

    if (!sessionId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Session ID is required' 
        },
        { status: 400 }
      )
    }

    // Find the quiz attempt
    const attempt = await QuizAttempt.findOne({ sessionId }).lean()
    
    if (!attempt) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Quiz results not found' 
        },
        { status: 404 }
      )
    }

    // Get category details
    const category = await QuizCategory.findById(attempt.categoryId).lean()
    
    if (!category) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Quiz category not found' 
        },
        { status: 404 }
      )
    }

    // Get question details for the answers
    const questionIds = attempt.answers.map(a => a.questionId)
    const questions = await QuizQuestion.find({
      _id: { $in: questionIds }
    }).lean()

    // Create a map for quick question lookup
    const questionMap = new Map(questions.map(q => [q._id.toString(), q]))

    // Format the detailed answers
    const detailedAnswers = attempt.answers.map((answer, index) => {
      const question = questionMap.get(answer.questionId.toString())
      
      return {
        questionId: answer.questionId,
        question: question?.question || 'Question not found',
        selectedAnswer: answer.selectedAnswer,
        correctAnswer: question?.correctAnswer || 0,
        isCorrect: answer.selectedAnswer === question?.correctAnswer,
        points: answer.selectedAnswer === question?.correctAnswer ? (question?.points || 0) : 0,
        timeSpent: answer.timeSpent || 0
      }
    })

    // Calculate results
    const correctAnswers = detailedAnswers.filter(a => a.isCorrect).length
    const totalPoints = detailedAnswers.reduce((sum, a) => sum + a.points, 0)
    const maxPoints = questions.reduce((sum, q) => sum + q.points, 0)
    const score = maxPoints > 0 ? Math.round((totalPoints / maxPoints) * 100) : 0

    // Determine skill level
    const getSkillLevel = (score: number) => {
      if (score >= 90) return "Expert"
      if (score >= 75) return "Advanced"
      if (score >= 60) return "Intermediate"
      return "Beginner"
    }

    // Check if award was earned (70%+ score)
    const earnedAward = score >= 70

    const result = {
      sessionId: attempt.sessionId,
      category: {
        id: category._id,
        name: category.name,
        icon: category.icon,
        difficulty: category.difficulty
      },
      score,
      totalQuestions: attempt.answers.length,
      correctAnswers,
      totalPoints,
      maxPoints,
      timeSpent: attempt.timeSpent,
      completedAt: attempt.completedAt,
      phase: attempt.phase || 'completed',
      earnedAward,
      skillLevel: getSkillLevel(score),
      answers: detailedAnswers
    }

    return NextResponse.json({
      success: true,
      data: result
    })

  } catch (error) {
    console.error('Error fetching quiz results:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch quiz results' 
      },
      { status: 500 }
    )
  }
}

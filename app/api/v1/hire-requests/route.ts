import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { HireRequest } from '@/lib/models/hire-request.model'
import { Company } from '@/lib/models/company.model'
import { Client } from '@/lib/models/client.model'
import { User } from '@/lib/models/user.model'
import { CreateHireRequest, HireSearchQuery } from '@/types/hire.types'

// GET /api/v1/hire-requests - Search hire requests
export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const user = authResult.user
    const { searchParams } = new URL(request.url)
    
    // Build query based on user role
    let baseQuery: any = { isActive: true }
    
    // Company users can only see their own hire requests
    if (user.role === 'company_admin' || user.role === 'recruiter') {
      if (!user.companyId) {
        return NextResponse.json(
          { error: 'Company ID not found for user' },
          { status: 400 }
        )
      }
      baseQuery.companyId = user.companyId
    }
    
    // Job seekers can only see hire requests sent to them
    if (user.role === 'job_seeker') {
      // Find client profile for this user
      const client = await Client.findOne({ user: user.id })
      if (!client) {
        return NextResponse.json(
          { error: 'Client profile not found' },
          { status: 404 }
        )
      }
      baseQuery.talentId = client._id
    }
    
    // Admin can see all hire requests
    if (user.role === 'admin') {
      // No additional filters for admin
    }

    // Parse search parameters
    const companyId = searchParams.get('companyId')
    const talentId = searchParams.get('talentId')
    const status = searchParams.getAll('status')
    const hireType = searchParams.getAll('hireType')
    const priority = searchParams.get('priority')
    const search = searchParams.get('search')
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // Apply additional filters
    if (companyId && user.role === 'admin') {
      baseQuery.companyId = companyId
    }
    if (talentId && (user.role === 'admin' || user.role === 'company_admin' || user.role === 'recruiter')) {
      baseQuery.talentId = talentId
    }
    if (status.length > 0) {
      baseQuery.status = { $in: status }
    }
    if (hireType.length > 0) {
      baseQuery.hireType = { $in: hireType }
    }
    if (priority) {
      baseQuery.priority = priority
    }
    if (dateFrom || dateTo) {
      baseQuery.createdAt = {}
      if (dateFrom) baseQuery.createdAt.$gte = new Date(dateFrom)
      if (dateTo) baseQuery.createdAt.$lte = new Date(dateTo)
    }
    if (search) {
      baseQuery.$text = { $search: search }
    }

    // Execute query with pagination
    const skip = (page - 1) * limit
    const sortOptions: any = {}
    sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1

    const [hireRequests, total] = await Promise.all([
      HireRequest.find(baseQuery)
        .populate('companyId', 'name logo industry')
        .populate('talentId', 'user headline currentTitle skills location')
        .populate('createdBy', 'profile.firstName profile.lastName role')
        .populate('lastUpdatedBy', 'profile.firstName profile.lastName role')
        .sort(sortOptions)
        .skip(skip)
        .limit(limit)
        .lean(),
      HireRequest.countDocuments(baseQuery)
    ])

    // Get filter aggregations
    const filterAggregations = await HireRequest.aggregate([
      { $match: baseQuery },
      {
        $group: {
          _id: null,
          statuses: { $push: '$status' },
          types: { $push: '$hireType' },
          priorities: { $push: '$priority' }
        }
      }
    ])

    const filters = filterAggregations[0] || { statuses: [], types: [], priorities: [] }
    
    // Count occurrences
    const statusCounts = filters.statuses.reduce((acc: any, status: string) => {
      acc[status] = (acc[status] || 0) + 1
      return acc
    }, {})
    
    const typeCounts = filters.types.reduce((acc: any, type: string) => {
      acc[type] = (acc[type] || 0) + 1
      return acc
    }, {})
    
    const priorityCounts = filters.priorities.reduce((acc: any, priority: string) => {
      acc[priority] = (acc[priority] || 0) + 1
      return acc
    }, {})

    const result = {
      hireRequests,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      },
      filters: {
        statuses: Object.entries(statusCounts).map(([status, count]) => ({ status, count })),
        types: Object.entries(typeCounts).map(([type, count]) => ({ type, count })),
        priorities: Object.entries(priorityCounts).map(([priority, count]) => ({ priority, count }))
      }
    }

    return NextResponse.json({
      success: true,
      data: result
    })

  } catch (error) {
    console.error('Search hire requests error:', error)
    return NextResponse.json(
      { error: 'Failed to search hire requests' },
      { status: 500 }
    )
  }
}

// POST /api/v1/hire-requests - Create new hire request
export async function POST(request: NextRequest) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const user = authResult.user
    
    // Only company admins and recruiters can create hire requests
    if (!['company_admin', 'recruiter'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Only company administrators and recruiters can create hire requests' },
        { status: 403 }
      )
    }

    if (!user.companyId) {
      return NextResponse.json(
        { error: 'Company ID not found for user' },
        { status: 400 }
      )
    }

    const hireData: CreateHireRequest = await request.json()

    // Validate required fields
    if (!hireData.talentId || !hireData.hireType || !hireData.projectDetails || !hireData.contractTerms) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Verify company exists
    const company = await Company.findById(user.companyId)
    if (!company) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Verify talent exists
    const talent = await Client.findById(hireData.talentId).populate('user')
    if (!talent) {
      return NextResponse.json(
        { error: 'Talent not found' },
        { status: 404 }
      )
    }

    // Check if there's already an active hire request for this talent
    const existingHireRequest = await HireRequest.findOne({
      companyId: user.companyId,
      talentId: hireData.talentId,
      status: { $in: ['pending', 'reviewing', 'negotiating', 'contract_sent'] },
      isActive: true
    })

    if (existingHireRequest) {
      return NextResponse.json(
        { error: 'An active hire request already exists for this talent' },
        { status: 400 }
      )
    }

    // Create hire request
    const hireRequest = new HireRequest({
      companyId: user.companyId,
      talentId: hireData.talentId,
      hireType: hireData.hireType,
      status: 'pending',
      projectDetails: {
        ...hireData.projectDetails,
        timeline: {
          ...hireData.projectDetails.timeline,
          startDate: new Date(hireData.projectDetails.timeline.startDate),
          endDate: hireData.projectDetails.timeline.endDate 
            ? new Date(hireData.projectDetails.timeline.endDate) 
            : undefined,
          milestones: hireData.projectDetails.timeline.milestones?.map(milestone => ({
            ...milestone,
            dueDate: new Date(milestone.dueDate)
          })) || []
        }
      },
      contractTerms: {
        ...hireData.contractTerms,
        duration: hireData.contractTerms.duration ? {
          ...hireData.contractTerms.duration,
          startDate: new Date(hireData.contractTerms.duration.startDate),
          endDate: hireData.contractTerms.duration.endDate 
            ? new Date(hireData.contractTerms.duration.endDate) 
            : undefined
        } : undefined
      },
      message: hireData.message,
      priority: hireData.priority || 'medium',
      source: 'talent_search',
      timeline: [{
        status: 'pending',
        date: new Date(),
        updatedBy: user.id,
        updatedByRole: 'company',
        notes: 'Hire request created'
      }],
      expiresAt: hireData.responseDeadline ? new Date(hireData.responseDeadline) : undefined,
      responseDeadline: hireData.responseDeadline ? new Date(hireData.responseDeadline) : undefined,
      createdBy: user.id,
      lastUpdatedBy: user.id
    })

    await hireRequest.save()

    // Populate for response
    await hireRequest.populate([
      { path: 'companyId', select: 'name logo industry' },
      { path: 'talentId', select: 'user headline currentTitle skills location' },
      { path: 'createdBy', select: 'profile.firstName profile.lastName role' },
      { path: 'lastUpdatedBy', select: 'profile.firstName profile.lastName role' }
    ])

    // TODO: Send notification to talent
    // await notificationService.sendHireRequestNotification(hireRequest)

    console.log(`Hire request created: ${hireRequest._id} for talent ${talent.user.profile?.firstName} ${talent.user.profile?.lastName}`)

    return NextResponse.json({
      success: true,
      data: hireRequest,
      message: 'Hire request created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Create hire request error:', error)
    return NextResponse.json(
      { error: 'Failed to create hire request' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { HireRequest } from '@/lib/models/hire-request.model'
import { Client } from '@/lib/models/client.model'
import { HireStats } from '@/types/hire.types'

// GET /api/v1/hire-requests/stats - Get hire request statistics
export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const user = authResult.user
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    // Build base query based on user role and permissions
    let baseQuery: any = { isActive: true }

    if (user.role === 'company_admin' || user.role === 'recruiter') {
      if (!user.companyId) {
        return NextResponse.json(
          { error: 'Company ID not found for user' },
          { status: 400 }
        )
      }
      baseQuery.companyId = user.companyId
    } else if (user.role === 'job_seeker') {
      // Find client profile for this user
      const client = await Client.findOne({ user: user.id })
      if (!client) {
        return NextResponse.json(
          { error: 'Client profile not found' },
          { status: 404 }
        )
      }
      baseQuery.talentId = client._id
    } else if (user.role === 'admin' && companyId) {
      baseQuery.companyId = companyId
    }

    // Get basic stats
    const [
      totalCount,
      statusStats,
      typeStats,
      priorityStats,
      successRateData,
      timeToHireData,
      expiringSoonCount
    ] = await Promise.all([
      // Total count
      HireRequest.countDocuments(baseQuery),
      
      // Status breakdown
      HireRequest.aggregate([
        { $match: baseQuery },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]),
      
      // Type breakdown
      HireRequest.aggregate([
        { $match: baseQuery },
        { $group: { _id: '$hireType', count: { $sum: 1 } } }
      ]),
      
      // Priority breakdown
      HireRequest.aggregate([
        { $match: baseQuery },
        { $group: { _id: '$priority', count: { $sum: 1 } } }
      ]),
      
      // Success rate calculation
      HireRequest.aggregate([
        { $match: baseQuery },
        {
          $group: {
            _id: null,
            total: { $sum: 1 },
            hired: {
              $sum: {
                $cond: [{ $eq: ['$status', 'hired'] }, 1, 0]
              }
            },
            rejected: {
              $sum: {
                $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0]
              }
            }
          }
        }
      ]),
      
      // Average time to hire
      HireRequest.aggregate([
        { 
          $match: { 
            ...baseQuery, 
            status: 'hired' 
          } 
        },
        {
          $addFields: {
            timeToHire: {
              $divide: [
                { $subtract: ['$updatedAt', '$createdAt'] },
                1000 * 60 * 60 * 24 // Convert to days
              ]
            }
          }
        },
        {
          $group: {
            _id: null,
            averageTimeToHire: { $avg: '$timeToHire' }
          }
        }
      ]),
      
      // Expiring soon count (within 7 days)
      HireRequest.countDocuments({
        ...baseQuery,
        expiresAt: {
          $lte: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          $gte: new Date()
        },
        status: { $in: ['pending', 'reviewing', 'negotiating'] }
      })
    ])

    // Process status stats
    const byStatus: Record<string, number> = {
      pending: 0,
      reviewing: 0,
      negotiating: 0,
      contract_sent: 0,
      contract_signed: 0,
      hired: 0,
      rejected: 0,
      cancelled: 0,
      expired: 0
    }
    
    statusStats.forEach((stat: any) => {
      byStatus[stat._id] = stat.count
    })

    // Process type stats
    const byType: Record<string, number> = {
      full_time: 0,
      part_time: 0,
      contract: 0,
      freelance: 0,
      internship: 0,
      temporary: 0
    }
    
    typeStats.forEach((stat: any) => {
      byType[stat._id] = stat.count
    })

    // Process priority stats
    const byPriority: Record<string, number> = {
      low: 0,
      medium: 0,
      high: 0,
      urgent: 0
    }
    
    priorityStats.forEach((stat: any) => {
      byPriority[stat._id] = stat.count
    })

    // Calculate success rate
    const successData = successRateData[0] || { total: 0, hired: 0, rejected: 0 }
    const completedRequests = successData.hired + successData.rejected
    const successRate = completedRequests > 0 
      ? Math.round((successData.hired / completedRequests) * 100) 
      : 0

    // Calculate average time to hire
    const timeToHire = timeToHireData[0]?.averageTimeToHire || 0
    const averageTimeToHire = Math.round(timeToHire)

    // Active requests (not in final states)
    const activeRequests = byStatus.pending + byStatus.reviewing + byStatus.negotiating + byStatus.contract_sent + byStatus.contract_signed

    const stats: HireStats = {
      total: totalCount,
      byStatus,
      byType,
      byPriority,
      successRate,
      averageTimeToHire,
      activeRequests,
      expiringSoon: expiringSoonCount
    }

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error) {
    console.error('Get hire stats error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch hire statistics' },
      { status: 500 }
    )
  }
}

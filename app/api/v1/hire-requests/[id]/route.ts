import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { HireRequest } from '@/lib/models/hire-request.model'
import { Client } from '@/lib/models/client.model'
import { UpdateHireRequest } from '@/types/hire.types'

interface RouteParams {
  params: {
    id: string
  }
}

// GET /api/v1/hire-requests/[id] - Get hire request by ID
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const user = authResult.user
    const hireRequestId = params.id

    // Find hire request
    const hireRequest = await HireRequest.findById(hireRequestId)
      .populate('companyId', 'name logo industry')
      .populate('talentId', 'user headline currentTitle skills location')
      .populate('createdBy', 'profile.firstName profile.lastName role')
      .populate('lastUpdatedBy', 'profile.firstName profile.lastName role')
      .lean()

    if (!hireRequest) {
      return NextResponse.json(
        { error: 'Hire request not found' },
        { status: 404 }
      )
    }

    // Check permissions
    const canAccess = 
      user.role === 'admin' ||
      (user.companyId && hireRequest.companyId._id.toString() === user.companyId) ||
      (user.role === 'job_seeker' && await checkTalentAccess(user.id, hireRequest.talentId._id))

    if (!canAccess) {
      return NextResponse.json(
        { error: 'Access denied to this hire request' },
        { status: 403 }
      )
    }

    return NextResponse.json({
      success: true,
      data: hireRequest
    })

  } catch (error) {
    console.error('Get hire request error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch hire request' },
      { status: 500 }
    )
  }
}

// PUT /api/v1/hire-requests/[id] - Update hire request
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const user = authResult.user
    const hireRequestId = params.id
    const updateData: UpdateHireRequest = await request.json()

    // Find hire request
    const hireRequest = await HireRequest.findById(hireRequestId)
    if (!hireRequest) {
      return NextResponse.json(
        { error: 'Hire request not found' },
        { status: 404 }
      )
    }

    // Check permissions
    const canUpdate = 
      user.role === 'admin' ||
      (user.companyId && hireRequest.companyId.toString() === user.companyId) ||
      (user.role === 'job_seeker' && await checkTalentAccess(user.id, hireRequest.talentId))

    if (!canUpdate) {
      return NextResponse.json(
        { error: 'Access denied to update this hire request' },
        { status: 403 }
      )
    }

    // Update fields
    const allowedUpdates = [
      'status', 'message', 'companyNotes', 'talentResponse', 
      'contractTerms', 'projectDetails', 'priority', 'responseDeadline'
    ]

    const updates: any = {}
    for (const field of allowedUpdates) {
      if (updateData[field as keyof UpdateHireRequest] !== undefined) {
        updates[field] = updateData[field as keyof UpdateHireRequest]
      }
    }

    // Handle date fields
    if (updateData.responseDeadline) {
      updates.responseDeadline = new Date(updateData.responseDeadline)
      updates.expiresAt = new Date(updateData.responseDeadline)
    }

    // Add timeline entry if status is changing
    if (updateData.status && updateData.status !== hireRequest.status) {
      const timelineEntry = {
        status: updateData.status,
        date: new Date(),
        updatedBy: user.id,
        updatedByRole: user.role === 'job_seeker' ? 'talent' : 'company',
        notes: updateData.companyNotes || updateData.talentResponse || `Status changed to ${updateData.status}`
      }
      
      updates.$push = { timeline: timelineEntry }
    }

    updates.lastUpdatedBy = user.id
    updates.updatedAt = new Date()

    // Update hire request
    const updatedHireRequest = await HireRequest.findByIdAndUpdate(
      hireRequestId,
      updates,
      { new: true, runValidators: true }
    )
      .populate('companyId', 'name logo industry')
      .populate('talentId', 'user headline currentTitle skills location')
      .populate('createdBy', 'profile.firstName profile.lastName role')
      .populate('lastUpdatedBy', 'profile.firstName profile.lastName role')

    // TODO: Send notification based on status change
    // await notificationService.sendHireStatusUpdateNotification(updatedHireRequest)

    return NextResponse.json({
      success: true,
      data: updatedHireRequest,
      message: 'Hire request updated successfully'
    })

  } catch (error) {
    console.error('Update hire request error:', error)
    return NextResponse.json(
      { error: 'Failed to update hire request' },
      { status: 500 }
    )
  }
}

// DELETE /api/v1/hire-requests/[id] - Delete hire request
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const user = authResult.user
    const hireRequestId = params.id

    // Find hire request
    const hireRequest = await HireRequest.findById(hireRequestId)
    if (!hireRequest) {
      return NextResponse.json(
        { error: 'Hire request not found' },
        { status: 404 }
      )
    }

    // Check permissions - only company admins and admins can delete
    const canDelete = 
      user.role === 'admin' ||
      (user.role === 'company_admin' && user.companyId && hireRequest.companyId.toString() === user.companyId)

    if (!canDelete) {
      return NextResponse.json(
        { error: 'Access denied to delete this hire request' },
        { status: 403 }
      )
    }

    // Soft delete - mark as inactive
    await HireRequest.findByIdAndUpdate(hireRequestId, {
      isActive: false,
      status: 'cancelled',
      lastUpdatedBy: user.id,
      $push: {
        timeline: {
          status: 'cancelled',
          date: new Date(),
          updatedBy: user.id,
          updatedByRole: 'company',
          notes: 'Hire request cancelled'
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Hire request deleted successfully'
    })

  } catch (error) {
    console.error('Delete hire request error:', error)
    return NextResponse.json(
      { error: 'Failed to delete hire request' },
      { status: 500 }
    )
  }
}

// Helper function to check if user has access to talent
async function checkTalentAccess(userId: string, talentId: string): Promise<boolean> {
  try {
    const client = await Client.findOne({ user: userId, _id: talentId })
    return !!client
  } catch (error) {
    return false
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { CourseEnrollment, CourseProgress } from '@/lib/models/course.model'
import { User } from '@/lib/models/user.model'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    // Get user session
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Find user
    const user = await User.findOne({ email: session.user.email })
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') // 'active', 'completed', 'all'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    // Build query
    let query: any = { userId: user._id }
    
    if (status === 'active') {
      query.isActive = true
      query.progress = { $lt: 100 }
    } else if (status === 'completed') {
      query.progress = 100
    }

    // Get enrollments with course details
    const enrollments = await CourseEnrollment.find(query)
      .populate({
        path: 'courseId',
        select: 'title slug thumbnail price originalPrice level duration rating totalStudents categoryId',
        populate: {
          path: 'categoryId',
          select: 'name icon color'
        }
      })
      .sort({ enrolledAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean()

    // Get total count
    const total = await CourseEnrollment.countDocuments(query)

    // Calculate progress for each enrollment
    const enrollmentsWithProgress = await Promise.all(
      enrollments.map(async (enrollment) => {
        // Get course progress
        const progressRecords = await CourseProgress.find({
          userId: user._id,
          courseId: enrollment.courseId._id
        }).lean()

        // Calculate overall progress percentage
        const totalLessons = 10 // Mock - would come from course content
        const completedLessons = progressRecords.filter(p => p.isCompleted).length
        const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0

        return {
          ...enrollment,
          progress: Math.round(progressPercentage),
          completedLessons,
          totalLessons,
          lastAccessedAt: progressRecords.length > 0 
            ? Math.max(...progressRecords.map(p => new Date(p.lastAccessedAt).getTime()))
            : enrollment.enrolledAt
        }
      })
    )

    // Calculate summary statistics
    const stats = {
      totalEnrollments: total,
      activeEnrollments: enrollmentsWithProgress.filter(e => e.progress < 100 && e.isActive).length,
      completedCourses: enrollmentsWithProgress.filter(e => e.progress === 100).length,
      totalHoursLearned: enrollmentsWithProgress.reduce((sum, e) => sum + (e.courseId.duration || 0), 0),
      averageProgress: enrollmentsWithProgress.length > 0 
        ? enrollmentsWithProgress.reduce((sum, e) => sum + e.progress, 0) / enrollmentsWithProgress.length 
        : 0
    }

    return NextResponse.json({
      success: true,
      data: {
        enrollments: enrollmentsWithProgress,
        stats,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('Get user enrollments error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get user enrollments' },
      { status: 500 }
    )
  }
}

// Update enrollment progress
export async function PUT(request: NextRequest) {
  try {
    await connectDB()

    // Get user session
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Find user
    const user = await User.findOne({ email: session.user.email })
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    const body = await request.json()
    const { courseId, lessonId, isCompleted, timeSpent, lastPosition } = body

    // Find or create progress record
    let progress = await CourseProgress.findOne({
      userId: user._id,
      courseId,
      lessonId
    })

    if (progress) {
      // Update existing progress
      progress.isCompleted = isCompleted ?? progress.isCompleted
      progress.timeSpent = (progress.timeSpent || 0) + (timeSpent || 0)
      progress.lastPosition = lastPosition ?? progress.lastPosition
      progress.lastAccessedAt = new Date()
      await progress.save()
    } else {
      // Create new progress record
      progress = new CourseProgress({
        userId: user._id,
        courseId,
        lessonId,
        isCompleted: isCompleted || false,
        timeSpent: timeSpent || 0,
        lastPosition: lastPosition || 0,
        lastAccessedAt: new Date()
      })
      await progress.save()
    }

    // Update enrollment progress
    const allProgress = await CourseProgress.find({
      userId: user._id,
      courseId
    })

    const totalLessons = 10 // Mock - would come from course content
    const completedLessons = allProgress.filter(p => p.isCompleted).length
    const overallProgress = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0

    await CourseEnrollment.findOneAndUpdate(
      { userId: user._id, courseId },
      { 
        progress: Math.round(overallProgress),
        lastAccessedAt: new Date(),
        ...(overallProgress === 100 && { completedAt: new Date() })
      }
    )

    return NextResponse.json({
      success: true,
      data: {
        progress,
        overallProgress: Math.round(overallProgress),
        message: 'Progress updated successfully'
      }
    })

  } catch (error) {
    console.error('Update progress error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update progress' },
      { status: 500 }
    )
  }
}

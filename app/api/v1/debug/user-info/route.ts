import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  validate<PERSON><PERSON><PERSON>, 
  createSuccessResponse,
  AuthenticatedRequest
} from '@/lib/middleware/api-middleware'
import { User } from '@/lib/models/user.model'
import { Company } from '@/lib/models/company.model'

// GET /api/v1/debug/user-info - Debug endpoint to check user and company info
export const GET = withErrorHandler(async (request: AuthenticatedRequest) => {
  validateMethod(request, ['GET'])

  const userId = request.user?.id
  if (!userId) {
    return createSuccessResponse({
      error: 'No user ID in request',
      user: request.user
    })
  }

  console.log('🔍 Debug - User ID from token:', userId)
  console.log('🔍 Debug - Request user object:', request.user)

  // Find user and their company
  const user = await User.findById(userId).populate('companyId')
  
  console.log('🔍 Debug - User found in DB:', user ? 'YES' : 'NO')
  
  if (user) {
    console.log('🔍 Debug - User details:', {
      id: user._id,
      email: user.email,
      role: user.role,
      companyId: user.companyId,
      hasCompany: !!user.companyId
    })
  }

  // Get total users and companies count
  const totalUsers = await User.countDocuments()
  const totalCompanies = await Company.countDocuments()

  // Try to find user by email as well
  let userByEmail = null
  if (request.user?.email) {
    userByEmail = await User.findOne({ email: request.user.email })
  }

  const debugInfo = {
    requestUser: request.user,
    userFound: !!user,
    userDetails: user ? {
      id: user._id.toString(),
      email: user.email,
      role: user.role,
      companyId: user.companyId?.toString(),
      hasCompany: !!user.companyId,
      isActive: user.isActive,
      isEmailVerified: user.isEmailVerified
    } : null,
    userByEmail: userByEmail ? {
      id: userByEmail._id.toString(),
      email: userByEmail.email,
      role: userByEmail.role,
      companyId: userByEmail.companyId?.toString(),
      hasCompany: !!userByEmail.companyId
    } : null,
    stats: {
      totalUsers,
      totalCompanies
    }
  }

  return createSuccessResponse(debugInfo, 200, 'Debug info retrieved')
}, {
  requireDatabase: true,
  requireAuth: true
})

import { NextRequest } from 'next/server'
import { User } from '@/lib/models/user.model'
import { Company } from '@/lib/models/company.model'
import { errorService, ErrorCode } from '@/lib/error-service'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON>,
  createSuccessResponse,
  AuthenticatedRequest
} from '@/lib/middleware/api-middleware'
import { connectToDatabase } from '@/lib/database/connection'

// GET /api/v1/debug/user - Debug current user data
export const GET = withErrorHandler(async (request: AuthenticatedRequest) => {
  validateMethod(request, ['GET'])

  // Get user ID from auth context
  const userId = request.user?.id
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }

  console.log('🔍 Debug User - User ID from token:', userId)
  console.log('🔍 Debug User - Request user object:', request.user)

  await connectToDatabase()

  // Find user with all details
  const user = await User.findById(userId)
    .populate('companyId')
    .lean()

  console.log('🔍 Debug User - User found in DB:', user ? 'YES' : 'NO')
  if (user) {
    console.log('🔍 Debug User - User details:', {
      id: user._id,
      email: user.email,
      role: user.role,
      companyId: user.companyId,
      isActive: user.isActive,
      isEmailVerified: user.isEmailVerified
    })
  }

  // If user has companyId, get company details
  let company = null
  if (user?.companyId) {
    company = await Company.findById(user.companyId).lean()
    console.log('🔍 Debug User - Company found:', company ? 'YES' : 'NO')
    if (company) {
      console.log('🔍 Debug User - Company details:', {
        id: company._id,
        name: company.name,
        slug: company.slug,
        admins: company.admins
      })
    }
  }

  // Check all companies where user is admin
  const companiesAsAdmin = await Company.find({
    admins: userId
  }).lean()

  console.log('🔍 Debug User - Companies where user is admin:', companiesAsAdmin.length)

  // Check all users with company_admin role
  const allCompanyAdmins = await User.find({
    role: 'company_admin'
  }).select('email companyId').lean()

  console.log('🔍 Debug User - All company admins:', allCompanyAdmins.length)

  return createSuccessResponse({
    user: user ? {
      id: user._id,
      email: user.email,
      role: user.role,
      companyId: user.companyId,
      isActive: user.isActive,
      isEmailVerified: user.isEmailVerified,
      profile: user.profile
    } : null,
    company,
    companiesAsAdmin: companiesAsAdmin.map(c => ({
      id: c._id,
      name: c.name,
      slug: c.slug
    })),
    allCompanyAdmins: allCompanyAdmins.map(u => ({
      id: u._id,
      email: u.email,
      companyId: u.companyId
    })),
    authContext: request.user
  }, 200, 'Debug user data retrieved')
}, {
  requireDatabase: true,
  requireAuth: true
})

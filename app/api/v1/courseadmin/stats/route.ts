import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { Course, CourseEnrollment, CourseSection, CourseLesson } from '@/lib/models/course.model'
import { User } from '@/lib/models/user.model'
import { authMiddleware } from '@/lib/middleware/auth.middleware'

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication required' },
        { status: authResult.status || 401 }
      )
    }

    // Check if user is admin or instructor
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'instructor') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Get query parameters for filtering
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '30' // days
    const instructorId = authResult.user.role === 'instructor' ? authResult.user.id : null

    // Calculate date range
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(endDate.getDate() - parseInt(timeRange))

    // Build course filter
    let courseFilter: any = {}
    if (instructorId) {
      courseFilter.instructorId = instructorId
    }

    // Get overall statistics
    const [
      totalCourses,
      publishedCourses,
      draftCourses,
      featuredCourses,
      totalStudents,
      totalRevenue,
      recentEnrollments,
      totalSections,
      totalLessons
    ] = await Promise.all([
      // Total courses
      Course.countDocuments(courseFilter),
      
      // Published courses
      Course.countDocuments({ ...courseFilter, isPublished: true }),
      
      // Draft courses
      Course.countDocuments({ ...courseFilter, isPublished: false }),
      
      // Featured courses
      Course.countDocuments({ ...courseFilter, isFeatured: true }),
      
      // Total students (unique enrollments)
      CourseEnrollment.aggregate([
        {
          $lookup: {
            from: 'courses',
            localField: 'courseId',
            foreignField: '_id',
            as: 'course'
          }
        },
        {
          $match: {
            'course.0': { $exists: true },
            ...(instructorId && { 'course.instructorId': instructorId })
          }
        },
        {
          $group: {
            _id: '$userId'
          }
        },
        {
          $count: 'totalStudents'
        }
      ]).then(result => result[0]?.totalStudents || 0),
      
      // Total revenue
      CourseEnrollment.aggregate([
        {
          $lookup: {
            from: 'courses',
            localField: 'courseId',
            foreignField: '_id',
            as: 'course'
          }
        },
        {
          $match: {
            'course.0': { $exists: true },
            paymentStatus: 'completed',
            ...(instructorId && { 'course.instructorId': instructorId })
          }
        },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: '$amountPaid' }
          }
        }
      ]).then(result => result[0]?.totalRevenue || 0),
      
      // Recent enrollments (last 30 days)
      CourseEnrollment.aggregate([
        {
          $lookup: {
            from: 'courses',
            localField: 'courseId',
            foreignField: '_id',
            as: 'course'
          }
        },
        {
          $match: {
            'course.0': { $exists: true },
            enrolledAt: { $gte: startDate },
            ...(instructorId && { 'course.instructorId': instructorId })
          }
        },
        {
          $count: 'recentEnrollments'
        }
      ]).then(result => result[0]?.recentEnrollments || 0),
      
      // Total sections
      CourseSection.aggregate([
        {
          $lookup: {
            from: 'courses',
            localField: 'courseId',
            foreignField: '_id',
            as: 'course'
          }
        },
        {
          $match: {
            'course.0': { $exists: true },
            ...(instructorId && { 'course.instructorId': instructorId })
          }
        },
        {
          $count: 'totalSections'
        }
      ]).then(result => result[0]?.totalSections || 0),
      
      // Total lessons
      CourseLesson.aggregate([
        {
          $lookup: {
            from: 'courses',
            localField: 'courseId',
            foreignField: '_id',
            as: 'course'
          }
        },
        {
          $match: {
            'course.0': { $exists: true },
            ...(instructorId && { 'course.instructorId': instructorId })
          }
        },
        {
          $count: 'totalLessons'
        }
      ]).then(result => result[0]?.totalLessons || 0)
    ])

    // Get top performing courses
    const topCourses = await Course.find(courseFilter)
      .sort({ totalStudents: -1, rating: -1 })
      .limit(5)
      .select('title slug totalStudents rating totalRevenue')
      .lean()

    // Get recent activity (enrollments by day for the last 7 days)
    const recentActivity = await CourseEnrollment.aggregate([
      {
        $lookup: {
          from: 'courses',
          localField: 'courseId',
          foreignField: '_id',
          as: 'course'
        }
      },
      {
        $match: {
          'course.0': { $exists: true },
          enrolledAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },
          ...(instructorId && { 'course.instructorId': instructorId })
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$enrolledAt'
            }
          },
          enrollments: { $sum: 1 },
          revenue: { $sum: '$amountPaid' }
        }
      },
      {
        $sort: { '_id': 1 }
      }
    ])

    // Calculate growth percentages (compare with previous period)
    const previousStartDate = new Date()
    previousStartDate.setDate(startDate.getDate() - parseInt(timeRange))
    
    const previousEnrollments = await CourseEnrollment.aggregate([
      {
        $lookup: {
          from: 'courses',
          localField: 'courseId',
          foreignField: '_id',
          as: 'course'
        }
      },
      {
        $match: {
          'course.0': { $exists: true },
          enrolledAt: { $gte: previousStartDate, $lt: startDate },
          ...(instructorId && { 'course.instructorId': instructorId })
        }
      },
      {
        $count: 'previousEnrollments'
      }
    ]).then(result => result[0]?.previousEnrollments || 0)

    const enrollmentGrowth = previousEnrollments > 0 
      ? ((recentEnrollments - previousEnrollments) / previousEnrollments) * 100 
      : recentEnrollments > 0 ? 100 : 0

    // Get course categories distribution
    const categoryStats = await Course.aggregate([
      {
        $match: courseFilter
      },
      {
        $lookup: {
          from: 'coursecategories',
          localField: 'categoryId',
          foreignField: '_id',
          as: 'category'
        }
      },
      {
        $group: {
          _id: '$category.name',
          count: { $sum: 1 },
          totalStudents: { $sum: '$totalStudents' }
        }
      },
      {
        $sort: { count: -1 }
      }
    ])

    return NextResponse.json({
      success: true,
      data: {
        overview: {
          totalCourses,
          publishedCourses,
          draftCourses,
          featuredCourses,
          totalStudents,
          totalRevenue,
          recentEnrollments,
          totalSections,
          totalLessons,
          enrollmentGrowth: Math.round(enrollmentGrowth * 100) / 100
        },
        topCourses,
        recentActivity,
        categoryStats,
        timeRange: parseInt(timeRange)
      }
    })

  } catch (error) {
    console.error('Get course admin stats error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get course statistics' },
      { status: 500 }
    )
  }
}

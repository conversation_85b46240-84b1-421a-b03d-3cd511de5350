import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { Application } from '@/lib/models/application.model'
import { Job } from '@/lib/models/job.model'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const timeframe = searchParams.get('timeframe') || '30' // days
    const type = searchParams.get('type') || 'overview'

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    const timeframeDate = new Date()
    timeframeDate.setDate(timeframeDate.getDate() - parseInt(timeframe))

    let analyticsData: any = {}

    if (type === 'overview' || type === 'all') {
      // Overview Analytics
      const overviewData = await getOverviewAnalytics(client._id, timeframeDate)
      analyticsData.overview = overviewData
    }

    if (type === 'applications' || type === 'all') {
      // Application Analytics
      const applicationData = await getApplicationAnalytics(client._id, timeframeDate)
      analyticsData.applications = applicationData
    }

    if (type === 'job_search' || type === 'all') {
      // Job Search Analytics
      const jobSearchData = await getJobSearchAnalytics(client._id, timeframeDate)
      analyticsData.jobSearch = jobSearchData
    }

    if (type === 'networking' || type === 'all') {
      // Networking Analytics
      const networkingData = await getNetworkingAnalytics(client._id, timeframeDate)
      analyticsData.networking = networkingData
    }

    if (type === 'performance' || type === 'all') {
      // Performance Analytics
      const performanceData = await getPerformanceAnalytics(client._id, timeframeDate)
      analyticsData.performance = performanceData
    }

    return NextResponse.json({
      success: true,
      data: {
        timeframe: parseInt(timeframe),
        generatedAt: new Date(),
        ...analyticsData
      }
    })

  } catch (error) {
    console.error('Get analytics error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch analytics data' },
      { status: 500 }
    )
  }
}

// Helper functions for different analytics types
async function getOverviewAnalytics(clientId: string, fromDate: Date) {
  const applications = await Application.find({
    client: clientId,
    createdAt: { $gte: fromDate }
  }).lean()

  const totalApplications = applications.length
  const interviewsScheduled = applications.filter(app => app.status === 'interview_scheduled').length
  const offersReceived = applications.filter(app => app.status === 'offer_received').length
  const rejections = applications.filter(app => app.status === 'rejected').length

  const responseRate = totalApplications > 0 ? 
    ((interviewsScheduled + offersReceived) / totalApplications * 100) : 0

  const successRate = totalApplications > 0 ? 
    (offersReceived / totalApplications * 100) : 0

  return {
    totalApplications,
    interviewsScheduled,
    offersReceived,
    rejections,
    responseRate: Math.round(responseRate * 100) / 100,
    successRate: Math.round(successRate * 100) / 100,
    averageResponseTime: await calculateAverageResponseTime(applications)
  }
}

async function getApplicationAnalytics(clientId: string, fromDate: Date) {
  const applications = await Application.find({
    client: clientId,
    createdAt: { $gte: fromDate }
  }).populate('job', 'title company industry location type').lean()

  // Applications by status
  const statusBreakdown = applications.reduce((acc: any, app) => {
    acc[app.status] = (acc[app.status] || 0) + 1
    return acc
  }, {})

  // Applications by industry
  const industryBreakdown = applications.reduce((acc: any, app) => {
    const industry = app.job?.industry || 'Unknown'
    acc[industry] = (acc[industry] || 0) + 1
    return acc
  }, {})

  // Applications by job type
  const jobTypeBreakdown = applications.reduce((acc: any, app) => {
    const type = app.job?.type || 'Unknown'
    acc[type] = (acc[type] || 0) + 1
    return acc
  }, {})

  // Applications timeline
  const timeline = await getApplicationTimeline(applications, fromDate)

  return {
    statusBreakdown,
    industryBreakdown,
    jobTypeBreakdown,
    timeline,
    totalApplications: applications.length
  }
}

async function getJobSearchAnalytics(clientId: string, fromDate: Date) {
  const client = await Client.findById(clientId).select('searchHistory savedJobs').lean()
  
  const searchHistory = client?.searchHistory || []
  const recentSearches = searchHistory.filter(search => 
    new Date(search.searchedAt) >= fromDate
  )

  // Most searched keywords
  const keywordFrequency = recentSearches.reduce((acc: any, search) => {
    const keywords = search.query.toLowerCase().split(' ')
    keywords.forEach(keyword => {
      if (keyword.length > 2) {
        acc[keyword] = (acc[keyword] || 0) + 1
      }
    })
    return acc
  }, {})

  // Search patterns by time
  const searchPatterns = await getSearchPatterns(recentSearches)

  // Saved jobs analytics
  const savedJobsCount = client?.savedJobs?.length || 0

  return {
    totalSearches: recentSearches.length,
    uniqueQueries: new Set(recentSearches.map(s => s.query)).size,
    keywordFrequency: Object.entries(keywordFrequency)
      .sort(([,a], [,b]) => (b as number) - (a as number))
      .slice(0, 10),
    searchPatterns,
    savedJobsCount,
    averageResultsPerSearch: recentSearches.length > 0 ? 
      recentSearches.reduce((sum, search) => sum + (search.resultsCount || 0), 0) / recentSearches.length : 0
  }
}

async function getNetworkingAnalytics(clientId: string, fromDate: Date) {
  const client = await Client.findById(clientId)
    .select('followedCompanies followedRecruiters messages activity')
    .lean()

  const followedCompaniesCount = client?.followedCompanies?.length || 0
  const followedRecruitersCount = client?.followedRecruiters?.length || 0

  // Messages analytics
  const sentMessages = client?.messages?.sent?.filter(msg => 
    new Date(msg.sentAt) >= fromDate
  ) || []
  
  const receivedMessages = client?.messages?.received?.filter(msg => 
    new Date(msg.sentAt) >= fromDate
  ) || []

  // Networking activity
  const networkingActivity = client?.activity?.recentActivity?.filter(activity => 
    ['company_followed', 'recruiter_followed', 'message_sent'].includes(activity.type) &&
    new Date(activity.createdAt) >= fromDate
  ) || []

  return {
    followedCompaniesCount,
    followedRecruitersCount,
    messagesSent: sentMessages.length,
    messagesReceived: receivedMessages.length,
    networkingActivity: networkingActivity.length,
    responseRate: sentMessages.length > 0 ? 
      (receivedMessages.length / sentMessages.length * 100) : 0
  }
}

async function getPerformanceAnalytics(clientId: string, fromDate: Date) {
  const applications = await Application.find({
    client: clientId,
    createdAt: { $gte: fromDate }
  }).lean()

  // Calculate performance metrics
  const totalApplications = applications.length
  const interviews = applications.filter(app => app.status === 'interview_scheduled').length
  const offers = applications.filter(app => app.status === 'offer_received').length

  // Performance trends
  const performanceTrend = await getPerformanceTrend(applications)

  // Benchmark comparison (industry averages)
  const benchmarks = {
    averageApplicationsPerWeek: 5,
    averageResponseRate: 15,
    averageInterviewRate: 8,
    averageOfferRate: 2
  }

  const userMetrics = {
    applicationsPerWeek: totalApplications / (parseInt('30') / 7),
    responseRate: totalApplications > 0 ? ((interviews + offers) / totalApplications * 100) : 0,
    interviewRate: totalApplications > 0 ? (interviews / totalApplications * 100) : 0,
    offerRate: totalApplications > 0 ? (offers / totalApplications * 100) : 0
  }

  return {
    userMetrics,
    benchmarks,
    performanceTrend,
    recommendations: generateRecommendations(userMetrics, benchmarks)
  }
}

// Additional helper functions
async function calculateAverageResponseTime(applications: any[]) {
  const responseTimes = applications
    .filter(app => app.timeline && app.timeline.length > 1)
    .map(app => {
      const applied = new Date(app.timeline[0].date)
      const response = new Date(app.timeline[1].date)
      return response.getTime() - applied.getTime()
    })

  if (responseTimes.length === 0) return 0

  const averageMs = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
  return Math.round(averageMs / (1000 * 60 * 60 * 24)) // Convert to days
}

async function getApplicationTimeline(applications: any[], fromDate: Date) {
  const timeline: any = {}
  
  applications.forEach(app => {
    const date = new Date(app.createdAt).toISOString().split('T')[0]
    timeline[date] = (timeline[date] || 0) + 1
  })

  return timeline
}

async function getSearchPatterns(searches: any[]) {
  const patterns: any = {}
  
  searches.forEach(search => {
    const hour = new Date(search.searchedAt).getHours()
    patterns[hour] = (patterns[hour] || 0) + 1
  })

  return patterns
}

async function getPerformanceTrend(applications: any[]) {
  // Group applications by week and calculate success rates
  const weeklyData: any = {}
  
  applications.forEach(app => {
    const week = getWeekNumber(new Date(app.createdAt))
    if (!weeklyData[week]) {
      weeklyData[week] = { total: 0, interviews: 0, offers: 0 }
    }
    weeklyData[week].total++
    if (app.status === 'interview_scheduled') weeklyData[week].interviews++
    if (app.status === 'offer_received') weeklyData[week].offers++
  })

  return Object.entries(weeklyData).map(([week, data]: [string, any]) => ({
    week,
    applications: data.total,
    interviewRate: data.total > 0 ? (data.interviews / data.total * 100) : 0,
    offerRate: data.total > 0 ? (data.offers / data.total * 100) : 0
  }))
}

function getWeekNumber(date: Date) {
  const firstDayOfYear = new Date(date.getFullYear(), 0, 1)
  const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000
  return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7)
}

function generateRecommendations(userMetrics: any, benchmarks: any) {
  const recommendations = []

  if (userMetrics.applicationsPerWeek < benchmarks.averageApplicationsPerWeek) {
    recommendations.push({
      type: 'increase_applications',
      message: 'Consider applying to more jobs per week to increase your chances',
      priority: 'high'
    })
  }

  if (userMetrics.responseRate < benchmarks.averageResponseRate) {
    recommendations.push({
      type: 'improve_profile',
      message: 'Optimize your profile and resume to improve response rates',
      priority: 'medium'
    })
  }

  return recommendations
}

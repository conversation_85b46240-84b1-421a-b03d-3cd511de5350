import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const MAX_FILE_SIZE = parseInt(process.env.MAX_FILE_SIZE || '10485760') // 10MB
const ALLOWED_FILE_TYPES = (process.env.ALLOWED_FILE_TYPES || 'pdf,doc,docx,jpg,jpeg,png').split(',')

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Parse form data
    const formData = await request.formData()
    const file = formData.get('file') as File
    const type = formData.get('type') as string
    const description = formData.get('description') as string || ''
    const isDefault = formData.get('isDefault') === 'true'

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    if (!['resume', 'cover_letter', 'portfolio', 'certificate', 'other'].includes(type)) {
      return NextResponse.json(
        { error: 'Invalid document type. Allowed types: resume, cover_letter, portfolio, certificate, other' },
        { status: 400 }
      )
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: `File size exceeds ${MAX_FILE_SIZE / 1024 / 1024}MB limit` },
        { status: 400 }
      )
    }

    // Validate file type
    const fileExtension = file.name.split('.').pop()?.toLowerCase()
    if (!fileExtension || !ALLOWED_FILE_TYPES.includes(fileExtension)) {
      return NextResponse.json(
        { error: `File type not allowed. Allowed types: ${ALLOWED_FILE_TYPES.join(', ')}` },
        { status: 400 }
      )
    }

    // Create upload directory
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'documents', userId)
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true })
    }

    // Generate unique filename
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const filename = `${type}_${timestamp}_${randomString}.${fileExtension}`
    const filepath = join(uploadDir, filename)

    // Save file
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    await writeFile(filepath, buffer)

    // Create file URL
    const fileUrl = `/uploads/documents/${userId}/${filename}`

    // Prepare document data for database
    const documentData = {
      id: `doc_${timestamp}_${randomString}`,
      type,
      filename,
      originalName: file.name,
      size: file.size,
      mimeType: file.type,
      url: fileUrl,
      description,
      isDefault,
      uploadedAt: new Date(),
      version: 1
    }

    // Update client documents
    const updatePath = `documents.${type}`
    await Client.findOneAndUpdate(
      { user: userId },
      {
        $push: { [updatePath]: documentData },
        $set: { 'activity.lastProfileUpdate': new Date() }
      },
      { new: true }
    )

    // If this is set as default, unset other defaults of the same type
    if (isDefault) {
      await Client.updateOne(
        { user: userId },
        {
          $set: {
            [`documents.${type}.$[elem].isDefault`]: false
          }
        },
        {
          arrayFilters: [{ 'elem.id': { $ne: documentData.id } }]
        }
      )

      // Set this document as default
      await Client.updateOne(
        { user: userId, [`documents.${type}.id`]: documentData.id },
        {
          $set: {
            [`documents.${type}.$.isDefault`]: true
          }
        }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Document uploaded successfully',
      data: {
        document: documentData,
        uploadUrl: fileUrl
      }
    }, { status: 201 })

  } catch (error) {
    console.error('Document upload error:', error)
    return NextResponse.json(
      { error: 'Failed to upload document' },
      { status: 500 }
    )
  }
}

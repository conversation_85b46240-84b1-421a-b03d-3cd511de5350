import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const documentSchema = z.object({
  type: z.enum(['resume', 'cover_letter', 'portfolio', 'certificate', 'other']),
  filename: z.string().min(1),
  originalName: z.string().min(1),
  size: z.number().positive(),
  mimeType: z.string(),
  url: z.string().url(),
  description: z.string().optional(),
  isDefault: z.boolean().optional().default(false)
})

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')

    // Get client profile
    const client = await Client.findOne({ user: userId })
      .select('documents')
      .lean()
    
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    let documents = client.documents || {}

    // Filter by type if specified
    if (type && ['resume', 'cover_letter', 'portfolio', 'certificate', 'other'].includes(type)) {
      documents = {
        [type]: documents[type] || []
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        documents,
        totalCount: Object.values(documents).flat().length
      }
    })

  } catch (error) {
    console.error('Get documents error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch documents' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const body = await request.json()

    // Validate request body
    const validationResult = documentSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid document data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const documentData = validationResult.data

    // Create document object
    const document = {
      id: generateDocumentId(),
      ...documentData,
      uploadedAt: new Date(),
      version: 1
    }

    // Update client documents
    const updatePath = `documents.${documentData.type}`
    const client = await Client.findOneAndUpdate(
      { user: userId },
      { 
        $push: { [updatePath]: document },
        $set: { 'activity.lastProfileUpdate': new Date() }
      },
      { new: true, select: 'documents' }
    )

    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // If this is set as default, unset other defaults of the same type
    if (documentData.isDefault) {
      await Client.updateOne(
        { user: userId },
        { 
          $set: { 
            [`documents.${documentData.type}.$[elem].isDefault`]: false 
          }
        },
        { 
          arrayFilters: [{ 'elem.id': { $ne: document.id } }] 
        }
      )
      
      // Set this document as default
      await Client.updateOne(
        { user: userId, [`documents.${documentData.type}.id`]: document.id },
        { 
          $set: { 
            [`documents.${documentData.type}.$.isDefault`]: true 
          }
        }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Document uploaded successfully',
      data: { document }
    }, { status: 201 })

  } catch (error) {
    console.error('Upload document error:', error)
    return NextResponse.json(
      { error: 'Failed to upload document' },
      { status: 500 }
    )
  }
}

function generateDocumentId(): string {
  return `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const updateDocumentSchema = z.object({
  description: z.string().optional(),
  isDefault: z.boolean().optional()
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const documentId = params.id

    // Get client profile
    const client = await Client.findOne({ user: userId })
      .select('documents')
      .lean()
    
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Find document across all types
    let foundDocument = null
    let documentType = null

    for (const [type, documents] of Object.entries(client.documents || {})) {
      const document = (documents as any[]).find(doc => doc.id === documentId)
      if (document) {
        foundDocument = document
        documentType = type
        break
      }
    }

    if (!foundDocument) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        document: foundDocument,
        type: documentType
      }
    })

  } catch (error) {
    console.error('Get document error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch document' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const documentId = params.id
    const body = await request.json()

    // Validate request body
    const validationResult = updateDocumentSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid update data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const updateData = validationResult.data

    // Find and update document
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    let documentFound = false
    let documentType = null

    // Find document across all types and update
    for (const [type, documents] of Object.entries(client.documents || {})) {
      const documentIndex = (documents as any[]).findIndex(doc => doc.id === documentId)
      if (documentIndex !== -1) {
        documentFound = true
        documentType = type

        // Update document
        const updatePath = `documents.${type}.${documentIndex}`
        const updateFields: any = {
          'activity.lastProfileUpdate': new Date()
        }

        if (updateData.description !== undefined) {
          updateFields[`${updatePath}.description`] = updateData.description
        }

        if (updateData.isDefault !== undefined) {
          updateFields[`${updatePath}.isDefault`] = updateData.isDefault

          // If setting as default, unset other defaults of the same type
          if (updateData.isDefault) {
            await Client.updateOne(
              { user: userId },
              { 
                $set: { 
                  [`documents.${type}.$[elem].isDefault`]: false 
                }
              },
              { 
                arrayFilters: [{ 'elem.id': { $ne: documentId } }] 
              }
            )
          }
        }

        await Client.updateOne(
          { user: userId },
          { $set: updateFields }
        )

        break
      }
    }

    if (!documentFound) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Document updated successfully'
    })

  } catch (error) {
    console.error('Update document error:', error)
    return NextResponse.json(
      { error: 'Failed to update document' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const documentId = params.id

    // Find and remove document
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    let documentFound = false

    // Find document across all types and remove
    for (const [type, documents] of Object.entries(client.documents || {})) {
      const documentIndex = (documents as any[]).findIndex(doc => doc.id === documentId)
      if (documentIndex !== -1) {
        documentFound = true

        await Client.updateOne(
          { user: userId },
          { 
            $pull: { [`documents.${type}`]: { id: documentId } },
            $set: { 'activity.lastProfileUpdate': new Date() }
          }
        )

        break
      }
    }

    if (!documentFound) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Document deleted successfully'
    })

  } catch (error) {
    console.error('Delete document error:', error)
    return NextResponse.json(
      { error: 'Failed to delete document' },
      { status: 500 }
    )
  }
}

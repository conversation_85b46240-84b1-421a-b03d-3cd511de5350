import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const markNotificationSchema = z.object({
  notificationIds: z.array(z.string()),
  action: z.enum(['read', 'unread', 'delete'])
})

const notificationPreferencesSchema = z.object({
  email: z.object({
    jobAlerts: z.boolean(),
    applicationUpdates: z.boolean(),
    messages: z.boolean(),
    companyUpdates: z.boolean(),
    networkingActivity: z.boolean(),
    weeklyDigest: z.boolean()
  }),
  push: z.object({
    jobAlerts: z.boolean(),
    applicationUpdates: z.boolean(),
    messages: z.boolean(),
    companyUpdates: z.boolean(),
    networkingActivity: z.boolean()
  }),
  frequency: z.object({
    jobAlerts: z.enum(['immediate', 'daily', 'weekly']),
    digest: z.enum(['daily', 'weekly', 'monthly'])
  })
})

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const type = searchParams.get('type') // 'all', 'unread', 'read'
    const category = searchParams.get('category') // 'job_alert', 'application_update', etc.

    // Get client
    const client = await Client.findOne({ user: userId })
      .select('notifications')
      .lean()
    
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    let notifications = client.notifications?.items || []

    // Apply filters
    if (type === 'unread') {
      notifications = notifications.filter(n => !n.isRead)
    } else if (type === 'read') {
      notifications = notifications.filter(n => n.isRead)
    }

    if (category) {
      notifications = notifications.filter(n => n.category === category)
    }

    // Sort by date (newest first)
    notifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())

    // Apply pagination
    const skip = (page - 1) * limit
    const paginatedNotifications = notifications.slice(skip, skip + limit)

    // Get counts
    const totalCount = notifications.length
    const unreadCount = (client.notifications?.items || []).filter(n => !n.isRead).length
    const totalPages = Math.ceil(totalCount / limit)

    return NextResponse.json({
      success: true,
      data: {
        notifications: paginatedNotifications,
        unreadCount,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    })

  } catch (error) {
    console.error('Get notifications error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch notifications' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const body = await request.json()

    // Validate request body
    const validationResult = markNotificationSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { notificationIds, action } = validationResult.data

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    let updateQuery: any = {}

    switch (action) {
      case 'read':
        updateQuery = {
          $set: {
            'notifications.items.$[elem].isRead': true,
            'notifications.items.$[elem].readAt': new Date()
          }
        }
        break
      case 'unread':
        updateQuery = {
          $set: {
            'notifications.items.$[elem].isRead': false
          },
          $unset: {
            'notifications.items.$[elem].readAt': ''
          }
        }
        break
      case 'delete':
        updateQuery = {
          $pull: {
            'notifications.items': { id: { $in: notificationIds } }
          }
        }
        break
    }

    if (action !== 'delete') {
      await Client.updateOne(
        { user: userId },
        updateQuery,
        {
          arrayFilters: [{ 'elem.id': { $in: notificationIds } }]
        }
      )
    } else {
      await Client.updateOne(
        { user: userId },
        updateQuery
      )
    }

    // Update unread count
    const updatedClient = await Client.findOne({ user: userId })
      .select('notifications')
      .lean()
    
    const unreadCount = (updatedClient?.notifications?.items || [])
      .filter(n => !n.isRead).length

    await Client.updateOne(
      { user: userId },
      { $set: { 'notifications.unreadCount': unreadCount } }
    )

    return NextResponse.json({
      success: true,
      message: `Notifications ${action === 'delete' ? 'deleted' : `marked as ${action}`} successfully`,
      data: { unreadCount }
    })

  } catch (error) {
    console.error('Update notifications error:', error)
    return NextResponse.json(
      { error: 'Failed to update notifications' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const body = await request.json()

    // Validate notification preferences
    const validationResult = notificationPreferencesSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid preferences data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const preferences = validationResult.data

    // Update client notification preferences
    await Client.findOneAndUpdate(
      { user: userId },
      {
        $set: {
          'notifications.preferences': preferences,
          'notifications.lastUpdated': new Date()
        }
      },
      { upsert: true }
    )

    return NextResponse.json({
      success: true,
      message: 'Notification preferences updated successfully',
      data: { preferences }
    })

  } catch (error) {
    console.error('Update notification preferences error:', error)
    return NextResponse.json(
      { error: 'Failed to update notification preferences' },
      { status: 500 }
    )
  }
}

// Helper function to create notifications
export async function createNotification(
  clientId: string,
  notification: {
    title: string
    message: string
    category: string
    type: 'info' | 'success' | 'warning' | 'error'
    actionUrl?: string
    metadata?: Record<string, any>
  }
) {
  try {
    const notificationData = {
      id: generateNotificationId(),
      ...notification,
      isRead: false,
      createdAt: new Date()
    }

    await Client.findByIdAndUpdate(clientId, {
      $push: {
        'notifications.items': {
          $each: [notificationData],
          $slice: -100 // Keep only last 100 notifications
        }
      },
      $inc: { 'notifications.unreadCount': 1 }
    })

    // Check if we should send email notification
    const client = await Client.findById(clientId)
      .select('notifications.preferences user')
      .populate('user', 'email')
      .lean()

    if (shouldSendEmailNotification(client, notification.category)) {
      await sendEmailNotification(client.user.email, notificationData)
    }

    return notificationData
  } catch (error) {
    console.error('Create notification error:', error)
    throw error
  }
}

// Helper functions
function generateNotificationId(): string {
  return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

function shouldSendEmailNotification(client: any, category: string): boolean {
  const preferences = client?.notifications?.preferences?.email
  if (!preferences) return false

  const categoryMap: Record<string, string> = {
    'job_alert': 'jobAlerts',
    'application_update': 'applicationUpdates',
    'message': 'messages',
    'company_update': 'companyUpdates',
    'networking': 'networkingActivity'
  }

  const prefKey = categoryMap[category]
  return prefKey ? preferences[prefKey] : false
}

async function sendEmailNotification(email: string, notification: any) {
  // This would integrate with your email service (SendGrid, AWS SES, etc.)
  // For now, we'll just log it
  console.log(`Email notification sent to ${email}:`, notification.title)
  
  // Example implementation:
  /*
  try {
    await emailService.send({
      to: email,
      subject: notification.title,
      template: 'notification',
      data: {
        title: notification.title,
        message: notification.message,
        actionUrl: notification.actionUrl
      }
    })
  } catch (error) {
    console.error('Email notification error:', error)
  }
  */
}

// Notification categories
export const NotificationCategories = {
  JOB_ALERT: 'job_alert',
  APPLICATION_UPDATE: 'application_update',
  MESSAGE: 'message',
  COMPANY_UPDATE: 'company_update',
  NETWORKING: 'networking',
  SYSTEM: 'system'
} as const

// Notification types
export const NotificationTypes = {
  INFO: 'info',
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error'
} as const

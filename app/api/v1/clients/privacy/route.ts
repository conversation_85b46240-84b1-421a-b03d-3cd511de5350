import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { User } from '@/lib/models/user.model'
import { anonymizeData, generateDataPortabilityReport, createAuditLog } from '@/lib/utils/security'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const privacySettingsSchema = z.object({
  profileVisibility: z.enum(['public', 'private', 'connections_only']),
  searchableByEmail: z.boolean(),
  searchableByName: z.boolean(),
  showOnlineStatus: z.boolean(),
  allowDirectMessages: z.enum(['everyone', 'connections_only', 'nobody']),
  shareActivityStatus: z.boolean(),
  shareApplicationStats: z.boolean(),
  allowRecruitersToContact: z.boolean(),
  dataProcessingConsent: z.object({
    analytics: z.boolean(),
    marketing: z.boolean(),
    thirdPartySharing: z.boolean(),
    profileEnhancement: z.boolean()
  }),
  cookiePreferences: z.object({
    necessary: z.boolean(),
    analytics: z.boolean(),
    marketing: z.boolean(),
    functional: z.boolean()
  })
})

const dataRequestSchema = z.object({
  requestType: z.enum(['access', 'portability', 'deletion', 'rectification']),
  reason: z.string().optional(),
  specificData: z.array(z.string()).optional()
})

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id

    // Get client privacy settings
    const client = await Client.findOne({ user: userId })
      .select('privacySettings dataProcessingConsent')
      .lean()

    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Default privacy settings if not set
    const defaultSettings = {
      profileVisibility: 'public',
      searchableByEmail: true,
      searchableByName: true,
      showOnlineStatus: true,
      allowDirectMessages: 'everyone',
      shareActivityStatus: true,
      shareApplicationStats: false,
      allowRecruitersToContact: true,
      dataProcessingConsent: {
        analytics: true,
        marketing: false,
        thirdPartySharing: false,
        profileEnhancement: true
      },
      cookiePreferences: {
        necessary: true,
        analytics: true,
        marketing: false,
        functional: true
      }
    }

    const privacySettings = {
      ...defaultSettings,
      ...client.privacySettings,
      dataProcessingConsent: {
        ...defaultSettings.dataProcessingConsent,
        ...client.dataProcessingConsent
      }
    }

    return NextResponse.json({
      success: true,
      data: { privacySettings }
    })

  } catch (error) {
    console.error('Get privacy settings error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch privacy settings' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const body = await request.json()

    // Validate privacy settings
    const validationResult = privacySettingsSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid privacy settings',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const privacySettings = validationResult.data

    // Update client privacy settings
    await Client.findOneAndUpdate(
      { user: userId },
      {
        $set: {
          privacySettings: {
            profileVisibility: privacySettings.profileVisibility,
            searchableByEmail: privacySettings.searchableByEmail,
            searchableByName: privacySettings.searchableByName,
            showOnlineStatus: privacySettings.showOnlineStatus,
            allowDirectMessages: privacySettings.allowDirectMessages,
            shareActivityStatus: privacySettings.shareActivityStatus,
            shareApplicationStats: privacySettings.shareApplicationStats,
            allowRecruitersToContact: privacySettings.allowRecruitersToContact,
            cookiePreferences: privacySettings.cookiePreferences,
            lastUpdated: new Date()
          },
          dataProcessingConsent: {
            ...privacySettings.dataProcessingConsent,
            consentDate: new Date(),
            ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
            userAgent: request.headers.get('user-agent') || 'unknown'
          }
        }
      },
      { upsert: true }
    )

    // Create audit log
    const auditLog = createAuditLog(
      userId,
      'privacy_settings_update',
      'privacy_settings',
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown',
      true,
      undefined,
      { updatedSettings: Object.keys(privacySettings) }
    )

    // Log the audit entry (in a real implementation, you'd save this to an audit log collection)
    console.log('Privacy settings audit:', auditLog)

    return NextResponse.json({
      success: true,
      message: 'Privacy settings updated successfully',
      data: { privacySettings }
    })

  } catch (error) {
    console.error('Update privacy settings error:', error)
    return NextResponse.json(
      { error: 'Failed to update privacy settings' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const body = await request.json()

    // Validate data request
    const validationResult = dataRequestSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid data request',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { requestType, reason, specificData } = validationResult.data

    // Get client data
    const client = await Client.findOne({ user: userId })
      .populate('user', 'email createdAt')
      .lean()

    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    let responseData: any = {}

    switch (requestType) {
      case 'access':
        // Right to access - provide user with their data
        responseData = await handleDataAccessRequest(client, specificData)
        break

      case 'portability':
        // Right to data portability - provide data in structured format
        responseData = generateDataPortabilityReport(client)
        break

      case 'deletion':
        // Right to erasure - delete user data
        responseData = await handleDataDeletionRequest(userId, reason)
        break

      case 'rectification':
        // Right to rectification - correct inaccurate data
        responseData = await handleDataRectificationRequest(client, specificData)
        break

      default:
        return NextResponse.json(
          { error: 'Invalid request type' },
          { status: 400 }
        )
    }

    // Create audit log
    const auditLog = createAuditLog(
      userId,
      `data_${requestType}_request`,
      'privacy_request',
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown',
      true,
      undefined,
      { requestType, reason, specificData }
    )

    console.log('Privacy request audit:', auditLog)

    return NextResponse.json({
      success: true,
      message: `Data ${requestType} request processed successfully`,
      data: responseData,
      requestId: generateRequestId()
    })

  } catch (error) {
    console.error('Privacy request error:', error)
    return NextResponse.json(
      { error: 'Failed to process privacy request' },
      { status: 500 }
    )
  }
}

// Handle data access request
async function handleDataAccessRequest(client: any, specificData?: string[]) {
  const sensitiveFields = ['password', 'tokens', 'internalNotes']
  
  let accessData = { ...client }

  // Remove sensitive fields
  sensitiveFields.forEach(field => {
    delete accessData[field]
  })

  // If specific data requested, filter to only those fields
  if (specificData && specificData.length > 0) {
    const filteredData: any = {}
    specificData.forEach(field => {
      if (accessData[field] !== undefined) {
        filteredData[field] = accessData[field]
      }
    })
    accessData = filteredData
  }

  return {
    requestType: 'access',
    processedAt: new Date().toISOString(),
    data: accessData,
    dataCategories: Object.keys(accessData),
    note: 'This is all the personal data we have about you in our system.'
  }
}

// Handle data deletion request
async function handleDataDeletionRequest(userId: string, reason?: string) {
  // In a real implementation, you would:
  // 1. Check if there are legal obligations to retain data
  // 2. Anonymize data instead of hard deletion where required
  // 3. Schedule deletion after grace period
  // 4. Notify other systems/services

  // For now, we'll mark the account for deletion
  await User.findByIdAndUpdate(userId, {
    $set: {
      deletionRequested: true,
      deletionRequestDate: new Date(),
      deletionReason: reason,
      status: 'pending_deletion'
    }
  })

  await Client.findOneAndUpdate(
    { user: userId },
    {
      $set: {
        deletionRequested: true,
        deletionRequestDate: new Date(),
        deletionReason: reason
      }
    }
  )

  return {
    requestType: 'deletion',
    processedAt: new Date().toISOString(),
    status: 'scheduled',
    deletionDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
    note: 'Your account has been scheduled for deletion. You have 30 days to cancel this request.',
    cancellationInstructions: 'To cancel deletion, contact support or log in to your account.'
  }
}

// Handle data rectification request
async function handleDataRectificationRequest(client: any, specificData?: string[]) {
  return {
    requestType: 'rectification',
    processedAt: new Date().toISOString(),
    status: 'manual_review_required',
    note: 'Your rectification request has been received and will be reviewed manually.',
    instructions: 'Please provide the correct information and we will update your records.',
    reviewTimeframe: '5-10 business days',
    contactInfo: '<EMAIL>'
  }
}

// Generate unique request ID
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// Privacy compliance utilities
export async function checkPrivacyCompliance(clientId: string) {
  const client = await Client.findById(clientId).lean()
  if (!client) return null

  const compliance = {
    hasPrivacySettings: !!client.privacySettings,
    hasDataProcessingConsent: !!client.dataProcessingConsent,
    consentDate: client.dataProcessingConsent?.consentDate,
    cookieConsent: !!client.privacySettings?.cookiePreferences,
    profileVisibility: client.privacySettings?.profileVisibility || 'public',
    dataRetentionCompliant: true, // Would check actual retention periods
    gdprCompliant: !!(client.privacySettings && client.dataProcessingConsent),
    lastPrivacyUpdate: client.privacySettings?.lastUpdated
  }

  return compliance
}

// Anonymize user data for analytics
export async function anonymizeUserDataForAnalytics(clientId: string) {
  const client = await Client.findById(clientId).lean()
  if (!client) return null

  const sensitiveFields = [
    'profile.firstName', 'profile.lastName', 'profile.email', 
    'profile.phone', 'profile.address', 'user.email'
  ]

  return anonymizeData(client, sensitiveFields)
}

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { User } from '@/lib/models/user.model'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const threadId = params.id
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')

    // Get client
    const client = await Client.findOne({ user: userId })
      .populate('messages.sent.recipient', 'profile role recruiterProfile')
      .populate('messages.received.sender', 'profile role recruiterProfile')
      .lean()
    
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Get all messages for this thread
    const allMessages = [
      ...(client.messages?.sent || []).map(msg => ({ ...msg, type: 'sent' })),
      ...(client.messages?.received || []).map(msg => ({ ...msg, type: 'received' }))
    ].filter(msg => msg.threadId === threadId)

    if (allMessages.length === 0) {
      return NextResponse.json(
        { error: 'Conversation not found' },
        { status: 404 }
      )
    }

    // Sort messages by date
    allMessages.sort((a, b) => new Date(a.sentAt).getTime() - new Date(b.sentAt).getTime())

    // Get conversation participant
    const firstMessage = allMessages[0]
    const otherParticipantId = firstMessage.type === 'sent' ? 
      firstMessage.recipient._id : 
      firstMessage.sender._id

    const otherParticipant = firstMessage.type === 'sent' ? 
      firstMessage.recipient : 
      firstMessage.sender

    // Format messages for response
    const formattedMessages = allMessages.map(message => ({
      id: message.id,
      content: message.content,
      subject: message.subject,
      attachments: message.attachments || [],
      sentAt: message.sentAt,
      readAt: message.readAt,
      isFromMe: message.type === 'sent',
      isRead: message.isRead,
      replyTo: message.replyTo,
      sender: message.type === 'sent' ? {
        id: client._id,
        name: getClientName(client),
        avatar: client.user?.profile?.avatar || '',
        role: 'client'
      } : {
        id: message.sender._id,
        name: getParticipantName(message.sender),
        avatar: message.sender.profile?.avatar || '',
        role: message.sender.role || 'client',
        title: getParticipantTitle(message.sender)
      }
    }))

    // Apply pagination (most recent messages first for display)
    const skip = (page - 1) * limit
    const paginatedMessages = formattedMessages.slice(-skip - limit, formattedMessages.length - skip)

    // Mark received messages as read
    const unreadMessageIds = allMessages
      .filter(msg => msg.type === 'received' && !msg.isRead)
      .map(msg => msg.id)

    if (unreadMessageIds.length > 0) {
      await markMessagesAsRead(client._id, unreadMessageIds)
    }

    const totalCount = allMessages.length
    const totalPages = Math.ceil(totalCount / limit)

    return NextResponse.json({
      success: true,
      data: {
        threadId,
        participant: {
          id: otherParticipantId,
          name: getParticipantName(otherParticipant),
          avatar: otherParticipant.profile?.avatar || '',
          role: otherParticipant.role || 'client',
          title: getParticipantTitle(otherParticipant),
          isOnline: isParticipantOnline(otherParticipant)
        },
        messages: paginatedMessages,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    })

  } catch (error) {
    console.error('Get conversation error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch conversation' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const messageId = params.id
    const body = await request.json()

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Mark message as read
    if (body.action === 'mark_read') {
      await markMessagesAsRead(client._id, [messageId])
      
      return NextResponse.json({
        success: true,
        message: 'Message marked as read'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Update message error:', error)
    return NextResponse.json(
      { error: 'Failed to update message' },
      { status: 500 }
    )
  }
}

// Helper functions
async function markMessagesAsRead(clientId: string, messageIds: string[]) {
  await Client.updateOne(
    { _id: clientId },
    {
      $set: {
        'messages.received.$[elem].isRead': true,
        'messages.received.$[elem].readAt': new Date()
      },
      $inc: { 'messages.unreadCount': -messageIds.length }
    },
    {
      arrayFilters: [{ 'elem.id': { $in: messageIds } }]
    }
  )
}

function getClientName(client: any): string {
  if (!client.user?.profile) return 'Unknown User'
  
  const firstName = client.user.profile.firstName || ''
  const lastName = client.user.profile.lastName || ''
  return `${firstName} ${lastName}`.trim() || 'Unknown User'
}

function getParticipantName(participant: any): string {
  if (!participant.profile) return 'Unknown User'
  
  const firstName = participant.profile.firstName || ''
  const lastName = participant.profile.lastName || ''
  return `${firstName} ${lastName}`.trim() || 'Unknown User'
}

function getParticipantTitle(participant: any): string {
  if (participant.role === 'recruiter') {
    return participant.recruiterProfile?.title || 'Recruiter'
  }
  return participant.profile?.title || 'Job Seeker'
}

function isParticipantOnline(participant: any): boolean {
  if (!participant.activity?.lastLogin) return false
  
  const lastLogin = new Date(participant.activity.lastLogin)
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
  
  return lastLogin > fiveMinutesAgo
}

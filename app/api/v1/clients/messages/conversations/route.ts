import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { User } from '@/lib/models/user.model'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    // Get client
    const client = await Client.findOne({ user: userId })
      .populate('messages.sent.recipient', 'profile role')
      .populate('messages.received.sender', 'profile role')
      .lean()
    
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Get all messages and group by thread
    const allMessages = [
      ...(client.messages?.sent || []).map(msg => ({ ...msg, type: 'sent' })),
      ...(client.messages?.received || []).map(msg => ({ ...msg, type: 'received' }))
    ]

    // Group messages by threadId
    const conversationMap = new Map()
    
    allMessages.forEach(message => {
      const threadId = message.threadId
      if (!conversationMap.has(threadId)) {
        conversationMap.set(threadId, {
          threadId,
          messages: [],
          participants: new Set(),
          lastMessage: null,
          unreadCount: 0
        })
      }
      
      const conversation = conversationMap.get(threadId)
      conversation.messages.push(message)
      
      // Add participants
      if (message.type === 'sent') {
        conversation.participants.add(message.recipient)
      } else {
        conversation.participants.add(message.sender)
      }
      
      // Update last message
      if (!conversation.lastMessage || new Date(message.sentAt) > new Date(conversation.lastMessage.sentAt)) {
        conversation.lastMessage = message
      }
      
      // Count unread messages
      if (message.type === 'received' && !message.isRead) {
        conversation.unreadCount++
      }
    })

    // Convert to array and format
    const conversations = Array.from(conversationMap.values())
      .map(conv => {
        const participants = Array.from(conv.participants)
        const otherParticipant = participants.find(p => p._id.toString() !== client._id.toString())
        
        return {
          threadId: conv.threadId,
          participant: otherParticipant ? {
            id: otherParticipant._id,
            name: getParticipantName(otherParticipant),
            avatar: otherParticipant.profile?.avatar || '',
            role: otherParticipant.role || 'client',
            title: getParticipantTitle(otherParticipant),
            isOnline: isParticipantOnline(otherParticipant)
          } : null,
          lastMessage: conv.lastMessage ? {
            id: conv.lastMessage.id,
            content: conv.lastMessage.content,
            subject: conv.lastMessage.subject,
            sentAt: conv.lastMessage.sentAt,
            isFromMe: conv.lastMessage.type === 'sent',
            isRead: conv.lastMessage.isRead
          } : null,
          messageCount: conv.messages.length,
          unreadCount: conv.unreadCount,
          updatedAt: conv.lastMessage?.sentAt || new Date()
        }
      })
      .filter(conv => conv.participant) // Remove conversations without valid participants
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())

    // Apply pagination
    const paginatedConversations = conversations.slice(skip, skip + limit)
    const totalCount = conversations.length
    const totalPages = Math.ceil(totalCount / limit)

    return NextResponse.json({
      success: true,
      data: {
        conversations: paginatedConversations,
        totalUnread: conversations.reduce((sum, conv) => sum + conv.unreadCount, 0),
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    })

  } catch (error) {
    console.error('Get conversations error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch conversations' },
      { status: 500 }
    )
  }
}

// Helper functions
function getParticipantName(participant: any): string {
  if (!participant.profile) return 'Unknown User'
  
  const firstName = participant.profile.firstName || ''
  const lastName = participant.profile.lastName || ''
  return `${firstName} ${lastName}`.trim() || 'Unknown User'
}

function getParticipantTitle(participant: any): string {
  if (participant.role === 'recruiter') {
    return participant.recruiterProfile?.title || 'Recruiter'
  }
  return participant.profile?.title || 'Job Seeker'
}

function isParticipantOnline(participant: any): boolean {
  if (!participant.activity?.lastLogin) return false
  
  const lastLogin = new Date(participant.activity.lastLogin)
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
  
  return lastLogin > fiveMinutesAgo
}

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { User } from '@/lib/models/user.model'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const sendMessageSchema = z.object({
  recipientId: z.string().min(1, 'Recipient ID is required'),
  subject: z.string().min(1, 'Subject is required').max(200),
  content: z.string().min(1, 'Message content is required').max(5000),
  attachments: z.array(z.string()).optional(),
  replyTo: z.string().optional()
})

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const type = searchParams.get('type') || 'all' // 'all', 'unread', 'sent'
    const skip = (page - 1) * limit

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Build query based on type
    let query: any = {}
    if (type === 'unread') {
      query = {
        recipient: client._id,
        isRead: false
      }
    } else if (type === 'sent') {
      query = {
        sender: client._id
      }
    } else {
      query = {
        $or: [
          { recipient: client._id },
          { sender: client._id }
        ]
      }
    }

    // Get messages with pagination
    const messages = await getMessagesFromClient(query, skip, limit)

    // Get unread count
    const unreadCount = await getUnreadCountFromClient(client._id)

    const totalCount = await getTotalMessageCountFromClient(query)
    const totalPages = Math.ceil(totalCount / limit)

    return NextResponse.json({
      success: true,
      data: {
        messages,
        unreadCount,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    })

  } catch (error) {
    console.error('Get messages error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch messages' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const body = await request.json()

    // Validate request body
    const validationResult = sendMessageSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid message data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const messageData = validationResult.data

    // Get sender (client)
    const senderClient = await Client.findOne({ user: userId })
    if (!senderClient) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Check if recipient exists (could be recruiter or another client)
    const recipient = await User.findById(messageData.recipientId)
      .select('role profile recruiterProfile')
    
    if (!recipient) {
      return NextResponse.json(
        { error: 'Recipient not found' },
        { status: 404 }
      )
    }

    // Get or create recipient client profile if they're a client
    let recipientClient = null
    if (recipient.role === 'client') {
      recipientClient = await Client.findOne({ user: messageData.recipientId })
    }

    // Generate thread ID for conversation grouping
    const threadId = messageData.replyTo ? 
      await getThreadIdFromReplyTo(messageData.replyTo) : 
      generateThreadId(senderClient._id.toString(), messageData.recipientId)

    // Create message object
    const message = {
      id: generateMessageId(),
      sender: senderClient._id,
      recipient: recipientClient?._id || messageData.recipientId,
      subject: messageData.subject,
      content: messageData.content,
      attachments: messageData.attachments || [],
      threadId,
      replyTo: messageData.replyTo || null,
      isRead: false,
      sentAt: new Date(),
      readAt: null
    }

    // Add message to both sender and recipient message arrays
    await Client.findByIdAndUpdate(senderClient._id, {
      $push: { 
        'messages.sent': message,
        'activity.recentActivity': {
          $each: [{
            type: 'message_sent',
            description: `Sent message to ${getRecipientName(recipient)}`,
            metadata: {
              recipientId: messageData.recipientId,
              subject: messageData.subject
            },
            createdAt: new Date()
          }],
          $slice: -50
        }
      }
    })

    if (recipientClient) {
      await Client.findByIdAndUpdate(recipientClient._id, {
        $push: { 'messages.received': message },
        $inc: { 'messages.unreadCount': 1 }
      })
    } else {
      // For recruiters, we'll need to handle this differently
      // For now, we'll store in a separate collection or handle via User model
      await User.findByIdAndUpdate(messageData.recipientId, {
        $push: { 
          'messages.received': message,
          'activity.recentActivity': {
            $each: [{
              type: 'message_received',
              description: `Received message from ${senderClient.user?.profile?.firstName || 'Client'}`,
              metadata: {
                senderId: senderClient._id,
                subject: messageData.subject
              },
              createdAt: new Date()
            }],
            $slice: -50
          }
        },
        $inc: { 'messages.unreadCount': 1 }
      })
    }

    return NextResponse.json({
      success: true,
      message: 'Message sent successfully',
      data: {
        messageId: message.id,
        threadId: message.threadId,
        sentAt: message.sentAt
      }
    }, { status: 201 })

  } catch (error) {
    console.error('Send message error:', error)
    return NextResponse.json(
      { error: 'Failed to send message' },
      { status: 500 }
    )
  }
}

// Helper functions
async function getMessagesFromClient(query: any, skip: number, limit: number) {
  // This is a simplified version - in a real implementation, you'd have a separate Message model
  const client = await Client.findOne(query)
    .select('messages')
    .populate('messages.sent.recipient', 'profile')
    .populate('messages.received.sender', 'profile')
    .lean()

  if (!client) return []

  const allMessages = [
    ...(client.messages?.sent || []),
    ...(client.messages?.received || [])
  ].sort((a, b) => new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime())

  return allMessages.slice(skip, skip + limit)
}

async function getUnreadCountFromClient(clientId: string) {
  const client = await Client.findById(clientId).select('messages.unreadCount').lean()
  return client?.messages?.unreadCount || 0
}

async function getTotalMessageCountFromClient(query: any) {
  const client = await Client.findOne(query).select('messages').lean()
  if (!client) return 0
  
  return (client.messages?.sent?.length || 0) + (client.messages?.received?.length || 0)
}

async function getThreadIdFromReplyTo(replyToId: string) {
  // Find the original message and return its threadId
  const client = await Client.findOne({
    $or: [
      { 'messages.sent.id': replyToId },
      { 'messages.received.id': replyToId }
    ]
  }).lean()

  const message = [
    ...(client?.messages?.sent || []),
    ...(client?.messages?.received || [])
  ].find(msg => msg.id === replyToId)

  return message?.threadId || generateThreadId('unknown', 'unknown')
}

function generateThreadId(senderId: string, recipientId: string): string {
  const ids = [senderId, recipientId].sort()
  return `thread_${ids[0]}_${ids[1]}_${Date.now()}`
}

function generateMessageId(): string {
  return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

function getRecipientName(recipient: any): string {
  if (recipient.role === 'recruiter') {
    return `${recipient.profile?.firstName || ''} ${recipient.profile?.lastName || ''}`.trim() || 'Recruiter'
  }
  return `${recipient.profile?.firstName || ''} ${recipient.profile?.lastName || ''}`.trim() || 'User'
}

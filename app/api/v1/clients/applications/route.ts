import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { Application } from '@/lib/models/application.model'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'
import { Job } from '@/lib/models/job.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    
    // Get query parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status')
    const search = searchParams.get('search')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Build query
    const query: any = { client: client._id }
    
    if (status) {
      query.status = status
    }

    // Build sort
    const sort: any = {}
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1

    // Get applications with pagination
    const skip = (page - 1) * limit
    
    let applications = await Application.find(query)
      .populate('job', 'title company location salary type')
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean()

    // Apply search filter if provided
    if (search) {
      applications = applications.filter(app => 
        app.job?.title?.toLowerCase().includes(search.toLowerCase()) ||
        app.job?.company?.toLowerCase().includes(search.toLowerCase())
      )
    }

    // Get total count
    const totalCount = await Application.countDocuments(query)
    const totalPages = Math.ceil(totalCount / limit)

    // Format applications
    const formattedApplications = applications.map(app => ({
      id: app._id.toString(),
      jobId: app.job?._id?.toString(),
      jobTitle: app.job?.title || 'Unknown Position',
      company: app.job?.company || 'Unknown Company',
      location: app.job?.location || 'Unknown Location',
      salary: app.job?.salary,
      appliedDate: app.createdAt,
      status: app.status,
      statusText: getStatusText(app.status),
      statusColor: getStatusColor(app.status),
      interviewDate: app.interviewDate,
      notes: app.notes,
      lastUpdate: app.updatedAt,
      timeline: app.timeline || [],
      documents: app.documents || [],
      feedback: app.feedback,
      nextSteps: app.nextSteps
    }))

    return NextResponse.json({
      success: true,
      data: {
        applications: formattedApplications,
        currentPage: page,
        totalPages,
        totalCount,
        hasMore: page < totalPages
      }
    })

  } catch (error) {
    console.error('Get applications error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch applications' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const applicationData = await request.json()

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Verify job exists
    const job = await Job.findById(applicationData.jobId)
    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    // Check if already applied
    const existingApplication = await Application.findOne({
      client: client._id,
      job: applicationData.jobId
    })

    if (existingApplication) {
      return NextResponse.json(
        { error: 'You have already applied to this job' },
        { status: 400 }
      )
    }

    // Create application
    const application = new Application({
      client: client._id,
      job: applicationData.jobId,
      status: 'applied',
      coverLetter: applicationData.coverLetter,
      resumeId: applicationData.resumeId,
      additionalDocuments: applicationData.additionalDocuments || [],
      customAnswers: applicationData.customAnswers || {},
      timeline: [{
        status: 'applied',
        date: new Date(),
        notes: 'Application submitted'
      }]
    })

    await application.save()

    // Update client application stats
    await Client.findByIdAndUpdate(client._id, {
      $inc: { 'applicationStats.totalApplications': 1 }
    })

    return NextResponse.json({
      success: true,
      message: 'Application submitted successfully',
      data: {
        applicationId: application._id,
        status: application.status
      }
    })

  } catch (error) {
    console.error('Create application error:', error)
    return NextResponse.json(
      { error: 'Failed to submit application' },
      { status: 500 }
    )
  }
}

function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    'applied': 'Application Sent',
    'under_review': 'Under Review',
    'interview_scheduled': 'Interview Scheduled',
    'offer_received': 'Offer Received',
    'rejected': 'Not Selected',
    'withdrawn': 'Withdrawn'
  }
  return statusMap[status] || status
}

function getStatusColor(status: string): string {
  const colorMap: Record<string, string> = {
    'applied': 'bg-blue-500',
    'under_review': 'bg-yellow-500',
    'interview_scheduled': 'bg-purple-500',
    'offer_received': 'bg-green-500',
    'rejected': 'bg-red-500',
    'withdrawn': 'bg-gray-500'
  }
  return colorMap[status] || 'bg-gray-500'
}

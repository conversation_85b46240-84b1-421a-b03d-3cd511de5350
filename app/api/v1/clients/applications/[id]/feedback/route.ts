import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { Application } from '@/lib/models/application.model'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const feedbackSchema = z.object({
  rating: z.number().min(1).max(5).optional(),
  feedback: z.string().min(1, 'Feedback is required'),
  category: z.enum(['interview_experience', 'company_culture', 'application_process', 'communication', 'other']).optional(),
  wouldRecommend: z.boolean().optional(),
  improvementSuggestions: z.string().optional()
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const applicationId = params.id

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Get application feedback
    const application = await Application.findOne({
      _id: applicationId,
      client: client._id
    })
      .select('feedback applicationFeedback')
      .lean()

    if (!application) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        feedback: application.feedback || null,
        applicationFeedback: application.applicationFeedback || null
      }
    })

  } catch (error) {
    console.error('Get application feedback error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch application feedback' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const applicationId = params.id
    const body = await request.json()

    // Validate request body
    const validationResult = feedbackSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid feedback data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const feedbackData = validationResult.data

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Get application
    const application = await Application.findOne({
      _id: applicationId,
      client: client._id
    })

    if (!application) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      )
    }

    // Create feedback object
    const feedback = {
      id: generateFeedbackId(),
      ...feedbackData,
      submittedAt: new Date(),
      submittedBy: client._id
    }

    // Update application with feedback
    const updateData = {
      applicationFeedback: feedback,
      updatedAt: new Date()
    }

    // Add timeline entry
    const timelineEntry = {
      status: application.status,
      date: new Date(),
      notes: 'Feedback submitted for application experience'
    }

    await Application.findByIdAndUpdate(
      applicationId,
      {
        ...updateData,
        $push: { timeline: timelineEntry }
      }
    )

    return NextResponse.json({
      success: true,
      message: 'Feedback submitted successfully',
      data: { feedback }
    }, { status: 201 })

  } catch (error) {
    console.error('Submit application feedback error:', error)
    return NextResponse.json(
      { error: 'Failed to submit feedback' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const applicationId = params.id
    const body = await request.json()

    // Validate request body
    const validationResult = feedbackSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid feedback data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const feedbackData = validationResult.data

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Update application feedback
    const updateData: any = {
      updatedAt: new Date()
    }

    Object.keys(feedbackData).forEach(key => {
      updateData[`applicationFeedback.${key}`] = feedbackData[key as keyof typeof feedbackData]
    })

    updateData['applicationFeedback.updatedAt'] = new Date()

    const application = await Application.findOneAndUpdate(
      { _id: applicationId, client: client._id },
      { $set: updateData },
      { new: true }
    )

    if (!application) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Feedback updated successfully'
    })

  } catch (error) {
    console.error('Update application feedback error:', error)
    return NextResponse.json(
      { error: 'Failed to update feedback' },
      { status: 500 }
    )
  }
}

function generateFeedbackId(): string {
  return `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

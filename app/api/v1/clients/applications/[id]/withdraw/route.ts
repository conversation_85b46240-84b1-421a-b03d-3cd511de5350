import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { Application } from '@/lib/models/application.model'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const withdrawApplicationSchema = z.object({
  reason: z.string().min(1, 'Withdrawal reason is required'),
  feedback: z.string().optional()
})

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const applicationId = params.id
    const body = await request.json()

    // Validate request body
    const validationResult = withdrawApplicationSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid withdrawal data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { reason, feedback } = validationResult.data

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Get application
    const application = await Application.findOne({
      _id: applicationId,
      client: client._id
    })

    if (!application) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      )
    }

    // Check if application can be withdrawn
    if (application.status === 'withdrawn') {
      return NextResponse.json(
        { error: 'Application is already withdrawn' },
        { status: 400 }
      )
    }

    if (application.status === 'rejected') {
      return NextResponse.json(
        { error: 'Cannot withdraw a rejected application' },
        { status: 400 }
      )
    }

    // Update application status to withdrawn
    const timelineEntry = {
      status: 'withdrawn',
      date: new Date(),
      notes: `Application withdrawn: ${reason}`
    }

    const updateData: any = {
      status: 'withdrawn',
      withdrawalReason: reason,
      updatedAt: new Date(),
      $push: {
        timeline: timelineEntry
      }
    }

    if (feedback) {
      updateData.feedback = feedback
    }

    await Application.findByIdAndUpdate(applicationId, updateData)

    return NextResponse.json({
      success: true,
      message: 'Application withdrawn successfully',
      data: {
        applicationId,
        status: 'withdrawn',
        withdrawalReason: reason,
        withdrawnAt: new Date()
      }
    })

  } catch (error) {
    console.error('Withdraw application error:', error)
    return NextResponse.json(
      { error: 'Failed to withdraw application' },
      { status: 500 }
    )
  }
}

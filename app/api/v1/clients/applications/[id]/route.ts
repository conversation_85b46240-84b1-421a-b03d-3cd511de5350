import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { Application } from '@/lib/models/application.model'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const updateApplicationSchema = z.object({
  status: z.enum(['applied', 'under_review', 'interview_scheduled', 'offer_received', 'rejected', 'withdrawn']).optional(),
  notes: z.string().optional(),
  interviewDate: z.string().optional(),
  feedback: z.string().optional(),
  nextSteps: z.string().optional()
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const applicationId = params.id

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Get application
    const application = await Application.findOne({
      _id: applicationId,
      client: client._id
    })
      .populate('job', 'title company location salary type description requirements')
      .populate('client', 'user')
      .lean()

    if (!application) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      )
    }

    // Format application details
    const formattedApplication = {
      id: application._id.toString(),
      job: {
        id: application.job?._id?.toString(),
        title: application.job?.title || 'Unknown Position',
        company: application.job?.company || 'Unknown Company',
        location: application.job?.location || 'Unknown Location',
        salary: application.job?.salary,
        type: application.job?.type,
        description: application.job?.description,
        requirements: application.job?.requirements || []
      },
      status: application.status,
      statusText: getStatusText(application.status),
      statusColor: getStatusColor(application.status),
      appliedDate: application.createdAt,
      lastUpdate: application.updatedAt,
      coverLetter: application.coverLetter,
      resumeId: application.resumeId,
      additionalDocuments: application.additionalDocuments || [],
      customAnswers: application.customAnswers || {},
      timeline: application.timeline || [],
      interviewDate: application.interviewDate,
      notes: application.notes,
      feedback: application.feedback,
      nextSteps: application.nextSteps,
      withdrawalReason: application.withdrawalReason
    }

    return NextResponse.json({
      success: true,
      data: formattedApplication
    })

  } catch (error) {
    console.error('Get application error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch application' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const applicationId = params.id
    const body = await request.json()

    // Validate request body
    const validationResult = updateApplicationSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid update data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const updateData = validationResult.data

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Get current application
    const currentApplication = await Application.findOne({
      _id: applicationId,
      client: client._id
    })

    if (!currentApplication) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      )
    }

    // Prepare update data
    const updateFields: any = {
      updatedAt: new Date()
    }

    // Add timeline entry if status is changing
    if (updateData.status && updateData.status !== currentApplication.status) {
      updateFields.status = updateData.status
      
      const timelineEntry = {
        status: updateData.status,
        date: new Date(),
        notes: updateData.notes || `Status changed to ${getStatusText(updateData.status)}`
      }

      updateFields.$push = {
        timeline: timelineEntry
      }
    }

    // Update other fields
    if (updateData.notes) updateFields.notes = updateData.notes
    if (updateData.interviewDate) updateFields.interviewDate = new Date(updateData.interviewDate)
    if (updateData.feedback) updateFields.feedback = updateData.feedback
    if (updateData.nextSteps) updateFields.nextSteps = updateData.nextSteps

    // Update application
    const updatedApplication = await Application.findByIdAndUpdate(
      applicationId,
      updateFields,
      { new: true }
    )

    return NextResponse.json({
      success: true,
      message: 'Application updated successfully',
      data: {
        id: updatedApplication._id,
        status: updatedApplication.status,
        lastUpdate: updatedApplication.updatedAt
      }
    })

  } catch (error) {
    console.error('Update application error:', error)
    return NextResponse.json(
      { error: 'Failed to update application' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const applicationId = params.id

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Delete application
    const deletedApplication = await Application.findOneAndDelete({
      _id: applicationId,
      client: client._id
    })

    if (!deletedApplication) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      )
    }

    // Update client application stats
    await Client.findByIdAndUpdate(client._id, {
      $inc: { 'applicationStats.totalApplications': -1 }
    })

    return NextResponse.json({
      success: true,
      message: 'Application deleted successfully'
    })

  } catch (error) {
    console.error('Delete application error:', error)
    return NextResponse.json(
      { error: 'Failed to delete application' },
      { status: 500 }
    )
  }
}

function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    'applied': 'Application Sent',
    'under_review': 'Under Review',
    'interview_scheduled': 'Interview Scheduled',
    'offer_received': 'Offer Received',
    'rejected': 'Not Selected',
    'withdrawn': 'Withdrawn'
  }
  return statusMap[status] || status
}

function getStatusColor(status: string): string {
  const colorMap: Record<string, string> = {
    'applied': 'bg-blue-500',
    'under_review': 'bg-yellow-500',
    'interview_scheduled': 'bg-purple-500',
    'offer_received': 'bg-green-500',
    'rejected': 'bg-red-500',
    'withdrawn': 'bg-gray-500'
  }
  return colorMap[status] || 'bg-gray-500'
}

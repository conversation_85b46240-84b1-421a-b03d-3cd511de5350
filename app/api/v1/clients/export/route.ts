import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { Application } from '@/lib/models/application.model'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const exportSchema = z.object({
  type: z.enum(['profile', 'applications', 'analytics', 'full']),
  format: z.enum(['json', 'csv', 'pdf']),
  dateRange: z.object({
    start: z.string().optional(),
    end: z.string().optional()
  }).optional(),
  includePersonalData: z.boolean().optional(),
  includeDocuments: z.boolean().optional()
})

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const body = await request.json()

    // Validate export parameters
    const validationResult = exportSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid export parameters',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { type, format, dateRange, includePersonalData = true, includeDocuments = false } = validationResult.data

    // Get client data
    const client = await Client.findOne({ user: userId })
      .populate('user', 'email createdAt')
      .lean()

    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    let exportData: any = {}

    // Build export data based on type
    switch (type) {
      case 'profile':
        exportData = await buildProfileExport(client, includePersonalData, includeDocuments)
        break
      case 'applications':
        exportData = await buildApplicationsExport(client._id, dateRange)
        break
      case 'analytics':
        exportData = await buildAnalyticsExport(client._id, dateRange)
        break
      case 'full':
        exportData = await buildFullExport(client, dateRange, includePersonalData, includeDocuments)
        break
    }

    // Generate export based on format
    switch (format) {
      case 'json':
        return generateJSONExport(exportData, type)
      case 'csv':
        return generateCSVExport(exportData, type)
      case 'pdf':
        return generatePDFExport(exportData, type)
      default:
        return NextResponse.json(
          { error: 'Unsupported export format' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Export error:', error)
    return NextResponse.json(
      { error: 'Failed to generate export' },
      { status: 500 }
    )
  }
}

// Build profile export data
async function buildProfileExport(client: any, includePersonalData: boolean, includeDocuments: boolean) {
  const profileData: any = {
    exportType: 'profile',
    exportDate: new Date().toISOString(),
    profile: {
      firstName: includePersonalData ? client.profile?.firstName : '[REDACTED]',
      lastName: includePersonalData ? client.profile?.lastName : '[REDACTED]',
      email: includePersonalData ? client.user?.email : '[REDACTED]',
      phone: includePersonalData ? client.profile?.phone : '[REDACTED]',
      location: client.profile?.location,
      title: client.profile?.title,
      summary: client.profile?.summary,
      website: client.profile?.website,
      linkedIn: client.profile?.linkedIn,
      github: client.profile?.github
    },
    experience: client.experience || [],
    education: client.education || [],
    skills: client.skills || [],
    certifications: client.certifications || [],
    languages: client.languages || [],
    preferences: client.preferences || {},
    profileCompleteness: client.profileCompleteness || 0,
    memberSince: client.user?.createdAt,
    lastUpdated: client.updatedAt
  }

  if (includeDocuments) {
    profileData.documents = {
      resume: client.documents?.resume || null,
      coverLetter: client.documents?.coverLetter || null,
      portfolio: client.documents?.portfolio || []
    }
  }

  return profileData
}

// Build applications export data
async function buildApplicationsExport(clientId: string, dateRange?: any) {
  const query: any = { client: clientId }
  
  if (dateRange?.start || dateRange?.end) {
    query.createdAt = {}
    if (dateRange.start) query.createdAt.$gte = new Date(dateRange.start)
    if (dateRange.end) query.createdAt.$lte = new Date(dateRange.end)
  }

  const applications = await Application.find(query)
    .populate('job', 'title company location type salary')
    .sort({ createdAt: -1 })
    .lean()

  return {
    exportType: 'applications',
    exportDate: new Date().toISOString(),
    totalApplications: applications.length,
    applications: applications.map(app => ({
      id: app._id,
      jobTitle: app.job?.title || 'Unknown',
      company: app.job?.company || 'Unknown',
      location: app.job?.location || 'Unknown',
      jobType: app.job?.type || 'Unknown',
      salary: app.job?.salary || null,
      status: app.status,
      appliedDate: app.createdAt,
      lastUpdate: app.updatedAt,
      coverLetter: app.coverLetter ? 'Included' : 'Not included',
      customAnswers: Object.keys(app.customAnswers || {}).length,
      timeline: app.timeline || [],
      notes: app.notes || '',
      feedback: app.feedback || ''
    }))
  }
}

// Build analytics export data
async function buildAnalyticsExport(clientId: string, dateRange?: any) {
  const client = await Client.findById(clientId).lean()
  const query: any = { client: clientId }
  
  if (dateRange?.start || dateRange?.end) {
    query.createdAt = {}
    if (dateRange.start) query.createdAt.$gte = new Date(dateRange.start)
    if (dateRange.end) query.createdAt.$lte = new Date(dateRange.end)
  }

  const applications = await Application.find(query).lean()

  // Calculate analytics
  const totalApplications = applications.length
  const statusBreakdown = applications.reduce((acc: any, app) => {
    acc[app.status] = (acc[app.status] || 0) + 1
    return acc
  }, {})

  const responseRate = totalApplications > 0 ? 
    ((statusBreakdown.interview_scheduled || 0) + (statusBreakdown.offer_received || 0)) / totalApplications * 100 : 0

  const successRate = totalApplications > 0 ? 
    (statusBreakdown.offer_received || 0) / totalApplications * 100 : 0

  return {
    exportType: 'analytics',
    exportDate: new Date().toISOString(),
    dateRange,
    summary: {
      totalApplications,
      responseRate: Math.round(responseRate * 100) / 100,
      successRate: Math.round(successRate * 100) / 100,
      interviewsScheduled: statusBreakdown.interview_scheduled || 0,
      offersReceived: statusBreakdown.offer_received || 0,
      rejections: statusBreakdown.rejected || 0
    },
    statusBreakdown,
    searchHistory: client?.searchHistory?.slice(-50) || [],
    savedJobsCount: client?.savedJobs?.length || 0,
    followedCompaniesCount: client?.followedCompanies?.length || 0,
    followedRecruitersCount: client?.followedRecruiters?.length || 0
  }
}

// Build full export data
async function buildFullExport(client: any, dateRange?: any, includePersonalData: boolean = true, includeDocuments: boolean = false) {
  const [profileData, applicationsData, analyticsData] = await Promise.all([
    buildProfileExport(client, includePersonalData, includeDocuments),
    buildApplicationsExport(client._id, dateRange),
    buildAnalyticsExport(client._id, dateRange)
  ])

  return {
    exportType: 'full',
    exportDate: new Date().toISOString(),
    profile: profileData,
    applications: applicationsData,
    analytics: analyticsData
  }
}

// Generate JSON export
function generateJSONExport(data: any, type: string) {
  const filename = `${type}_export_${new Date().toISOString().split('T')[0]}.json`
  
  return new NextResponse(JSON.stringify(data, null, 2), {
    headers: {
      'Content-Type': 'application/json',
      'Content-Disposition': `attachment; filename="${filename}"`
    }
  })
}

// Generate CSV export
function generateCSVExport(data: any, type: string) {
  let csvContent = ''
  const filename = `${type}_export_${new Date().toISOString().split('T')[0]}.csv`

  if (type === 'applications' && data.applications) {
    // CSV for applications
    const headers = [
      'Job Title', 'Company', 'Location', 'Job Type', 'Status', 
      'Applied Date', 'Last Update', 'Cover Letter', 'Notes'
    ]
    csvContent = headers.join(',') + '\n'
    
    data.applications.forEach((app: any) => {
      const row = [
        `"${app.jobTitle}"`,
        `"${app.company}"`,
        `"${app.location}"`,
        `"${app.jobType}"`,
        `"${app.status}"`,
        `"${new Date(app.appliedDate).toLocaleDateString()}"`,
        `"${new Date(app.lastUpdate).toLocaleDateString()}"`,
        `"${app.coverLetter}"`,
        `"${app.notes.replace(/"/g, '""')}"`
      ]
      csvContent += row.join(',') + '\n'
    })
  } else {
    // Generic CSV for other types
    csvContent = 'Export Type,Export Date,Data\n'
    csvContent += `"${type}","${data.exportDate}","${JSON.stringify(data).replace(/"/g, '""')}"\n`
  }

  return new NextResponse(csvContent, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="${filename}"`
    }
  })
}

// Generate PDF export (simplified version)
function generatePDFExport(data: any, type: string) {
  // This is a simplified PDF generation
  // In a real implementation, you would use a library like puppeteer, jsPDF, or PDFKit
  
  const filename = `${type}_export_${new Date().toISOString().split('T')[0]}.pdf`
  
  // For now, return a simple text-based PDF content
  const pdfContent = generatePDFContent(data, type)
  
  return new NextResponse(pdfContent, {
    headers: {
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${filename}"`
    }
  })
}

function generatePDFContent(data: any, type: string): string {
  // This is a placeholder for PDF generation
  // In a real implementation, you would generate actual PDF binary data
  
  let content = `%PDF-1.4\n`
  content += `1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n`
  content += `2 0 obj\n<< /Type /Pages /Kids [3 0 R] /Count 1 >>\nendobj\n`
  content += `3 0 obj\n<< /Type /Page /Parent 2 0 R /MediaBox [0 0 612 792] /Contents 4 0 R >>\nendobj\n`
  content += `4 0 obj\n<< /Length 44 >>\nstream\nBT\n/F1 12 Tf\n100 700 Td\n(${type.toUpperCase()} Export Report) Tj\nET\nendstream\nendobj\n`
  content += `xref\n0 5\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \n0000000206 00000 n \n`
  content += `trailer\n<< /Size 5 /Root 1 0 R >>\nstartxref\n300\n%%EOF`
  
  return content
}

// Generate export report
export async function generateExportReport(clientId: string, exportType: string) {
  try {
    const client = await Client.findById(clientId).lean()
    if (!client) return null

    const reportData = {
      clientId,
      exportType,
      exportDate: new Date(),
      dataIncluded: {
        profile: exportType === 'profile' || exportType === 'full',
        applications: exportType === 'applications' || exportType === 'full',
        analytics: exportType === 'analytics' || exportType === 'full'
      }
    }

    // Log export activity
    await Client.findByIdAndUpdate(clientId, {
      $push: {
        'activity.recentActivity': {
          $each: [{
            type: 'data_export',
            description: `Exported ${exportType} data`,
            metadata: reportData,
            createdAt: new Date()
          }],
          $slice: -50
        }
      }
    })

    return reportData
  } catch (error) {
    console.error('Generate export report error:', error)
    return null
  }
}

// app/api/v1/clients/[id]/analytics/route.ts
import { NextRequest } from 'next/server'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  validate<PERSON>eth<PERSON>, 
  createSuccessResponse, 
  AuthenticatedRequest 
} from '@/lib/middleware/auth.middleware'
import { ClientDetailService } from '@/lib/services/client-detail.service'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const clientDetailService = new ClientDetailService()

// GET /api/v1/clients/[id]/analytics - Get client analytics
export const GET = withErrorHandler(async (request: AuthenticatedRequest, { params }: { params: { id: string } }) => {
  validateMethod(request, ['GET'])
  
  const clientId = params.id
  const userId = request.user?.id
  
  if (!clientId) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      'Client ID is required',
      'client'
    )
  }

  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }

  const analytics = await clientDetailService.getClientAnalytics(clientId, userId)
  
  return createSuccessResponse(analytics)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['job_seeker'] // Only the profile owner can view analytics
})

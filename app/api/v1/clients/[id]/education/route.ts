// app/api/v1/clients/[id]/education/route.ts
import { NextRequest } from 'next/server'
import { validateRequestBody } from '@/lib/api/utils'
import { 
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
  validate<PERSON>ethod, 
  createSuccessResponse, 
  AuthenticatedRequest 
} from '@/lib/middleware/auth.middleware'
import { ClientDetailService } from '@/lib/services/client-detail.service'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import { ClientDetailProfile } from '@/types/client-detail.types'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const clientDetailService = new ClientDetailService()

// Validation schema for education
const educationSchema = z.object({
  institution: z.string().min(1).max(100),
  degree: z.string().min(1).max(100),
  fieldOfStudy: z.string().min(1).max(100),
  startDate: z.string(),
  endDate: z.string().optional(),
  current: z.boolean(),
  gpa: z.number().min(0).max(4).optional(),
  description: z.string().max(1000).optional(),
  achievements: z.array(z.string()).default([])
})

// POST /api/v1/clients/[id]/education - Add education
export const POST = withErrorHandler(async (request: AuthenticatedRequest, { params }: { params: { id: string } }) => {
  validateMethod(request, ['POST'])
  
  const clientId = params.id
  const userId = request.user?.id
  
  if (!clientId) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      'Client ID is required',
      'client'
    )
  }

  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }

  const educationData = await validateRequestBody(request, (data: unknown) => {
    const result = educationSchema.safeParse(data)
    if (!result.success) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        'Invalid education data',
        'validation',
        result.error.errors
      )
    }
    return result.data as ClientDetailProfile['education'][0]
  })

  const updatedClient = await clientDetailService.addEducation(clientId, educationData, userId)
  
  return createSuccessResponse(updatedClient)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['job_seeker']
})

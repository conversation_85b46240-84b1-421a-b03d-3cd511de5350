// app/api/v1/clients/[id]/route.ts
import { NextRequest } from 'next/server'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON>ethod,
  createSuccessResponse,
  AuthenticatedRequest
} from '@/lib/middleware/auth.middleware'
import { ClientDetailService } from '@/lib/services/client-detail.service'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import { UpdateClientDetailRequest } from '@/types/client-detail.types'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const clientDetailService = new ClientDetailService()

// Validation schemas
const updateClientSchema = z.object({
  headline: z.string().min(1).max(200).optional(),
  summary: z.string().min(1).max(2000).optional(),
  experience: z.object({
    level: z.enum(['entry', 'junior', 'mid', 'senior', 'lead', 'executive']).optional(),
    yearsOfExperience: z.number().min(0).max(50).optional(),
    industries: z.array(z.string()).optional(),
    currentSalary: z.object({
      amount: z.number().min(0).optional(),
      currency: z.string().optional(),
      period: z.enum(['hourly', 'monthly', 'yearly']).optional()
    }).optional()
  }).optional(),
  jobPreferences: z.object({
    desiredRoles: z.array(z.string()).optional(),
    industries: z.array(z.string()).optional(),
    locations: z.array(z.object({
      city: z.string(),
      state: z.string(),
      country: z.string(),
      remote: z.boolean(),
      relocationWilling: z.boolean()
    })).optional(),
    salaryExpectation: z.object({
      min: z.number().min(0),
      max: z.number().min(0),
      currency: z.string(),
      period: z.enum(['hourly', 'monthly', 'yearly']),
      negotiable: z.boolean()
    }).optional(),
    jobTypes: z.array(z.enum(['full-time', 'part-time', 'contract', 'freelance', 'internship', 'temporary'])).optional(),
    workArrangement: z.array(z.enum(['onsite', 'remote', 'hybrid'])).optional(),
    availability: z.enum(['immediately', '2-weeks', '1-month', '2-months', '3-months', 'not-looking']).optional(),
    benefits: z.array(z.string()).optional(),
    companySize: z.array(z.enum(['startup', 'small', 'medium', 'large', 'enterprise'])).optional()
  }).optional(),
  privacy: z.object({
    profileVisibility: z.enum(['public', 'private', 'recruiter-only']).optional(),
    showSalaryExpectation: z.boolean().optional(),
    showCurrentCompany: z.boolean().optional(),
    allowRecruiterContact: z.boolean().optional(),
    showProfileToCurrentEmployer: z.boolean().optional()
  }).optional()
})

// GET /api/v1/clients/[id] - Get client detail
export const GET = withErrorHandler(async (request: NextRequest, context: { params: Record<string, string> }) => {
  validateMethod(request, ['GET'])

  const clientId = context.params.id

  if (!clientId) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      'Client ID is required',
      'client'
    )
  }

  const clientDetail = await clientDetailService.getClientDetail(clientId)

  return createSuccessResponse(clientDetail)
}, {
  requireDatabase: true,
  requireAuth: false // Public endpoint for viewing client profiles
})

// PUT /api/v1/clients/[id] - Update client detail
export const PUT = withErrorHandler(async (request: AuthenticatedRequest, context: { params: Record<string, string> }) => {
  validateMethod(request, ['PUT'])

  const clientId = context.params.id
  const userId = request.user?.id

  if (!clientId) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      'Client ID is required',
      'client'
    )
  }

  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }

  // Parse and validate request body
  const body = await request.json()
  const result = updateClientSchema.safeParse(body)

  if (!result.success) {
    const errorDetails = result.error.errors.reduce((acc, error) => {
      acc[error.path.join('.')] = error.message
      return acc
    }, {} as Record<string, string>)

    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      'Invalid update data',
      'validation',
      errorDetails
    )
  }

  const updateData = result.data as UpdateClientDetailRequest
  const updatedClient = await clientDetailService.updateClientDetail(clientId, updateData, userId)

  return createSuccessResponse(updatedClient)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['job_seeker'] // Only job seekers can update their profiles
})

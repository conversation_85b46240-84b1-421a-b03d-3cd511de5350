// app/api/v1/clients/[id]/skills/route.ts
import { NextRequest } from 'next/server'
import { validateRequestBody } from '@/lib/api/utils'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  validate<PERSON>ethod, 
  createSuccessResponse, 
  AuthenticatedRequest 
} from '@/lib/middleware/auth.middleware'
import { ClientDetailService } from '@/lib/services/client-detail.service'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import { ClientDetailProfile } from '@/types/client-detail.types'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const clientDetailService = new ClientDetailService()

// Validation schema for skills
const skillSchema = z.object({
  name: z.string().min(1).max(50),
  category: z.string().min(1).max(50),
  level: z.enum(['beginner', 'intermediate', 'advanced', 'expert']),
  yearsOfExperience: z.number().min(0).max(50),
  endorsed: z.boolean().default(false),
  endorsements: z.number().min(0).default(0)
})

// POST /api/v1/clients/[id]/skills - Add skill
export const POST = withErrorHandler(async (request: AuthenticatedRequest, { params }: { params: { id: string } }) => {
  validateMethod(request, ['POST'])
  
  const clientId = params.id
  const userId = request.user?.id
  
  if (!clientId) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      'Client ID is required',
      'client'
    )
  }

  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }

  const skillData = await validateRequestBody(request, (data: unknown) => {
    const result = skillSchema.safeParse(data)
    if (!result.success) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        'Invalid skill data',
        'validation',
        result.error.errors
      )
    }
    return result.data as ClientDetailProfile['skills'][0]
  })

  const updatedClient = await clientDetailService.addSkill(clientId, skillData, userId)
  
  return createSuccessResponse(updatedClient)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['job_seeker']
})

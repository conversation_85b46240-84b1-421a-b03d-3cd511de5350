// app/api/v1/clients/[id]/experience/route.ts
import { NextRequest } from 'next/server'
import { validateRequestBody } from '@/lib/api/utils'
import { 
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
  validate<PERSON>ethod, 
  createSuccessResponse, 
  AuthenticatedRequest 
} from '@/lib/middleware/auth.middleware'
import { ClientDetailService } from '@/lib/services/client-detail.service'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import { ClientDetailProfile } from '@/types/client-detail.types'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const clientDetailService = new ClientDetailService()

// Validation schema for work experience
const workExperienceSchema = z.object({
  company: z.string().min(1).max(100),
  position: z.string().min(1).max(100),
  location: z.string().min(1).max(100),
  startDate: z.string(),
  endDate: z.string().optional(),
  current: z.boolean(),
  description: z.string().min(1).max(2000),
  achievements: z.array(z.string()).default([]),
  skills: z.array(z.string()).default([]),
  employmentType: z.enum(['full-time', 'part-time', 'contract', 'freelance', 'internship'])
})

// POST /api/v1/clients/[id]/experience - Add work experience
export const POST = withErrorHandler(async (request: AuthenticatedRequest, { params }: { params: { id: string } }) => {
  validateMethod(request, ['POST'])
  
  const clientId = params.id
  const userId = request.user?.id
  
  if (!clientId) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      'Client ID is required',
      'client'
    )
  }

  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }

  const workData = await validateRequestBody(request, (data: unknown) => {
    const result = workExperienceSchema.safeParse(data)
    if (!result.success) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        'Invalid work experience data',
        'validation',
        result.error.errors
      )
    }
    return result.data as ClientDetailProfile['workHistory'][0]
  })

  const updatedClient = await clientDetailService.addWorkExperience(clientId, workData, userId)
  
  return createSuccessResponse(updatedClient)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['job_seeker']
})

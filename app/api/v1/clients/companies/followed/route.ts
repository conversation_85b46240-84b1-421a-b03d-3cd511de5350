import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { Company } from '@/lib/models/company.model'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const followCompanySchema = z.object({
  companyId: z.string().min(1, 'Company ID is required')
})

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    // Get client profile
    const client = await Client.findOne({ user: userId })
      .select('followedCompanies')
      .lean()
    
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    const followedCompanyIds = client.followedCompanies || []
    
    if (followedCompanyIds.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          followedCompanies: [],
          recentUpdates: [],
          pagination: {
            currentPage: page,
            totalPages: 0,
            totalCount: 0,
            hasNext: false,
            hasPrev: false
          }
        }
      })
    }

    // Get followed companies with pagination
    const followedCompanies = await Company.find({ _id: { $in: followedCompanyIds } })
      .select('name logo location industry size website description verification jobCount followerCount recentUpdates')
      .sort({ name: 1 })
      .skip(skip)
      .limit(limit)
      .lean()

    // Get recent updates from followed companies
    const recentUpdates = await Company.find({ 
      _id: { $in: followedCompanyIds },
      'recentUpdates.0': { $exists: true }
    })
      .select('name logo recentUpdates')
      .sort({ 'recentUpdates.createdAt': -1 })
      .limit(20)
      .lean()

    const allUpdates = recentUpdates.flatMap(company => 
      (company.recentUpdates || []).slice(0, 3).map(update => ({
        ...update,
        company: {
          id: company._id,
          name: company.name,
          logo: company.logo
        }
      }))
    ).sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())

    const totalCount = followedCompanyIds.length
    const totalPages = Math.ceil(totalCount / limit)

    return NextResponse.json({
      success: true,
      data: {
        followedCompanies,
        recentUpdates: allUpdates.slice(0, 10),
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    })

  } catch (error) {
    console.error('Get followed companies error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch followed companies' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const body = await request.json()

    // Validate request body
    const validationResult = followCompanySchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { companyId } = validationResult.data

    // Check if company exists
    const company = await Company.findById(companyId)
    if (!company) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Add company to followed companies
    const client = await Client.findOneAndUpdate(
      { user: userId },
      { 
        $addToSet: { followedCompanies: companyId },
        $set: { 'activity.lastProfileUpdate': new Date() }
      },
      { new: true, upsert: true }
    )

    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Update company follower count
    await Company.findByIdAndUpdate(companyId, {
      $inc: { followerCount: 1 }
    })

    return NextResponse.json({
      success: true,
      message: 'Company followed successfully',
      data: { companyId }
    }, { status: 201 })

  } catch (error) {
    console.error('Follow company error:', error)
    return NextResponse.json(
      { error: 'Failed to follow company' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Remove company from followed companies
    const client = await Client.findOneAndUpdate(
      { user: userId },
      { 
        $pull: { followedCompanies: companyId },
        $set: { 'activity.lastProfileUpdate': new Date() }
      },
      { new: true }
    )

    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Update company follower count
    await Company.findByIdAndUpdate(companyId, {
      $inc: { followerCount: -1 }
    })

    return NextResponse.json({
      success: true,
      message: 'Company unfollowed successfully',
      data: { companyId }
    })

  } catch (error) {
    console.error('Unfollow company error:', error)
    return NextResponse.json(
      { error: 'Failed to unfollow company' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { Company } from '@/lib/models/company.model'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const companyId = params.id

    // Check if company exists
    const company = await Company.findById(companyId)
    if (!company) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Check if already following
    const isAlreadyFollowing = client.followedCompanies?.includes(companyId as any)
    if (isAlreadyFollowing) {
      return NextResponse.json(
        { error: 'Already following this company' },
        { status: 400 }
      )
    }

    // Add company to followed companies
    await Client.findByIdAndUpdate(client._id, {
      $addToSet: { followedCompanies: companyId },
      $set: { 'activity.lastProfileUpdate': new Date() }
    })

    // Update company follower count
    await Company.findByIdAndUpdate(companyId, {
      $inc: { followerCount: 1 }
    })

    // Create activity log entry
    const activityEntry = {
      type: 'company_followed',
      description: `Started following ${company.name}`,
      metadata: {
        companyId: company._id,
        companyName: company.name
      },
      createdAt: new Date()
    }

    await Client.findByIdAndUpdate(client._id, {
      $push: { 
        'activity.recentActivity': {
          $each: [activityEntry],
          $slice: -50 // Keep only last 50 activities
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: `Now following ${company.name}`,
      data: {
        companyId,
        companyName: company.name,
        isFollowing: true,
        followerCount: (company.followerCount || 0) + 1
      }
    })

  } catch (error) {
    console.error('Follow company error:', error)
    return NextResponse.json(
      { error: 'Failed to follow company' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const companyId = params.id

    // Check if company exists
    const company = await Company.findById(companyId)
    if (!company) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Check if currently following
    const isFollowing = client.followedCompanies?.includes(companyId as any)
    if (!isFollowing) {
      return NextResponse.json(
        { error: 'Not following this company' },
        { status: 400 }
      )
    }

    // Remove company from followed companies
    await Client.findByIdAndUpdate(client._id, {
      $pull: { followedCompanies: companyId },
      $set: { 'activity.lastProfileUpdate': new Date() }
    })

    // Update company follower count
    await Company.findByIdAndUpdate(companyId, {
      $inc: { followerCount: -1 }
    })

    // Create activity log entry
    const activityEntry = {
      type: 'company_unfollowed',
      description: `Stopped following ${company.name}`,
      metadata: {
        companyId: company._id,
        companyName: company.name
      },
      createdAt: new Date()
    }

    await Client.findByIdAndUpdate(client._id, {
      $push: { 
        'activity.recentActivity': {
          $each: [activityEntry],
          $slice: -50 // Keep only last 50 activities
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: `Unfollowed ${company.name}`,
      data: {
        companyId,
        companyName: company.name,
        isFollowing: false,
        followerCount: Math.max((company.followerCount || 1) - 1, 0)
      }
    })

  } catch (error) {
    console.error('Unfollow company error:', error)
    return NextResponse.json(
      { error: 'Failed to unfollow company' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const companyId = params.id

    // Get client
    const client = await Client.findOne({ user: userId })
      .select('followedCompanies')
      .lean()
    
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Check if following this company
    const isFollowing = client.followedCompanies?.includes(companyId as any) || false

    return NextResponse.json({
      success: true,
      data: {
        companyId,
        isFollowing
      }
    })

  } catch (error) {
    console.error('Check follow status error:', error)
    return NextResponse.json(
      { error: 'Failed to check follow status' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const searchHistorySchema = z.object({
  query: z.string().min(1, 'Search query is required'),
  filters: z.object({
    location: z.string().optional(),
    jobType: z.enum(['full-time', 'part-time', 'contract', 'freelance', 'internship']).optional(),
    experienceLevel: z.enum(['entry', 'mid', 'senior', 'executive']).optional(),
    salaryMin: z.number().optional(),
    salaryMax: z.number().optional(),
    industries: z.array(z.string()).optional(),
    companies: z.array(z.string()).optional(),
    datePosted: z.enum(['today', 'week', 'month', 'anytime']).optional()
  }).optional(),
  resultsCount: z.number().optional()
})

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')

    // Get client profile with search history
    const client = await Client.findOne({ user: userId })
      .select('searchHistory')
      .lean()
    
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    const searchHistory = (client.searchHistory || [])
      .sort((a: any, b: any) => new Date(b.searchedAt).getTime() - new Date(a.searchedAt).getTime())
      .slice(0, limit)

    // Get popular searches (most frequent queries)
    const queryFrequency: Record<string, number> = {}
    client.searchHistory?.forEach((search: any) => {
      const query = search.query.toLowerCase()
      queryFrequency[query] = (queryFrequency[query] || 0) + 1
    })

    const popularSearches = Object.entries(queryFrequency)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([query, count]) => ({ query, count }))

    return NextResponse.json({
      success: true,
      data: {
        searchHistory,
        popularSearches,
        totalSearches: client.searchHistory?.length || 0
      }
    })

  } catch (error) {
    console.error('Get search history error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch search history' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const body = await request.json()

    // Validate request body
    const validationResult = searchHistorySchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid search data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const searchData = validationResult.data

    // Create search history entry
    const searchEntry = {
      id: generateSearchId(),
      ...searchData,
      searchedAt: new Date()
    }

    // Add to search history (keep only last 100 searches)
    const client = await Client.findOneAndUpdate(
      { user: userId },
      { 
        $push: { 
          searchHistory: {
            $each: [searchEntry],
            $slice: -100 // Keep only last 100 searches
          }
        },
        $set: { 'activity.lastProfileUpdate': new Date() }
      },
      { new: true, upsert: true }
    )

    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Search saved to history',
      data: { searchEntry }
    }, { status: 201 })

  } catch (error) {
    console.error('Save search history error:', error)
    return NextResponse.json(
      { error: 'Failed to save search history' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const searchId = searchParams.get('searchId')
    const clearAll = searchParams.get('clearAll') === 'true'

    if (clearAll) {
      // Clear all search history
      await Client.findOneAndUpdate(
        { user: userId },
        { 
          $set: { 
            searchHistory: [],
            'activity.lastProfileUpdate': new Date()
          }
        }
      )

      return NextResponse.json({
        success: true,
        message: 'Search history cleared'
      })
    }

    if (!searchId) {
      return NextResponse.json(
        { error: 'Search ID is required' },
        { status: 400 }
      )
    }

    // Remove specific search from history
    const client = await Client.findOneAndUpdate(
      { user: userId },
      { 
        $pull: { searchHistory: { id: searchId } },
        $set: { 'activity.lastProfileUpdate': new Date() }
      },
      { new: true }
    )

    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Search removed from history'
    })

  } catch (error) {
    console.error('Delete search history error:', error)
    return NextResponse.json(
      { error: 'Failed to delete search history' },
      { status: 500 }
    )
  }
}

function generateSearchId(): string {
  return `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

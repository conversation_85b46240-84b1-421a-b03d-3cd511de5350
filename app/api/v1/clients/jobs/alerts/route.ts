import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const jobAlertSchema = z.object({
  name: z.string().min(1, 'Alert name is required'),
  criteria: z.object({
    keywords: z.array(z.string()).optional(),
    location: z.string().optional(),
    jobType: z.enum(['full-time', 'part-time', 'contract', 'freelance', 'internship']).optional(),
    experienceLevel: z.enum(['entry', 'mid', 'senior', 'executive']).optional(),
    salaryMin: z.number().optional(),
    salaryMax: z.number().optional(),
    industries: z.array(z.string()).optional(),
    companies: z.array(z.string()).optional()
  }),
  frequency: z.enum(['immediate', 'daily', 'weekly']).default('daily'),
  isActive: z.boolean().default(true)
})

const updateJobAlertSchema = z.object({
  name: z.string().optional(),
  criteria: z.object({
    keywords: z.array(z.string()).optional(),
    location: z.string().optional(),
    jobType: z.enum(['full-time', 'part-time', 'contract', 'freelance', 'internship']).optional(),
    experienceLevel: z.enum(['entry', 'mid', 'senior', 'executive']).optional(),
    salaryMin: z.number().optional(),
    salaryMax: z.number().optional(),
    industries: z.array(z.string()).optional(),
    companies: z.array(z.string()).optional()
  }).optional(),
  frequency: z.enum(['immediate', 'daily', 'weekly']).optional(),
  isActive: z.boolean().optional()
})

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id

    // Get client profile with job alerts
    const client = await Client.findOne({ user: userId })
      .select('jobAlerts')
      .lean()
    
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    const jobAlerts = client.jobAlerts || []

    return NextResponse.json({
      success: true,
      data: {
        jobAlerts,
        totalCount: jobAlerts.length,
        activeCount: jobAlerts.filter((alert: any) => alert.isActive).length
      }
    })

  } catch (error) {
    console.error('Get job alerts error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch job alerts' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const body = await request.json()

    // Validate request body
    const validationResult = jobAlertSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid job alert data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const alertData = validationResult.data

    // Create job alert object
    const jobAlert = {
      id: generateAlertId(),
      ...alertData,
      createdAt: new Date(),
      lastTriggered: null,
      matchCount: 0
    }

    // Add job alert to client
    const client = await Client.findOneAndUpdate(
      { user: userId },
      { 
        $push: { jobAlerts: jobAlert },
        $set: { 'activity.lastProfileUpdate': new Date() }
      },
      { new: true, upsert: true }
    )

    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Job alert created successfully',
      data: { jobAlert }
    }, { status: 201 })

  } catch (error) {
    console.error('Create job alert error:', error)
    return NextResponse.json(
      { error: 'Failed to create job alert' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const alertId = searchParams.get('alertId')
    const body = await request.json()

    if (!alertId) {
      return NextResponse.json(
        { error: 'Alert ID is required' },
        { status: 400 }
      )
    }

    // Validate request body
    const validationResult = updateJobAlertSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid update data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const updateData = validationResult.data

    // Update job alert
    const updateFields: any = {
      'activity.lastProfileUpdate': new Date()
    }

    Object.keys(updateData).forEach(key => {
      updateFields[`jobAlerts.$.${key}`] = updateData[key as keyof typeof updateData]
    })

    const client = await Client.findOneAndUpdate(
      { user: userId, 'jobAlerts.id': alertId },
      { $set: updateFields },
      { new: true }
    )

    if (!client) {
      return NextResponse.json(
        { error: 'Job alert not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Job alert updated successfully'
    })

  } catch (error) {
    console.error('Update job alert error:', error)
    return NextResponse.json(
      { error: 'Failed to update job alert' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const alertId = searchParams.get('alertId')

    if (!alertId) {
      return NextResponse.json(
        { error: 'Alert ID is required' },
        { status: 400 }
      )
    }

    // Remove job alert
    const client = await Client.findOneAndUpdate(
      { user: userId },
      { 
        $pull: { jobAlerts: { id: alertId } },
        $set: { 'activity.lastProfileUpdate': new Date() }
      },
      { new: true }
    )

    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Job alert deleted successfully'
    })

  } catch (error) {
    console.error('Delete job alert error:', error)
    return NextResponse.json(
      { error: 'Failed to delete job alert' },
      { status: 500 }
    )
  }
}

function generateAlertId(): string {
  return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

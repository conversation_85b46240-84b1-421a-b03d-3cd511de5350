import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { Job } from '@/lib/models/job.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '5')

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Build recommendation query based on client preferences
    const query: any = { 
      status: 'active',
      expiryDate: { $gt: new Date() }
    }

    // Add filters based on client job preferences
    if (client.jobPreferences) {
      // Filter by desired roles
      if (client.jobPreferences.desiredRoles?.length > 0) {
        query.$or = client.jobPreferences.desiredRoles.map(role => ({
          title: { $regex: role, $options: 'i' }
        }))
      }

      // Filter by job types
      if (client.jobPreferences.jobTypes?.length > 0) {
        query.type = { $in: client.jobPreferences.jobTypes }
      }

      // Filter by work arrangement
      if (client.jobPreferences.workArrangement?.includes('remote')) {
        query.remote = true
      }

      // Filter by salary range
      if (client.jobPreferences.salaryRange) {
        // This would need more complex salary parsing logic
        // For now, we'll skip salary filtering
      }
    }

    // Get jobs
    let jobs = await Job.find(query)
      .populate('companyId', 'name logo verification')
      .sort({ featured: -1, createdAt: -1 })
      .limit(limit * 2) // Get more to allow for filtering
      .lean()

    // Calculate match scores and format jobs
    const formattedJobs = jobs.map(job => {
      const matchScore = calculateMatchScore(job, client)
      
      return {
        id: job._id.toString(),
        title: job.title,
        company: job.companyId?.name || 'Unknown Company',
        location: job.location,
        salary: job.salary,
        type: job.type,
        matchScore,
        postedDate: job.createdAt.toISOString(),
        description: job.description,
        requirements: job.requirements || [],
        remote: job.remote || false,
        urgent: job.urgent || false,
        featured: job.featured || false
      }
    })

    // Sort by match score and take top results
    const recommendations = formattedJobs
      .sort((a, b) => b.matchScore - a.matchScore)
      .slice(0, limit)

    return NextResponse.json({
      success: true,
      data: recommendations
    })

  } catch (error) {
    console.error('Get job recommendations error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch job recommendations' },
      { status: 500 }
    )
  }
}

function calculateMatchScore(job: any, client: any): number {
  let score = 50 // Base score

  // Match job title with desired roles
  if (client.jobPreferences?.desiredRoles?.length > 0) {
    const titleMatches = client.jobPreferences.desiredRoles.some((role: string) =>
      job.title.toLowerCase().includes(role.toLowerCase())
    )
    if (titleMatches) score += 20
  }

  // Match job type
  if (client.jobPreferences?.jobTypes?.includes(job.type)) {
    score += 15
  }

  // Match remote preference
  if (client.jobPreferences?.workArrangement?.includes('remote') && job.remote) {
    score += 10
  }

  // Match skills
  if (client.skills?.length > 0 && job.requirements?.length > 0) {
    const clientSkills = client.skills.map((skill: any) => skill.name.toLowerCase())
    const jobRequirements = job.requirements.map((req: string) => req.toLowerCase())
    
    const skillMatches = jobRequirements.filter((req: string) =>
      clientSkills.some((skill: string) => 
        skill.includes(req) || req.includes(skill)
      )
    ).length

    const skillMatchPercentage = (skillMatches / jobRequirements.length) * 100
    score += Math.min(skillMatchPercentage * 0.2, 20) // Max 20 points for skills
  }

  // Match experience level
  if (client.experience?.level && job.experience) {
    const experienceLevels = ['entry', 'mid', 'senior', 'executive']
    const clientLevel = experienceLevels.indexOf(client.experience.level)
    const jobLevel = experienceLevels.indexOf(job.experience)
    
    if (Math.abs(clientLevel - jobLevel) <= 1) {
      score += 10
    }
  }

  // Boost for featured jobs
  if (job.featured) {
    score += 5
  }

  // Boost for verified companies
  if (job.company?.verification?.isVerified) {
    score += 5
  }

  return Math.min(Math.round(score), 100) // Cap at 100%
}

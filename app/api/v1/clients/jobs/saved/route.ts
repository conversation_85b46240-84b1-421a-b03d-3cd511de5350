import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { Job } from '@/lib/models/job.model'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const saveJobSchema = z.object({
  jobId: z.string().min(1, 'Job ID is required')
})

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    // Get client profile
    const client = await Client.findOne({ user: userId })
      .select('savedJobs')
      .lean()
    
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    const savedJobIds = client.savedJobs || []
    
    if (savedJobIds.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          savedJobs: [],
          pagination: {
            currentPage: page,
            totalPages: 0,
            totalCount: 0,
            hasNext: false,
            hasPrev: false
          }
        }
      })
    }

    // Get saved jobs with pagination
    const savedJobs = await Job.find({ _id: { $in: savedJobIds } })
      .populate('company', 'name logo location')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean()

    const totalCount = savedJobIds.length
    const totalPages = Math.ceil(totalCount / limit)

    return NextResponse.json({
      success: true,
      data: {
        savedJobs,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    })

  } catch (error) {
    console.error('Get saved jobs error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch saved jobs' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const body = await request.json()

    // Validate request body
    const validationResult = saveJobSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { jobId } = validationResult.data

    // Check if job exists
    const job = await Job.findById(jobId)
    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    // Add job to saved jobs
    const client = await Client.findOneAndUpdate(
      { user: userId },
      { 
        $addToSet: { savedJobs: jobId },
        $set: { 'activity.lastProfileUpdate': new Date() }
      },
      { new: true, upsert: true }
    )

    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Job saved successfully',
      data: { jobId }
    }, { status: 201 })

  } catch (error) {
    console.error('Save job error:', error)
    return NextResponse.json(
      { error: 'Failed to save job' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const jobId = searchParams.get('jobId')

    if (!jobId) {
      return NextResponse.json(
        { error: 'Job ID is required' },
        { status: 400 }
      )
    }

    // Remove job from saved jobs
    const client = await Client.findOneAndUpdate(
      { user: userId },
      { 
        $pull: { savedJobs: jobId },
        $set: { 'activity.lastProfileUpdate': new Date() }
      },
      { new: true }
    )

    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Job removed from saved jobs',
      data: { jobId }
    })

  } catch (error) {
    console.error('Unsave job error:', error)
    return NextResponse.json(
      { error: 'Failed to remove saved job' },
      { status: 500 }
    )
  }
}

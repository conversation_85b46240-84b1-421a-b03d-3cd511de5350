import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { with<PERSON>ache, CacheConfigs, CacheKeys } from '@/lib/middleware/cache.middleware'
import { Client } from '@/lib/models/client.model'
import { Job } from '@/lib/models/job.model'
import { Company } from '@/lib/models/company.model'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const advancedSearchSchema = z.object({
  query: z.string().optional(),
  location: z.string().optional(),
  remote: z.boolean().optional(),
  jobType: z.array(z.string()).optional(),
  experienceLevel: z.array(z.string()).optional(),
  salaryMin: z.number().optional(),
  salaryMax: z.number().optional(),
  industries: z.array(z.string()).optional(),
  companies: z.array(z.string()).optional(),
  skills: z.array(z.string()).optional(),
  datePosted: z.enum(['today', 'week', 'month', 'anytime']).optional(),
  sortBy: z.enum(['relevance', 'date', 'salary', 'company']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  includeRecommendations: z.boolean().optional(),
  useAI: z.boolean().optional()
})

async function searchHandler(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const body = await request.json()

    // Validate search parameters
    const validationResult = advancedSearchSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid search parameters',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const searchParams = validationResult.data
    const {
      query = '',
      location,
      remote,
      jobType,
      experienceLevel,
      salaryMin,
      salaryMax,
      industries,
      companies,
      skills,
      datePosted,
      sortBy = 'relevance',
      sortOrder = 'desc',
      page = 1,
      limit = 20,
      includeRecommendations = false,
      useAI = false
    } = searchParams

    // Get client profile for personalization
    const client = await Client.findOne({ user: userId })
      .select('profile skills experience preferences searchHistory')
      .lean()

    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Build search query
    const searchQuery = await buildAdvancedSearchQuery(searchParams, client)
    
    // Execute search with pagination
    const skip = (page - 1) * limit
    let jobs = await Job.find(searchQuery)
      .populate('company', 'name logo location industry verification')
      .sort(buildSortQuery(sortBy, sortOrder, client, query))
      .skip(skip)
      .limit(limit)
      .lean()

    // Apply AI-powered ranking if requested
    if (useAI && query) {
      jobs = await applyAIRanking(jobs, query, client)
    }

    // Get total count for pagination
    const totalCount = await Job.countDocuments(searchQuery)
    const totalPages = Math.ceil(totalCount / limit)

    // Format job results
    const formattedJobs = jobs.map(job => ({
      id: job._id,
      title: job.title,
      company: {
        id: job.companyId._id,
        name: job.companyId.name,
        logo: job.companyId.logo,
        location: job.companyId.location,
        industry: job.companyId.industry,
        isVerified: job.companyId.verification?.isVerified || false
      },
      location: job.location,
      remote: job.remote,
      type: job.type,
      experienceLevel: job.experienceLevel,
      salary: job.salary,
      description: job.description,
      requirements: job.requirements,
      skills: job.skills,
      postedDate: job.createdAt,
      urgentHiring: job.urgentHiring,
      featured: job.featured,
      matchScore: calculateMatchScore(job, client),
      relevanceScore: calculateRelevanceScore(job, query, client)
    }))

    // Get search suggestions and recommendations
    let recommendations: any[] = []
    let suggestions: any[] = []

    if (includeRecommendations) {
      recommendations = await getJobRecommendations(client, 5)
      suggestions = await getSearchSuggestions(query, client)
    }

    // Save search to history
    await saveSearchToHistory(client._id, searchParams, formattedJobs.length)

    // Get search analytics
    const analytics = await getSearchAnalytics(searchParams, formattedJobs.length)

    return NextResponse.json({
      success: true,
      data: {
        jobs: formattedJobs,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          hasNext: page < totalPages,
          hasPrev: page > 1,
          limit
        },
        searchParams,
        analytics,
        recommendations: includeRecommendations ? recommendations : undefined,
        suggestions: includeRecommendations ? suggestions : undefined,
        searchId: generateSearchId()
      }
    })

  } catch (error) {
    console.error('Advanced search error:', error)
    return NextResponse.json(
      { error: 'Failed to perform search' },
      { status: 500 }
    )
  }
}

// Build advanced search query
async function buildAdvancedSearchQuery(params: any, client: any) {
  const query: any = {
    isActive: true,
    isPublished: true
  }

  // Text search
  if (params.query) {
    query.$or = [
      { title: { $regex: params.query, $options: 'i' } },
      { description: { $regex: params.query, $options: 'i' } },
      { requirements: { $in: [new RegExp(params.query, 'i')] } },
      { skills: { $in: [new RegExp(params.query, 'i')] } }
    ]
  }

  // Location filter
  if (params.location) {
    query.location = { $regex: params.location, $options: 'i' }
  }

  // Remote filter
  if (params.remote !== undefined) {
    query.remote = params.remote
  }

  // Job type filter
  if (params.jobType && params.jobType.length > 0) {
    query.type = { $in: params.jobType }
  }

  // Experience level filter
  if (params.experienceLevel && params.experienceLevel.length > 0) {
    query.experienceLevel = { $in: params.experienceLevel }
  }

  // Salary range filter
  if (params.salaryMin || params.salaryMax) {
    query['salary.min'] = {}
    if (params.salaryMin) {
      query['salary.min'].$gte = params.salaryMin
    }
    if (params.salaryMax) {
      query['salary.max'] = { $lte: params.salaryMax }
    }
  }

  // Industry filter
  if (params.industries && params.industries.length > 0) {
    const companies = await Company.find({ 
      industry: { $in: params.industries } 
    }).select('_id').lean()
    
    query.company = { $in: companies.map(c => c._id) }
  }

  // Company filter
  if (params.companies && params.companies.length > 0) {
    query.company = { $in: params.companies }
  }

  // Skills filter
  if (params.skills && params.skills.length > 0) {
    query.skills = { $in: params.skills }
  }

  // Date posted filter
  if (params.datePosted && params.datePosted !== 'anytime') {
    const now = new Date()
    let dateThreshold: Date

    switch (params.datePosted) {
      case 'today':
        dateThreshold = new Date(now.setHours(0, 0, 0, 0))
        break
      case 'week':
        dateThreshold = new Date(now.setDate(now.getDate() - 7))
        break
      case 'month':
        dateThreshold = new Date(now.setMonth(now.getMonth() - 1))
        break
      default:
        dateThreshold = new Date(0)
    }

    query.createdAt = { $gte: dateThreshold }
  }

  return query
}

// Build sort query with personalization
function buildSortQuery(sortBy: string, sortOrder: string, client: any, query?: string) {
  const order = sortOrder === 'asc' ? 1 : -1

  switch (sortBy) {
    case 'date':
      return { createdAt: order }
    case 'salary':
      return { 'salary.max': order }
    case 'company':
      return { 'company.name': order }
    case 'relevance':
    default:
      // For relevance, we'll use a combination of factors
      const relevanceSort: any = {}
      
      // Prioritize featured jobs
      relevanceSort.featured = -1
      
      // Then by urgent hiring
      relevanceSort.urgentHiring = -1
      
      // Then by creation date (newer first)
      relevanceSort.createdAt = -1
      
      return relevanceSort
  }
}

// Apply AI-powered ranking (simplified version)
async function applyAIRanking(jobs: any[], query: string, client: any) {
  // This is a simplified AI ranking algorithm
  // In a real implementation, you would use ML models or external AI services
  
  const rankedJobs = jobs.map(job => {
    let aiScore = 0
    
    // Text similarity scoring
    const titleMatch = calculateTextSimilarity(query.toLowerCase(), job.title.toLowerCase())
    const descriptionMatch = calculateTextSimilarity(query.toLowerCase(), job.description.toLowerCase())
    aiScore += (titleMatch * 0.4) + (descriptionMatch * 0.2)
    
    // Skills matching
    const clientSkills = client.skills || []
    const jobSkills = job.skills || []
    const skillsMatch = calculateArraySimilarity(clientSkills, jobSkills)
    aiScore += skillsMatch * 0.3
    
    // Experience level matching
    if (client.experience?.level === job.experienceLevel) {
      aiScore += 0.1
    }
    
    return { ...job, aiScore }
  })
  
  // Sort by AI score
  return rankedJobs.sort((a, b) => b.aiScore - a.aiScore)
}

// Calculate match score between job and client
function calculateMatchScore(job: any, client: any): number {
  let score = 0
  let factors = 0

  // Skills matching
  const clientSkills = client.skills || []
  const jobSkills = job.skills || []
  if (clientSkills.length > 0 && jobSkills.length > 0) {
    score += calculateArraySimilarity(clientSkills, jobSkills) * 40
    factors += 40
  }

  // Experience level matching
  if (client.experience?.level && job.experienceLevel) {
    if (client.experience.level === job.experienceLevel) {
      score += 30
    } else {
      // Partial match for adjacent levels
      const levels = ['entry', 'mid', 'senior', 'executive']
      const clientIndex = levels.indexOf(client.experience.level)
      const jobIndex = levels.indexOf(job.experienceLevel)
      if (Math.abs(clientIndex - jobIndex) === 1) {
        score += 15
      }
    }
    factors += 30
  }

  // Location preference
  if (client.preferences?.location && job.location) {
    if (job.location.toLowerCase().includes(client.preferences.location.toLowerCase())) {
      score += 20
    }
    factors += 20
  }

  // Remote preference
  if (client.preferences?.remote !== undefined && job.remote !== undefined) {
    if (client.preferences.remote === job.remote) {
      score += 10
    }
    factors += 10
  }

  return factors > 0 ? Math.round((score / factors) * 100) / 100 : 0
}

// Calculate relevance score
function calculateRelevanceScore(job: any, query: string, client: any): number {
  if (!query) return 0

  let score = 0
  const queryLower = query.toLowerCase()

  // Title relevance (highest weight)
  score += calculateTextSimilarity(queryLower, job.title.toLowerCase()) * 0.4

  // Description relevance
  score += calculateTextSimilarity(queryLower, job.description.toLowerCase()) * 0.2

  // Skills relevance
  const skillsText = (job.skills || []).join(' ').toLowerCase()
  score += calculateTextSimilarity(queryLower, skillsText) * 0.2

  // Requirements relevance
  const requirementsText = (job.requirements || []).join(' ').toLowerCase()
  score += calculateTextSimilarity(queryLower, requirementsText) * 0.2

  return Math.round(score * 100) / 100
}

// Helper functions
function calculateTextSimilarity(text1: string, text2: string): number {
  const words1 = text1.split(' ').filter(word => word.length > 2)
  const words2 = text2.split(' ').filter(word => word.length > 2)
  
  if (words1.length === 0 || words2.length === 0) return 0
  
  const matches = words1.filter(word => words2.some(w => w.includes(word) || word.includes(w)))
  return matches.length / Math.max(words1.length, words2.length)
}

function calculateArraySimilarity(arr1: string[], arr2: string[]): number {
  if (arr1.length === 0 || arr2.length === 0) return 0
  
  const intersection = arr1.filter(item => 
    arr2.some(item2 => item.toLowerCase() === item2.toLowerCase())
  )
  
  return intersection.length / Math.max(arr1.length, arr2.length)
}

async function getJobRecommendations(client: any, limit: number) {
  // Get personalized job recommendations
  const recommendations = await Job.find({
    isActive: true,
    isPublished: true,
    skills: { $in: client.skills || [] }
  })
    .populate('companyId', 'name logo')
    .limit(limit)
    .lean()

  return recommendations.map(job => ({
    id: job._id,
    title: job.title,
    company: job.company,
    matchScore: calculateMatchScore(job, client)
  }))
}

async function getSearchSuggestions(query: string, client: any) {
  // Generate search suggestions based on query and client history
  const suggestions = []
  
  if (query) {
    // Add related skills
    const relatedSkills = client.skills?.filter((skill: string) => 
      skill.toLowerCase().includes(query.toLowerCase())
    ) || []
    
    suggestions.push(...relatedSkills.map((skill: string) => ({
      type: 'skill',
      text: skill,
      category: 'Your Skills'
    })))
  }

  return suggestions.slice(0, 5)
}

async function saveSearchToHistory(clientId: string, searchParams: any, resultsCount: number) {
  const searchEntry = {
    id: generateSearchId(),
    query: searchParams.query || '',
    filters: searchParams,
    resultsCount,
    searchedAt: new Date()
  }

  await Client.findByIdAndUpdate(clientId, {
    $push: {
      searchHistory: {
        $each: [searchEntry],
        $slice: -100 // Keep only last 100 searches
      }
    }
  })
}

async function getSearchAnalytics(searchParams: any, resultsCount: number) {
  return {
    resultsCount,
    searchTime: Date.now(),
    hasFilters: Object.keys(searchParams).length > 2, // More than query and basic params
    filterCount: Object.keys(searchParams).length
  }
}

function generateSearchId(): string {
  return `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// Apply caching to the search handler
export const GET = withCache({
  ttl: CacheConfigs.SHORT.ttl,
  key: (req) => {
    const url = new URL(req.url)
    return CacheKeys.jobSearch(
      url.searchParams.get('query') || '',
      Object.fromEntries(url.searchParams.entries())
    )
  },
  condition: (req) => {
    // Only cache GET requests with query parameters
    const url = new URL(req.url)
    return url.searchParams.has('query')
  }
})(searchHandler)

export const POST = searchHandler

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { User } from '@/lib/models/user.model'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const recruiterId = params.id

    // Check if recruiter exists and is actually a recruiter
    const recruiter = await User.findOne({ 
      _id: recruiterId, 
      role: 'recruiter' 
    }).select('profile recruiterProfile')

    if (!recruiter) {
      return NextResponse.json(
        { error: 'Recruiter not found' },
        { status: 404 }
      )
    }

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Check if already following
    const isAlreadyFollowing = client.followedRecruiters?.includes(recruiterId as any)
    if (isAlreadyFollowing) {
      return NextResponse.json(
        { error: 'Already following this recruiter' },
        { status: 400 }
      )
    }

    // Add recruiter to followed recruiters
    await Client.findByIdAndUpdate(client._id, {
      $addToSet: { followedRecruiters: recruiterId },
      $set: { 'activity.lastProfileUpdate': new Date() }
    })

    // Update recruiter follower count
    await User.findByIdAndUpdate(recruiterId, {
      $inc: { 'recruiterProfile.followerCount': 1 }
    })

    // Create activity log entry
    const recruiterName = `${recruiter.profile?.firstName || ''} ${recruiter.profile?.lastName || ''}`.trim()
    const activityEntry = {
      type: 'recruiter_followed',
      description: `Started following ${recruiterName}`,
      metadata: {
        recruiterId: recruiter._id,
        recruiterName
      },
      createdAt: new Date()
    }

    await Client.findByIdAndUpdate(client._id, {
      $push: { 
        'activity.recentActivity': {
          $each: [activityEntry],
          $slice: -50 // Keep only last 50 activities
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: `Now following ${recruiterName}`,
      data: {
        recruiterId,
        recruiterName,
        isFollowing: true,
        followerCount: (recruiter.recruiterProfile?.followerCount || 0) + 1
      }
    })

  } catch (error) {
    console.error('Follow recruiter error:', error)
    return NextResponse.json(
      { error: 'Failed to follow recruiter' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const recruiterId = params.id

    // Check if recruiter exists
    const recruiter = await User.findOne({ 
      _id: recruiterId, 
      role: 'recruiter' 
    }).select('profile recruiterProfile')

    if (!recruiter) {
      return NextResponse.json(
        { error: 'Recruiter not found' },
        { status: 404 }
      )
    }

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Check if currently following
    const isFollowing = client.followedRecruiters?.includes(recruiterId as any)
    if (!isFollowing) {
      return NextResponse.json(
        { error: 'Not following this recruiter' },
        { status: 400 }
      )
    }

    // Remove recruiter from followed recruiters
    await Client.findByIdAndUpdate(client._id, {
      $pull: { followedRecruiters: recruiterId },
      $set: { 'activity.lastProfileUpdate': new Date() }
    })

    // Update recruiter follower count
    await User.findByIdAndUpdate(recruiterId, {
      $inc: { 'recruiterProfile.followerCount': -1 }
    })

    // Create activity log entry
    const recruiterName = `${recruiter.profile?.firstName || ''} ${recruiter.profile?.lastName || ''}`.trim()
    const activityEntry = {
      type: 'recruiter_unfollowed',
      description: `Stopped following ${recruiterName}`,
      metadata: {
        recruiterId: recruiter._id,
        recruiterName
      },
      createdAt: new Date()
    }

    await Client.findByIdAndUpdate(client._id, {
      $push: { 
        'activity.recentActivity': {
          $each: [activityEntry],
          $slice: -50 // Keep only last 50 activities
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: `Unfollowed ${recruiterName}`,
      data: {
        recruiterId,
        recruiterName,
        isFollowing: false,
        followerCount: Math.max((recruiter.recruiterProfile?.followerCount || 1) - 1, 0)
      }
    })

  } catch (error) {
    console.error('Unfollow recruiter error:', error)
    return NextResponse.json(
      { error: 'Failed to unfollow recruiter' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const recruiterId = params.id

    // Get client
    const client = await Client.findOne({ user: userId })
      .select('followedRecruiters')
      .lean()
    
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Check if following this recruiter
    const isFollowing = client.followedRecruiters?.includes(recruiterId as any) || false

    return NextResponse.json({
      success: true,
      data: {
        recruiterId,
        isFollowing
      }
    })

  } catch (error) {
    console.error('Check follow status error:', error)
    return NextResponse.json(
      { error: 'Failed to check follow status' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { User } from '@/lib/models/user.model'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const followRecruiterSchema = z.object({
  recruiterId: z.string().min(1, 'Recruiter ID is required')
})

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    // Get client profile
    const client = await Client.findOne({ user: userId })
      .select('followedRecruiters')
      .lean()
    
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    const followedRecruiterIds = client.followedRecruiters || []
    
    if (followedRecruiterIds.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          followedRecruiters: [],
          networkingRecommendations: [],
          pagination: {
            currentPage: page,
            totalPages: 0,
            totalCount: 0,
            hasNext: false,
            hasPrev: false
          }
        }
      })
    }

    // Get followed recruiters with their profiles
    const followedRecruiters = await User.find({ 
      _id: { $in: followedRecruiterIds },
      role: 'recruiter'
    })
      .select('profile email recruiterProfile verification activity')
      .populate('recruiterProfile.company', 'name logo')
      .sort({ 'profile.firstName': 1 })
      .skip(skip)
      .limit(limit)
      .lean()

    // Get networking recommendations (recruiters from followed companies)
    const followedCompanies = client.followedCompanies || []
    const networkingRecommendations = await User.find({
      role: 'recruiter',
      _id: { $nin: followedRecruiterIds },
      'recruiterProfile.company': { $in: followedCompanies }
    })
      .select('profile recruiterProfile verification')
      .populate('recruiterProfile.company', 'name logo')
      .limit(5)
      .lean()

    const totalCount = followedRecruiterIds.length
    const totalPages = Math.ceil(totalCount / limit)

    // Format recruiter data
    const formattedRecruiters = followedRecruiters.map(recruiter => ({
      id: recruiter._id,
      name: `${recruiter.profile?.firstName || ''} ${recruiter.profile?.lastName || ''}`.trim(),
      email: recruiter.email,
      title: recruiter.recruiterProfile?.title || 'Recruiter',
      company: recruiter.recruiterProfile?.company || null,
      specializations: recruiter.recruiterProfile?.specializations || [],
      experience: recruiter.recruiterProfile?.experience || 0,
      location: recruiter.profile?.location || '',
      avatar: recruiter.profile?.avatar || '',
      isVerified: recruiter.verification?.isVerified || false,
      connectionDate: recruiter.activity?.lastLogin || recruiter.createdAt,
      activeJobsCount: recruiter.recruiterProfile?.activeJobsCount || 0
    }))

    const formattedRecommendations = networkingRecommendations.map(recruiter => ({
      id: recruiter._id,
      name: `${recruiter.profile?.firstName || ''} ${recruiter.profile?.lastName || ''}`.trim(),
      title: recruiter.recruiterProfile?.title || 'Recruiter',
      company: recruiter.recruiterProfile?.company || null,
      specializations: recruiter.recruiterProfile?.specializations || [],
      avatar: recruiter.profile?.avatar || '',
      isVerified: recruiter.verification?.isVerified || false,
      reason: 'Works at a company you follow'
    }))

    return NextResponse.json({
      success: true,
      data: {
        followedRecruiters: formattedRecruiters,
        networkingRecommendations: formattedRecommendations,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    })

  } catch (error) {
    console.error('Get followed recruiters error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch followed recruiters' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const body = await request.json()

    // Validate request body
    const validationResult = followRecruiterSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { recruiterId } = validationResult.data

    // Check if recruiter exists and is actually a recruiter
    const recruiter = await User.findOne({ 
      _id: recruiterId, 
      role: 'recruiter' 
    }).select('profile recruiterProfile')

    if (!recruiter) {
      return NextResponse.json(
        { error: 'Recruiter not found' },
        { status: 404 }
      )
    }

    // Add recruiter to followed recruiters
    const client = await Client.findOneAndUpdate(
      { user: userId },
      { 
        $addToSet: { followedRecruiters: recruiterId },
        $set: { 'activity.lastProfileUpdate': new Date() }
      },
      { new: true, upsert: true }
    )

    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Update recruiter follower count
    await User.findByIdAndUpdate(recruiterId, {
      $inc: { 'recruiterProfile.followerCount': 1 }
    })

    return NextResponse.json({
      success: true,
      message: 'Recruiter followed successfully',
      data: { recruiterId }
    }, { status: 201 })

  } catch (error) {
    console.error('Follow recruiter error:', error)
    return NextResponse.json(
      { error: 'Failed to follow recruiter' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const recruiterId = searchParams.get('recruiterId')

    if (!recruiterId) {
      return NextResponse.json(
        { error: 'Recruiter ID is required' },
        { status: 400 }
      )
    }

    // Remove recruiter from followed recruiters
    const client = await Client.findOneAndUpdate(
      { user: userId },
      { 
        $pull: { followedRecruiters: recruiterId },
        $set: { 'activity.lastProfileUpdate': new Date() }
      },
      { new: true }
    )

    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Update recruiter follower count
    await User.findByIdAndUpdate(recruiterId, {
      $inc: { 'recruiterProfile.followerCount': -1 }
    })

    return NextResponse.json({
      success: true,
      message: 'Recruiter unfollowed successfully',
      data: { recruiterId }
    })

  } catch (error) {
    console.error('Unfollow recruiter error:', error)
    return NextResponse.json(
      { error: 'Failed to unfollow recruiter' },
      { status: 500 }
    )
  }
}

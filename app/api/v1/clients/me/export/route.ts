import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { User } from '@/lib/models/user.model'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const format = searchParams.get('format') || 'json'

    if (!['json', 'pdf'].includes(format)) {
      return NextResponse.json(
        { error: 'Invalid format. Supported formats: json, pdf' },
        { status: 400 }
      )
    }

    // Get client profile with user data
    const client = await Client.findOne({ user: userId })
      .populate('user', 'profile email createdAt')
      .lean()
    
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    const exportData = {
      profile: {
        personalInfo: {
          fullName: client.user?.profile?.firstName + ' ' + client.user?.profile?.lastName,
          email: client.user?.email,
          phone: client.user?.profile?.phone,
          location: client.user?.profile?.location,
          memberSince: client.user?.createdAt
        },
        professional: {
          headline: client.headline,
          summary: client.summary,
          currentTitle: client.currentTitle,
          experience: client.experience,
          workHistory: client.workHistory,
          education: client.education,
          skills: client.skills,
          certifications: client.certifications,
          languages: client.languages
        },
        preferences: {
          jobPreferences: client.jobPreferences,
          privacy: client.privacy
        },
        activity: {
          profileViews: client.activity?.profileViews || 0,
          searchAppearances: client.activity?.searchAppearances || 0,
          lastProfileUpdate: client.activity?.lastProfileUpdate,
          profileCompleteness: client.activity?.profileCompleteness || 0
        },
        stats: {
          applicationStats: client.applicationStats,
          savedJobsCount: client.savedJobs?.length || 0,
          followedCompaniesCount: client.followedCompanies?.length || 0
        }
      },
      exportInfo: {
        exportedAt: new Date(),
        format: format,
        version: '1.0'
      }
    }

    if (format === 'json') {
      return NextResponse.json({
        success: true,
        data: exportData
      })
    }

    if (format === 'pdf') {
      // For PDF export, we'll return a URL to generate the PDF
      // This would typically integrate with a PDF generation service
      return NextResponse.json({
        success: true,
        data: {
          downloadUrl: `/api/v1/clients/me/export/pdf?token=${generateExportToken(userId)}`,
          expiresAt: new Date(Date.now() + 3600000), // 1 hour
          format: 'pdf'
        }
      })
    }

  } catch (error) {
    console.error('Export profile error:', error)
    return NextResponse.json(
      { error: 'Failed to export profile' },
      { status: 500 }
    )
  }
}

function generateExportToken(userId: string): string {
  // In a real implementation, this would generate a secure token
  // For now, we'll use a simple base64 encoding
  const tokenData = {
    userId,
    timestamp: Date.now(),
    purpose: 'profile_export'
  }
  return Buffer.from(JSON.stringify(tokenData)).toString('base64')
}

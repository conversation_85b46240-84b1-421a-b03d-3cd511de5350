import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id

    // Get client profile
    const client = await Client.findOne({ user: userId }).lean()
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Calculate profile completeness
    const completeness = calculateProfileCompleteness(client)

    return NextResponse.json({
      success: true,
      data: {
        completeness: completeness.percentage,
        completedSections: completeness.completed,
        missingSections: completeness.missing,
        recommendations: completeness.recommendations
      }
    })

  } catch (error) {
    console.error('Get profile completeness error:', error)
    return NextResponse.json(
      { error: 'Failed to calculate profile completeness' },
      { status: 500 }
    )
  }
}

function calculateProfileCompleteness(client: any) {
  const sections = [
    {
      name: 'Basic Information',
      weight: 15,
      completed: !!(client.headline && client.summary && client.currentTitle),
      recommendation: 'Add professional headline, summary, and current title'
    },
    {
      name: 'Experience',
      weight: 20,
      completed: !!(client.experience?.level && client.experience?.yearsOfExperience >= 0),
      recommendation: 'Add your experience level and years of experience'
    },
    {
      name: 'Work History',
      weight: 25,
      completed: !!(client.workHistory && client.workHistory.length > 0),
      recommendation: 'Add at least one work experience entry'
    },
    {
      name: 'Education',
      weight: 15,
      completed: !!(client.education && client.education.length > 0),
      recommendation: 'Add your educational background'
    },
    {
      name: 'Skills',
      weight: 15,
      completed: !!(client.skills && client.skills.length >= 3),
      recommendation: 'Add at least 3 relevant skills'
    },
    {
      name: 'Job Preferences',
      weight: 10,
      completed: !!(client.jobPreferences?.desiredRoles && client.jobPreferences.desiredRoles.length > 0),
      recommendation: 'Set your job preferences and desired roles'
    }
  ]

  const completed = sections.filter(section => section.completed)
  const missing = sections.filter(section => !section.completed)
  
  const totalWeight = sections.reduce((sum, section) => sum + section.weight, 0)
  const completedWeight = completed.reduce((sum, section) => sum + section.weight, 0)
  
  const percentage = Math.round((completedWeight / totalWeight) * 100)

  return {
    percentage,
    completed: completed.map(s => s.name),
    missing: missing.map(s => s.name),
    recommendations: missing.map(s => s.recommendation)
  }
}

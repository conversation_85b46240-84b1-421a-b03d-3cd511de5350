import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { z } from 'zod'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const visibilitySchema = z.object({
  profileVisibility: z.enum(['public', 'private', 'recruiters_only']),
  showSalaryExpectation: z.boolean().optional(),
  showCurrentCompany: z.boolean().optional(),
  allowRecruiterContact: z.boolean().optional(),
  showProfileToCurrentEmployer: z.boolean().optional()
})

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id

    // Get client profile
    const client = await Client.findOne({ user: userId })
      .select('privacy isPublic')
      .lean()
    
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        privacy: client.privacy || {
          profileVisibility: 'public',
          showSalaryExpectation: true,
          showCurrentCompany: true,
          allowRecruiterContact: true,
          showProfileToCurrentEmployer: false
        },
        isPublic: client.isPublic
      }
    })

  } catch (error) {
    console.error('Get visibility settings error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch visibility settings' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    await connectDB()

    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const body = await request.json()

    // Validate request body
    const validationResult = visibilitySchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid visibility settings',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const visibilitySettings = validationResult.data

    // Update client privacy settings
    const updateData: any = {
      privacy: visibilitySettings,
      isPublic: visibilitySettings.profileVisibility !== 'private'
    }

    const updatedClient = await Client.findOneAndUpdate(
      { user: userId },
      { $set: updateData },
      { new: true, select: 'privacy isPublic' }
    )

    if (!updatedClient) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Visibility settings updated successfully',
      data: {
        privacy: updatedClient.privacy,
        isPublic: updatedClient.isPublic
      }
    })

  } catch (error) {
    console.error('Update visibility settings error:', error)
    return NextResponse.json(
      { error: 'Failed to update visibility settings' },
      { status: 500 }
    )
  }
}

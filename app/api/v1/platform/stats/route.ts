import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { Job } from '@/lib/models/job.model'
import { Company } from '@/lib/models/company.model'
import { Application } from '@/lib/models/application.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    // Get platform statistics in parallel for better performance
    const [
      activeJobsCount,
      totalCompaniesCount,
      totalApplicationsCount,
      totalHiresCount
    ] = await Promise.all([
      // Active jobs count
      Job.countDocuments({ 
        isActive: true,
        $or: [
          { applicationDeadline: { $gte: new Date() } },
          { applicationDeadline: { $exists: false } }
        ]
      }),
      
      // Total companies count
      Company.countDocuments({ isActive: true }),
      
      // Total applications count
      Application.countDocuments(),
      
      // Total successful hires (applications with status 'offer_received')
      Application.countDocuments({ status: 'offer_received' })
    ])

    // Calculate additional stats
    const stats = {
      activeJobs: activeJobsCount,
      totalCompanies: totalCompaniesCount,
      totalApplications: totalApplicationsCount,
      successfulHires: totalHiresCount,
      
      // Additional derived stats
      averageApplicationsPerJob: activeJobsCount > 0 ? Math.round(totalApplicationsCount / activeJobsCount) : 0,
      hireRate: totalApplicationsCount > 0 ? Math.round((totalHiresCount / totalApplicationsCount) * 100) : 0
    }

    return NextResponse.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error fetching platform stats:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch platform statistics' 
      },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'

export async function GET(request: NextRequest) {
  try {
    // Test database connection
    await connectDB()

    // Test authentication
    const authResult = await authMiddleware(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    // Return test data
    return NextResponse.json({
      success: true,
      message: 'Billing API is working correctly',
      data: {
        timestamp: new Date().toISOString(),
        user: authResult.user.email,
        dbConnected: true
      }
    })

  } catch (error) {
    console.error('Billing test API error:', error)
    return NextResponse.json(
      { 
        error: 'Billing API test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { BillingPlan } from '@/lib/models/billing-plan.model'
import { Subscription } from '@/lib/models/subscription.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    // Verify admin authentication
    const authResult = await authMiddleware(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')
    const status = searchParams.get('status')

    // Build query
    const query: any = {}
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { slug: { $regex: search, $options: 'i' } }
      ]
    }

    if (status && status !== 'all') {
      query.isActive = status === 'active'
    }

    // Get plans with subscriber counts
    const plans = await BillingPlan.find(query)
      .sort({ sortOrder: 1, createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean()

    // Get subscriber counts for each plan
    const plansWithCounts = await Promise.all(
      plans.map(async (plan) => {
        const subscriberCount = await Subscription.countDocuments({
          plan: plan._id,
          status: { $in: ['active', 'trialing'] }
        })
        return {
          ...plan,
          subscriberCount
        }
      })
    )

    const total = await BillingPlan.countDocuments(query)

    return NextResponse.json({
      success: true,
      data: {
        plans: plansWithCounts,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('Error fetching billing plans:', error)
    return NextResponse.json(
      { error: 'Failed to fetch billing plans' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    // Verify admin authentication
    const authResult = await verifyToken(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      name,
      slug,
      description,
      price,
      features,
      limits,
      isActive = true,
      isPopular = false,
      sortOrder = 0,
      trialDays = 14
    } = body

    // Validate required fields
    if (!name || !slug || !description || !price) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Check if slug already exists
    const existingPlan = await BillingPlan.findOne({ slug })
    if (existingPlan) {
      return NextResponse.json(
        { error: 'Plan with this slug already exists' },
        { status: 400 }
      )
    }

    // Create new plan
    const plan = new BillingPlan({
      name,
      slug,
      description,
      price,
      features: features || [],
      limits: {
        jobPostings: limits?.jobPostings || -1,
        featuredJobs: limits?.featuredJobs || 0,
        applicationsPerJob: limits?.applicationsPerJob || -1,
        teamMembers: limits?.teamMembers || 1,
        customBranding: limits?.customBranding || false,
        prioritySupport: limits?.prioritySupport || false,
        analytics: limits?.analytics || false,
        apiAccess: limits?.apiAccess || false,
        ...limits
      },
      isActive,
      isPopular,
      sortOrder,
      trialDays,
      createdBy: authResult.user.id
    })

    await plan.save()

    return NextResponse.json({
      success: true,
      data: { plan }
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating billing plan:', error)
    return NextResponse.json(
      { error: 'Failed to create billing plan' },
      { status: 500 }
    )
  }
}

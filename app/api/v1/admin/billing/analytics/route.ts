import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Subscription } from '@/lib/models/subscription.model'
import { Payment } from '@/lib/models/payment.model'
import { Invoice } from '@/lib/models/invoice.model'
import { BillingPlan } from '@/lib/models/billing-plan.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    // Verify admin authentication
    const authResult = await authMiddleware(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '30d'
    const reportType = searchParams.get('type') || 'overview'

    // Calculate date ranges
    const now = new Date()
    let startDate: Date
    let previousStartDate: Date

    switch (timeRange) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        previousStartDate = new Date(startDate.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        previousStartDate = new Date(startDate.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        previousStartDate = new Date(startDate.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
        previousStartDate = new Date(startDate.getTime() - 365 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        previousStartDate = new Date(startDate.getTime() - 30 * 24 * 60 * 60 * 1000)
    }

    let analytics: any = {}

    switch (reportType) {
      case 'overview':
        analytics = await getOverviewAnalytics(startDate, previousStartDate, now)
        break
      case 'revenue':
        analytics = await getRevenueAnalytics(startDate, now)
        break
      case 'subscriptions':
        analytics = await getSubscriptionAnalytics(startDate, now)
        break
      case 'plans':
        analytics = await getPlanAnalytics(startDate, now)
        break
      case 'cohort':
        analytics = await getCohortAnalytics(startDate, now)
        break
      default:
        analytics = await getOverviewAnalytics(startDate, previousStartDate, now)
    }

    return NextResponse.json({
      success: true,
      data: {
        analytics,
        timeRange,
        reportType,
        generatedAt: new Date()
      }
    })

  } catch (error) {
    console.error('Error fetching billing analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch billing analytics' },
      { status: 500 }
    )
  }
}

async function getOverviewAnalytics(startDate: Date, previousStartDate: Date, endDate: Date) {
  // Revenue metrics
  const [currentRevenue, previousRevenue] = await Promise.all([
    Payment.aggregate([
      {
        $match: {
          status: 'succeeded',
          paidAt: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      }
    ]),
    Payment.aggregate([
      {
        $match: {
          status: 'succeeded',
          paidAt: { $gte: previousStartDate, $lt: startDate }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      }
    ])
  ])

  // Subscription metrics
  const [newSubscriptions, canceledSubscriptions, activeSubscriptions] = await Promise.all([
    Subscription.countDocuments({
      createdAt: { $gte: startDate, $lte: endDate }
    }),
    Subscription.countDocuments({
      canceledAt: { $gte: startDate, $lte: endDate }
    }),
    Subscription.countDocuments({
      status: 'active'
    })
  ])

  // Calculate MRR and growth
  const activeSubs = await Subscription.find({ status: 'active' }).populate('plan')
  const mrr = activeSubs.reduce((sum, sub: any) => {
    const monthlyAmount = sub.billingCycle === 'yearly' ? sub.pricePerPeriod / 12 : sub.pricePerPeriod
    return sum + monthlyAmount
  }, 0)

  // Revenue growth calculation
  const currentRevenueTotal = currentRevenue[0]?.total || 0
  const previousRevenueTotal = previousRevenue[0]?.total || 0
  const revenueGrowth = previousRevenueTotal > 0 
    ? ((currentRevenueTotal - previousRevenueTotal) / previousRevenueTotal) * 100 
    : 0

  // Churn rate calculation
  const totalSubscriptions = await Subscription.countDocuments()
  const churnRate = totalSubscriptions > 0 ? (canceledSubscriptions / totalSubscriptions) * 100 : 0

  return {
    revenue: {
      current: currentRevenueTotal,
      previous: previousRevenueTotal,
      growth: revenueGrowth,
      transactions: currentRevenue[0]?.count || 0
    },
    subscriptions: {
      new: newSubscriptions,
      canceled: canceledSubscriptions,
      active: activeSubscriptions,
      churnRate
    },
    mrr: {
      current: mrr,
      arr: mrr * 12
    },
    kpis: {
      arpu: activeSubscriptions > 0 ? currentRevenueTotal / activeSubscriptions : 0,
      ltv: mrr > 0 && churnRate > 0 ? (mrr / activeSubscriptions) / (churnRate / 100) : 0
    }
  }
}

async function getRevenueAnalytics(startDate: Date, endDate: Date) {
  // Daily revenue breakdown
  const dailyRevenue = await Payment.aggregate([
    {
      $match: {
        status: 'succeeded',
        paidAt: { $gte: startDate, $lte: endDate }
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$paidAt' },
          month: { $month: '$paidAt' },
          day: { $dayOfMonth: '$paidAt' }
        },
        revenue: { $sum: '$amount' },
        transactions: { $sum: 1 }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
    }
  ])

  // Revenue by payment method
  const revenueByMethod = await Payment.aggregate([
    {
      $match: {
        status: 'succeeded',
        paidAt: { $gte: startDate, $lte: endDate }
      }
    },
    {
      $group: {
        _id: '$paymentMethod',
        revenue: { $sum: '$amount' },
        transactions: { $sum: 1 }
      }
    }
  ])

  // Revenue by plan
  const revenueByPlan = await Payment.aggregate([
    {
      $match: {
        status: 'succeeded',
        paidAt: { $gte: startDate, $lte: endDate },
        subscription: { $exists: true }
      }
    },
    {
      $lookup: {
        from: 'subscriptions',
        localField: 'subscription',
        foreignField: '_id',
        as: 'subscription'
      }
    },
    {
      $unwind: '$subscription'
    },
    {
      $lookup: {
        from: 'billingplans',
        localField: 'subscription.plan',
        foreignField: '_id',
        as: 'plan'
      }
    },
    {
      $unwind: '$plan'
    },
    {
      $group: {
        _id: '$plan.name',
        revenue: { $sum: '$amount' },
        transactions: { $sum: 1 }
      }
    }
  ])

  return {
    dailyRevenue,
    revenueByMethod,
    revenueByPlan,
    summary: {
      totalRevenue: dailyRevenue.reduce((sum, day) => sum + day.revenue, 0),
      totalTransactions: dailyRevenue.reduce((sum, day) => sum + day.transactions, 0),
      averageTransactionValue: dailyRevenue.length > 0 
        ? dailyRevenue.reduce((sum, day) => sum + day.revenue, 0) / dailyRevenue.reduce((sum, day) => sum + day.transactions, 0)
        : 0
    }
  }
}

async function getSubscriptionAnalytics(startDate: Date, endDate: Date) {
  // Subscription status distribution
  const statusDistribution = await Subscription.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ])

  // Subscription lifecycle metrics
  const lifecycleMetrics = await Subscription.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate, $lte: endDate }
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        },
        newSubscriptions: { $sum: 1 },
        trialSubscriptions: {
          $sum: { $cond: [{ $eq: ['$status', 'trialing'] }, 1, 0] }
        }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
    }
  ])

  // Billing cycle distribution
  const billingCycleDistribution = await Subscription.aggregate([
    {
      $group: {
        _id: '$billingCycle',
        count: { $sum: 1 },
        revenue: { $sum: '$pricePerPeriod' }
      }
    }
  ])

  return {
    statusDistribution,
    lifecycleMetrics,
    billingCycleDistribution,
    summary: {
      totalSubscriptions: statusDistribution.reduce((sum, status) => sum + status.count, 0),
      activeSubscriptions: statusDistribution.find(s => s._id === 'active')?.count || 0,
      trialSubscriptions: statusDistribution.find(s => s._id === 'trialing')?.count || 0
    }
  }
}

async function getPlanAnalytics(startDate: Date, endDate: Date) {
  // Plan popularity and revenue
  const planMetrics = await BillingPlan.aggregate([
    {
      $lookup: {
        from: 'subscriptions',
        localField: '_id',
        foreignField: 'plan',
        as: 'subscriptions'
      }
    },
    {
      $project: {
        name: 1,
        price: 1,
        isActive: 1,
        isPopular: 1,
        totalSubscriptions: { $size: '$subscriptions' },
        activeSubscriptions: {
          $size: {
            $filter: {
              input: '$subscriptions',
              cond: { $eq: ['$$this.status', 'active'] }
            }
          }
        },
        trialSubscriptions: {
          $size: {
            $filter: {
              input: '$subscriptions',
              cond: { $eq: ['$$this.status', 'trialing'] }
            }
          }
        },
        monthlyRevenue: {
          $sum: {
            $map: {
              input: {
                $filter: {
                  input: '$subscriptions',
                  cond: { $eq: ['$$this.status', 'active'] }
                }
              },
              as: 'sub',
              in: {
                $cond: [
                  { $eq: ['$$sub.billingCycle', 'yearly'] },
                  { $divide: ['$$sub.pricePerPeriod', 12] },
                  '$$sub.pricePerPeriod'
                ]
              }
            }
          }
        }
      }
    },
    {
      $sort: { totalSubscriptions: -1 }
    }
  ])

  return {
    planMetrics,
    summary: {
      totalPlans: planMetrics.length,
      activePlans: planMetrics.filter(p => p.isActive).length,
      totalSubscriptions: planMetrics.reduce((sum, plan) => sum + plan.totalSubscriptions, 0),
      totalMonthlyRevenue: planMetrics.reduce((sum, plan) => sum + plan.monthlyRevenue, 0)
    }
  }
}

async function getCohortAnalytics(startDate: Date, endDate: Date) {
  // Cohort analysis by month
  const cohortData = await Subscription.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate, $lte: endDate }
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        },
        cohortSize: { $sum: 1 },
        subscriptions: { $push: '$$ROOT' }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1 }
    }
  ])

  // Calculate retention rates for each cohort
  const cohortAnalysis = await Promise.all(
    cohortData.map(async (cohort) => {
      const cohortStart = new Date(cohort._id.year, cohort._id.month - 1, 1)
      const subscriptionIds = cohort.subscriptions.map((sub: any) => sub._id)

      // Calculate retention for different periods
      const retentionPeriods = [1, 3, 6, 12] // months
      const retention = await Promise.all(
        retentionPeriods.map(async (months) => {
          const periodEnd = new Date(cohortStart)
          periodEnd.setMonth(periodEnd.getMonth() + months)

          const activeCount = await Subscription.countDocuments({
            _id: { $in: subscriptionIds },
            $or: [
              { status: 'active' },
              { canceledAt: { $gte: periodEnd } }
            ]
          })

          return {
            period: months,
            retained: activeCount,
            rate: (activeCount / cohort.cohortSize) * 100
          }
        })
      )

      return {
        cohort: `${cohort._id.year}-${cohort._id.month.toString().padStart(2, '0')}`,
        size: cohort.cohortSize,
        retention
      }
    })
  )

  return {
    cohortAnalysis,
    summary: {
      totalCohorts: cohortData.length,
      averageCohortSize: cohortData.length > 0 
        ? cohortData.reduce((sum, cohort) => sum + cohort.cohortSize, 0) / cohortData.length 
        : 0
    }
  }
}

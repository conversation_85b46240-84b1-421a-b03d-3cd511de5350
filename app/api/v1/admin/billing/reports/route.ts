import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Subscription } from '@/lib/models/subscription.model'
import { Payment } from '@/lib/models/payment.model'
import { Invoice } from '@/lib/models/invoice.model'
import { BillingPlan } from '@/lib/models/billing-plan.model'

// Mock report storage (in production, use a proper database model)
const reports: any[] = []

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    // Verify admin authentication
    const authResult = await authMiddleware(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    // Return mock reports for now
    const mockReports = [
      {
        _id: 'report-1',
        name: 'Monthly Revenue Report',
        type: 'revenue',
        description: 'Comprehensive revenue analysis for the current month',
        parameters: {
          dateRange: {
            start: new Date(2024, 0, 1),
            end: new Date(2024, 0, 31)
          },
          filters: {},
          groupBy: 'day',
          metrics: ['total_revenue', 'mrr', 'arr', 'growth_rate']
        },
        status: 'completed',
        fileUrl: '/reports/revenue-2024-01.xlsx',
        fileSize: 245760,
        generatedAt: new Date(),
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        createdBy: {
          _id: authResult.user.id,
          name: authResult.user.name || 'Admin User',
          email: authResult.user.email
        },
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
      },
      {
        _id: 'report-2',
        name: 'Subscription Analytics',
        type: 'subscriptions',
        description: 'Subscription lifecycle and churn analysis',
        parameters: {
          dateRange: {
            start: new Date(2024, 0, 1),
            end: new Date(2024, 0, 31)
          },
          filters: {},
          groupBy: 'week',
          metrics: ['new_subscriptions', 'canceled_subscriptions', 'churn_rate']
        },
        status: 'generating',
        generatedAt: null,
        createdBy: {
          _id: authResult.user.id,
          name: authResult.user.name || 'Admin User',
          email: authResult.user.email
        },
        createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000)
      }
    ]

    return NextResponse.json({
      success: true,
      data: {
        reports: mockReports,
        pagination: {
          page: 1,
          limit: 10,
          total: mockReports.length,
          pages: 1
        }
      }
    })

  } catch (error) {
    console.error('Error fetching reports:', error)
    return NextResponse.json(
      { error: 'Failed to fetch reports' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    // Verify admin authentication
    const authResult = await verifyToken(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { name, description, type, parameters } = body

    // Validate required fields
    if (!name || !type || !parameters) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Generate report ID
    const reportId = `report-${Date.now()}`

    // Create report record
    const report = {
      _id: reportId,
      name,
      type,
      description: description || '',
      parameters: {
        dateRange: {
          start: new Date(parameters.dateRange.start),
          end: new Date(parameters.dateRange.end)
        },
        filters: parameters.filters || {},
        groupBy: parameters.groupBy || 'day',
        metrics: parameters.metrics || []
      },
      status: 'generating',
      createdBy: {
        _id: authResult.user.id,
        name: authResult.user.name || 'Admin User',
        email: authResult.user.email
      },
      createdAt: new Date(),
      updatedAt: new Date()
    }

    // Start report generation (async)
    generateReport(report)

    return NextResponse.json({
      success: true,
      data: { report }
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating report:', error)
    return NextResponse.json(
      { error: 'Failed to create report' },
      { status: 500 }
    )
  }
}

async function generateReport(report: any) {
  try {
    // Simulate report generation delay
    await new Promise(resolve => setTimeout(resolve, 5000))

    const { type, parameters } = report
    let data: any = {}

    switch (type) {
      case 'revenue':
        data = await generateRevenueReport(parameters)
        break
      case 'subscriptions':
        data = await generateSubscriptionReport(parameters)
        break
      case 'payments':
        data = await generatePaymentReport(parameters)
        break
      case 'customers':
        data = await generateCustomerReport(parameters)
        break
      default:
        throw new Error(`Unknown report type: ${type}`)
    }

    // Update report status
    report.status = 'completed'
    report.generatedAt = new Date()
    report.fileUrl = `/reports/${report._id}.xlsx`
    report.fileSize = Math.floor(Math.random() * 500000) + 100000 // Mock file size

    console.log(`Report ${report._id} generated successfully`)

  } catch (error) {
    console.error(`Error generating report ${report._id}:`, error)
    report.status = 'failed'
    report.failureReason = error instanceof Error ? error.message : 'Unknown error'
  }
}

async function generateRevenueReport(parameters: any) {
  const { dateRange } = parameters

  // Get revenue data
  const revenueData = await Payment.aggregate([
    {
      $match: {
        status: 'succeeded',
        paidAt: {
          $gte: new Date(dateRange.start),
          $lte: new Date(dateRange.end)
        }
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$paidAt' },
          month: { $month: '$paidAt' },
          day: { $dayOfMonth: '$paidAt' }
        },
        totalRevenue: { $sum: '$amount' },
        transactionCount: { $sum: 1 }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
    }
  ])

  // Calculate MRR
  const activeSubs = await Subscription.find({ status: 'active' })
  const mrr = activeSubs.reduce((sum, sub: any) => {
    const monthlyAmount = sub.billingCycle === 'yearly' ? sub.pricePerPeriod / 12 : sub.pricePerPeriod
    return sum + monthlyAmount
  }, 0)

  return {
    dailyRevenue: revenueData,
    mrr,
    arr: mrr * 12,
    totalRevenue: revenueData.reduce((sum, day) => sum + day.totalRevenue, 0),
    totalTransactions: revenueData.reduce((sum, day) => sum + day.transactionCount, 0)
  }
}

async function generateSubscriptionReport(parameters: any) {
  const { dateRange } = parameters

  // Get subscription data
  const subscriptionData = await Subscription.aggregate([
    {
      $match: {
        createdAt: {
          $gte: new Date(dateRange.start),
          $lte: new Date(dateRange.end)
        }
      }
    },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ])

  // Get churn data
  const canceledSubs = await Subscription.countDocuments({
    canceledAt: {
      $gte: new Date(dateRange.start),
      $lte: new Date(dateRange.end)
    }
  })

  const totalSubs = await Subscription.countDocuments()
  const churnRate = totalSubs > 0 ? (canceledSubs / totalSubs) * 100 : 0

  return {
    subscriptionsByStatus: subscriptionData,
    churnRate,
    totalSubscriptions: totalSubs,
    canceledSubscriptions: canceledSubs
  }
}

async function generatePaymentReport(parameters: any) {
  const { dateRange } = parameters

  // Get payment data
  const paymentData = await Payment.aggregate([
    {
      $match: {
        createdAt: {
          $gte: new Date(dateRange.start),
          $lte: new Date(dateRange.end)
        }
      }
    },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalAmount: { $sum: '$amount' }
      }
    }
  ])

  // Calculate success rate
  const totalPayments = paymentData.reduce((sum, status) => sum + status.count, 0)
  const successfulPayments = paymentData.find(s => s._id === 'succeeded')?.count || 0
  const successRate = totalPayments > 0 ? (successfulPayments / totalPayments) * 100 : 0

  return {
    paymentsByStatus: paymentData,
    successRate,
    totalPayments,
    successfulPayments
  }
}

async function generateCustomerReport(parameters: any) {
  const { dateRange } = parameters

  // Get customer data (simplified)
  const customerData = await Subscription.aggregate([
    {
      $lookup: {
        from: 'companies',
        localField: 'company',
        foreignField: '_id',
        as: 'company'
      }
    },
    {
      $unwind: '$company'
    },
    {
      $group: {
        _id: '$company._id',
        companyName: { $first: '$company.name' },
        totalSpent: { $sum: '$pricePerPeriod' },
        subscriptionCount: { $sum: 1 }
      }
    },
    {
      $sort: { totalSpent: -1 }
    }
  ])

  // Calculate ARPU
  const totalRevenue = customerData.reduce((sum, customer) => sum + customer.totalSpent, 0)
  const arpu = customerData.length > 0 ? totalRevenue / customerData.length : 0

  return {
    customers: customerData,
    arpu,
    totalCustomers: customerData.length,
    totalRevenue
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/middleware/auth.middleware'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const authResult = await authMiddleware(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    const reportId = params.id

    // In a real implementation, you would:
    // 1. Find the report in the database
    // 2. Check if the file exists
    // 3. Return the actual file

    // For now, generate a mock Excel file
    const mockExcelData = generateMockExcelFile(reportId)

    return new NextResponse(mockExcelData, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="report-${reportId}.xlsx"`,
        'Content-Length': mockExcelData.length.toString()
      }
    })

  } catch (error) {
    console.error('Error downloading report:', error)
    return NextResponse.json(
      { error: 'Failed to download report' },
      { status: 500 }
    )
  }
}

function generateMockExcelFile(reportId: string): Buffer {
  // This is a mock implementation
  // In a real application, you would use a library like 'exceljs' to generate actual Excel files
  
  const csvContent = `Report ID,${reportId}
Generated At,${new Date().toISOString()}
Type,Revenue Report

Date,Revenue,Transactions,MRR
2024-01-01,1250.00,15,8500.00
2024-01-02,1890.50,22,8500.00
2024-01-03,2100.75,28,8500.00
2024-01-04,1675.25,19,8500.00
2024-01-05,2250.00,31,8500.00

Summary
Total Revenue,9166.50
Total Transactions,115
Average Transaction Value,79.71
Monthly Recurring Revenue,8500.00
Annual Recurring Revenue,102000.00`

  return Buffer.from(csvContent, 'utf-8')
}

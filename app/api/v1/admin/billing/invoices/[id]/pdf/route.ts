import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { PDFService } from '@/lib/services/pdf.service'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const authResult = await authMiddleware(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    const invoiceId = params.id

    // Generate PDF
    const pdfBuffer = await PDFService.generateInvoicePDF(invoiceId)

    // Return PDF response
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="invoice-${invoiceId}.pdf"`,
        'Content-Length': pdfBuffer.length.toString()
      }
    })

  } catch (error) {
    console.error('Error generating invoice PDF:', error)
    return NextResponse.json(
      { error: 'Failed to generate invoice PDF' },
      { status: 500 }
    )
  }
}

// Also support POST for generating and sending PDF via email
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyToken(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    const invoiceId = params.id
    const body = await request.json()
    const { email, sendEmail = false } = body

    // Generate PDF
    const pdfBuffer = await PDFService.generateInvoicePDF(invoiceId)

    if (sendEmail && email) {
      // Import EmailService here to avoid circular dependencies
      const { EmailService } = await import('@/lib/services/email.service')
      
      await EmailService.sendInvoiceEmail(
        email,
        `Invoice #${invoiceId}`,
        pdfBuffer,
        `invoice-${invoiceId}.pdf`
      )

      return NextResponse.json({
        success: true,
        message: 'Invoice PDF generated and sent via email'
      })
    }

    // Return PDF as base64 for frontend handling
    return NextResponse.json({
      success: true,
      data: {
        pdf: pdfBuffer.toString('base64'),
        filename: `invoice-${invoiceId}.pdf`
      }
    })

  } catch (error) {
    console.error('Error generating/sending invoice PDF:', error)
    return NextResponse.json(
      { error: 'Failed to generate or send invoice PDF' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Payment } from '@/lib/models/payment.model'
import { StripeService } from '@/lib/services/stripe.service'

// Define refund interface
interface IRefund {
  _id: string
  payment: string
  amount: number
  currency: string
  reason: string
  status: 'pending' | 'succeeded' | 'failed' | 'canceled'
  stripeRefundId?: string
  failureReason?: string
  processedAt?: Date
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    // Verify admin authentication
    const authResult = await authMiddleware(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')
    const status = searchParams.get('status')

    // Build query for refunded payments
    const query: any = {
      $or: [
        { status: 'refunded' },
        { status: 'partially_refunded' }
      ]
    }

    // Build aggregation pipeline
    const pipeline: any[] = [
      {
        $lookup: {
          from: 'companies',
          localField: 'company',
          foreignField: '_id',
          as: 'company'
        }
      },
      {
        $unwind: '$company'
      },
      {
        $match: query
      }
    ]

    // Add search filter
    if (search) {
      pipeline.push({
        $match: {
          $or: [
            { 'company.name': { $regex: search, $options: 'i' } },
            { description: { $regex: search, $options: 'i' } }
          ]
        }
      })
    }

    // Add status filter
    if (status && status !== 'all') {
      pipeline.push({
        $match: { status }
      })
    }

    // Add sorting
    pipeline.push({ $sort: { refundedAt: -1 } })

    // Add pagination
    pipeline.push(
      { $skip: (page - 1) * limit },
      { $limit: limit }
    )

    // Transform to refund format
    pipeline.push({
      $project: {
        _id: 1,
        payment: {
          _id: '$_id',
          amount: '$amount',
          currency: '$currency',
          description: '$description',
          company: '$company'
        },
        amount: '$refundAmount',
        currency: '$currency',
        reason: '$refundReason',
        status: {
          $cond: {
            if: { $eq: ['$status', 'refunded'] },
            then: 'succeeded',
            else: 'succeeded'
          }
        },
        stripeRefundId: '$stripeRefundId',
        processedAt: '$refundedAt',
        createdBy: {
          _id: '$createdBy',
          name: 'Admin User',
          email: '<EMAIL>'
        },
        createdAt: '$refundedAt',
        updatedAt: '$updatedAt'
      }
    })

    const refunds = await Payment.aggregate(pipeline)

    // Get total count for pagination
    const countPipeline = [...pipeline.slice(0, -3)] // Remove sort, skip, limit, and project
    const totalResult = await Payment.aggregate([
      ...countPipeline,
      { $count: 'total' }
    ])
    const total = totalResult[0]?.total || 0

    return NextResponse.json({
      success: true,
      data: {
        refunds,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('Error fetching refunds:', error)
    return NextResponse.json(
      { error: 'Failed to fetch refunds' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    // Verify admin authentication
    const authResult = await verifyToken(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { paymentId, amount, reason, description } = body

    // Validate required fields
    if (!paymentId || !reason) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Find the payment
    const payment = await Payment.findById(paymentId)
    if (!payment) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { status: 404 }
      )
    }

    // Check if payment can be refunded
    if (payment.status !== 'succeeded') {
      return NextResponse.json(
        { error: 'Payment cannot be refunded' },
        { status: 400 }
      )
    }

    // Check if already fully refunded
    if (payment.status === 'refunded') {
      return NextResponse.json(
        { error: 'Payment is already fully refunded' },
        { status: 400 }
      )
    }

    // Calculate refund amount
    const refundAmount = amount || payment.amount
    const currentRefundAmount = payment.refundAmount || 0
    const availableAmount = payment.amount - currentRefundAmount

    if (refundAmount > availableAmount) {
      return NextResponse.json(
        { error: 'Refund amount exceeds available amount' },
        { status: 400 }
      )
    }

    try {
      // Process refund through Stripe
      const stripeRefund = await StripeService.createRefund(
        paymentId,
        refundAmount,
        reason
      )

      // Update payment record
      const newRefundAmount = currentRefundAmount + refundAmount
      const newStatus = newRefundAmount >= payment.amount ? 'refunded' : 'partially_refunded'

      await Payment.findByIdAndUpdate(paymentId, {
        status: newStatus,
        refundAmount: newRefundAmount,
        refundReason: reason,
        refundedAt: new Date(),
        stripeRefundId: stripeRefund.id,
        metadata: {
          ...payment.metadata,
          refundDescription: description,
          refundedBy: authResult.user.id
        }
      })

      // Get updated payment for response
      const updatedPayment = await Payment.findById(paymentId)
        .populate('company', 'name logo isVerified')

      return NextResponse.json({
        success: true,
        data: {
          refund: {
            _id: updatedPayment._id,
            payment: {
              _id: updatedPayment._id,
              amount: updatedPayment.amount,
              currency: updatedPayment.currency,
              description: updatedPayment.description,
              company: updatedPayment.company
            },
            amount: refundAmount,
            currency: updatedPayment.currency,
            reason,
            status: 'succeeded',
            stripeRefundId: stripeRefund.id,
            processedAt: new Date(),
            createdBy: {
              _id: authResult.user.id,
              name: authResult.user.name || 'Admin User',
              email: authResult.user.email
            },
            createdAt: new Date(),
            updatedAt: new Date()
          }
        }
      }, { status: 201 })

    } catch (stripeError) {
      console.error('Stripe refund error:', stripeError)
      
      // Update payment with failed refund status
      await Payment.findByIdAndUpdate(paymentId, {
        metadata: {
          ...payment.metadata,
          refundAttempt: {
            amount: refundAmount,
            reason,
            failedAt: new Date(),
            error: stripeError instanceof Error ? stripeError.message : 'Refund failed'
          }
        }
      })

      return NextResponse.json(
        { error: 'Failed to process refund through payment processor' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Error creating refund:', error)
    return NextResponse.json(
      { error: 'Failed to create refund' },
      { status: 500 }
    )
  }
}

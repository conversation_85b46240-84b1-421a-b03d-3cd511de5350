import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Payment } from '@/lib/models/payment.model'
import { Subscription } from '@/lib/models/subscription.model'
import { Invoice } from '@/lib/models/invoice.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    // Verify admin authentication
    const authResult = await authMiddleware(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')

    // Check if models are available
    if (!Payment || !Subscription || !Invoice) {
      console.warn('Some billing models are not available, returning empty activity')
      return NextResponse.json({
        success: true,
        data: { activity: [] }
      })
    }

    // Get recent data with error handling
    const [recentPayments, recentSubscriptions, recentInvoices] = await Promise.allSettled([
      Payment.find()
        .populate('company', 'name logo isVerified')
        .populate('subscription.plan', 'name')
        .sort({ createdAt: -1 })
        .limit(Math.floor(limit / 2))
        .lean(),

      Subscription.find()
        .populate('company', 'name logo isVerified')
        .populate('plan', 'name')
        .sort({ updatedAt: -1 })
        .limit(Math.floor(limit / 4))
        .lean(),

      Invoice.find()
        .populate('company', 'name logo isVerified')
        .populate('subscription.plan', 'name')
        .sort({ createdAt: -1 })
        .limit(Math.floor(limit / 4))
        .lean()
    ])

    // Extract successful results with fallbacks
    const payments = recentPayments.status === 'fulfilled' ? recentPayments.value : []
    const subscriptions = recentSubscriptions.status === 'fulfilled' ? recentSubscriptions.value : []
    const invoices = recentInvoices.status === 'fulfilled' ? recentInvoices.value : []

    // Format activity items
    const activity = []

    // Add payment activities
    payments.forEach((payment: any) => {
      let description = ''
      switch (payment.status) {
        case 'succeeded':
          description = `Payment received from ${payment.company.name}`
          break
        case 'failed':
          description = `Payment failed for ${payment.company.name}`
          break
        case 'refunded':
          description = `Payment refunded to ${payment.company.name}`
          break
        default:
          description = `Payment ${payment.status} for ${payment.company.name}`
      }

      activity.push({
        _id: payment._id,
        type: payment.status === 'refunded' ? 'refund' : 'payment',
        description,
        amount: payment.amount,
        currency: payment.currency,
        company: payment.company,
        status: payment.status,
        createdAt: payment.createdAt
      })
    })

    // Add subscription activities
    subscriptions.forEach((subscription: any) => {
      let description = ''
      switch (subscription.status) {
        case 'active':
          description = `${subscription.company.name} activated ${subscription.plan.name} subscription`
          break
        case 'canceled':
          description = `${subscription.company.name} canceled ${subscription.plan.name} subscription`
          break
        case 'trialing':
          description = `${subscription.company.name} started trial for ${subscription.plan.name}`
          break
        default:
          description = `${subscription.company.name} subscription ${subscription.status}`
      }

      activity.push({
        _id: subscription._id,
        type: 'subscription',
        description,
        amount: subscription.pricePerPeriod,
        currency: subscription.currency,
        company: subscription.company,
        status: subscription.status,
        createdAt: subscription.updatedAt
      })
    })

    // Add invoice activities
    invoices.forEach((invoice: any) => {
      let description = ''
      switch (invoice.status) {
        case 'sent':
          description = `Invoice #${invoice.invoiceNumber} sent to ${invoice.company.name}`
          break
        case 'paid':
          description = `Invoice #${invoice.invoiceNumber} paid by ${invoice.company.name}`
          break
        case 'overdue':
          description = `Invoice #${invoice.invoiceNumber} overdue for ${invoice.company.name}`
          break
        default:
          description = `Invoice #${invoice.invoiceNumber} ${invoice.status}`
      }

      activity.push({
        _id: invoice._id,
        type: 'invoice',
        description,
        amount: invoice.totalAmount,
        currency: invoice.currency,
        company: invoice.company,
        status: invoice.status,
        createdAt: invoice.createdAt
      })
    })

    // Sort all activities by date and limit
    const sortedActivity = activity
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, limit)

    return NextResponse.json({
      success: true,
      data: { activity: sortedActivity }
    })

  } catch (error) {
    console.error('Error fetching billing activity:', error)
    return NextResponse.json(
      { error: 'Failed to fetch billing activity' },
      { status: 500 }
    )
  }
}

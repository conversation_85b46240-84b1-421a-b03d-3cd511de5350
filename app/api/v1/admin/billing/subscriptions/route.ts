import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Subscription } from '@/lib/models/subscription.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    // Verify admin authentication
    const authResult = await authMiddleware(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')
    const status = searchParams.get('status')
    const plan = searchParams.get('plan')

    // Build query
    const query: any = {}
    
    if (status && status !== 'all') {
      query.status = status
    }

    if (plan && plan !== 'all') {
      query.plan = plan
    }

    // Build aggregation pipeline
    const pipeline: any[] = [
      {
        $lookup: {
          from: 'companies',
          localField: 'company',
          foreignField: '_id',
          as: 'company'
        }
      },
      {
        $lookup: {
          from: 'billingplans',
          localField: 'plan',
          foreignField: '_id',
          as: 'plan'
        }
      },
      {
        $unwind: '$company'
      },
      {
        $unwind: '$plan'
      }
    ]

    // Add search filter
    if (search) {
      pipeline.push({
        $match: {
          $or: [
            { 'company.name': { $regex: search, $options: 'i' } },
            { 'plan.name': { $regex: search, $options: 'i' } }
          ]
        }
      })
    }

    // Add status and plan filters
    if (Object.keys(query).length > 0) {
      pipeline.push({ $match: query })
    }

    // Add sorting
    pipeline.push({ $sort: { createdAt: -1 } })

    // Add pagination
    pipeline.push(
      { $skip: (page - 1) * limit },
      { $limit: limit }
    )

    // Add virtual fields calculation
    pipeline.push({
      $addFields: {
        daysRemaining: {
          $max: [
            0,
            {
              $ceil: {
                $divide: [
                  { $subtract: ['$currentPeriodEnd', new Date()] },
                  1000 * 60 * 60 * 24
                ]
              }
            }
          ]
        },
        trialDaysRemaining: {
          $cond: {
            if: { $ne: ['$trialEnd', null] },
            then: {
              $max: [
                0,
                {
                  $ceil: {
                    $divide: [
                      { $subtract: ['$trialEnd', new Date()] },
                      1000 * 60 * 60 * 24
                    ]
                  }
                }
              ]
            },
            else: 0
          }
        }
      }
    })

    const subscriptions = await Subscription.aggregate(pipeline)

    // Get total count for pagination
    const countPipeline = [...pipeline.slice(0, -3)] // Remove sort, skip, limit, and addFields
    const totalResult = await Subscription.aggregate([
      ...countPipeline,
      { $count: 'total' }
    ])
    const total = totalResult[0]?.total || 0

    return NextResponse.json({
      success: true,
      data: {
        subscriptions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('Error fetching subscriptions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch subscriptions' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()

    // Verify admin authentication
    const authResult = await verifyToken(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      company,
      plan,
      billingCycle,
      pricePerPeriod,
      currency = 'USD',
      trialDays = 14
    } = body

    // Validate required fields
    if (!company || !plan || !billingCycle || !pricePerPeriod) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Check if company already has an active subscription
    const existingSubscription = await Subscription.findOne({
      company,
      status: { $in: ['active', 'trialing'] }
    })

    if (existingSubscription) {
      return NextResponse.json(
        { error: 'Company already has an active subscription' },
        { status: 400 }
      )
    }

    // Calculate period dates
    const now = new Date()
    const trialEnd = new Date(now.getTime() + (trialDays * 24 * 60 * 60 * 1000))
    const periodStart = trialDays > 0 ? trialEnd : now
    const periodEnd = new Date(periodStart)
    
    if (billingCycle === 'monthly') {
      periodEnd.setMonth(periodEnd.getMonth() + 1)
    } else {
      periodEnd.setFullYear(periodEnd.getFullYear() + 1)
    }

    // Create subscription
    const subscription = new Subscription({
      company,
      plan,
      status: trialDays > 0 ? 'trialing' : 'active',
      billingCycle,
      currentPeriodStart: periodStart,
      currentPeriodEnd: periodEnd,
      trialStart: trialDays > 0 ? now : undefined,
      trialEnd: trialDays > 0 ? trialEnd : undefined,
      pricePerPeriod,
      currency,
      usage: {
        jobPostings: 0,
        featuredJobs: 0,
        teamMembers: 1,
        apiCalls: 0
      }
    })

    await subscription.save()

    // Populate the subscription for response
    await subscription.populate(['company', 'plan'])

    return NextResponse.json({
      success: true,
      data: { subscription }
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating subscription:', error)
    return NextResponse.json(
      { error: 'Failed to create subscription' },
      { status: 500 }
    )
  }
}

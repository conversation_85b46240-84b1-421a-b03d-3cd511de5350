import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Subscription } from '@/lib/models/subscription.model'
import { Payment } from '@/lib/models/payment.model'
import { Invoice } from '@/lib/models/invoice.model'
import { BillingPlan } from '@/lib/models/billing-plan.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    // Verify admin authentication
    const authResult = await authMiddleware(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    // Check if models are available
    if (!Subscription || !Payment || !Invoice || !BillingPlan) {
      console.warn('Some billing models are not available, returning default stats')
      return NextResponse.json({
        success: true,
        data: {
          stats: {
            revenue: { total: 0, thisMonth: 0, lastMonth: 0, growth: 0 },
            subscriptions: { total: 0, active: 0, trialing: 0, canceled: 0 },
            payments: { total: 0, succeeded: 0, failed: 0, pending: 0 },
            invoices: { total: 0, paid: 0, overdue: 0, draft: 0 },
            mrr: 0,
            arr: 0,
            churnRate: 0,
            averageRevenuePerUser: 0
          }
        }
      })
    }

    // Get date ranges for calculations
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0)

    // Revenue calculations with fallbacks
    const [
      totalRevenue,
      thisMonthRevenue,
      lastMonthRevenue,
      totalSubscriptions,
      activeSubscriptions,
      trialingSubscriptions,
      canceledSubscriptions,
      totalPayments,
      succeededPayments,
      failedPayments,
      pendingPayments,
      totalInvoices,
      paidInvoices,
      overdueInvoices,
      draftInvoices
    ] = await Promise.allSettled([
      // Revenue calculations
      Payment.aggregate([
        { $match: { status: 'succeeded' } },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]),
      Payment.aggregate([
        { 
          $match: { 
            status: 'succeeded',
            paidAt: { $gte: startOfMonth }
          }
        },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]),
      Payment.aggregate([
        { 
          $match: { 
            status: 'succeeded',
            paidAt: { $gte: startOfLastMonth, $lte: endOfLastMonth }
          }
        },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]),

      // Subscription counts
      Subscription.countDocuments(),
      Subscription.countDocuments({ status: 'active' }),
      Subscription.countDocuments({ status: 'trialing' }),
      Subscription.countDocuments({ status: 'canceled' }),

      // Payment counts
      Payment.countDocuments(),
      Payment.countDocuments({ status: 'succeeded' }),
      Payment.countDocuments({ status: 'failed' }),
      Payment.countDocuments({ status: 'pending' }),

      // Invoice counts
      Invoice.countDocuments(),
      Invoice.countDocuments({ status: 'paid' }),
      Invoice.countDocuments({ status: 'overdue' }),
      Invoice.countDocuments({ status: 'draft' })
    ])

    // Calculate metrics with fallbacks
    const totalRevenueAmount = (totalRevenue.status === 'fulfilled' ? totalRevenue.value[0]?.total : 0) || 0
    const thisMonthRevenueAmount = (thisMonthRevenue.status === 'fulfilled' ? thisMonthRevenue.value[0]?.total : 0) || 0
    const lastMonthRevenueAmount = (lastMonthRevenue.status === 'fulfilled' ? lastMonthRevenue.value[0]?.total : 0) || 0

    // Get counts with fallbacks
    const totalSubscriptionsCount = (totalSubscriptions.status === 'fulfilled' ? totalSubscriptions.value : 0) || 0
    const activeSubscriptionsCount = (activeSubscriptions.status === 'fulfilled' ? activeSubscriptions.value : 0) || 0
    const trialingSubscriptionsCount = (trialingSubscriptions.status === 'fulfilled' ? trialingSubscriptions.value : 0) || 0
    const canceledSubscriptionsCount = (canceledSubscriptions.status === 'fulfilled' ? canceledSubscriptions.value : 0) || 0
    const totalPaymentsCount = (totalPayments.status === 'fulfilled' ? totalPayments.value : 0) || 0
    const succeededPaymentsCount = (succeededPayments.status === 'fulfilled' ? succeededPayments.value : 0) || 0
    const failedPaymentsCount = (failedPayments.status === 'fulfilled' ? failedPayments.value : 0) || 0
    const pendingPaymentsCount = (pendingPayments.status === 'fulfilled' ? pendingPayments.value : 0) || 0
    const totalInvoicesCount = (totalInvoices.status === 'fulfilled' ? totalInvoices.value : 0) || 0
    const paidInvoicesCount = (paidInvoices.status === 'fulfilled' ? paidInvoices.value : 0) || 0
    const overdueInvoicesCount = (overdueInvoices.status === 'fulfilled' ? overdueInvoices.value : 0) || 0
    const draftInvoicesCount = (draftInvoices.status === 'fulfilled' ? draftInvoices.value : 0) || 0

    const revenueGrowth = lastMonthRevenueAmount > 0 
      ? ((thisMonthRevenueAmount - lastMonthRevenueAmount) / lastMonthRevenueAmount) * 100
      : 0

    // Calculate MRR (Monthly Recurring Revenue) with error handling
    let mrr = 0
    let arr = 0
    try {
      const activeSubs = await Subscription.find({ status: 'active' }).populate('plan')
      mrr = activeSubs.reduce((sum, sub: any) => {
        const monthlyAmount = sub.billingCycle === 'yearly'
          ? sub.pricePerPeriod / 12
          : sub.pricePerPeriod
        return sum + monthlyAmount
      }, 0)
      arr = mrr * 12
    } catch (error) {
      console.warn('Error calculating MRR:', error)
    }

    // Calculate churn rate (simplified)
    const churnRate = totalSubscriptionsCount > 0
      ? (canceledSubscriptionsCount / totalSubscriptionsCount) * 100
      : 0

    // Calculate ARPU (Average Revenue Per User)
    const averageRevenuePerUser = activeSubscriptionsCount > 0
      ? totalRevenueAmount / activeSubscriptionsCount
      : 0

    const stats = {
      revenue: {
        total: totalRevenueAmount,
        thisMonth: thisMonthRevenueAmount,
        lastMonth: lastMonthRevenueAmount,
        growth: revenueGrowth
      },
      subscriptions: {
        total: totalSubscriptionsCount,
        active: activeSubscriptionsCount,
        trialing: trialingSubscriptionsCount,
        canceled: canceledSubscriptionsCount
      },
      payments: {
        total: totalPaymentsCount,
        succeeded: succeededPaymentsCount,
        failed: failedPaymentsCount,
        pending: pendingPaymentsCount
      },
      invoices: {
        total: totalInvoicesCount,
        paid: paidInvoicesCount,
        overdue: overdueInvoicesCount,
        draft: draftInvoicesCount
      },
      mrr,
      arr,
      churnRate,
      averageRevenuePerUser
    }

    return NextResponse.json({
      success: true,
      data: { stats }
    })

  } catch (error) {
    console.error('Error fetching billing stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch billing statistics' },
      { status: 500 }
    )
  }
}

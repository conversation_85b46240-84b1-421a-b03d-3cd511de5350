import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { connectDB } from '@/lib/database/connection'
import { User } from '@/lib/models/user.model'
import { Company } from '@/lib/models/company.model'
import { Job } from '@/lib/models/job.model'
import { Application } from '@/lib/models/application.model'
import { ApiResponse } from '@/lib/types/api.types'

interface DashboardStats {
  totalUsers: number
  totalCompanies: number
  totalJobs: number
  totalApplications: number
  activeUsers: number
  newUsersToday: number
  newCompaniesToday: number
  newJobsToday: number
  pendingReports: number
  pendingVerifications: number
}

interface DashboardData {
  stats: DashboardStats
  charts: any[]
  recentActivity: any[]
  alerts: any[]
}

export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<DashboardData>>> {
  try {
    await connectDB()

    // Authenticate user and check admin role
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'AUTHENTICATION_FAILED',
            message: authResult.error || 'Authentication required',
            statusCode: authResult.status || 401
          },
          timestamp: new Date().toISOString()
        },
        { status: authResult.status || 401 }
      )
    }

    // Check if user is admin
    if (authResult.user.role !== 'admin') {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'FORBIDDEN',
            message: 'Admin access required',
            statusCode: 403
          },
          timestamp: new Date().toISOString()
        },
        { status: 403 }
      )
    }

    console.log('🔍 Admin Dashboard API: Fetching stats for admin:', authResult.user.email)

    // Calculate date ranges
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    // Fetch statistics
    const [
      totalUsers,
      totalCompanies,
      totalJobs,
      totalApplications,
      activeUsers,
      newUsersToday,
      newCompaniesToday,
      newJobsToday
    ] = await Promise.all([
      User.countDocuments(),
      Company.countDocuments(),
      Job.countDocuments(),
      Application.countDocuments(),
      User.countDocuments({ isActive: true }),
      User.countDocuments({ createdAt: { $gte: today, $lt: tomorrow } }),
      Company.countDocuments({ createdAt: { $gte: today, $lt: tomorrow } }),
      Job.countDocuments({ createdAt: { $gte: today, $lt: tomorrow } })
    ])

    // Mock data for features not yet implemented
    const pendingReports = 0
    const pendingVerifications = await Company.countDocuments({ isVerified: false })

    const stats: DashboardStats = {
      totalUsers,
      totalCompanies,
      totalJobs,
      totalApplications,
      activeUsers,
      newUsersToday,
      newCompaniesToday,
      newJobsToday,
      pendingReports,
      pendingVerifications
    }

    // Mock chart data (can be replaced with real data later)
    const charts = [
      {
        id: 'user-growth',
        title: 'User Growth',
        data: [
          { date: '2024-01-01', users: 100 },
          { date: '2024-01-02', users: 120 },
          { date: '2024-01-03', users: 150 },
          { date: '2024-01-04', users: 180 },
          { date: '2024-01-05', users: 200 }
        ]
      }
    ]

    // Mock recent activity (can be replaced with real data later)
    const recentActivity = [
      {
        id: 1,
        type: 'user_registration',
        message: 'New user registered',
        timestamp: new Date(),
        user: '<EMAIL>'
      },
      {
        id: 2,
        type: 'company_verification',
        message: 'Company verification pending',
        timestamp: new Date(),
        company: 'Tech Corp'
      }
    ]

    // Mock alerts (can be replaced with real data later)
    const alerts = [
      {
        id: 1,
        type: 'info',
        message: `${pendingVerifications} companies pending verification`,
        priority: 'medium'
      }
    ]

    const dashboardData: DashboardData = {
      stats,
      charts,
      recentActivity,
      alerts
    }

    console.log('🔍 Admin Dashboard API: Stats calculated:', stats)

    return NextResponse.json(
      {
        success: true,
        data: dashboardData,
        timestamp: new Date().toISOString()
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('Admin dashboard error:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch dashboard data',
          statusCode: 500,
          details: process.env.NODE_ENV === 'development' ? { originalError: (error as Error).message } : undefined
        },
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// Method not allowed for other HTTP methods
export async function POST(): Promise<NextResponse<ApiResponse>> {
  return NextResponse.json(
    {
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'POST method not allowed',
        statusCode: 405
      },
      timestamp: new Date().toISOString()
    },
    { status: 405 }
  )
}

export async function PUT(): Promise<NextResponse<ApiResponse>> {
  return NextResponse.json(
    {
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'PUT method not allowed',
        statusCode: 405
      },
      timestamp: new Date().toISOString()
    },
    { status: 405 }
  )
}

export async function DELETE(): Promise<NextResponse<ApiResponse>> {
  return NextResponse.json(
    {
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'DELETE method not allowed',
        statusCode: 405
      },
      timestamp: new Date().toISOString()
    },
    { status: 405 }
  )
}

import { NextRequest } from 'next/server'
import { User } from '@/lib/models/user.model'
import { Company } from '@/lib/models/company.model'
import { Client } from '@/lib/models/client.model'
import { Job } from '@/lib/models/job.model'
import { Application } from '@/lib/models/application.model'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import { withErrorHandler, createSuccessResponse, validateMethod } from '@/lib/api/route-handler'
import { connectToDatabase } from '@/lib/database/connection'

interface DatabaseStats {
  collections: {
    users: {
      total: number
      byRole: Record<string, number>
      active: number
      verified: number
      recent: number
    }
    companies: {
      total: number
      bySize: Record<string, number>
      active: number
      verified: number
      recent: number
    }
    clients: {
      total: number
      active: number
      public: number
      recent: number
    }
    jobs: {
      total: number
      active: number
      byType: Record<string, number>
      byLevel: Record<string, number>
      recent: number
    }
    applications: {
      total: number
      byStatus: Record<string, number>
      recent: number
    }
  }
  relationships: {
    usersWithCompanies: number
    usersWithClients: number
    companiesWithJobs: number
    jobsWithApplications: number
  }
  systemHealth: {
    totalDocuments: number
    averageResponseTime: number
    lastUpdated: Date
  }
}

/**
 * GET /api/v1/admin/database - Get database statistics and overview
 */
export const GET = withErrorHandler<DatabaseStats>(async (request: NextRequest) => {
  validateMethod(request, ['GET'])
  
  await connectToDatabase()
  
  console.log('🔍 Fetching database statistics...')
  
  try {
    const startTime = Date.now()
    
    // Get collection counts and statistics
    const [
      userStats,
      companyStats,
      clientStats,
      jobStats,
      applicationStats
    ] = await Promise.all([
      getUserStats(),
      getCompanyStats(),
      getClientStats(),
      getJobStats(),
      getApplicationStats()
    ])
    
    // Get relationship statistics
    const relationships = await getRelationshipStats()
    
    const endTime = Date.now()
    const responseTime = endTime - startTime
    
    const totalDocuments = 
      userStats.total + 
      companyStats.total + 
      clientStats.total + 
      jobStats.total + 
      applicationStats.total
    
    const stats: DatabaseStats = {
      collections: {
        users: userStats,
        companies: companyStats,
        clients: clientStats,
        jobs: jobStats,
        applications: applicationStats
      },
      relationships,
      systemHealth: {
        totalDocuments,
        averageResponseTime: responseTime,
        lastUpdated: new Date()
      }
    }
    
    console.log('✅ Database statistics fetched successfully:', {
      totalDocuments,
      responseTime: `${responseTime}ms`,
      collections: Object.keys(stats.collections).length
    })
    
    return createSuccessResponse(stats, { message: 'Database statistics retrieved successfully' })
    
  } catch (error: unknown) {
    console.error('❌ Failed to fetch database statistics:', error)
    throw error
  }
}, {
  requireDatabase: true,
  rateLimit: {
    requests: 30,
    windowMs: 5 * 60 * 1000 // 5 minutes
  }
})

// Helper functions for statistics
async function getUserStats() {
  const [total, byRole, active, verified, recent] = await Promise.all([
    User.countDocuments(),
    User.aggregate([
      { $group: { _id: '$role', count: { $sum: 1 } } }
    ]),
    User.countDocuments({ isActive: true }),
    User.countDocuments({ isEmailVerified: true }),
    User.countDocuments({ 
      createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } 
    })
  ])
  
  const roleStats: Record<string, number> = {}
  byRole.forEach((item: any) => {
    roleStats[item._id] = item.count
  })
  
  return {
    total,
    byRole: roleStats,
    active,
    verified,
    recent
  }
}

async function getCompanyStats() {
  const [total, bySize, active, verified, recent] = await Promise.all([
    Company.countDocuments(),
    Company.aggregate([
      { $group: { _id: '$size', count: { $sum: 1 } } }
    ]),
    Company.countDocuments({ isActive: true }),
    Company.countDocuments({ 'verification.isVerified': true }),
    Company.countDocuments({ 
      createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } 
    })
  ])
  
  const sizeStats: Record<string, number> = {}
  bySize.forEach((item: any) => {
    sizeStats[item._id] = item.count
  })
  
  return {
    total,
    bySize: sizeStats,
    active,
    verified,
    recent
  }
}

async function getClientStats() {
  const [total, active, publicClients, recent] = await Promise.all([
    Client.countDocuments(),
    Client.countDocuments({ isActive: true }),
    Client.countDocuments({ isPublic: true }),
    Client.countDocuments({ 
      createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } 
    })
  ])
  
  return {
    total,
    active,
    public: publicClients,
    recent
  }
}

async function getJobStats() {
  const [total, active, byType, byLevel, recent] = await Promise.all([
    Job.countDocuments(),
    Job.countDocuments({ isActive: true }),
    Job.aggregate([
      { $group: { _id: '$type', count: { $sum: 1 } } }
    ]),
    Job.aggregate([
      { $group: { _id: '$level', count: { $sum: 1 } } }
    ]),
    Job.countDocuments({ 
      createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } 
    })
  ])
  
  const typeStats: Record<string, number> = {}
  byType.forEach((item: any) => {
    typeStats[item._id] = item.count
  })
  
  const levelStats: Record<string, number> = {}
  byLevel.forEach((item: any) => {
    levelStats[item._id] = item.count
  })
  
  return {
    total,
    active,
    byType: typeStats,
    byLevel: levelStats,
    recent
  }
}

async function getApplicationStats() {
  const [total, byStatus, recent] = await Promise.all([
    Application.countDocuments(),
    Application.aggregate([
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]),
    Application.countDocuments({ 
      createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } 
    })
  ])
  
  const statusStats: Record<string, number> = {}
  byStatus.forEach((item: any) => {
    statusStats[item._id] = item.count
  })
  
  return {
    total,
    byStatus: statusStats,
    recent
  }
}

async function getRelationshipStats() {
  const [usersWithCompanies, usersWithClients, companiesWithJobs, jobsWithApplications] = await Promise.all([
    User.countDocuments({ companyId: { $exists: true, $ne: null } }),
    User.aggregate([
      {
        $lookup: {
          from: 'clients',
          localField: '_id',
          foreignField: 'user',
          as: 'client'
        }
      },
      { $match: { client: { $ne: [] } } },
      { $count: 'count' }
    ]).then(result => result[0]?.count || 0),
    Company.aggregate([
      {
        $lookup: {
          from: 'jobs',
          localField: '_id',
          foreignField: 'companyId',
          as: 'jobs'
        }
      },
      { $match: { jobs: { $ne: [] } } },
      { $count: 'count' }
    ]).then(result => result[0]?.count || 0),
    Job.aggregate([
      {
        $lookup: {
          from: 'applications',
          localField: '_id',
          foreignField: 'job',
          as: 'applications'
        }
      },
      { $match: { applications: { $ne: [] } } },
      { $count: 'count' }
    ]).then(result => result[0]?.count || 0)
  ])
  
  return {
    usersWithCompanies,
    usersWithClients,
    companiesWithJobs,
    jobsWithApplications
  }
}

// Method not allowed for other HTTP methods
/**
 * POST /api/v1/admin/database - Query specific collection data
 */
export const POST = withErrorHandler<any>(async (request: NextRequest) => {
  validateMethod(request, ['POST'])

  await connectToDatabase()

  const body = await request.json()
  const { collection, page = 1, limit = 20, search, filters, sort } = body

  if (!collection) {
    throw errorService.createError(ErrorCode.VALIDATION_ERROR, 'Collection name is required')
  }

  const validCollections = ['users', 'companies', 'clients', 'jobs', 'applications']
  if (!validCollections.includes(collection)) {
    throw errorService.createError(ErrorCode.VALIDATION_ERROR, `Invalid collection. Must be one of: ${validCollections.join(', ')}`)
  }

  console.log(`🔍 Fetching ${collection} data:`, { page, limit, search, filters })

  try {
    const result = await getCollectionData(collection, { page, limit, search, filters, sort })

    console.log(`✅ ${collection} data fetched:`, {
      total: result.total,
      returned: result.data.length,
      page,
      totalPages: result.totalPages
    })

    return createSuccessResponse(result, { message: `${collection} data retrieved successfully` })

  } catch (error: unknown) {
    console.error(`❌ Failed to fetch ${collection} data:`, error)
    throw error
  }
}, {
  requireDatabase: true,
  rateLimit: {
    requests: 60,
    windowMs: 5 * 60 * 1000 // 5 minutes
  }
})

async function getCollectionData(collection: string, options: any) {
  const { page, limit, search, filters, sort } = options
  const skip = (page - 1) * limit

  let Model: any
  let searchFields: string[] = []
  let populateFields: string[] = []

  // Configure model and search fields based on collection
  switch (collection) {
    case 'users':
      Model = User
      searchFields = ['email', 'profile.firstName', 'profile.lastName']
      break
    case 'companies':
      Model = Company
      searchFields = ['name', 'description', 'industry']
      populateFields = ['admins', 'recruiters']
      break
    case 'clients':
      Model = Client
      searchFields = ['headline', 'summary']
      populateFields = ['user']
      break
    case 'jobs':
      Model = Job
      searchFields = ['title', 'description', 'category']
      populateFields = ['companyId', 'createdBy']
      break
    case 'applications':
      Model = Application
      populateFields = ['client', 'job']
      break
    default:
      throw new Error('Invalid collection')
  }

  // Build query
  let query: any = {}

  // Add search conditions
  if (search && searchFields.length > 0) {
    query.$or = searchFields.map(field => ({
      [field]: { $regex: search, $options: 'i' }
    }))
  }

  // Add filters
  if (filters) {
    Object.assign(query, filters)
  }

  // Build sort
  let sortOptions: any = { createdAt: -1 } // Default sort
  if (sort) {
    sortOptions = sort
  }

  // Execute query
  const [data, total] = await Promise.all([
    Model.find(query)
      .populate(populateFields.join(' '))
      .sort(sortOptions)
      .skip(skip)
      .limit(limit)
      .lean(),
    Model.countDocuments(query)
  ])

  return {
    data,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
    hasNext: page * limit < total,
    hasPrev: page > 1
  }
}

export async function PUT() {
  throw errorService.createError(ErrorCode.METHOD_NOT_ALLOWED, 'PUT method not allowed')
}

export async function DELETE() {
  throw errorService.createError(ErrorCode.METHOD_NOT_ALLOWED, 'DELETE method not allowed')
}

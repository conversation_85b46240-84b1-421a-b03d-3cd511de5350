import { NextRequest } from 'next/server'
import { User } from '@/lib/models/user.model'
import { Company } from '@/lib/models/company.model'
import { Client } from '@/lib/models/client.model'
import { Job } from '@/lib/models/job.model'
import { Application } from '@/lib/models/application.model'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import { withErrorHandler, createSuccessResponse, validateMethod } from '@/lib/api/route-handler'
import { connectToDatabase } from '@/lib/database/connection'

interface ManagementRequest {
  action: 'export' | 'cleanup' | 'delete' | 'backup'
  collection?: string
  filters?: Record<string, any>
  options?: Record<string, any>
}

/**
 * POST /api/v1/admin/database/manage - Database management operations
 */
export const POST = withErrorHandler<any>(async (request: NextRequest) => {
  validateMethod(request, ['POST'])
  
  await connectToDatabase()
  
  const body = await request.json() as ManagementRequest
  const { action, collection, filters = {}, options = {} } = body
  
  if (!action) {
    throw errorService.createError(ErrorCode.VALIDATION_ERROR, 'Action is required')
  }
  
  const validActions = ['export', 'cleanup', 'delete', 'backup']
  if (!validActions.includes(action)) {
    throw errorService.createError(ErrorCode.VALIDATION_ERROR, `Invalid action. Must be one of: ${validActions.join(', ')}`)
  }
  
  console.log(`🔧 Database management action: ${action}`, { collection, filters, options })
  
  try {
    let result
    
    switch (action) {
      case 'export':
        result = await exportData(collection, filters, options)
        break
      case 'cleanup':
        result = await cleanupData(collection, filters, options)
        break
      case 'delete':
        result = await deleteData(collection, filters, options)
        break
      case 'backup':
        result = await backupData(collection, options)
        break
      default:
        throw errorService.createError(ErrorCode.VALIDATION_ERROR, 'Invalid action')
    }
    
    console.log(`✅ Database management completed: ${action}`, result)
    
    return createSuccessResponse(result, { message: `${action} operation completed successfully` })
    
  } catch (error: unknown) {
    console.error(`❌ Database management failed: ${action}`, error)
    throw error
  }
}, {
  requireDatabase: true,
  rateLimit: {
    requests: 10,
    windowMs: 5 * 60 * 1000 // 5 minutes
  }
})

// Export data from collections
async function exportData(collection?: string, filters: Record<string, any> = {}, options: Record<string, any> = {}) {
  const { format = 'json', limit = 1000 } = options
  
  if (!collection) {
    // Export all collections summary
    const [users, companies, clients, jobs, applications] = await Promise.all([
      User.find(filters).limit(limit).lean(),
      Company.find(filters).limit(limit).lean(),
      Client.find(filters).limit(limit).lean(),
      Job.find(filters).limit(limit).lean(),
      Application.find(filters).limit(limit).lean()
    ])
    
    return {
      exportType: 'all_collections',
      timestamp: new Date().toISOString(),
      data: {
        users: users.length,
        companies: companies.length,
        clients: clients.length,
        jobs: jobs.length,
        applications: applications.length
      },
      format,
      totalRecords: users.length + companies.length + clients.length + jobs.length + applications.length
    }
  }
  
  const Model = getModel(collection)
  const data = await Model.find(filters).limit(limit).lean()
  
  return {
    exportType: collection,
    timestamp: new Date().toISOString(),
    data,
    format,
    totalRecords: data.length,
    filters
  }
}

// Cleanup test data and old records
async function cleanupData(collection?: string, filters: Record<string, any> = {}, options: Record<string, any> = {}) {
  const { dryRun = true, olderThanDays = 30 } = options
  
  // Default cleanup filters for test data
  const testDataFilters = {
    $or: [
      { email: { $regex: /test/i } },
      { name: { $regex: /test/i } },
      { 'profile.firstName': { $regex: /test/i } },
      { 'profile.lastName': { $regex: /test/i } }
    ]
  }
  
  // Add age filter if specified
  if (olderThanDays > 0) {
    const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000)
    testDataFilters.createdAt = { $lt: cutoffDate }
  }
  
  const finalFilters = { ...testDataFilters, ...filters }
  
  if (!collection) {
    // Cleanup all collections
    const results = await Promise.all([
      cleanupCollection('users', finalFilters, dryRun),
      cleanupCollection('companies', finalFilters, dryRun),
      cleanupCollection('clients', finalFilters, dryRun),
      cleanupCollection('jobs', finalFilters, dryRun),
      cleanupCollection('applications', finalFilters, dryRun)
    ])
    
    return {
      cleanupType: 'all_collections',
      dryRun,
      results: {
        users: results[0],
        companies: results[1],
        clients: results[2],
        jobs: results[3],
        applications: results[4]
      },
      totalCleaned: results.reduce((sum, result) => sum + result.deleted, 0)
    }
  }
  
  const result = await cleanupCollection(collection, finalFilters, dryRun)
  
  return {
    cleanupType: collection,
    dryRun,
    ...result
  }
}

async function cleanupCollection(collection: string, filters: Record<string, any>, dryRun: boolean) {
  const Model = getModel(collection)
  
  if (dryRun) {
    const count = await Model.countDocuments(filters)
    return { found: count, deleted: 0, dryRun: true }
  }
  
  const result = await Model.deleteMany(filters)
  return { found: result.deletedCount, deleted: result.deletedCount, dryRun: false }
}

// Delete specific data
async function deleteData(collection?: string, filters: Record<string, any> = {}, options: Record<string, any> = {}) {
  const { confirm = false } = options
  
  if (!confirm) {
    throw errorService.createError(ErrorCode.VALIDATION_ERROR, 'Delete operation requires confirmation')
  }
  
  if (!collection) {
    throw errorService.createError(ErrorCode.VALIDATION_ERROR, 'Collection is required for delete operation')
  }
  
  const Model = getModel(collection)
  const result = await Model.deleteMany(filters)
  
  return {
    collection,
    deleted: result.deletedCount,
    filters,
    timestamp: new Date().toISOString()
  }
}

// Backup data
async function backupData(collection?: string, options: Record<string, any> = {}) {
  const { includeIndexes = false } = options
  
  if (!collection) {
    // Get counts for all collections
    const [userCount, companyCount, clientCount, jobCount, applicationCount] = await Promise.all([
      User.countDocuments(),
      Company.countDocuments(),
      Client.countDocuments(),
      Job.countDocuments(),
      Application.countDocuments()
    ])
    
    return {
      backupType: 'metadata',
      timestamp: new Date().toISOString(),
      collections: {
        users: userCount,
        companies: companyCount,
        clients: clientCount,
        jobs: jobCount,
        applications: applicationCount
      },
      totalDocuments: userCount + companyCount + clientCount + jobCount + applicationCount
    }
  }
  
  const Model = getModel(collection)
  const count = await Model.countDocuments()
  
  return {
    backupType: collection,
    timestamp: new Date().toISOString(),
    documentCount: count,
    includeIndexes
  }
}

// Helper function to get model by collection name
function getModel(collection: string) {
  switch (collection) {
    case 'users':
      return User
    case 'companies':
      return Company
    case 'clients':
      return Client
    case 'jobs':
      return Job
    case 'applications':
      return Application
    default:
      throw errorService.createError(ErrorCode.VALIDATION_ERROR, `Invalid collection: ${collection}`)
  }
}

// Method not allowed for other HTTP methods
export async function GET() {
  throw errorService.createError(ErrorCode.METHOD_NOT_ALLOWED, 'GET method not allowed')
}

export async function PUT() {
  throw errorService.createError(ErrorCode.METHOD_NOT_ALLOWED, 'PUT method not allowed')
}

export async function DELETE() {
  throw errorService.createError(ErrorCode.METHOD_NOT_ALLOWED, 'DELETE method not allowed')
}

import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { Job } from '@/lib/models/job.model'
import { Company } from '@/lib/models/company.model'
import { User } from '@/lib/models/user.model'
import { authMiddleware } from '@/lib/middleware/auth.middleware'

// GET /api/v1/admin/jobs - Get all jobs with filtering
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()

    // Verify admin authentication
    const authResult = await authMiddleware(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    // Admin user is verified
    const user = await User.findById(decoded.id)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')
    const status = searchParams.get('status')
    const category = searchParams.get('category')
    const featured = searchParams.get('featured')
    const company = searchParams.get('company')

    // Build filter query
    const filter: any = {}

    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { 'company.name': { $regex: search, $options: 'i' } }
      ]
    }

    if (status) {
      filter.status = status
    }

    if (category) {
      filter.category = category
    }

    if (featured) {
      filter.isFeatured = featured === 'true'
    }

    if (company) {
      filter.companyId = company
    }

    // Calculate pagination
    const skip = (page - 1) * limit

    // Fetch jobs with population
    const jobs = await Job.find(filter)
      .populate('companyId', 'name logo verification.isVerified')
      .populate('postedBy', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean()

    // Get total count for pagination
    const total = await Job.countDocuments(filter)
    const totalPages = Math.ceil(total / limit)

    // Transform data for response
    const transformedJobs = jobs.map(job => ({
      _id: job._id,
      title: job.title,
      description: job.description,
      company: {
        _id: job.companyId._id,
        name: job.companyId.name,
        logo: job.companyId.logo,
        isVerified: job.companyId.verification?.isVerified || false
      },
      location: job.location,
      salary: job.salary,
      type: job.type,
      category: job.category,
      experienceLevel: job.experienceLevel,
      status: job.status,
      featured: job.featured,
      urgent: job.urgent,
      applicationCount: job.stats?.applicationCount || 0,
      viewCount: job.stats?.viewCount || 0,
      createdAt: job.createdAt,
      updatedAt: job.updatedAt,
      expiresAt: job.expiresAt,
      postedBy: job.postedBy
    }))

    return NextResponse.json({
      success: true,
      data: {
        jobs: transformedJobs,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    })

  } catch (error) {
    console.error('Error fetching jobs:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/v1/admin/jobs - Create new job (admin only)
export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()

    // Verify admin authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Verify admin role
    const user = await User.findById(decoded.id)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const body = await request.json()
    const {
      title,
      description,
      company,
      location,
      salary,
      type,
      category,
      experienceLevel,
      requirements,
      benefits,
      featured = false,
      urgent = false,
      expiresAt
    } = body

    // Validate required fields
    if (!title || !description || !company || !category) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Verify company exists
    const companyDoc = await Company.findById(company)
    if (!companyDoc) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Create job
    const job = new Job({
      title,
      description,
      company,
      location,
      salary,
      type,
      category,
      experienceLevel,
      requirements,
      benefits,
      status: 'active', // Admin-created jobs are automatically active
      featured,
      urgent,
      postedBy: decoded.id,
      expiresAt: expiresAt ? new Date(expiresAt) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days default
    })

    await job.save()

    // Populate for response
    await job.populate('companyId', 'name logo verification.isVerified')
    await job.populate('postedBy', 'firstName lastName email')

    return NextResponse.json({
      success: true,
      data: {
        job: {
          _id: job._id,
          title: job.title,
          description: job.description,
          company: {
            _id: job.companyId._id,
            name: job.companyId.name,
            logo: job.companyId.logo,
            isVerified: job.companyId.verification?.isVerified || false
          },
          location: job.location,
          salary: job.salary,
          type: job.type,
          category: job.category,
          experienceLevel: job.experienceLevel,
          status: job.status,
          featured: job.featured,
          urgent: job.urgent,
          createdAt: job.createdAt,
          expiresAt: job.expiresAt,
          postedBy: job.postedBy
        }
      },
      message: 'Job created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating job:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

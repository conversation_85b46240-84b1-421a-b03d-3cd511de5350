import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { Job } from '@/lib/models/job.model'
import { User } from '@/lib/models/user.model'
import { authMiddleware } from '@/lib/middleware/auth.middleware'

// PUT /api/v1/admin/jobs/[id]/[action] - Perform job actions
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; action: string } }
) {
  try {
    await connectToDatabase()

    // Verify admin authentication
    const authResult = await authMiddleware(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    // Verify admin role
    const user = await User.findById(decoded.id)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const job = await Job.findById(params.id)
    if (!job) {
      return NextResponse.json({ error: 'Job not found' }, { status: 404 })
    }

    const { action } = params
    let updateData: any = { updatedAt: new Date() }
    let message = ''

    switch (action) {
      case 'feature':
        updateData.isFeatured = true
        updateData.featuredAt = new Date()
        // Set featured expiry to 30 days from now if not specified
        if (!job.featuredUntil) {
          updateData.featuredUntil = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        }
        message = 'Job featured successfully'
        break

      case 'unfeature':
        updateData.isFeatured = false
        updateData.featuredUntil = null
        message = 'Job unfeatured successfully'
        break

      case 'activate':
        updateData.status = 'active'
        message = 'Job activated successfully'
        break

      case 'pause':
        updateData.status = 'paused'
        message = 'Job paused successfully'
        break

      case 'close':
        updateData.status = 'closed'
        message = 'Job closed successfully'
        break

      case 'expire':
        updateData.status = 'expired'
        updateData.expiresAt = new Date()
        message = 'Job expired successfully'
        break

      case 'urgent':
        updateData.isUrgent = true
        message = 'Job marked as urgent'
        break

      case 'remove-urgent':
        updateData.isUrgent = false
        message = 'Urgent status removed from job'
        break

      case 'moderate':
        const body = await request.json()
        const { moderationAction, notes } = body

        if (moderationAction === 'approve') {
          updateData.status = 'active'
          updateData.moderationStatus = 'approved'
          updateData.moderationNotes = notes
          updateData.moderatedBy = decoded.id
          updateData.moderatedAt = new Date()
          message = 'Job approved successfully'
        } else if (moderationAction === 'reject') {
          updateData.status = 'rejected'
          updateData.moderationStatus = 'rejected'
          updateData.moderationNotes = notes
          updateData.moderatedBy = decoded.id
          updateData.moderatedAt = new Date()
          message = 'Job rejected successfully'
        } else {
          return NextResponse.json(
            { error: 'Invalid moderation action' },
            { status: 400 }
          )
        }
        break

      case 'boost':
        // Boost job visibility (move to top of listings)
        updateData.boostedAt = new Date()
        updateData.boostedUntil = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
        message = 'Job boosted successfully'
        break

      case 'duplicate':
        // Create a duplicate of the job
        const originalJob = job.toObject()
        delete originalJob._id
        delete originalJob.createdAt
        delete originalJob.updatedAt
        
        originalJob.title = `${originalJob.title} (Copy)`
        originalJob.status = 'draft'
        originalJob.isFeatured = false
        originalJob.isUrgent = false
        originalJob.postedBy = decoded.id

        const duplicatedJob = new Job(originalJob)
        await duplicatedJob.save()

        return NextResponse.json({
          success: true,
          data: { job: duplicatedJob },
          message: 'Job duplicated successfully'
        })

      case 'archive':
        updateData.status = 'archived'
        updateData.archivedAt = new Date()
        message = 'Job archived successfully'
        break

      case 'restore':
        updateData.status = 'active'
        updateData.archivedAt = null
        message = 'Job restored successfully'
        break

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

    // Update the job
    const updatedJob = await Job.findByIdAndUpdate(
      params.id,
      updateData,
      { new: true, runValidators: true }
    )
      .populate('companyId', 'name logo isVerified')
      .populate('postedBy', 'firstName lastName email')

    return NextResponse.json({
      success: true,
      data: { job: updatedJob },
      message
    })

  } catch (error) {
    console.error(`Error performing job action ${params.action}:`, error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

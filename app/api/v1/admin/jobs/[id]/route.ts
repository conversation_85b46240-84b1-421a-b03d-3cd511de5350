import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { Job } from '@/lib/models/job.model'
import { User } from '@/lib/models/user.model'
import { Application } from '@/lib/models/application.model'
import { authMiddleware } from '@/lib/middleware/auth.middleware'

// GET /api/v1/admin/jobs/[id] - Get specific job details
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectToDatabase()

    // Verify admin authentication
    const authResult = await authMiddleware(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    // Verify admin role
    const user = await User.findById(decoded.id)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const job = await Job.findById(params.id)
      .populate('companyId', 'name logo isVerified description website')
      .populate('postedBy', 'firstName lastName email')
      .lean()

    if (!job) {
      return NextResponse.json({ error: 'Job not found' }, { status: 404 })
    }

    // Get application statistics
    const applicationStats = await Application.aggregate([
      { $match: { jobId: job._id } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ])

    const stats = applicationStats.reduce((acc, stat) => {
      acc[stat._id] = stat.count
      return acc
    }, {})

    return NextResponse.json({
      success: true,
      data: {
        job: {
          ...job,
          company: job.companyId,
          applicationStats: stats,
          totalApplications: Object.values(stats).reduce((sum: number, count: any) => sum + count, 0)
        }
      }
    })

  } catch (error) {
    console.error('Error fetching job:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/v1/admin/jobs/[id] - Update job
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    // Verify admin authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Verify admin role
    const user = await User.findById(decoded.id)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const body = await request.json()
    const updateData = { ...body, updatedAt: new Date() }

    const job = await Job.findByIdAndUpdate(
      params.id,
      updateData,
      { new: true, runValidators: true }
    )
      .populate('companyId', 'name logo isVerified')
      .populate('postedBy', 'firstName lastName email')

    if (!job) {
      return NextResponse.json({ error: 'Job not found' }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: { job },
      message: 'Job updated successfully'
    })

  } catch (error) {
    console.error('Error updating job:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/v1/admin/jobs/[id] - Delete job
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    // Verify admin authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Verify admin role
    const user = await User.findById(decoded.id)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const job = await Job.findById(params.id)
    if (!job) {
      return NextResponse.json({ error: 'Job not found' }, { status: 404 })
    }

    // Check if job has applications
    const applicationCount = await Application.countDocuments({ jobId: params.id })
    if (applicationCount > 0) {
      // Soft delete - mark as closed instead of deleting
      job.status = 'closed'
      job.updatedAt = new Date()
      await job.save()

      return NextResponse.json({
        success: true,
        message: 'Job marked as closed due to existing applications'
      })
    } else {
      // Hard delete if no applications
      await Job.findByIdAndDelete(params.id)

      return NextResponse.json({
        success: true,
        message: 'Job deleted successfully'
      })
    }

  } catch (error) {
    console.error('Error deleting job:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { JobCategory } from '@/lib/models/job-category.model'
import { User } from '@/lib/models/user.model'
import { authMiddleware } from '@/lib/middleware/auth.middleware'

// PUT /api/v1/admin/job-categories/[id]/toggle-status - Toggle category active status
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectToDatabase()

    // Verify admin authentication
    const authResult = await authMiddleware(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    // Verify admin role
    const user = await User.findById(decoded.id)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const body = await request.json()
    const { isActive } = body

    if (typeof isActive !== 'boolean') {
      return NextResponse.json(
        { error: 'isActive must be a boolean value' },
        { status: 400 }
      )
    }

    const category = await JobCategory.findById(params.id)
    if (!category) {
      return NextResponse.json({ error: 'Job category not found' }, { status: 404 })
    }

    // Update category status
    category.isActive = isActive
    category.updatedAt = new Date()
    await category.save()

    // If deactivating, also deactivate all subcategories
    if (!isActive) {
      await JobCategory.updateMany(
        { parentCategory: params.id },
        { 
          isActive: false,
          updatedAt: new Date()
        }
      )
    }

    return NextResponse.json({
      success: true,
      data: { category },
      message: `Job category ${isActive ? 'activated' : 'deactivated'} successfully`
    })

  } catch (error) {
    console.error('Error toggling job category status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

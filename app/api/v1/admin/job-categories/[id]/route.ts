import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { JobCategory } from '@/lib/models/job-category.model'
import { Job } from '@/lib/models/job.model'
import { User } from '@/lib/models/user.model'
import { verifyToken } from '@/lib/auth'

// GET /api/v1/admin/job-categories/[id] - Get specific job category
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectToDatabase()

    // Verify admin authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Verify admin role
    const user = await User.findById(decoded.id)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const category = await JobCategory.findById(params.id)
      .populate('parentCategory', 'name slug')
      .populate('createdBy', 'firstName lastName')
      .lean()

    if (!category) {
      return NextResponse.json({ error: 'Job category not found' }, { status: 404 })
    }

    // Get job count and subcategories
    const [jobCount, subcategories] = await Promise.all([
      Job.countDocuments({ 
        category: category.slug,
        status: { $in: ['active', 'paused'] }
      }),
      JobCategory.find({ parentCategory: category._id }).select('name slug isActive')
    ])

    return NextResponse.json({
      success: true,
      data: {
        category: {
          ...category,
          jobCount,
          subcategories
        }
      }
    })

  } catch (error) {
    console.error('Error fetching job category:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/v1/admin/job-categories/[id] - Update job category
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectToDatabase()

    // Verify admin authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Verify admin role
    const user = await User.findById(decoded.id)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const body = await request.json()
    const { name, description, icon, color, isActive, parentCategory } = body

    const category = await JobCategory.findById(params.id)
    if (!category) {
      return NextResponse.json({ error: 'Job category not found' }, { status: 404 })
    }

    // If name is being updated, regenerate slug
    let updateData: any = { ...body, updatedAt: new Date() }
    
    if (name && name !== category.name) {
      const slug = name.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()

      // Check if new slug conflicts with existing categories
      const existingCategory = await JobCategory.findOne({
        _id: { $ne: params.id },
        $or: [
          { name: { $regex: new RegExp(`^${name}$`, 'i') } },
          { slug }
        ]
      })

      if (existingCategory) {
        return NextResponse.json(
          { error: 'Category with this name already exists' },
          { status: 409 }
        )
      }

      updateData.slug = slug
    }

    // Validate parent category if provided
    if (parentCategory && parentCategory !== category.parentCategory?.toString()) {
      if (parentCategory === params.id) {
        return NextResponse.json(
          { error: 'Category cannot be its own parent' },
          { status: 400 }
        )
      }

      const parent = await JobCategory.findById(parentCategory)
      if (!parent) {
        return NextResponse.json(
          { error: 'Parent category not found' },
          { status: 404 }
        )
      }
    }

    const updatedCategory = await JobCategory.findByIdAndUpdate(
      params.id,
      updateData,
      { new: true, runValidators: true }
    )
      .populate('parentCategory', 'name slug')

    return NextResponse.json({
      success: true,
      data: { category: updatedCategory },
      message: 'Job category updated successfully'
    })

  } catch (error) {
    console.error('Error updating job category:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/v1/admin/job-categories/[id] - Delete job category
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectToDatabase()

    // Verify admin authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Verify admin role
    const user = await User.findById(decoded.id)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const category = await JobCategory.findById(params.id)
    if (!category) {
      return NextResponse.json({ error: 'Job category not found' }, { status: 404 })
    }

    // Check if category has jobs
    const jobCount = await Job.countDocuments({ category: category.slug })
    if (jobCount > 0) {
      return NextResponse.json(
        { error: `Cannot delete category with ${jobCount} associated jobs` },
        { status: 409 }
      )
    }

    // Check if category has subcategories
    const subcategoryCount = await JobCategory.countDocuments({ parentCategory: params.id })
    if (subcategoryCount > 0) {
      return NextResponse.json(
        { error: `Cannot delete category with ${subcategoryCount} subcategories` },
        { status: 409 }
      )
    }

    await JobCategory.findByIdAndDelete(params.id)

    return NextResponse.json({
      success: true,
      message: 'Job category deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting job category:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

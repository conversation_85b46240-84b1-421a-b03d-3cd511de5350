import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { JobCategory } from '@/lib/models/job-category.model'
import { Job } from '@/lib/models/job.model'
import { User } from '@/lib/models/user.model'
import { authMiddleware } from '@/lib/middleware/auth.middleware'

// GET /api/v1/admin/job-categories - Get all job categories
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()

    // Verify admin authentication
    const authResult = await authMiddleware(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')

    // Build filter query
    const filter: any = {}

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ]
    }

    // Fetch categories
    const categories = await JobCategory.find(filter)
      .populate('parentCategory', 'name')
      .sort({ name: 1 })
      .lean()

    // Get job counts for each category
    const categoriesWithCounts = await Promise.all(
      categories.map(async (category) => {
        const jobCount = await Job.countDocuments({ 
          category: category.slug,
          status: { $in: ['active', 'paused'] }
        })

        return {
          ...category,
          jobCount
        }
      })
    )

    return NextResponse.json({
      success: true,
      data: {
        categories: categoriesWithCounts
      }
    })

  } catch (error) {
    console.error('Error fetching job categories:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/v1/admin/job-categories - Create new job category
export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()

    // Verify admin authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Verify admin role
    const user = await User.findById(decoded.id)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const body = await request.json()
    const { name, description, icon, color, isActive = true, parentCategory } = body

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: 'Category name is required' },
        { status: 400 }
      )
    }

    // Generate slug from name
    const slug = name.toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()

    // Check if category with same name or slug exists
    const existingCategory = await JobCategory.findOne({
      $or: [
        { name: { $regex: new RegExp(`^${name}$`, 'i') } },
        { slug }
      ]
    })

    if (existingCategory) {
      return NextResponse.json(
        { error: 'Category with this name already exists' },
        { status: 409 }
      )
    }

    // Validate parent category if provided
    if (parentCategory) {
      const parent = await JobCategory.findById(parentCategory)
      if (!parent) {
        return NextResponse.json(
          { error: 'Parent category not found' },
          { status: 404 }
        )
      }
    }

    // Create category
    const category = new JobCategory({
      name,
      slug,
      description,
      icon,
      color: color || '#3B82F6',
      isActive,
      parentCategory: parentCategory || null,
      createdBy: decoded.id
    })

    await category.save()

    // Populate for response
    await category.populate('parentCategory', 'name')

    return NextResponse.json({
      success: true,
      data: {
        category: {
          ...category.toObject(),
          jobCount: 0
        }
      },
      message: 'Job category created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating job category:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { Application } from '@/lib/models/application.model'
import { User } from '@/lib/models/user.model'
import { verifyToken } from '@/lib/auth'

// GET /api/v1/admin/applications/success-metrics - Get success metrics
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()

    // Verify admin authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Verify admin role
    const user = await User.findById(decoded.id)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '30d'

    // Calculate date range
    const now = new Date()
    let startDate: Date

    switch (timeRange) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    }

    // Get total successful hires
    const totalHires = await Application.countDocuments({
      status: 'hired',
      updatedAt: { $gte: startDate }
    })

    // Calculate average time to hire
    const timeToHireData = await Application.aggregate([
      {
        $match: {
          status: 'hired',
          updatedAt: { $gte: startDate }
        }
      },
      {
        $addFields: {
          timeToHire: {
            $divide: [
              { $subtract: ['$updatedAt', '$appliedAt'] },
              1000 * 60 * 60 * 24 // Convert to days
            ]
          }
        }
      },
      {
        $group: {
          _id: null,
          averageTimeToHire: { $avg: '$timeToHire' }
        }
      }
    ])

    const averageTimeToHire = Math.round(timeToHireData[0]?.averageTimeToHire || 0)

    // Calculate average salary offered
    const salaryData = await Application.aggregate([
      {
        $match: {
          status: 'hired',
          updatedAt: { $gte: startDate },
          salaryOffered: { $exists: true, $ne: null }
        }
      },
      {
        $group: {
          _id: null,
          averageSalary: { $avg: '$salaryOffered' }
        }
      }
    ])

    const averageSalary = Math.round(salaryData[0]?.averageSalary || 0)

    // Get top hiring sources
    const topSources = await Application.aggregate([
      {
        $match: {
          status: 'hired',
          updatedAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: { $ifNull: ['$source', 'Direct Application'] },
          count: { $sum: 1 }
        }
      },
      {
        $addFields: {
          percentage: {
            $multiply: [
              { $divide: ['$count', totalHires] },
              100
            ]
          }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 5 },
      {
        $project: {
          source: '$_id',
          count: 1,
          percentage: { $round: ['$percentage', 1] }
        }
      }
    ])

    // Get hiring trends by month
    const hiringTrends = await Application.aggregate([
      {
        $match: {
          status: 'hired',
          updatedAt: { $gte: startDate }
        }
      },
      {
        $addFields: {
          timeToHire: {
            $divide: [
              { $subtract: ['$updatedAt', '$appliedAt'] },
              1000 * 60 * 60 * 24 // Convert to days
            ]
          }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$updatedAt' },
            month: { $month: '$updatedAt' }
          },
          hires: { $sum: 1 },
          averageTime: { $avg: '$timeToHire' }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } },
      {
        $project: {
          month: {
            $concat: [
              { $toString: '$_id.year' },
              '-',
              {
                $cond: {
                  if: { $lt: ['$_id.month', 10] },
                  then: { $concat: ['0', { $toString: '$_id.month' }] },
                  else: { $toString: '$_id.month' }
                }
              }
            ]
          },
          hires: 1,
          averageTime: { $round: ['$averageTime', 1] }
        }
      }
    ])

    return NextResponse.json({
      success: true,
      data: {
        totalHires,
        averageTimeToHire,
        averageSalary,
        topSources,
        hiringTrends
      }
    })

  } catch (error) {
    console.error('Error fetching success metrics:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

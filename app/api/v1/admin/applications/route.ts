import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { Application } from '@/lib/models/application.model'
import { Job } from '@/lib/models/job.model'
import { User } from '@/lib/models/user.model'
import { Company } from '@/lib/models/company.model'
import { verifyToken } from '@/lib/auth'

// GET /api/v1/admin/applications - Get all applications with filtering
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()

    // Verify admin authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Verify admin role
    const user = await User.findById(decoded.id)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')
    const status = searchParams.get('status')
    const job = searchParams.get('job')
    const dateRange = searchParams.get('dateRange')

    // Build filter query
    const filter: any = {}

    if (status) {
      filter.status = status
    }

    if (job) {
      filter.jobId = job
    }

    if (dateRange) {
      const now = new Date()
      let startDate: Date

      switch (dateRange) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          break
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1)
          break
        case 'quarter':
          const quarterStart = Math.floor(now.getMonth() / 3) * 3
          startDate = new Date(now.getFullYear(), quarterStart, 1)
          break
        default:
          startDate = new Date(0) // All time
      }

      filter.appliedAt = { $gte: startDate }
    }

    // Calculate pagination
    const skip = (page - 1) * limit

    // Build aggregation pipeline for search
    let pipeline: any[] = []

    if (search) {
      pipeline.push({
        $lookup: {
          from: 'users',
          localField: 'applicantId',
          foreignField: '_id',
          as: 'applicant'
        }
      })
      pipeline.push({
        $lookup: {
          from: 'jobs',
          localField: 'jobId',
          foreignField: '_id',
          as: 'job'
        }
      })
      pipeline.push({
        $lookup: {
          from: 'companies',
          localField: 'job.companyId',
          foreignField: '_id',
          as: 'company'
        }
      })
      pipeline.push({
        $match: {
          ...filter,
          $or: [
            { 'applicant.firstName': { $regex: search, $options: 'i' } },
            { 'applicant.lastName': { $regex: search, $options: 'i' } },
            { 'applicant.email': { $regex: search, $options: 'i' } },
            { 'job.title': { $regex: search, $options: 'i' } },
            { 'company.name': { $regex: search, $options: 'i' } }
          ]
        }
      })
    } else {
      pipeline.push({ $match: filter })
      pipeline.push({
        $lookup: {
          from: 'users',
          localField: 'applicantId',
          foreignField: '_id',
          as: 'applicant'
        }
      })
      pipeline.push({
        $lookup: {
          from: 'jobs',
          localField: 'jobId',
          foreignField: '_id',
          as: 'job'
        }
      })
      pipeline.push({
        $lookup: {
          from: 'companies',
          localField: 'job.companyId',
          foreignField: '_id',
          as: 'company'
        }
      })
    }

    // Add sorting and pagination
    pipeline.push({ $sort: { appliedAt: -1 } })
    pipeline.push({ $skip: skip })
    pipeline.push({ $limit: limit })

    // Execute aggregation
    const applications = await Application.aggregate(pipeline)

    // Get total count for pagination
    const totalPipeline = pipeline.slice(0, -2) // Remove skip and limit
    totalPipeline.push({ $count: 'total' })
    const totalResult = await Application.aggregate(totalPipeline)
    const total = totalResult[0]?.total || 0
    const totalPages = Math.ceil(total / limit)

    // Transform data for response
    const transformedApplications = applications.map(app => ({
      _id: app._id,
      applicant: {
        _id: app.applicant[0]?._id,
        firstName: app.applicant[0]?.firstName,
        lastName: app.applicant[0]?.lastName,
        email: app.applicant[0]?.email,
        avatar: app.applicant[0]?.avatar,
        profile: app.applicant[0]?.profile
      },
      job: {
        _id: app.job[0]?._id,
        title: app.job[0]?.title,
        company: {
          _id: app.company[0]?._id,
          name: app.company[0]?.name,
          logo: app.company[0]?.logo
        },
        location: app.job[0]?.location,
        salary: app.job[0]?.salary
      },
      status: app.status,
      appliedAt: app.appliedAt,
      updatedAt: app.updatedAt,
      coverLetter: app.coverLetter,
      resumeUrl: app.resumeUrl,
      customAnswers: app.customAnswers,
      notes: app.notes,
      rating: app.rating,
      interviewScheduled: app.interviewScheduled,
      salaryExpectation: app.salaryExpectation
    }))

    return NextResponse.json({
      success: true,
      data: {
        applications: transformedApplications,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    })

  } catch (error) {
    console.error('Error fetching applications:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

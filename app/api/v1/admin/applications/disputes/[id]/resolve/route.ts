import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { Dispute } from '@/lib/models/dispute.model'
import { User } from '@/lib/models/user.model'
import { verifyToken } from '@/lib/auth'

// PUT /api/v1/admin/applications/disputes/[id]/resolve - Resolve dispute
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectToDatabase()

    // Verify admin authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Verify admin role
    const user = await User.findById(decoded.id)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const body = await request.json()
    const { resolution, status = 'resolved' } = body

    // Validate required fields
    if (!resolution) {
      return NextResponse.json(
        { error: 'Resolution details are required' },
        { status: 400 }
      )
    }

    const dispute = await Dispute.findById(params.id)
    if (!dispute) {
      return NextResponse.json({ error: 'Dispute not found' }, { status: 404 })
    }

    // Update dispute
    dispute.status = status
    dispute.resolution = resolution
    dispute.resolutionDate = new Date()
    dispute.resolvedBy = decoded.id
    dispute.updatedAt = new Date()

    // Add resolution message to messages array
    dispute.messages.push({
      sender: {
        _id: decoded.id,
        firstName: user.firstName,
        lastName: user.lastName,
        role: 'admin'
      },
      message: `Dispute resolved: ${resolution}`,
      timestamp: new Date(),
      isInternal: false
    })

    await dispute.save()

    // Populate for response
    await dispute.populate([
      {
        path: 'applicationId',
        populate: [
          { path: 'applicant', select: 'firstName lastName email avatar' },
          {
            path: 'job',
            select: 'title',
            populate: {
              path: 'company',
              select: 'name logo'
            }
          }
        ]
      },
      { path: 'resolvedBy', select: 'firstName lastName email' }
    ])

    return NextResponse.json({
      success: true,
      data: { dispute },
      message: 'Dispute resolved successfully'
    })

  } catch (error) {
    console.error('Error resolving dispute:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { Dispute } from '@/lib/models/dispute.model'
import { User } from '@/lib/models/user.model'
import { authMiddleware } from '@/lib/middleware/auth.middleware'

// PUT /api/v1/admin/applications/disputes/[id]/status - Update dispute status
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectToDatabase()

    // Verify admin authentication
    const authResult = await authMiddleware(request)
    if (!authResult.success || authResult.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    // Verify admin role
    const user = await User.findById(decoded.id)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const body = await request.json()
    const { status } = body

    // Validate status
    const validStatuses = ['open', 'investigating', 'resolved', 'closed']
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status' },
        { status: 400 }
      )
    }

    const dispute = await Dispute.findById(params.id)
    if (!dispute) {
      return NextResponse.json({ error: 'Dispute not found' }, { status: 404 })
    }

    const oldStatus = dispute.status

    // Update dispute status
    dispute.status = status
    dispute.updatedAt = new Date()

    // Assign to admin if status is investigating
    if (status === 'investigating' && !dispute.assignedTo) {
      dispute.assignedTo = decoded.id
    }

    // Add status change message
    const statusMessages = {
      open: 'Dispute reopened',
      investigating: 'Investigation started',
      resolved: 'Dispute resolved',
      closed: 'Dispute closed'
    }

    dispute.messages.push({
      sender: {
        _id: decoded.id,
        firstName: user.firstName,
        lastName: user.lastName,
        role: 'admin'
      },
      message: `Status changed from ${oldStatus} to ${status}. ${statusMessages[status as keyof typeof statusMessages]}`,
      timestamp: new Date(),
      isInternal: true
    })

    await dispute.save()

    // Populate for response
    await dispute.populate([
      {
        path: 'applicationId',
        populate: [
          { path: 'applicant', select: 'firstName lastName email avatar' },
          {
            path: 'job',
            select: 'title',
            populate: {
              path: 'company',
              select: 'name logo'
            }
          }
        ]
      },
      { path: 'assignedTo', select: 'firstName lastName email' }
    ])

    return NextResponse.json({
      success: true,
      data: { dispute },
      message: `Dispute status updated to ${status}`
    })

  } catch (error) {
    console.error('Error updating dispute status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { Dispute } from '@/lib/models/dispute.model'
import { Application } from '@/lib/models/application.model'
import { User } from '@/lib/models/user.model'
import { verifyToken } from '@/lib/auth'

// GET /api/v1/admin/applications/disputes - Get all disputes
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()

    // Verify admin authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Verify admin role
    const user = await User.findById(decoded.id)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const status = searchParams.get('status')
    const priority = searchParams.get('priority')

    // Build filter query
    const filter: any = {}

    if (status) {
      filter.status = status
    }

    if (priority) {
      filter.priority = priority
    }

    // Build aggregation pipeline
    let pipeline: any[] = [
      { $match: filter },
      {
        $lookup: {
          from: 'applications',
          localField: 'applicationId',
          foreignField: '_id',
          as: 'application'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'application.applicant',
          foreignField: '_id',
          as: 'applicant'
        }
      },
      {
        $lookup: {
          from: 'jobs',
          localField: 'application.job',
          foreignField: '_id',
          as: 'job'
        }
      },
      {
        $lookup: {
          from: 'companies',
          localField: 'job.company',
          foreignField: '_id',
          as: 'company'
        }
      }
    ]

    // Add search filter if provided
    if (search) {
      pipeline.push({
        $match: {
          $or: [
            { description: { $regex: search, $options: 'i' } },
            { 'applicant.firstName': { $regex: search, $options: 'i' } },
            { 'applicant.lastName': { $regex: search, $options: 'i' } },
            { 'applicant.email': { $regex: search, $options: 'i' } },
            { 'job.title': { $regex: search, $options: 'i' } },
            { 'company.name': { $regex: search, $options: 'i' } }
          ]
        }
      })
    }

    // Add sorting
    pipeline.push({ $sort: { createdAt: -1 } })

    // Execute aggregation
    const disputes = await Dispute.aggregate(pipeline)

    // Transform data for response
    const transformedDisputes = disputes.map(dispute => ({
      _id: dispute._id,
      application: {
        _id: dispute.application[0]?._id,
        applicant: {
          _id: dispute.applicant[0]?._id,
          firstName: dispute.applicant[0]?.firstName,
          lastName: dispute.applicant[0]?.lastName,
          email: dispute.applicant[0]?.email,
          avatar: dispute.applicant[0]?.avatar
        },
        job: {
          _id: dispute.job[0]?._id,
          title: dispute.job[0]?.title,
          company: {
            _id: dispute.company[0]?._id,
            name: dispute.company[0]?.name,
            logo: dispute.company[0]?.logo
          }
        }
      },
      disputeType: dispute.disputeType,
      status: dispute.status,
      priority: dispute.priority,
      reportedBy: dispute.reportedBy,
      description: dispute.description,
      evidence: dispute.evidence,
      createdAt: dispute.createdAt,
      updatedAt: dispute.updatedAt,
      assignedTo: dispute.assignedTo,
      resolution: dispute.resolution,
      resolutionDate: dispute.resolutionDate,
      messages: dispute.messages || []
    }))

    return NextResponse.json({
      success: true,
      data: {
        disputes: transformedDisputes
      }
    })

  } catch (error) {
    console.error('Error fetching disputes:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/v1/admin/applications/disputes - Create new dispute
export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()

    // Verify authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    const body = await request.json()
    const {
      applicationId,
      disputeType,
      priority = 'medium',
      reportedBy,
      description,
      evidence
    } = body

    // Validate required fields
    if (!applicationId || !disputeType || !description) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Verify application exists
    const application = await Application.findById(applicationId)
    if (!application) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      )
    }

    // Create dispute
    const dispute = new Dispute({
      applicationId,
      disputeType,
      status: 'open',
      priority,
      reportedBy: reportedBy || 'applicant',
      description,
      evidence: evidence || [],
      reportedById: decoded.id,
      messages: []
    })

    await dispute.save()

    // Populate for response
    await dispute.populate([
      {
        path: 'applicationId',
        populate: [
          { path: 'applicant', select: 'firstName lastName email avatar' },
          {
            path: 'job',
            select: 'title',
            populate: {
              path: 'company',
              select: 'name logo'
            }
          }
        ]
      }
    ])

    return NextResponse.json({
      success: true,
      data: { dispute },
      message: 'Dispute created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating dispute:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

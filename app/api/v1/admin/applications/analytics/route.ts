import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { Application } from '@/lib/models/application.model'
import { Job } from '@/lib/models/job.model'
import { User } from '@/lib/models/user.model'
import { Company } from '@/lib/models/company.model'
import { verifyToken } from '@/lib/auth'

// GET /api/v1/admin/applications/analytics - Get application analytics
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()

    // Verify admin authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Verify admin role
    const user = await User.findById(decoded.id)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '30d'

    // Calculate date range
    const now = new Date()
    let startDate: Date

    switch (timeRange) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    }

    // Get total applications count
    const totalApplications = await Application.countDocuments({
      appliedAt: { $gte: startDate }
    })

    // Get applications by status
    const applicationsByStatus = await Application.aggregate([
      { $match: { appliedAt: { $gte: startDate } } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ])

    const statusCounts = applicationsByStatus.reduce((acc, item) => {
      acc[item._id] = item.count
      return acc
    }, {
      pending: 0,
      reviewing: 0,
      shortlisted: 0,
      interviewed: 0,
      offered: 0,
      hired: 0,
      rejected: 0,
      withdrawn: 0
    })

    // Get application trends (monthly data)
    const applicationTrends = await Application.aggregate([
      { $match: { appliedAt: { $gte: startDate } } },
      {
        $group: {
          _id: {
            year: { $year: '$appliedAt' },
            month: { $month: '$appliedAt' }
          },
          applications: { $sum: 1 },
          hires: {
            $sum: {
              $cond: [{ $eq: ['$status', 'hired'] }, 1, 0]
            }
          }
        }
      },
      {
        $addFields: {
          conversionRate: {
            $multiply: [
              { $divide: ['$hires', '$applications'] },
              100
            ]
          }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } }
    ])

    const trends = applicationTrends.map(trend => ({
      period: `${trend._id.year}-${String(trend._id.month).padStart(2, '0')}`,
      applications: trend.applications,
      hires: trend.hires,
      conversionRate: trend.conversionRate || 0
    }))

    // Get top performing jobs
    const topPerformingJobs = await Application.aggregate([
      { $match: { appliedAt: { $gte: startDate } } },
      {
        $group: {
          _id: '$jobId',
          applications: { $sum: 1 },
          hires: {
            $sum: {
              $cond: [{ $eq: ['$status', 'hired'] }, 1, 0]
            }
          }
        }
      },
      {
        $addFields: {
          conversionRate: {
            $multiply: [
              { $divide: ['$hires', '$applications'] },
              100
            ]
          }
        }
      },
      { $sort: { applications: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'jobs',
          localField: '_id',
          foreignField: '_id',
          as: 'job'
        }
      },
      {
        $lookup: {
          from: 'companies',
          localField: 'job.companyId',
          foreignField: '_id',
          as: 'company'
        }
      },
      {
        $project: {
          jobId: '$_id',
          jobTitle: { $arrayElemAt: ['$job.title', 0] },
          company: { $arrayElemAt: ['$company.name', 0] },
          applications: 1,
          hires: 1,
          conversionRate: 1
        }
      }
    ])

    // Get top performing companies
    const topPerformingCompanies = await Application.aggregate([
      { $match: { appliedAt: { $gte: startDate } } },
      {
        $lookup: {
          from: 'jobs',
          localField: 'jobId',
          foreignField: '_id',
          as: 'job'
        }
      },
      {
        $group: {
          _id: { $arrayElemAt: ['$job.companyId', 0] },
          applications: { $sum: 1 },
          hires: {
            $sum: {
              $cond: [{ $eq: ['$status', 'hired'] }, 1, 0]
            }
          },
          avgTimeToHire: {
            $avg: {
              $cond: [
                { $eq: ['$status', 'hired'] },
                {
                  $divide: [
                    { $subtract: ['$updatedAt', '$appliedAt'] },
                    1000 * 60 * 60 * 24 // Convert to days
                  ]
                },
                null
              ]
            }
          }
        }
      },
      {
        $addFields: {
          conversionRate: {
            $multiply: [
              { $divide: ['$hires', '$applications'] },
              100
            ]
          }
        }
      },
      { $sort: { applications: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'companies',
          localField: '_id',
          foreignField: '_id',
          as: 'company'
        }
      },
      {
        $project: {
          companyId: '$_id',
          companyName: { $arrayElemAt: ['$company.name', 0] },
          applications: 1,
          hires: 1,
          conversionRate: 1,
          averageTimeToHire: { $round: ['$avgTimeToHire', 1] }
        }
      }
    ])

    // Calculate application growth
    const lastMonthStart = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000) // 60 days ago
    const thisMonthStart = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) // 30 days ago

    const [thisMonth, lastMonth] = await Promise.all([
      Application.countDocuments({ appliedAt: { $gte: thisMonthStart } }),
      Application.countDocuments({ 
        appliedAt: { 
          $gte: lastMonthStart, 
          $lt: thisMonthStart 
        } 
      })
    ])

    const growth = lastMonth > 0 ? ((thisMonth - lastMonth) / lastMonth) * 100 : 0

    // Create conversion funnel
    const conversionFunnel = [
      { stage: 'Applied', count: statusCounts.pending + statusCounts.reviewing + statusCounts.shortlisted + statusCounts.interviewed + statusCounts.offered + statusCounts.hired + statusCounts.rejected + statusCounts.withdrawn, percentage: 100 },
      { stage: 'Under Review', count: statusCounts.reviewing + statusCounts.shortlisted + statusCounts.interviewed + statusCounts.offered + statusCounts.hired, percentage: 0 },
      { stage: 'Shortlisted', count: statusCounts.shortlisted + statusCounts.interviewed + statusCounts.offered + statusCounts.hired, percentage: 0 },
      { stage: 'Interviewed', count: statusCounts.interviewed + statusCounts.offered + statusCounts.hired, percentage: 0 },
      { stage: 'Offered', count: statusCounts.offered + statusCounts.hired, percentage: 0 },
      { stage: 'Hired', count: statusCounts.hired, percentage: 0 }
    ]

    // Calculate percentages for funnel
    const totalApps = conversionFunnel[0].count
    conversionFunnel.forEach(stage => {
      stage.percentage = totalApps > 0 ? (stage.count / totalApps) * 100 : 0
    })

    return NextResponse.json({
      success: true,
      data: {
        totalApplications,
        applicationsByStatus: statusCounts,
        applicationsByPeriod: trends,
        topPerformingJobs,
        topPerformingCompanies,
        applicationTrends: {
          thisMonth,
          lastMonth,
          growth
        },
        conversionFunnel
      }
    })

  } catch (error) {
    console.error('Error fetching application analytics:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

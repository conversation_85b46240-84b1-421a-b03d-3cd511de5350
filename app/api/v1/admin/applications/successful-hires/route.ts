import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { Application } from '@/lib/models/application.model'
import { User } from '@/lib/models/user.model'
import { verifyToken } from '@/lib/auth'

// GET /api/v1/admin/applications/successful-hires - Get successful hires data
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()

    // Verify admin authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Verify admin role
    const user = await User.findById(decoded.id)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '30d'
    const search = searchParams.get('search')

    // Calculate date range
    const now = new Date()
    let startDate: Date

    switch (timeRange) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    }

    // Build aggregation pipeline
    let pipeline: any[] = [
      {
        $match: {
          status: 'hired',
          updatedAt: { $gte: startDate }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'applicantId',
          foreignField: '_id',
          as: 'applicant'
        }
      },
      {
        $lookup: {
          from: 'jobs',
          localField: 'jobId',
          foreignField: '_id',
          as: 'job'
        }
      },
      {
        $lookup: {
          from: 'companies',
          localField: 'job.companyId',
          foreignField: '_id',
          as: 'company'
        }
      }
    ]

    // Add search filter if provided
    if (search) {
      pipeline.push({
        $match: {
          $or: [
            { 'applicant.firstName': { $regex: search, $options: 'i' } },
            { 'applicant.lastName': { $regex: search, $options: 'i' } },
            { 'applicant.email': { $regex: search, $options: 'i' } },
            { 'job.title': { $regex: search, $options: 'i' } },
            { 'company.name': { $regex: search, $options: 'i' } }
          ]
        }
      })
    }

    // Add sorting
    pipeline.push({ $sort: { updatedAt: -1 } })

    // Execute aggregation
    const hires = await Application.aggregate(pipeline)

    // Transform data for response
    const transformedHires = hires.map(hire => {
      const timeToHire = Math.ceil(
        (new Date(hire.updatedAt).getTime() - new Date(hire.appliedAt).getTime()) / 
        (1000 * 60 * 60 * 24)
      )

      return {
        _id: hire._id,
        applicant: {
          _id: hire.applicant[0]?._id,
          firstName: hire.applicant[0]?.firstName,
          lastName: hire.applicant[0]?.lastName,
          email: hire.applicant[0]?.email,
          avatar: hire.applicant[0]?.avatar,
          profile: hire.applicant[0]?.profile
        },
        job: {
          _id: hire.job[0]?._id,
          title: hire.job[0]?.title,
          category: hire.job[0]?.category,
          company: {
            _id: hire.company[0]?._id,
            name: hire.company[0]?.name,
            logo: hire.company[0]?.logo
          },
          salary: hire.job[0]?.salary
        },
        hiredAt: hire.updatedAt,
        appliedAt: hire.appliedAt,
        timeToHire,
        salaryOffered: hire.salaryOffered,
        startDate: hire.startDate,
        rating: hire.rating,
        feedback: hire.feedback,
        source: hire.source || 'Direct Application',
        recruiterNotes: hire.recruiterNotes
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        hires: transformedHires
      }
    })

  } catch (error) {
    console.error('Error fetching successful hires:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

import { NextRequest } from 'next/server'
import { validateRequestBody } from '@/lib/api/route-handler'
import { withError<PERSON>and<PERSON>, createSuccessResponse, validateMethod, AuthenticatedRequest } from '@/lib/middleware/auth.middleware'
import { companyService, jobService } from '@/lib/services'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import type { UpdateCompanyRequest } from '@/lib/services'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

interface RouteParams {
  params: {
    id: string
  }
}

// Validation function for update company request
function validateUpdateCompanyRequest(data: any): UpdateCompanyRequest {
  const errors: string[] = []
  
  // Validate website URL if provided
  if (data.website && !/^https?:\/\/.+/.test(data.website)) {
    errors.push('Website must be a valid URL')
  }
  
  // Validate social links if provided
  if (data.socialLinks) {
    const socialFields = ['linkedin', 'twitter', 'facebook', 'instagram']
    socialFields.forEach(field => {
      if (data.socialLinks[field] && !/^https?:\/\/.+/.test(data.socialLinks[field])) {
        errors.push(`${field} must be a valid URL`)
      }
    })
  }
  
  if (errors.length > 0) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      `Validation failed: ${errors.join(', ')}`,
      undefined,
      { validationErrors: errors }
    )
  }
  
  const updateData: UpdateCompanyRequest = {}
  
  if (data.name) updateData.name = data.name.trim()
  if (data.description) updateData.description = data.description.trim()
  if (data.website) updateData.website = data.website.trim()
  if (data.industry) updateData.industry = data.industry.trim()
  if (data.size) updateData.size = data.size.trim()
  if (data.location) updateData.location = data.location
  if (data.logo) updateData.logo = data.logo.trim()
  if (data.banner) updateData.banner = data.banner.trim()
  if (data.socialLinks) updateData.socialLinks = data.socialLinks
  
  return updateData
}

// GET /api/v1/companies/[id] - Get company by ID
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  validateMethod(request, ['GET'])
  
  const companyId = params.id
  const { searchParams } = new URL(request.url)
  
  // Check if requesting company jobs
  if (searchParams.get('jobs') === 'true') {
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    
    const result = await jobService.getJobsByCompany(companyId, page, limit)
    return createSuccessResponse(result)
  }
  
  // Check if requesting by slug instead of ID
  let result
  if (companyId.length !== 24) { // Not a MongoDB ObjectId, assume it's a slug
    result = await companyService.getCompanyBySlug(companyId)
  } else {
    result = await companyService.getCompanyById(companyId)
  }
  
  return createSuccessResponse(result)
}, {
  requireDatabase: true
})

// PUT /api/v1/companies/[id] - Update company
export const PUT = withErrorHandler(async (request: AuthenticatedRequest, { params }: RouteParams) => {
  validateMethod(request, ['PUT'])

  const companyId = params.id
  const updateData = await validateRequestBody(request, validateUpdateCompanyRequest)

  // Get user ID from authenticated request
  const userId = request.user?.id
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }
  
  const result = await companyService.updateCompany(companyId, updateData)
  
  return createSuccessResponse(result)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['company_admin', 'admin', 'super_admin']
})

// DELETE /api/v1/companies/[id] - Deactivate company (admin only)
export const DELETE = withErrorHandler(async (request: AuthenticatedRequest, { params }: RouteParams) => {
  validateMethod(request, ['DELETE'])

  const companyId = params.id

  // Get user ID from authenticated request
  const userId = request.user?.id
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }
  
  // This would need to be implemented in the company service
  // For now, return a placeholder response
  return createSuccessResponse({ 
    message: 'Company deactivated successfully' 
  })
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['admin', 'super_admin']
})

// Method not allowed for other HTTP methods
export async function POST() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'POST method not allowed for individual company resources'
  )
}

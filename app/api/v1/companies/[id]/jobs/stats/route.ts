import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { createSuccessResponse, createErrorResponse } from '@/lib/api/utils'
import { findCompany, getCompanyJobStats } from '@/lib/utils/company-jobs'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectToDatabase()

    const { id } = params

    // Find company using utility function
    const company = await findCompany(id)
    if (!company) {
      return NextResponse.json(
        createErrorResponse('NOT_FOUND', 'Company not found'),
        { status: 404 }
      )
    }

    // Get comprehensive job statistics using utility function
    const stats = await getCompanyJobStats(company._id)

    // Add company info to response
    const responseData = {
      ...stats,
      company: {
        _id: company._id,
        name: company.name,
        slug: company.slug
      }
    }

    return NextResponse.json(
      createSuccessResponse(responseData),
      { status: 200 }
    )

  } catch (error) {
    console.error('Error fetching company job stats:', error)
    return NextResponse.json(
      createErrorResponse('INTERNAL_ERROR', 'Failed to fetch company job statistics'),
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/database/connection'
import { createSuccessResponse, createErrorResponse } from '@/lib/api/utils'
import { findCompany, getCompanyJobs } from '@/lib/utils/company-jobs'
import { Job } from '@/lib/models/job.model'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectToDatabase()

    const { id } = params
    const { searchParams } = new URL(request.url)
    
    // Get query parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status') || 'active'
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return NextResponse.json(
        createErrorResponse('VALIDATION_ERROR', 'Invalid pagination parameters'),
        { status: 400 }
      )
    }

    // Find company using utility function
    const company = await findCompany(id)
    if (!company) {
      return NextResponse.json(
        createErrorResponse('NOT_FOUND', 'Company not found'),
        { status: 404 }
      )
    }

    // Build query for jobs - using companyId field as per your data structure
    const query: any = {
      companyId: company._id
    }

    // Add status filter
    if (status !== 'all') {
      query.status = status
    }

    // Build sort object
    const sort: any = {}
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1

    // Calculate skip value for pagination
    const skip = (page - 1) * limit

    // Execute queries
    const [jobs, totalJobs] = await Promise.all([
      Job.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      Job.countDocuments(query)
    ])

    // Calculate pagination info
    const totalPages = Math.ceil(totalJobs / limit)
    const hasNextPage = page < totalPages
    const hasPrevPage = page > 1

    const pagination = {
      page,
      limit,
      total: totalJobs,
      totalPages,
      hasNextPage,
      hasPrevPage
    }

    return NextResponse.json(
      createSuccessResponse({
        jobs,
        pagination,
        company: {
          _id: company._id,
          name: company.name,
          slug: company.slug,
          logo: company.logo
        }
      })
    )

  } catch (error) {
    console.error('Error fetching company jobs:', error)
    return NextResponse.json(
      createErrorResponse('INTERNAL_ERROR', 'Failed to fetch company jobs'),
      { status: 500 }
    )
  }
}

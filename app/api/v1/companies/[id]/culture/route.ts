import { NextRequest } from 'next/server'
import { companyManagementService } from '@/lib/services/company-management.service'
import { errorService, ErrorCode } from '@/lib/error-service'
import { 
  withE<PERSON>r<PERSON><PERSON><PERSON>, 
  validate<PERSON>eth<PERSON>, 
  validateRequestBody,
  createSuccessResponse 
} from '@/lib/middleware/api-middleware'
import { companyCultureSchema } from '@/types/company-management.types'
import { z } from 'zod'

interface RouteContext {
  params: Record<string, string>
}

const updateCultureRequestSchema = z.object({
  culture: companyCultureSchema
})

// PUT /api/v1/companies/[id]/culture - Update company culture
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  validateMethod(request, ['PUT'])
  
  const companyId = params.id
  
  if (!companyId) {
    throw errorService.createError(
      ErrorCode.BAD_REQUEST,
      'Company ID is required',
      'companyId'
    )
  }

  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }

  // Validate request body
  const cultureData = await validateRequestBody(request, updateCultureRequestSchema)
  
  const result = await companyManagementService.updateCompanyCulture(
    companyId,
    cultureData,
    userId
  )
  
  return createSuccessResponse(result.data, 200, result.message)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['company_admin', 'admin', 'super_admin']
})

// GET /api/v1/companies/[id]/culture - Get company culture
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  validateMethod(request, ['GET'])
  
  const companyId = params.id
  
  if (!companyId) {
    throw errorService.createError(
      ErrorCode.BAD_REQUEST,
      'Company ID is required',
      'companyId'
    )
  }

  const result = await companyManagementService.getCompanyProfile(companyId)
  
  // Return only culture data
  return createSuccessResponse(result.data.culture, 200, 'Company culture retrieved successfully')
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['company_admin', 'admin', 'super_admin']
})

// Method not allowed for other HTTP methods
export async function DELETE() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'DELETE method not allowed for company culture.'
  )
}

export async function PATCH() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PATCH method not allowed. Use PUT for culture updates.'
  )
}

export async function POST() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'POST method not allowed. Use PUT for culture updates.'
  )
}

export async function HEAD() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'HEAD method not allowed.'
  )
}

export async function OPTIONS() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'OPTIONS method not allowed.'
  )
}

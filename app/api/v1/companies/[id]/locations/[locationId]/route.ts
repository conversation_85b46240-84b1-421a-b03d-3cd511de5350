import { NextRequest } from 'next/server'
import { companyManagementService } from '@/lib/services/company-management.service'
import { errorService, ErrorCode } from '@/lib/error-service'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  validate<PERSON><PERSON><PERSON>, 
  validateRequestBody,
  createSuccessResponse 
} from '@/lib/middleware/api-middleware'
import { companyLocationSchema } from '@/types/company-management.types'
import { z } from 'zod'

interface RouteContext {
  params: Record<string, string>
}

const updateLocationRequestSchema = z.object({
  location: companyLocationSchema.partial()
})

// PUT /api/v1/companies/[id]/locations/[locationId] - Update specific location
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  validateMethod(request, ['PUT'])
  
  const companyId = params.id
  const locationId = params.locationId
  
  if (!companyId) {
    throw errorService.createError(
      ErrorCode.BAD_REQUEST,
      'Company ID is required',
      'companyId'
    )
  }

  if (!locationId) {
    throw errorService.createError(
      ErrorCode.BAD_REQUEST,
      'Location ID is required',
      'locationId'
    )
  }

  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }

  // Validate request body
  const locationData = await validateRequestBody(request, updateLocationRequestSchema)
  
  const result = await companyManagementService.updateLocation(
    companyId,
    {
      locationId,
      location: locationData.location
    },
    userId
  )
  
  return createSuccessResponse(result.data, 200, result.message)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['company_admin', 'admin', 'super_admin']
})

// DELETE /api/v1/companies/[id]/locations/[locationId] - Remove specific location
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  validateMethod(request, ['DELETE'])
  
  const companyId = params.id
  const locationId = params.locationId
  
  if (!companyId) {
    throw errorService.createError(
      ErrorCode.BAD_REQUEST,
      'Company ID is required',
      'companyId'
    )
  }

  if (!locationId) {
    throw errorService.createError(
      ErrorCode.BAD_REQUEST,
      'Location ID is required',
      'locationId'
    )
  }

  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }

  const result = await companyManagementService.removeLocation(
    companyId,
    locationId,
    userId
  )
  
  return createSuccessResponse(result.data, 200, result.message)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['company_admin', 'admin', 'super_admin']
})

// Method not allowed for other HTTP methods
export async function GET() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'GET method not allowed. Use GET /api/v1/companies/[id]/locations to get all locations.'
  )
}

export async function POST() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'POST method not allowed. Use POST /api/v1/companies/[id]/locations to add a new location.'
  )
}

export async function PATCH() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PATCH method not allowed. Use PUT for location updates.'
  )
}

export async function HEAD() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'HEAD method not allowed.'
  )
}

export async function OPTIONS() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'OPTIONS method not allowed.'
  )
}

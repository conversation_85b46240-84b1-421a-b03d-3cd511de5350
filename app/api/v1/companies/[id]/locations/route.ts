import { NextRequest } from 'next/server'
import { companyManagementService } from '@/lib/services/company-management.service'
import { errorService, ErrorCode } from '@/lib/error-service'
import { 
  withError<PERSON><PERSON><PERSON>, 
  validate<PERSON>eth<PERSON>, 
  validateRequestBody,
  createSuccessResponse 
} from '@/lib/middleware/api-middleware'
import { companyLocationSchema } from '@/types/company-management.types'
import { z } from 'zod'

interface RouteContext {
  params: Record<string, string>
}

const addLocationRequestSchema = z.object({
  location: companyLocationSchema
})

// GET /api/v1/companies/[id]/locations - Get company locations
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  validateMethod(request, ['GET'])
  
  const companyId = params.id
  
  if (!companyId) {
    throw errorService.createError(
      ErrorCode.BAD_REQUEST,
      'Company ID is required',
      'companyId'
    )
  }

  const result = await companyManagementService.getCompanyProfile(companyId)
  
  // Return only locations data
  return createSuccessResponse(result.data.locations, 200, 'Company locations retrieved successfully')
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['company_admin', 'admin', 'super_admin']
})

// POST /api/v1/companies/[id]/locations - Add new location
export const POST = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  validateMethod(request, ['POST'])
  
  const companyId = params.id
  
  if (!companyId) {
    throw errorService.createError(
      ErrorCode.BAD_REQUEST,
      'Company ID is required',
      'companyId'
    )
  }

  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }

  // Validate request body
  const locationData = await validateRequestBody(request, addLocationRequestSchema)
  
  const result = await companyManagementService.addLocation(
    companyId,
    locationData,
    userId
  )
  
  return createSuccessResponse(result.data, 201, result.message)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['company_admin', 'admin', 'super_admin']
})

// Method not allowed for other HTTP methods
export async function DELETE() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'DELETE method not allowed. Use DELETE /api/v1/companies/[id]/locations/[locationId] to delete a specific location.'
  )
}

export async function PUT() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PUT method not allowed. Use PUT /api/v1/companies/[id]/locations/[locationId] to update a specific location.'
  )
}

export async function PATCH() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PATCH method not allowed. Use PUT /api/v1/companies/[id]/locations/[locationId] to update a specific location.'
  )
}

export async function HEAD() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'HEAD method not allowed.'
  )
}

export async function OPTIONS() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'OPTIONS method not allowed.'
  )
}

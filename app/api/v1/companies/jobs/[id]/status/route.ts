import { NextRequest, NextResponse } from 'next/server'
import { with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, AuthenticatedRequest, validate<PERSON>eth<PERSON> } from '@/lib/middleware/auth.middleware'
import { connectDB } from '@/lib/database/connection'
import { Job } from '@/lib/models/job.model'
import { User } from '@/lib/models/user.model'
import { cacheService, CacheService } from '@/lib/services/cache.service'

interface RouteParams {
  params: {
    id: string
  }
}

export const PUT = withErrorHandler(async (request: AuthenticatedRequest, { params }: RouteParams) => {
  validateMethod(request, ['PUT'])

  const userId = request.user?.id
  if (!userId) {
    throw new Error('Authentication required')
  }
    const jobId = params.id
    const { status, reason } = await request.json()

    // Validate status
    const validStatuses = ['draft', 'active', 'paused', 'closed', 'expired']
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be one of: ' + validStatuses.join(', ') },
        { status: 400 }
      )
    }

    // Get user's company
    const user = await User.findById(userId).populate('companyId')
    if (!user || !user.companyId) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    const companyId = user.companyId._id

    // Find the job and verify ownership
    const job = await Job.findById(jobId)
    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    if (job.companyId.toString() !== companyId.toString()) {
      return NextResponse.json(
        { error: 'Access denied. Job does not belong to your company' },
        { status: 403 }
      )
    }

    // Validate status transitions
    const currentStatus = job.status
    const validTransitions: Record<string, string[]> = {
      'draft': ['active', 'closed'],
      'active': ['paused', 'closed', 'expired'],
      'paused': ['active', 'closed'],
      'closed': ['active'], // Allow reopening
      'expired': ['active', 'closed']
    }

    console.log('🔍 Status transition check:', {
      jobId,
      currentStatus,
      requestedStatus: status,
      validTransitions: validTransitions[currentStatus] || [],
      isValidTransition: validTransitions[currentStatus]?.includes(status)
    })

    // If status is the same, return early with success
    if (currentStatus === status) {
      return NextResponse.json({
        success: true,
        message: `Job is already ${status}`,
        data: {
          id: job._id,
          title: job.title,
          status: job.status,
          isActive: job.isActive,
          updatedAt: job.updatedAt,
          statusHistory: job.statusHistory
        }
      })
    }

    // Validate status transitions
    if (!validTransitions[currentStatus]?.includes(status)) {
      return NextResponse.json(
        {
          error: `Invalid status transition from ${currentStatus} to ${status}`,
          validTransitions: validTransitions[currentStatus] || [],
          currentStatus,
          requestedStatus: status
        },
        { status: 400 }
      )
    }

    // Update job status
    const updateData: any = {
      status,
      updatedAt: new Date(),
      updatedBy: userId
    }

    // Set specific fields based on status
    switch (status) {
      case 'active':
        updateData.isActive = true
        updateData.publishedAt = job.publishedAt || new Date()
        break
      case 'paused':
        updateData.isActive = false
        break
      case 'closed':
        updateData.isActive = false
        updateData.closedAt = new Date()
        updateData.closedBy = userId
        if (reason) {
          updateData.closedReason = reason
        }
        break
      case 'expired':
        updateData.isActive = false
        updateData.expiredAt = new Date()
        break
      case 'draft':
        updateData.isActive = false
        break
    }

    // Add status history entry
    if (!job.statusHistory) {
      job.statusHistory = []
    }
    
    job.statusHistory.push({
      fromStatus: currentStatus,
      toStatus: status,
      changedBy: userId,
      reason: reason || undefined,
      timestamp: new Date()
    })

    updateData.statusHistory = job.statusHistory

    const updatedJob = await Job.findByIdAndUpdate(
      jobId,
      updateData,
      { new: true, runValidators: true }
    ).populate('companyId', 'name logo')

    // Clear job cache to ensure fresh data is returned
    const jobCacheKey = CacheService.keys.job(jobId)
    console.log('🔍 Clearing job cache with key:', jobCacheKey)
    await cacheService.del(jobCacheKey)

    // Clear related caches
    const cachePatterns = [
      `search:jobs:*`,
      `jobs:company:${companyId}:*`
    ]

    console.log('🔍 Clearing cache patterns:', cachePatterns)
    for (const pattern of cachePatterns) {
      const keys = await cacheService.keys(pattern)
      console.log(`🔍 Found ${keys.length} keys for pattern ${pattern}:`, keys)
      for (const key of keys) {
        await cacheService.del(key)
      }
    }

    // Verify cache is cleared by trying to get the job from cache
    const cachedJob = await cacheService.get(jobCacheKey)
    console.log('🔍 Job cache after clearing (should be null):', cachedJob)

    // Log the status change
    console.log(`🔍 Job ${jobId} status changed from ${currentStatus} to ${status} by user ${userId}`)
    console.log('🔍 Updated job data:', {
      id: updatedJob._id,
      status: updatedJob.status,
      isActive: updatedJob.isActive
    })

    return NextResponse.json({
      success: true,
      message: `Job status updated to ${status}`,
      data: {
        id: updatedJob._id,
        title: updatedJob.title,
        status: updatedJob.status,
        isActive: updatedJob.isActive,
        updatedAt: updatedJob.updatedAt,
        statusHistory: updatedJob.statusHistory
      }
    })
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['company_admin', 'admin', 'super_admin']
})

// GET method to retrieve current status and valid transitions
export const GET = withErrorHandler(async (request: AuthenticatedRequest, { params }: RouteParams) => {
  validateMethod(request, ['GET'])

  const userId = request.user?.id
  if (!userId) {
    throw new Error('Authentication required')
  }
    const jobId = params.id

    // Get user's company
    const user = await User.findById(userId).populate('companyId')
    if (!user || !user.companyId) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    const companyId = user.companyId._id

    // Find the job and verify ownership
    const job = await Job.findById(jobId).select('status isActive statusHistory companyId')
    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    if (job.companyId.toString() !== companyId.toString()) {
      return NextResponse.json(
        { error: 'Access denied. Job does not belong to your company' },
        { status: 403 }
      )
    }

    // Define valid transitions
    const validTransitions: Record<string, string[]> = {
      'draft': ['active', 'closed'],
      'active': ['paused', 'closed', 'expired'],
      'paused': ['active', 'closed'],
      'closed': ['active'],
      'expired': ['active', 'closed']
    }

    return NextResponse.json({
      success: true,
      data: {
        currentStatus: job.status,
        isActive: job.isActive,
        validTransitions: validTransitions[job.status] || [],
        statusHistory: job.statusHistory || []
      }
    })
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['company_admin', 'admin', 'super_admin']
})

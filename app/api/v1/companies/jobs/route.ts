import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { connectDB } from '@/lib/db'
import { Job } from '@/lib/models/job.model'
import { Application } from '@/lib/models/application.model'
import { User } from '@/lib/models/user.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    
    // Get query parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status')
    const department = searchParams.get('department')
    const type = searchParams.get('type')
    const search = searchParams.get('search')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // Get user's company
    const user = await User.findById(userId).populate('companyId')
    if (!user || !user.companyId) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    const companyId = user.companyId._id

    // Build query
    const query: Record<string, unknown> = {
      companyId: companyId,
      isActive: true
    }

    if (status) {
      query.status = status
    }

    if (department) {
      query.department = department
    }

    if (type) {
      query.type = type
    }

    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { department: { $regex: search, $options: 'i' } }
      ]
    }

    // Calculate pagination
    const skip = (page - 1) * limit

    // Build sort object
    const sort: Record<string, 1 | -1> = {}
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1

    // Get jobs
    const jobs = await Job.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('createdBy', 'profile.firstName profile.lastName')
      .lean()

    // Get total count for pagination
    const total = await Job.countDocuments(query)

    // Get application counts for each job
    const jobsWithStats = await Promise.all(
      jobs.map(async (job) => {
        const applicationsCount = await Application.countDocuments({
          jobId: job._id
        })

        const viewsCount = job.stats?.views || 0

        return {
          _id: job._id,
          title: job.title,
          department: job.department,
          location: job.location,
          type: job.type,
          status: job.status,
          applicationsCount,
          viewsCount,
          createdAt: job.createdAt,
          expiryDate: job.expiryDate,
          salary: job.salary,
          createdBy: job.createdBy,
          description: job.description,
          requirements: job.requirements,
          benefits: job.benefits
        }
      })
    )

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit)
    const hasNextPage = page < totalPages
    const hasPrevPage = page > 1

    return NextResponse.json({
      success: true,
      data: jobsWithStats,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage,
        hasPrevPage
      }
    })

  } catch (error) {
    console.error('Get company jobs error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch company jobs' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const jobData = await request.json()

    // Get user's company
    const user = await User.findById(userId).populate('companyId')
    if (!user || !user.companyId) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    const companyId = user.companyId._id

    // Create new job
    const job = new Job({
      ...jobData,
      companyId: companyId,
      createdBy: userId,
      isActive: true,
      status: jobData.status || 'active'
    })

    await job.save()

    // Populate the created job
    await job.populate('companyId', 'name logo locations')
    await job.populate('createdBy', 'profile.firstName profile.lastName')

    return NextResponse.json({
      success: true,
      message: 'Job created successfully',
      data: job
    }, { status: 201 })

  } catch (error) {
    console.error('Create job error:', error)
    return NextResponse.json(
      { error: 'Failed to create job' },
      { status: 500 }
    )
  }
}

import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON>,
  createSuccessResponse,
  AuthenticatedRequest
} from '@/lib/middleware/auth.middleware'
import { Job } from '@/lib/models/job.model'
import { Application } from '@/lib/models/application.model'
import { Company } from '@/lib/models/company.model'
import { User } from '@/lib/models/user.model'

export const GET = withErrorHandler(async (request: AuthenticatedRequest) => {
  validateMethod(request, ['GET'])

  const userId = request.user?.id
  if (!userId) {
    throw new Error('Authentication required')
  }

  console.log('🔍 Dashboard Stats - User ID:', userId)

  // Get user's company
  const user = await User.findById(userId).populate('companyId')

  console.log('🔍 Dashboard Stats - User found:', user ? 'YES' : 'NO')
  if (user) {
    console.log('🔍 Dashboard Stats - User details:', {
      id: user._id,
      email: user.email,
      role: user.role,
      companyId: user.companyId,
      companies: user.companies
    })
  }

  if (!user || !user.companyId) {
    console.log('🔍 Dashboard Stats - Company not found. User has companyId:', !!user?.companyId)

    // Return empty stats instead of throwing an error
    const emptyStats = {
      totalJobs: 0,
      activeJobs: 0,
      totalApplications: 0,
      totalHires: 0,
      averageTimeToHire: 0,
      responseRate: 0,
      profileViews: 0,
      followerCount: 0,
      recentApplications: 0,
      pendingApplications: 0,
      interviewsScheduled: 0,
      offersExtended: 0,
      needsCompanySetup: true,
      message: 'No company associated with this user. Please create a company profile first.'
    }

    return createSuccessResponse(emptyStats, 200, 'Dashboard stats retrieved (no company)')
  }

    const companyId = user.companyId._id

    // Get time ranges for comparison
    const now = new Date()
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000)

    // Get active jobs count
    const activeJobs = await Job.countDocuments({
      company: companyId,
      status: 'active',
      isActive: true
    })

    // Get total applications count
    const totalApplications = await Application.countDocuments({
      company: companyId
    })

    // Get applications from last 30 days for comparison
    const recentApplications = await Application.countDocuments({
      company: companyId,
      createdAt: { $gte: thirtyDaysAgo }
    })

    const previousApplications = await Application.countDocuments({
      company: companyId,
      createdAt: { $gte: sixtyDaysAgo, $lt: thirtyDaysAgo }
    })

    // Get pending reviews count
    const pendingReviews = await Application.countDocuments({
      company: companyId,
      status: { $in: ['pending', 'reviewing'] }
    })

    // Get scheduled interviews count
    const interviewsScheduled = await Application.countDocuments({
      company: companyId,
      status: 'interviewed',
      'interview.scheduledAt': { $gte: now }
    })

    // Get hired candidates count (last 30 days)
    const hiredCandidates = await Application.countDocuments({
      company: companyId,
      status: 'hired',
      updatedAt: { $gte: thirtyDaysAgo }
    })

    // Get company profile views (mock for now)
    const company = await Company.findById(companyId)
    const profileViews = company?.stats?.profileViews || 0
    const previousProfileViews = Math.floor(profileViews * 0.85) // Mock 15% growth

    // Calculate response rate
    const respondedApplications = await Application.countDocuments({
      company: companyId,
      status: { $ne: 'pending' }
    })
    const responseRate = totalApplications > 0 ? Math.round((respondedApplications / totalApplications) * 100) : 0

    // Calculate average time to hire (mock calculation)
    const hiredApplications = await Application.find({
      company: companyId,
      status: 'hired'
    }).select('createdAt updatedAt').limit(10)

    let averageTimeToHire = 0
    if (hiredApplications.length > 0) {
      const totalTime = hiredApplications.reduce((sum, app) => {
        const timeDiff = new Date(app.updatedAt).getTime() - new Date(app.createdAt).getTime()
        return sum + (timeDiff / (1000 * 60 * 60 * 24)) // Convert to days
      }, 0)
      averageTimeToHire = Math.round(totalTime / hiredApplications.length)
    }

    // Calculate job fill rate
    const totalJobsPosted = await Job.countDocuments({
      company: companyId,
      createdAt: { $gte: thirtyDaysAgo }
    })
    const filledJobs = await Job.countDocuments({
      company: companyId,
      status: 'closed',
      createdAt: { $gte: thirtyDaysAgo }
    })
    const jobFillRate = totalJobsPosted > 0 ? Math.round((filledJobs / totalJobsPosted) * 100) : 0

    // Calculate growth percentages
    const applicationsGrowth = previousApplications > 0 
      ? Math.round(((recentApplications - previousApplications) / previousApplications) * 100)
      : 0

    const profileViewsGrowth = previousProfileViews > 0
      ? Math.round(((profileViews - previousProfileViews) / previousProfileViews) * 100)
      : 0

    // Get active recruiters count
    const activeRecruiters = await User.countDocuments({
      companyId: companyId,
      role: { $in: ['company_admin', 'recruiter'] },
      isActive: true
    })

    const stats = {
      activeJobs,
      totalApplications,
      pendingReviews,
      interviewsScheduled,
      hiredCandidates,
      profileViews,
      responseRate,
      averageTimeToHire,
      jobFillRate,
      applicationQuality: 4.2, // Mock rating
      activeRecruiters,
      // Growth metrics
      jobsGrowth: 2, // Mock growth
      applicationsGrowth,
      profileViewsGrowth,
      hiredGrowth: hiredCandidates > 0 ? 25 : 0 // Mock growth
    }

  return createSuccessResponse(stats, 200, 'Dashboard stats retrieved successfully')
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['company_admin', 'admin', 'super_admin']
})

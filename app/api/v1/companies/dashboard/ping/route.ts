import { NextRequest, NextResponse } from 'next/server'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  console.log('=== PING ENDPOINT CALLED ===')
  // Remove request.url to avoid dynamic server usage error

  return NextResponse.json({
    success: true,
    message: 'Dashboard API is working',
    timestamp: new Date().toISOString(),
    path: '/api/v1/companies/dashboard/ping'
  })
}

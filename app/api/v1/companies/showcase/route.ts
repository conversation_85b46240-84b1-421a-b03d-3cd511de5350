import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { Company } from '@/lib/models/company.model'
import { Job } from '@/lib/models/job.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '6')
    const featured = searchParams.get('featured') === 'true'

    // Build query for companies
    const companyQuery: any = { 
      isActive: true,
      verification: { $exists: true }
    }

    if (featured) {
      companyQuery.isFeatured = true
    }

    // Get companies with job counts
    const companies = await Company.aggregate([
      { $match: companyQuery },
      
      // Lookup active jobs for each company
      {
        $lookup: {
          from: 'jobs',
          let: { companyId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ['$companyId', '$$companyId'] },
                isActive: true,
                $or: [
                  { applicationDeadline: { $gte: new Date() } },
                  { applicationDeadline: { $exists: false } }
                ]
              }
            }
          ],
          as: 'activeJobs'
        }
      },
      
      // Add job count field
      {
        $addFields: {
          activeJobsCount: { $size: '$activeJobs' }
        }
      },
      
      // Sort by featured first, then by job count, then by creation date
      {
        $sort: {
          isFeatured: -1,
          activeJobsCount: -1,
          createdAt: -1
        }
      },
      
      // Limit results
      { $limit: limit },
      
      // Project final fields
      {
        $project: {
          _id: 1,
          name: 1,
          slug: 1,
          description: 1,
          logo: 1,
          website: 1,
          industries: 1,
          size: 1,
          founded: 1,
          location: 1,
          verification: 1,
          isFeatured: 1,
          activeJobsCount: 1,
          createdAt: 1
        }
      }
    ])

    // Format companies for frontend
    const formattedCompanies = companies.map(company => ({
      id: company._id,
      name: company.name,
      slug: company.slug,
      description: company.description || `${company.name} is a leading company in the ${company.industries?.[0] || 'industry'} sector.`,
      logo: company.logo || null,
      website: company.website,
      industry: company.industries?.[0] || 'Technology',
      size: formatCompanySize(company.size),
      location: formatLocation(company.location),
      openJobs: company.activeJobsCount || 0,
      featured: company.isFeatured || false,
      verified: company.verification?.isVerified || false,
      founded: company.founded
    }))

    return NextResponse.json({
      success: true,
      data: {
        companies: formattedCompanies,
        total: formattedCompanies.length
      }
    })

  } catch (error) {
    console.error('Error fetching showcase companies:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch companies' 
      },
      { status: 500 }
    )
  }
}

// Helper function to format company size
function formatCompanySize(size: string): string {
  const sizeMap: Record<string, string> = {
    'startup': '1-10 employees',
    'small': '11-50 employees',
    'medium': '51-200 employees',
    'large': '201-1000 employees',
    'enterprise': '1000+ employees'
  }
  
  return sizeMap[size] || size || 'Size not specified'
}

// Helper function to format location
function formatLocation(location: any): string {
  if (!location) return 'Location not specified'
  
  if (typeof location === 'string') return location
  
  const parts = []
  if (location.city) parts.push(location.city)
  if (location.state) parts.push(location.state)
  if (location.country) parts.push(location.country)
  
  return parts.length > 0 ? parts.join(', ') : 'Location not specified'
}

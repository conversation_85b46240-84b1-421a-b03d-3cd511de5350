import { NextRequest } from 'next/server'
import { Company } from '@/lib/models/company.model'
import { User } from '@/lib/models/user.model'
import { errorService, ErrorCode } from '@/lib/error-service'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  validate<PERSON><PERSON><PERSON>,
  createSuccessResponse 
} from '@/lib/middleware/api-middleware'
import { connectToDatabase } from '@/lib/database/connection'

// POST /api/v1/companies/me/logo - Upload company logo
export const POST = withErrorHandler(async (request: NextRequest) => {
  validateMethod(request, ['POST'])
  
  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }

  await connectToDatabase()

  // Find user and their company
  const user = await User.findById(userId)
  
  if (!user || !user.companyId) {
    throw errorService.createError(
      ErrorCode.NOT_FOUND,
      'No company associated with user',
      'company'
    )
  }

  const company = await Company.findById(user.companyId)
  
  if (!company) {
    throw errorService.createError(
      ErrorCode.NOT_FOUND,
      'Company not found',
      'company'
    )
  }

  // Check if user is admin
  const isAdmin = company.admins.some((adminId: unknown) => {
    if (adminId && typeof adminId === 'object' && 'equals' in adminId) {
      return (adminId as { equals: (id: unknown) => boolean }).equals(userId)
    }
    return false
  })

  if (!isAdmin) {
    throw errorService.createError(
      ErrorCode.FORBIDDEN,
      'Insufficient permissions to update company logo',
      'permissions'
    )
  }

  try {
    // Get form data
    const formData = await request.formData()
    const file = formData.get('logo') as File
    
    if (!file) {
      throw errorService.createError(
        ErrorCode.BAD_REQUEST,
        'No logo file provided',
        'file'
      )
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      throw errorService.createError(
        ErrorCode.BAD_REQUEST,
        'Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed',
        'fileType'
      )
    }

    // Validate file size (5MB max)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      throw errorService.createError(
        ErrorCode.BAD_REQUEST,
        'File size too large. Maximum size is 5MB',
        'fileSize'
      )
    }

    // For now, we'll just simulate the upload and store a placeholder URL
    // In a real implementation, you would upload to a cloud storage service like AWS S3, Cloudinary, etc.
    const logoUrl = `/uploads/logos/${company._id}-${Date.now()}.${file.type.split('/')[1]}`
    
    // Update company logo
    company.logo = logoUrl
    company.updatedAt = new Date()
    await company.save()

    // Return updated company
    const updatedCompany = await Company.findById(company._id)
      .populate('admins', 'name email avatar')
      .populate('recruiters', 'name email avatar')
      .populate('teamMembers.user', 'name email avatar')
      .lean()

    return createSuccessResponse(updatedCompany, 200, 'Company logo uploaded successfully')

  } catch (error) {
    if (error instanceof Error && error.message.includes('ErrorCode')) {
      throw error
    }
    
    throw errorService.createError(
      ErrorCode.INTERNAL_SERVER_ERROR,
      'Failed to process logo upload',
      'upload'
    )
  }
}, {
  requireDatabase: true,
  requireAuth: true
})

// Method not allowed for other HTTP methods
export async function GET() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'GET method not allowed for logo upload.'
  )
}

export async function PUT() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PUT method not allowed. Use POST for logo upload.'
  )
}

export async function DELETE() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'DELETE method not allowed for logo upload.'
  )
}

export async function PATCH() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PATCH method not allowed. Use POST for logo upload.'
  )
}

export async function HEAD() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'HEAD method not allowed.'
  )
}

export async function OPTIONS() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'OPTIONS method not allowed.'
  )
}

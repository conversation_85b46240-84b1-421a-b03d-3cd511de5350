import { Company } from '@/lib/models/company.model'
import { User } from '@/lib/models/user.model'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON>,
  createSuccessResponse,
  AuthenticatedRequest
} from '@/lib/api/route-handler'
import { connectToDatabase } from '@/lib/database/connection'

// GET /api/v1/companies/me/verify - Verify company-admin relationships
export const GET = withError<PERSON>andler(async (request: AuthenticatedRequest) => {
  validateMethod(request, ['GET'])

  // Get user ID from auth context
  const userId = request.user?.id
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }

  await connectToDatabase()

  // Find user with company data
  const user = await User.findById(userId)
    .populate('companyId')
    .lean()
  
  if (!user) {
    throw errorService.createError(
      ErrorCode.NOT_FOUND,
      'User not found',
      'user'
    )
  }

  // Check if user is company admin
  if (user.role !== 'company_admin') {
    throw errorService.createError(
      ErrorCode.FORBIDDEN,
      'User is not a company admin',
      'role'
    )
  }

  // Check if user has company
  if (!user.companyId) {
    return createSuccessResponse({
      verified: false,
      issues: ['User has no company assigned'],
      user: {
        id: user._id,
        email: user.email,
        role: user.role,
        companyId: null
      },
      company: null
    }, 200, 'Company-admin relationship verification completed')
  }

  // Get detailed company information
  const company = await Company.findById(user.companyId)
    .populate('admins', 'email profile.firstName profile.lastName role')
    .populate('teamMembers.user', 'email profile.firstName profile.lastName role')
    .lean()

  if (!company) {
    return createSuccessResponse({
      verified: false,
      issues: ['Company not found'],
      user: {
        id: user._id,
        email: user.email,
        role: user.role,
        companyId: user.companyId
      },
      company: null
    }, 200, 'Company-admin relationship verification completed')
  }

  // Verify relationships
  const issues: string[] = []
  
  // Check if user is in company admins
  const isInAdmins = company.admins.some((admin: any) => 
    admin._id.toString() === userId
  )
  
  if (!isInAdmins) {
    issues.push('User is not in company admins array')
  }

  // Check if user is in team members
  const teamMember = company.teamMembers?.find((member: any) => 
    member.user._id.toString() === userId
  )
  
  if (!teamMember) {
    issues.push('User is not in company team members')
  } else if (!teamMember.isActive) {
    issues.push('User is inactive in team members')
  }

  // Check if company was created by user
  const isCreatedByUser = company.createdBy?.toString() === userId
  
  if (!isCreatedByUser) {
    issues.push('Company was not created by this user')
  }

  const verified = issues.length === 0

  return createSuccessResponse({
    verified,
    issues,
    user: {
      id: user._id,
      email: user.email,
      role: user.role,
      companyId: user.companyId,
      name: `${user.profile?.firstName} ${user.profile?.lastName}`
    },
    company: {
      id: company._id,
      name: company.name,
      slug: company.slug,
      description: company.description,
      createdBy: company.createdBy,
      admins: company.admins.map((admin: any) => ({
        id: admin._id,
        email: admin.email,
        name: `${admin.profile?.firstName} ${admin.profile?.lastName}`,
        role: admin.role
      })),
      teamMembers: company.teamMembers?.map((member: any) => ({
        userId: member.user._id,
        email: member.user.email,
        name: `${member.user.profile?.firstName} ${member.user.profile?.lastName}`,
        role: member.role,
        department: member.department,
        isActive: member.isActive,
        joinedAt: member.joinedAt
      })) || []
    },
    relationships: {
      userInAdmins: isInAdmins,
      userInTeamMembers: !!teamMember,
      userIsActive: teamMember?.isActive || false,
      userCreatedCompany: isCreatedByUser,
      userRole: teamMember?.role || null
    }
  }, 200, verified ? 'Company-admin relationships verified successfully' : 'Company-admin relationship issues found')

}, {
  requireDatabase: true,
  requireAuth: true
})

// Method not allowed for other HTTP methods
export async function POST() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'POST method not allowed for verification endpoint'
  )
}

export async function PUT() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PUT method not allowed for verification endpoint'
  )
}

export async function DELETE() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'DELETE method not allowed for verification endpoint'
  )
}

export async function PATCH() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PATCH method not allowed for verification endpoint'
  )
}

export async function HEAD() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'HEAD method not allowed'
  )
}

export async function OPTIONS() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'OPTIONS method not allowed'
  )
}

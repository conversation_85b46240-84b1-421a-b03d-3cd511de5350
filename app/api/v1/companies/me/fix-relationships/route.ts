import { Company } from '@/lib/models/company.model'
import { User } from '@/lib/models/user.model'
import { errorService, ErrorCode } from '@/lib/error-service'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON>ethod,
  createSuccessResponse,
  AuthenticatedRequest
} from '@/lib/middleware/api-middleware'
import { connectToDatabase } from '@/lib/database/connection'

// POST /api/v1/companies/me/fix-relationships - Fix broken company-admin relationships
export const POST = withErrorHandler(async (request: AuthenticatedRequest) => {
  validateMethod(request, ['POST'])

  // Get user ID from auth context
  const userId = request.user?.id
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }

  await connectToDatabase()

  // Find user with company data
  const user = await User.findById(userId)
  
  if (!user) {
    throw errorService.createError(
      ErrorCode.NOT_FOUND,
      'User not found',
      'user'
    )
  }

  // Check if user is company admin
  if (user.role !== 'company_admin') {
    throw errorService.createError(
      ErrorCode.FORBIDDEN,
      'User is not a company admin',
      'role'
    )
  }

  // Check if user has company
  if (!user.companyId) {
    throw errorService.createError(
      ErrorCode.BAD_REQUEST,
      'User has no company assigned',
      'companyId'
    )
  }

  // Get company
  const company = await Company.findById(user.companyId)

  if (!company) {
    throw errorService.createError(
      ErrorCode.NOT_FOUND,
      'Company not found',
      'company'
    )
  }

  const fixes: string[] = []
  let updated = false

  // Fix 1: Add user to admins array if missing
  const isInAdmins = company.admins.some(adminId => 
    adminId.toString() === userId
  )
  
  if (!isInAdmins) {
    company.admins.push(user._id)
    fixes.push('Added user to company admins array')
    updated = true
  }

  // Fix 2: Add user to team members if missing
  const teamMemberIndex = company.teamMembers?.findIndex(member => 
    member.user.toString() === userId
  )
  
  if (teamMemberIndex === -1 || teamMemberIndex === undefined) {
    if (!company.teamMembers) {
      company.teamMembers = []
    }
    
    company.teamMembers.push({
      user: user._id,
      role: 'owner',
      department: 'Management',
      joinedAt: new Date(),
      isActive: true
    })
    fixes.push('Added user to company team members')
    updated = true
  } else {
    // Fix 3: Ensure team member is active
    const teamMember = company.teamMembers[teamMemberIndex]
    if (!teamMember.isActive) {
      teamMember.isActive = true
      fixes.push('Activated user in team members')
      updated = true
    }
    
    // Fix 4: Ensure team member has owner role
    if (teamMember.role !== 'owner') {
      teamMember.role = 'owner'
      fixes.push('Updated user role to owner in team members')
      updated = true
    }
  }

  // Fix 5: Set createdBy if missing
  if (!company.createdBy) {
    company.createdBy = user._id
    fixes.push('Set company createdBy field')
    updated = true
  }

  // Save changes if any fixes were applied
  if (updated) {
    await company.save()
  }

  // Get updated company data for verification
  const updatedCompany = await Company.findById(company._id)
    .populate('admins', 'email profile.firstName profile.lastName')
    .populate('teamMembers.user', 'email profile.firstName profile.lastName')

  return createSuccessResponse({
    fixed: updated,
    fixes,
    company: {
      id: updatedCompany._id,
      name: updatedCompany.name,
      admins: updatedCompany.admins.map((admin: any) => ({
        id: admin._id,
        email: admin.email,
        name: `${admin.profile?.firstName} ${admin.profile?.lastName}`
      })),
      teamMembers: updatedCompany.teamMembers?.map((member: any) => ({
        userId: member.user._id,
        email: member.user.email,
        name: `${member.user.profile?.firstName} ${member.user.profile?.lastName}`,
        role: member.role,
        department: member.department,
        isActive: member.isActive
      })) || [],
      createdBy: updatedCompany.createdBy
    },
    relationships: {
      userInAdmins: updatedCompany.admins.some((admin: any) => 
        admin._id.toString() === userId
      ),
      userInTeamMembers: updatedCompany.teamMembers?.some((member: any) => 
        member.user._id.toString() === userId
      ) || false,
      userCreatedCompany: updatedCompany.createdBy?.toString() === userId
    }
  }, 200, updated ? 'Company-admin relationships fixed successfully' : 'No fixes needed - relationships are already correct')

}, {
  requireDatabase: true,
  requireAuth: true
})

// Method not allowed for other HTTP methods
export async function GET() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'GET method not allowed. Use POST to fix relationships.'
  )
}

export async function PUT() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PUT method not allowed. Use POST to fix relationships.'
  )
}

export async function DELETE() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'DELETE method not allowed'
  )
}

export async function PATCH() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PATCH method not allowed. Use POST to fix relationships.'
  )
}

export async function HEAD() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'HEAD method not allowed'
  )
}

export async function OPTIONS() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'OPTIONS method not allowed'
  )
}

import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { connectDB } from '@/lib/db'
import { User } from '@/lib/models/user.model'
import { Company } from '@/lib/models/company.model'

// GET /api/v1/companies/me - Get current user's company
export async function GET(request: NextRequest) {
  try {
    await connectDB()

    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error
        },
        { status: authResult.status || 401 }
      )
    }

    const userId = authResult.user?.id
    console.log('🔍 Companies/me - User ID from token:', userId)
    console.log('🔍 Companies/me - Auth result:', authResult)

    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: 'User ID not found in token'
        },
        { status: 401 }
      )
    }

    // Find user and their company
    const user = await User.findById(userId).populate('companyId')

    console.log('🔍 Companies/me - User found in DB:', user ? 'YES' : 'NO')
    if (user) {
      console.log('🔍 Companies/me - User details:', {
        id: user._id,
        email: user.email,
        role: user.role,
        companyId: user.companyId
      })
    }

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'User not found'
        },
        { status: 404 }
      )
    }

    // If user has no company, return helpful response
    if (!user.companyId) {
      return NextResponse.json({
        success: true,
        data: {
          company: null,
          needsCompanySetup: true,
          message: 'No company associated with this user. Please create a company profile.',
          user: {
            id: user._id,
            email: user.email,
            role: user.role,
            name: user.profile.firstName + ' ' + user.profile.lastName
          }
        }
      }, { status: 200 })
    }

    // Get full company details
    const company = await Company.findById(user.companyId)
      .populate('admins', 'profile.firstName profile.lastName email')
      .populate('recruiters', 'profile.firstName profile.lastName email')
      .populate('teamMembers.user', 'profile.firstName profile.lastName email')
      .lean()

    if (!company) {
      return NextResponse.json({
        success: true,
        data: {
          company: null,
          message: 'Company not found'
        }
      }, { status: 200 })
    }

    return NextResponse.json({
      success: true,
      data: {
        company: company
      },
      message: 'Company profile retrieved successfully'
    }, { status: 200 })

  } catch (error) {
    console.error('Companies/me error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch company profile'
      },
      { status: 500 }
    )
  }
}

// POST /api/v1/companies/me - Create company for current user
export async function POST(request: NextRequest) {
  try {
    await connectDB()

    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error
        },
        { status: authResult.status || 401 }
      )
    }

    const userId = authResult.user?.id
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: 'User ID not found in token'
        },
        { status: 401 }
      )
    }

    // Check if user already has a company
    const user = await User.findById(userId)

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'User not found'
        },
        { status: 404 }
      )
    }

    if (user.companyId) {
      return NextResponse.json(
        {
          success: false,
          error: 'User already has a company associated'
        },
        { status: 400 }
      )
    }

    // Get request body
    const companyData = await request.json()

    // Create company slug from name
    const slug = companyData.name
      ?.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '') || 'company'

    // Create new company
    const company = new Company({
      name: companyData.name,
      slug: slug,
      description: companyData.description || '',
      tagline: companyData.tagline || '',
      website: companyData.website || '',
      industry: companyData.industry || [],
      size: companyData.size || 'startup',
      founded: companyData.founded || new Date().getFullYear(),
      locations: [],
      contact: {
        email: companyData.contact?.email || user.email,
        phone: companyData.contact?.phone || '',
        address: companyData.contact?.address || '',
        supportEmail: companyData.contact?.supportEmail || '',
        hrEmail: companyData.contact?.hrEmail || ''
      },
      culture: {
        values: [],
        benefits: [],
        workEnvironment: '',
        diversity: '',
        mission: '',
        vision: '',
        perks: []
      },
      socialLinks: {
        linkedin: '',
        twitter: '',
        facebook: '',
        instagram: '',
        github: '',
        youtube: '',
        glassdoor: ''
      },
      subscription: {
        plan: 'starter',
        status: 'trial',
        jobPostingLimit: 5,
        jobPostingsUsed: 0,
        featuredJobsLimit: 1,
        featuredJobsUsed: 0
      },
      admins: [userId],
      recruiters: [],
      teamMembers: [{
        user: userId,
        role: 'owner',
        department: 'Management',
        joinedAt: new Date(),
        isActive: true
      }],
      stats: {
        totalJobs: 0,
        activeJobs: 0,
        totalApplications: 0,
        totalHires: 0,
        profileViews: 0,
        followerCount: 0
      },
      verification: {
        isVerified: false,
        status: 'pending'
      },
      settings: {
        allowPublicProfile: true,
        showSalaryRanges: true,
        allowDirectContact: true,
        showCompanyStats: false,
        autoRejectAfterDays: 30,
        requireCoverLetter: false,
        enableApplicationTracking: true,
        emailNotifications: {
          newApplications: true,
          applicationUpdates: true,
          interviewReminders: true,
          jobExpiring: true,
          weeklyReports: false,
          marketingEmails: false
        },
        pushNotifications: {
          newApplications: true,
          applicationUpdates: false,
          interviewReminders: true,
          systemUpdates: true
        },
        branding: {
          primaryColor: '#000000',
          secondaryColor: '#ffffff',
          logoUrl: '',
          coverImageUrl: '',
          customCss: ''
        },
        team: {
          allowMemberInvites: true,
          requireApprovalForInvites: true,
          defaultMemberRole: 'recruiter',
          maxTeamMembers: 10
        },
        integrations: {
          atsEnabled: false,
          atsProvider: '',
          atsApiKey: '',
          slackWebhookUrl: '',
          googleAnalyticsId: '',
          linkedinCompanyId: ''
        },
        advanced: {
          customApplicationFields: [],
          autoResponseEnabled: true,
          autoResponseMessage: 'Thank you for your application. We will review it and get back to you soon.',
          applicationDeadlineReminder: true,
          bulkActionsEnabled: true
        }
      },
      isActive: true,
      isFeatured: false
    })

    await company.save()

    // Update user with company reference
    user.companyId = company._id
    user.role = 'company_admin'
    await user.save()

    // Return the created company
    const populatedCompany = await Company.findById(company._id)
      .populate('admins', 'profile.firstName profile.lastName email')
      .populate('recruiters', 'profile.firstName profile.lastName email')
      .populate('teamMembers.user', 'profile.firstName profile.lastName email')
      .lean()

    return NextResponse.json({
      success: true,
      data: {
        company: populatedCompany
      },
      message: 'Company created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Create company error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create company'
      },
      { status: 500 }
    )
  }
}

// PUT /api/v1/companies/me - Update current user's company
export async function PUT(request: NextRequest) {
  try {
    await connectDB()

    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error
        },
        { status: authResult.status || 401 }
      )
    }

    const userId = authResult.user?.id
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: 'User ID not found in token'
        },
        { status: 401 }
      )
    }

    // Find user and their company
    const user = await User.findById(userId)

    if (!user || !user.companyId) {
      return NextResponse.json(
        {
          success: false,
          error: 'No company associated with user'
        },
        { status: 404 }
      )
    }

    const company = await Company.findById(user.companyId)

    if (!company) {
      return NextResponse.json(
        {
          success: false,
          error: 'Company not found'
        },
        { status: 404 }
      )
    }

    // Check if user is admin
    const isAdmin = company.admins.some((adminId: unknown) => {
      if (adminId && typeof adminId === 'object' && 'equals' in adminId) {
        return (adminId as { equals: (id: unknown) => boolean }).equals(userId)
      }
      return false
    })

    if (!isAdmin) {
      return NextResponse.json(
        {
          success: false,
          error: 'Insufficient permissions to update company'
        },
        { status: 403 }
      )
    }

    // Get request body
    const updateData = await request.json()

    // Update company
    Object.assign(company, updateData)
    company.updatedAt = new Date()
    await company.save()

    // Return updated company
    const updatedCompany = await Company.findById(company._id)
      .populate('admins', 'profile.firstName profile.lastName email')
      .populate('recruiters', 'profile.firstName profile.lastName email')
      .populate('teamMembers.user', 'profile.firstName profile.lastName email')
      .lean()

    return NextResponse.json({
      success: true,
      data: {
        company: updatedCompany
      },
      message: 'Company updated successfully'
    }, { status: 200 })

  } catch (error) {
    console.error('Update company error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update company'
      },
      { status: 500 }
    )
  }
}

// Method not allowed for other HTTP methods
export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      error: 'DELETE method not allowed for company profiles.'
    },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    {
      success: false,
      error: 'PATCH method not allowed. Use PUT for company updates.'
    },
    { status: 405 }
  )
}

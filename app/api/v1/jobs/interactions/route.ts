// app/api/v1/jobs/interactions/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

interface JobInteraction {
  jobId: string
  type: 'like' | 'bookmark' | 'share' | 'view' | 'apply'
  timestamp: Date
  metadata?: Record<string, unknown>
}

// POST /api/v1/jobs/interactions - Track job interaction
export async function POST(request: NextRequest) {
  try {
    await connectDB()
    
    const interaction: JobInteraction = await request.json()
    
    // Validate interaction data
    if (!interaction.jobId || !interaction.type) {
      return NextResponse.json(
        { error: 'Job ID and interaction type are required' },
        { status: 400 }
      )
    }
    
    // For now, just log the interaction
    // In a real app, you'd store this in a database
    console.log('Job interaction tracked:', {
      jobId: interaction.jobId,
      type: interaction.type,
      timestamp: interaction.timestamp || new Date(),
      metadata: interaction.metadata
    })
    
    // You could also update job statistics here
    // For example, increment view count, like count, etc.
    
    return NextResponse.json({ success: true })
    
  } catch (error) {
    console.error('Error tracking job interaction:', error)
    return NextResponse.json(
      { error: 'Failed to track interaction' },
      { status: 500 }
    )
  }
}

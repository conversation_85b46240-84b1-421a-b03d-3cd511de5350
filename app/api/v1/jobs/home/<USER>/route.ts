// app/api/v1/jobs/home/<USER>/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { Job } from '@/lib/models/job.model'
import { Company } from '@/lib/models/company.model'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

interface LocationData {
  city: string
  region: string
  country: string
  continent: string
  coordinates?: {
    latitude: number
    longitude: number
  }
}

interface SearchQuery {
  searchTerm?: string
  locationTerm?: string
  userLocation?: LocationData
  locationLevel?: 'local' | 'regional' | 'national' | 'continental' | 'international'
  maxDistance?: number
  jobTypes?: string[]
  experienceLevels?: string[]
  salaryRange?: { min?: number; max?: number }
  workModels?: string[]
  categories?: string[]
  skills?: string[]
  companies?: string[]
  page?: number
  limit?: number
  sortBy?: 'relevance' | 'date' | 'salary' | 'distance'
  sortOrder?: 'asc' | 'desc'
  featuredOnly?: boolean
  urgentOnly?: boolean
  remoteOnly?: boolean
  recentOnly?: boolean
}

function parseSearchQuery(searchParams: URLSearchParams): SearchQuery {
  const query: SearchQuery = {}
  
  // Basic search terms
  if (searchParams.get('searchTerm')) query.searchTerm = searchParams.get('searchTerm')!
  if (searchParams.get('locationTerm')) query.locationTerm = searchParams.get('locationTerm')!
  
  // User location
  if (searchParams.get('userLocation')) {
    try {
      query.userLocation = JSON.parse(searchParams.get('userLocation')!)
    } catch (e) {
      console.warn('Invalid userLocation JSON')
    }
  }
  
  // Location level
  if (searchParams.get('locationLevel')) {
    query.locationLevel = searchParams.get('locationLevel') as any
  }
  
  // Distance
  if (searchParams.get('maxDistance')) {
    query.maxDistance = parseInt(searchParams.get('maxDistance')!)
  }
  
  // Arrays
  if (searchParams.getAll('jobTypes').length) query.jobTypes = searchParams.getAll('jobTypes')
  if (searchParams.getAll('experienceLevels').length) query.experienceLevels = searchParams.getAll('experienceLevels')
  if (searchParams.getAll('workModels').length) query.workModels = searchParams.getAll('workModels')
  if (searchParams.getAll('categories').length) query.categories = searchParams.getAll('categories')
  if (searchParams.getAll('skills').length) query.skills = searchParams.getAll('skills')
  if (searchParams.getAll('companies').length) query.companies = searchParams.getAll('companies')
  
  // Salary range
  if (searchParams.get('salaryMin') || searchParams.get('salaryMax')) {
    query.salaryRange = {
      min: searchParams.get('salaryMin') ? parseInt(searchParams.get('salaryMin')!) : undefined,
      max: searchParams.get('salaryMax') ? parseInt(searchParams.get('salaryMax')!) : undefined
    }
  }
  
  // Pagination
  query.page = parseInt(searchParams.get('page') || '1')
  query.limit = parseInt(searchParams.get('limit') || '10')
  
  // Sorting
  query.sortBy = (searchParams.get('sortBy') as any) || 'relevance'
  query.sortOrder = (searchParams.get('sortOrder') as any) || 'desc'
  
  // Flags
  query.featuredOnly = searchParams.get('featuredOnly') === 'true'
  query.urgentOnly = searchParams.get('urgentOnly') === 'true'
  query.remoteOnly = searchParams.get('remoteOnly') === 'true'
  query.recentOnly = searchParams.get('recentOnly') === 'true'
  
  return query
}

function buildLocationFilter(query: SearchQuery) {
  const locationFilter: any = {}
  
  if (!query.userLocation) return locationFilter
  
  const { userLocation, locationLevel } = query
  
  switch (locationLevel) {
    case 'local':
      locationFilter.$or = [
        { 'location.city': { $regex: userLocation.city, $options: 'i' } },
        { 'location.remote': true }
      ]
      break
      
    case 'regional':
      locationFilter.$or = [
        { 'location.state': { $regex: userLocation.region, $options: 'i' } },
        { 'location.remote': true }
      ]
      break
      
    case 'national':
      locationFilter.$or = [
        { 'location.country': { $regex: userLocation.country, $options: 'i' } },
        { 'location.remote': true }
      ]
      break
      
    case 'continental':
      // For continental, we'd need continent data in job location
      // For now, use country-based filtering
      locationFilter.$or = [
        { 'location.country': { $regex: userLocation.country, $options: 'i' } },
        { 'location.remote': true }
      ]
      break
      
    case 'international':
      locationFilter.$or = [
        { 'location.remote': true },
        { 'location.country': { $ne: userLocation.country } }
      ]
      break
  }
  
  return locationFilter
}

function buildSearchFilter(query: SearchQuery) {
  const searchFilter: any = {}

  if (query.searchTerm) {
    const searchRegex = { $regex: query.searchTerm, $options: 'i' }
    searchFilter.$or = [
      { title: searchRegex },
      { description: searchRegex },
      { tags: { $elemMatch: { $regex: query.searchTerm, $options: 'i' } } }, // Fixed: proper array search
      { category: searchRegex },
      { department: searchRegex }
    ]
  }
  
  if (query.locationTerm) {
    const locationRegex = { $regex: query.locationTerm, $options: 'i' }
    const locationConditions = [
      { 'location.city': locationRegex },
      { 'location.state': locationRegex },
      { 'location.country': locationRegex }
    ]
    
    if (searchFilter.$or) {
      searchFilter.$and = [
        { $or: searchFilter.$or },
        { $or: locationConditions }
      ]
      delete searchFilter.$or
    } else {
      searchFilter.$or = locationConditions
    }
  }
  
  return searchFilter
}

function buildFilters(query: SearchQuery) {
  const filters: any = { isActive: true }
  
  // Job types
  if (query.jobTypes?.length) {
    filters.type = { $in: query.jobTypes }
  }
  
  // Experience levels
  if (query.experienceLevels?.length) {
    filters.level = { $in: query.experienceLevels }
  }
  
  // Work models
  if (query.workModels?.length) {
    const workModelConditions = []
    if (query.workModels.includes('remote')) workModelConditions.push({ 'location.remote': true })
    if (query.workModels.includes('hybrid')) workModelConditions.push({ 'location.hybrid': true })
    if (query.workModels.includes('onsite')) {
      workModelConditions.push({ 
        'location.remote': { $ne: true }, 
        'location.hybrid': { $ne: true } 
      })
    }
    if (workModelConditions.length) {
      filters.$or = workModelConditions
    }
  }
  
  // Categories
  if (query.categories?.length) {
    filters.category = { $in: query.categories }
  }
  
  // Skills
  if (query.skills?.length) {
    filters.tags = { $in: query.skills }
  }
  
  // Salary range
  if (query.salaryRange) {
    const salaryConditions: any = {}
    if (query.salaryRange.min) {
      salaryConditions['salary.min'] = { $gte: query.salaryRange.min }
    }
    if (query.salaryRange.max) {
      salaryConditions['salary.max'] = { $lte: query.salaryRange.max }
    }
    Object.assign(filters, salaryConditions)
  }
  
  // Special flags
  if (query.featuredOnly) filters.isFeatured = true
  if (query.urgentOnly) filters.isUrgent = true
  if (query.remoteOnly) filters['location.remote'] = true
  
  // Recent jobs (last 7 days)
  if (query.recentOnly) {
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    filters.createdAt = { $gte: sevenDaysAgo }
  }
  
  return filters
}

function buildSortOptions(query: SearchQuery) {
  const { sortBy = 'relevance', sortOrder = 'desc' } = query
  const sortDirection = sortOrder === 'asc' ? 1 : -1
  
  switch (sortBy) {
    case 'date':
      return { createdAt: sortDirection }
    case 'salary':
      return { 'salary.max': sortDirection }
    case 'relevance':
    default:
      // For relevance, we'll use a combination of factors
      return { 
        isFeatured: -1, 
        isUrgent: -1, 
        createdAt: -1,
        viewsCount: -1 
      }
  }
}

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    const { searchParams } = new URL(request.url)
    const query = parseSearchQuery(searchParams)
    
    // Build MongoDB aggregation pipeline
    const pipeline: any[] = []
    
    // Match stage - combine all filters
    const matchStage: any = {
      ...buildFilters(query),
      ...buildLocationFilter(query),
      ...buildSearchFilter(query)
    }
    
    pipeline.push({ $match: matchStage })
    
    // Lookup company data
    pipeline.push({
      $lookup: {
        from: 'companies',
        localField: 'companyId',
        foreignField: '_id',
        as: 'company'
      }
    })
    
    pipeline.push({
      $unwind: {
        path: '$company',
        preserveNullAndEmptyArrays: true
      }
    })
    
    // Add relevance scoring for search
    if (query.searchTerm || query.locationTerm) {
      pipeline.push({
        $addFields: {
          relevanceScore: {
            $add: [
              // Title match gets highest score
              { $cond: [
                { $regexMatch: { input: '$title', regex: query.searchTerm || '', options: 'i' } },
                10, 0
              ]},
              // Company name match
              { $cond: [
                { $regexMatch: { input: '$company.name', regex: query.searchTerm || '', options: 'i' } },
                5, 0
              ]},
              // Skills/tags match
              { $cond: [
                { $anyElementTrue: {
                  $map: {
                    input: '$tags',
                    as: 'tag',
                    in: { $regexMatch: { input: '$$tag', regex: query.searchTerm || '', options: 'i' } }
                  }
                }},
                8, 0
              ]},
              // Category match
              { $cond: [
                { $regexMatch: { input: '$category', regex: query.searchTerm || '', options: 'i' } },
                6, 0
              ]},
              // Featured bonus
              { $cond: ['$isFeatured', 3, 0] },
              // Urgent bonus
              { $cond: ['$isUrgent', 2, 0] }
            ]
          }
        }
      })
    }
    
    // Sort
    const sortOptions = buildSortOptions(query)
    if (query.searchTerm || query.locationTerm) {
      pipeline.push({ $sort: { relevanceScore: -1, ...sortOptions } })
    } else {
      pipeline.push({ $sort: sortOptions })
    }
    
    // Pagination
    const skip = (query.page! - 1) * query.limit!
    pipeline.push({ $skip: skip })
    pipeline.push({ $limit: query.limit! })
    
    // Project final fields
    pipeline.push({
      $project: {
        _id: 1,
        title: 1,
        description: 1,
        location: 1,
        salary: 1,
        type: 1,
        level: 1,
        category: 1,
        department: 1,
        tags: 1,
        requirements: 1,
        responsibilities: 1,
        benefits: 1,
        isFeatured: 1,
        isUrgent: 1,
        isActive: 1,
        applicationDeadline: 1,
        createdAt: 1,
        updatedAt: 1,
        applicationsCount: 1,
        viewsCount: 1,
        maxApplicants: 1,
        company: {
          _id: 1,
          name: 1,
          logo: 1,
          industry: 1,
          size: 1,
          verification: 1
        },
        relevanceScore: 1
      }
    })
    
    // Execute aggregation
    const [jobs, totalCount] = await Promise.all([
      Job.aggregate(pipeline),
      Job.countDocuments(matchStage)
    ])
    
    // Calculate pagination
    const totalPages = Math.ceil(totalCount / query.limit!)
    
    // Get location stats
    const locationStats = await getLocationStats(query.userLocation)
    
    const response = {
      jobs,
      pagination: {
        page: query.page!,
        limit: query.limit!,
        total: totalCount,
        totalPages
      },
      locationStats,
      searchMeta: {
        searchTime: Date.now(),
        totalResults: totalCount,
        query: [query.searchTerm, query.locationTerm].filter(Boolean).join(' in '),
        appliedFilters: getAppliedFilters(query)
      }
    }
    
    return NextResponse.json(response)
    
  } catch (error) {
    console.error('Error in home jobs search:', error)
    return NextResponse.json(
      { error: 'Failed to search jobs' },
      { status: 500 }
    )
  }
}

async function getLocationStats(userLocation?: LocationData) {
  if (!userLocation) {
    return { local: 0, regional: 0, national: 0, continental: 0, international: 0 }
  }
  
  const [local, regional, national, continental, international] = await Promise.all([
    Job.countDocuments({
      isActive: true,
      $or: [
        { 'location.city': { $regex: userLocation.city, $options: 'i' } },
        { 'location.remote': true }
      ]
    }),
    Job.countDocuments({
      isActive: true,
      $or: [
        { 'location.state': { $regex: userLocation.region, $options: 'i' } },
        { 'location.remote': true }
      ]
    }),
    Job.countDocuments({
      isActive: true,
      $or: [
        { 'location.country': { $regex: userLocation.country, $options: 'i' } },
        { 'location.remote': true }
      ]
    }),
    Job.countDocuments({
      isActive: true,
      $or: [
        { 'location.country': { $regex: userLocation.country, $options: 'i' } },
        { 'location.remote': true }
      ]
    }),
    Job.countDocuments({
      isActive: true,
      $or: [
        { 'location.remote': true },
        { 'location.country': { $ne: userLocation.country } }
      ]
    })
  ])
  
  return { local, regional, national, continental, international }
}

function getAppliedFilters(query: SearchQuery): string[] {
  const filters: string[] = []
  
  if (query.jobTypes?.length) filters.push(`Job Types: ${query.jobTypes.join(', ')}`)
  if (query.experienceLevels?.length) filters.push(`Experience: ${query.experienceLevels.join(', ')}`)
  if (query.workModels?.length) filters.push(`Work Model: ${query.workModels.join(', ')}`)
  if (query.categories?.length) filters.push(`Categories: ${query.categories.join(', ')}`)
  if (query.salaryRange) {
    const { min, max } = query.salaryRange
    if (min && max) filters.push(`Salary: $${min.toLocaleString()} - $${max.toLocaleString()}`)
    else if (min) filters.push(`Salary: $${min.toLocaleString()}+`)
    else if (max) filters.push(`Salary: Up to $${max.toLocaleString()}`)
  }
  if (query.featuredOnly) filters.push('Featured Only')
  if (query.urgentOnly) filters.push('Urgent Only')
  if (query.remoteOnly) filters.push('Remote Only')
  if (query.recentOnly) filters.push('Recent (7 days)')
  
  return filters
}

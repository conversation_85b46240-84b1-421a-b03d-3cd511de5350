// app/api/v1/jobs/home/<USER>/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { Job } from '@/lib/models/job.model'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

interface LocationData {
  city: string
  region: string
  country: string
  continent: string
  coordinates?: {
    latitude: number
    longitude: number
  }
}

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    const { searchParams } = new URL(request.url)
    let userLocation: LocationData | null = null
    
    if (searchParams.get('userLocation')) {
      try {
        userLocation = JSON.parse(searchParams.get('userLocation')!)
      } catch (e) {
        console.warn('Invalid userLocation JSON')
      }
    }
    
    if (!userLocation) {
      // Return global stats if no location provided
      const total = await Job.countDocuments({ isActive: true })
      return NextResponse.json({
        local: 0,
        regional: 0,
        national: 0,
        continental: 0,
        international: total
      })
    }
    
    // Get counts for each location level
    const [local, regional, national, continental, international] = await Promise.all([
      // Local: City-based jobs + remote
      Job.countDocuments({
        isActive: true,
        $or: [
          { 'location.city': { $regex: userLocation.city, $options: 'i' } },
          { 'location.remote': true }
        ]
      }),
      
      // Regional: State/Region-based jobs + remote
      Job.countDocuments({
        isActive: true,
        $or: [
          { 'location.state': { $regex: userLocation.region, $options: 'i' } },
          { 'location.remote': true }
        ]
      }),
      
      // National: Country-based jobs + remote
      Job.countDocuments({
        isActive: true,
        $or: [
          { 'location.country': { $regex: userLocation.country, $options: 'i' } },
          { 'location.remote': true }
        ]
      }),
      
      // Continental: Same country for now (would need continent data)
      Job.countDocuments({
        isActive: true,
        $or: [
          { 'location.country': { $regex: userLocation.country, $options: 'i' } },
          { 'location.remote': true }
        ]
      }),
      
      // International: Remote jobs + jobs outside user's country
      Job.countDocuments({
        isActive: true,
        $or: [
          { 'location.remote': true },
          { 'location.country': { $ne: userLocation.country } }
        ]
      })
    ])
    
    return NextResponse.json({
      local,
      regional,
      national,
      continental,
      international
    })
    
  } catch (error) {
    console.error('Error getting location stats:', error)
    return NextResponse.json(
      { error: 'Failed to get location statistics' },
      { status: 500 }
    )
  }
}

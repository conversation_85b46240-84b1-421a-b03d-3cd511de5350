import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { connectDB } from '@/lib/db'
import { User } from '@/lib/models/user.model'
import { Company } from '@/lib/models/company.model'
import { Job } from '@/lib/models/job.model'
import type { SimpleCreateJobRequest } from '@/types/job.types'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

// Validation function for create job request
function validateCreateJobRequest(data: Record<string, unknown>): SimpleCreateJobRequest {
  const errors: string[] = []

  // Required fields
  if (!data.title || typeof data.title !== 'string') errors.push('Job title is required')
  if (!data.description || typeof data.description !== 'string') errors.push('Job description is required')
  if (!data.type || typeof data.type !== 'string') errors.push('Job type is required')
  if (!data.level || typeof data.level !== 'string') errors.push('Job level is required')
  if (!data.category || typeof data.category !== 'string') errors.push('Job category is required')

  // Validate enums
  if (data.type && !['full-time', 'part-time', 'contract', 'internship'].includes(data.type as string)) {
    errors.push('Invalid job type')
  }

  if (data.level && !['entry', 'mid', 'senior', 'executive'].includes(data.level as string)) {
    errors.push('Invalid job level')
  }

  // Validate requirements array
  if (!data.requirements || !Array.isArray(data.requirements) || data.requirements.length === 0) {
    errors.push('At least one requirement is needed. Please add job requirements.')
  }

  // Validate salary if provided
  if (data.salary && typeof data.salary === 'object') {
    const salary = data.salary as Record<string, unknown>
    if (salary.min && salary.max && Number(salary.min) > Number(salary.max)) {
      errors.push('Minimum salary cannot be greater than maximum salary')
    }
  }

  // Validate application deadline if provided
  if (data.applicationDeadline) {
    const deadline = new Date(data.applicationDeadline as string)
    if (deadline <= new Date()) {
      errors.push('Application deadline must be in the future')
    }
  }

  if (errors.length > 0) {
    throw new Error(`Validation failed: ${errors.join(', ')}`)
  }

  return {
    title: (data.title as string).trim(),
    description: (data.description as string).trim(),
    location: (data.location as SimpleCreateJobRequest['location']) || {},
    salary: data.salary as SimpleCreateJobRequest['salary'],
    requirements: (data.requirements as string[]).map((req: string) => req.trim()),
    benefits: (data.benefits as string[])?.map((benefit: string) => benefit.trim()),
    type: data.type as SimpleCreateJobRequest['type'],
    level: data.level as SimpleCreateJobRequest['level'],
    category: (data.category as string).trim(),
    tags: (data.tags as string[])?.map((tag: string) => tag.trim().toLowerCase()) || [],
    applicationDeadline: data.applicationDeadline ? new Date(data.applicationDeadline as string) : undefined,
    status: (data.status as string) || 'active'
  }
}

// GET /api/v1/jobs - Search and filter jobs
export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const { searchParams } = new URL(request.url)

    // Pagination
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    // Search term
    const searchTerm = searchParams.get('search') || ''

    // Build query
    const query: Record<string, unknown> = { isActive: true }

    // Search in title and description
    if (searchTerm) {
      query.$or = [
        { title: { $regex: searchTerm, $options: 'i' } },
        { description: { $regex: searchTerm, $options: 'i' } }
      ]
    }

    // Filters
    if (searchParams.get('location')) {
      query['location.city'] = { $regex: searchParams.get('location'), $options: 'i' }
    }
    if (searchParams.get('remote')) {
      query['location.remote'] = searchParams.get('remote') === 'true'
    }
    if (searchParams.get('type')) {
      query.type = searchParams.get('type')
    }
    if (searchParams.get('level')) {
      query.level = searchParams.get('level')
    }
    if (searchParams.get('category')) {
      query.category = searchParams.get('category')
    }
    if (searchParams.get('companyId')) {
      query.companyId = searchParams.get('companyId')
    }

    // Salary range
    if (searchParams.get('salaryMin') || searchParams.get('salaryMax')) {
      query['salary.min'] = {}
      if (searchParams.get('salaryMin')) {
        query['salary.min'].$gte = parseInt(searchParams.get('salaryMin')!)
      }
      if (searchParams.get('salaryMax')) {
        query['salary.max'].$lte = parseInt(searchParams.get('salaryMax')!)
      }
    }

    // Tags
    const tagsParam = searchParams.get('tags')
    if (tagsParam) {
      const tags = tagsParam.split(',').map(tag => tag.trim().toLowerCase())
      query.tags = { $in: tags }
    }

    // Execute query
    const [jobs, total] = await Promise.all([
      Job.find(query)
        .populate('companyId', 'name slug logo verification')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Job.countDocuments(query)
    ])

    return NextResponse.json({
      success: true,
      data: {
        jobs,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    }, { status: 200 })

  } catch (error) {
    console.error('Jobs GET error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch jobs'
      },
      { status: 500 }
    )
  }
}

// POST /api/v1/jobs - Create a new job
export async function POST(request: NextRequest) {
  try {
    await connectDB()

    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error
        },
        { status: authResult.status || 401 }
      )
    }

    const userId = authResult.user?.id
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: 'User ID not found in token'
        },
        { status: 401 }
      )
    }

    // Find user and their company
    const user = await User.findById(userId)
    if (!user || !user.companyId) {
      return NextResponse.json(
        {
          success: false,
          error: 'No company associated with user'
        },
        { status: 400 }
      )
    }

    // Verify user has permission to create jobs for this company
    const company = await Company.findById(user.companyId)
    if (!company) {
      return NextResponse.json(
        {
          success: false,
          error: 'Company not found'
        },
        { status: 404 }
      )
    }

    // Check if user is admin or recruiter for this company
    const isAdmin = company.admins.some((adminId: unknown) => adminId?.toString() === userId)
    const isRecruiter = company.recruiters.some((recruiterId: unknown) => recruiterId?.toString() === userId)

    if (!isAdmin && !isRecruiter) {
      return NextResponse.json(
        {
          success: false,
          error: 'Insufficient permissions to create jobs for this company'
        },
        { status: 403 }
      )
    }

    // Get and validate job data
    const jobData = await request.json()
    console.log('🔍 Received job data:', JSON.stringify(jobData, null, 2))
    const validatedData = validateCreateJobRequest(jobData)

    // Create job with company association
    const job = new Job({
      ...validatedData,
      companyId: company._id,
      createdBy: userId,
      status: 'active',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    })

    await job.save()

    // Update company stats
    await Company.findByIdAndUpdate(company._id, {
      $inc: {
        'stats.totalJobs': 1,
        'stats.activeJobs': 1,
        'subscription.jobPostingsUsed': 1
      }
    })

    // Populate company data for response
    const populatedJob = await Job.findById(job._id)
      .populate('companyId', 'name slug logo verification')
      .populate('createdBy', 'profile.firstName profile.lastName email')
      .lean()

    return NextResponse.json({
      success: true,
      data: {
        job: populatedJob
      },
      message: 'Job created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Create job error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create job'
      },
      { status: 500 }
    )
  }
}

// Method not allowed for other HTTP methods
export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      error: 'PUT method not allowed for jobs collection'
    },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      error: 'DELETE method not allowed for jobs collection'
    },
    { status: 405 }
  )
}

import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const type = searchParams.get('type') // 'categories' or 'questions'
    const format = searchParams.get('format') || 'csv' // 'csv' or 'json'
    
    if (type === 'categories') {
      return generateCategoryTemplate(format)
    } else if (type === 'questions') {
      return generateQuestionTemplate(format)
    } else {
      return NextResponse.json(
        { success: false, error: 'Type parameter is required (categories or questions)' },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Error generating template:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to generate template' },
      { status: 500 }
    )
  }
}

function generateCategoryTemplate(format: string) {
  const sampleData = [
    {
      name: 'JavaScript Fundamentals',
      description: 'Test your knowledge of JavaScript basics including variables, functions, and control structures.',
      icon: '🟨',
      color: 'bg-yellow-500',
      skills: 'JavaScript, ES6, Functions, Variables',
      difficulty: 'beginner',
      estimatedTime: 15,
      totalQuestions: 20,
      passingScore: 70,
      isActive: true
    },
    {
      name: 'React Advanced',
      description: 'Advanced React concepts including hooks, context, and performance optimization.',
      icon: '⚛️',
      color: 'bg-blue-500',
      skills: 'React, Hooks, Context, Performance',
      difficulty: 'advanced',
      estimatedTime: 30,
      totalQuestions: 25,
      passingScore: 80,
      isActive: true
    }
  ]

  if (format === 'json') {
    return NextResponse.json({
      success: true,
      data: {
        template: sampleData,
        instructions: {
          fields: {
            name: 'Required. Unique category name',
            description: 'Required. Category description',
            icon: 'Optional. Emoji icon (default: 📚)',
            color: 'Optional. Tailwind color class (default: bg-blue-500)',
            skills: 'Optional. Comma-separated list of skills',
            difficulty: 'Required. One of: beginner, intermediate, advanced, expert',
            estimatedTime: 'Optional. Time in minutes (1-120, default: 15)',
            totalQuestions: 'Optional. Number of questions (1-100, default: 10)',
            passingScore: 'Optional. Percentage (50-100, default: 70)',
            isActive: 'Optional. Boolean (default: true)'
          },
          usage: 'Send this data as JSON array in POST request to /api/v1/quiz-admin/categories/bulk'
        }
      }
    })
  } else {
    const csv = convertToCSV(sampleData)
    const csvWithInstructions = `# Quiz Categories Template
# Instructions:
# - name: Required. Unique category name
# - description: Required. Category description  
# - icon: Optional. Emoji icon (default: 📚)
# - color: Optional. Tailwind color class (default: bg-blue-500)
# - skills: Optional. Comma-separated list of skills
# - difficulty: Required. One of: beginner, intermediate, advanced, expert
# - estimatedTime: Optional. Time in minutes (1-120, default: 15)
# - totalQuestions: Optional. Number of questions (1-100, default: 10)
# - passingScore: Optional. Percentage (50-100, default: 70)
# - isActive: Optional. Boolean (default: true)
#
# Remove these instruction lines before uploading
#
${csv}`

    return new NextResponse(csvWithInstructions, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename="quiz-categories-template.csv"'
      }
    })
  }
}

function generateQuestionTemplate(format: string) {
  const sampleData = [
    {
      categoryName: 'JavaScript Fundamentals',
      question: 'What is the difference between let and var in JavaScript?',
      option1: 'let has block scope, var has function scope',
      option2: 'var has block scope, let has function scope',
      option3: 'They are exactly the same',
      option4: 'let is older than var',
      option5: '',
      option6: '',
      correctAnswer: 1,
      explanation: 'let has block scope and temporal dead zone, while var has function scope and is hoisted.',
      difficulty: 'beginner',
      points: 5,
      tags: 'JavaScript, Variables, Scope',
      isActive: true
    },
    {
      categoryName: 'JavaScript Fundamentals',
      question: 'What is a closure in JavaScript?',
      option1: 'A way to close functions',
      option2: 'A function that has access to variables in its outer scope',
      option3: 'A method to end loops',
      option4: 'A type of object',
      option5: '',
      option6: '',
      correctAnswer: 2,
      explanation: 'A closure is a function that retains access to variables from its outer scope even after the outer function has returned.',
      difficulty: 'intermediate',
      points: 10,
      tags: 'JavaScript, Closures, Functions',
      isActive: true
    }
  ]

  if (format === 'json') {
    return NextResponse.json({
      success: true,
      data: {
        template: sampleData,
        instructions: {
          fields: {
            categoryName: 'Required. Name of existing category (or use categoryId)',
            categoryId: 'Alternative to categoryName. MongoDB ObjectId of category',
            question: 'Required. The question text',
            option1: 'Required. First answer option',
            option2: 'Required. Second answer option',
            option3: 'Optional. Third answer option',
            option4: 'Optional. Fourth answer option',
            option5: 'Optional. Fifth answer option',
            option6: 'Optional. Sixth answer option',
            correctAnswer: 'Required. Number of correct option (1-based index)',
            explanation: 'Optional. Explanation of correct answer',
            difficulty: 'Required. One of: beginner, intermediate, advanced, expert',
            points: 'Optional. Points for question (1-20, auto-calculated if not provided)',
            tags: 'Optional. Comma-separated list of tags',
            isActive: 'Optional. Boolean (default: true)'
          },
          usage: 'Send this data as JSON array in POST request to /api/v1/quiz-admin/questions/bulk'
        }
      }
    })
  } else {
    const csv = convertToCSV(sampleData)
    const csvWithInstructions = `# Quiz Questions Template
# Instructions:
# - categoryName: Required. Name of existing category (or use categoryId)
# - categoryId: Alternative to categoryName. MongoDB ObjectId of category
# - question: Required. The question text
# - option1: Required. First answer option
# - option2: Required. Second answer option  
# - option3-6: Optional. Additional answer options
# - correctAnswer: Required. Number of correct option (1-based index)
# - explanation: Optional. Explanation of correct answer
# - difficulty: Required. One of: beginner, intermediate, advanced, expert
# - points: Optional. Points for question (1-20, auto-calculated if not provided)
# - tags: Optional. Comma-separated list of tags
# - isActive: Optional. Boolean (default: true)
#
# Remove these instruction lines before uploading
#
${csv}`

    return new NextResponse(csvWithInstructions, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename="quiz-questions-template.csv"'
      }
    })
  }
}

function convertToCSV(data: any[]): string {
  if (data.length === 0) return ''
  
  const headers = Object.keys(data[0])
  const csvHeaders = headers.join(',')
  
  const csvRows = data.map(row => 
    headers.map(header => {
      const value = row[header]
      // Escape commas and quotes in CSV
      if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
        return `"${value.replace(/"/g, '""')}"`
      }
      return value
    }).join(',')
  )
  
  return [csvHeaders, ...csvRows].join('\n')
}

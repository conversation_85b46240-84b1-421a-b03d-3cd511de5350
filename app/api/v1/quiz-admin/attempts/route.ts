import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { QuizAttempt, QuizCategory } from '@/lib/models/quiz.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    const searchParams = request.nextUrl.searchParams
    const categoryId = searchParams.get('categoryId')
    const timeRange = searchParams.get('timeRange')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = (page - 1) * limit

    // Build query
    const query: any = {}
    if (categoryId && categoryId !== 'all') {
      query.categoryId = categoryId
    }

    // Add time range filter
    if (timeRange && timeRange !== 'all') {
      const now = new Date()
      let startDate: Date
      
      switch (timeRange) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          break
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        default:
          startDate = new Date(0) // Beginning of time
      }
      
      query.completedAt = { $gte: startDate }
    }

    const attempts = await QuizAttempt.find(query)
      .sort({ completedAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean()

    const total = await QuizAttempt.countDocuments(query)

    // Get category information for each attempt
    const categoryIds = [...new Set(attempts.map(a => a.categoryId.toString()))]
    const categories = categoryIds.length > 0
      ? await QuizCategory.find({ _id: { $in: categoryIds } }).lean()
      : []
    const categoryMap = categories.reduce((acc, cat) => {
      acc[cat._id.toString()] = cat
      return acc
    }, {} as any)

    // Enhance attempts with category info
    const attemptsWithDetails = attempts.map(attempt => {
      const category = categoryMap[attempt.categoryId.toString()]
      return {
        ...attempt,
        id: attempt._id.toString(),
        categoryName: category?.name || 'Unknown',
        categoryIcon: category?.icon || '📚'
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        attempts: attemptsWithDetails,
        total,
        page,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching attempts:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch attempts' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await connectDB()
    
    const searchParams = request.nextUrl.searchParams
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Attempt ID is required' },
        { status: 400 }
      )
    }

    const attempt = await QuizAttempt.findByIdAndDelete(id)

    if (!attempt) {
      return NextResponse.json(
        { success: false, error: 'Attempt not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Attempt deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting attempt:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete attempt' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { AwardTemplate, UserAward } from '@/lib/models/award.model'
import { QuizCategory, QuizAttempt } from '@/lib/models/quiz.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    const searchParams = request.nextUrl.searchParams
    const categoryId = searchParams.get('categoryId')
    const rarity = searchParams.get('rarity')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = (page - 1) * limit

    // Build query
    const query: any = {}
    if (categoryId && categoryId !== 'all') {
      query.categoryId = categoryId
    }
    if (rarity && rarity !== 'all') {
      query.rarity = rarity
    }

    const awards = await AwardTemplate.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean()

    const total = await AwardTemplate.countDocuments(query)

    // Get category information for each award
    const categoryIds = [...new Set(awards.map(a => a.categoryId.toString()))]
    const categories = categoryIds.length > 0
      ? await QuizCategory.find({ _id: { $in: categoryIds } }).lean()
      : []
    const categoryMap = categories.reduce((acc, cat) => {
      acc[cat._id.toString()] = cat
      return acc
    }, {} as any)

    // Enhance awards with category info and real stats
    const awardsWithDetails = await Promise.all(
      awards.map(async (award) => {
        const category = categoryMap[award.categoryId.toString()]

        // Get real statistics for this award
        const userAwards = await UserAward.find({
          awardTemplateId: award._id
        }).lean()

        const timesEarned = userAwards.length

        // Calculate average score of earners
        const averageScoreOfEarners = timesEarned > 0
          ? Math.round((userAwards.reduce((sum, userAward) => sum + userAward.score, 0) / timesEarned) * 10) / 10
          : 0

        return {
          ...award,
          id: award._id.toString(),
          categoryName: category?.name || 'Unknown',
          categoryIcon: category?.icon || '📚',
          timesEarned,
          averageScoreOfEarners
        }
      })
    )

    return NextResponse.json({
      success: true,
      data: {
        awards: awardsWithDetails,
        total,
        page,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching awards:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch awards' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()
    
    const body = await request.json()
    const {
      name,
      description,
      categoryId,
      badgeIcon,
      badgeColor,
      minimumScore,
      rarity,
      benefits,
      certificateTemplate
    } = body

    // Validate required fields
    if (!name || !description || !categoryId || !minimumScore || !rarity) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Verify category exists
    const category = await QuizCategory.findById(categoryId)
    if (!category) {
      return NextResponse.json(
        { success: false, error: 'Category not found' },
        { status: 404 }
      )
    }

    // Generate slug from name
    const slug = name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')

    // Check if slug already exists for this category
    const existingAward = await AwardTemplate.findOne({ slug, categoryId })
    if (existingAward) {
      return NextResponse.json(
        { success: false, error: 'Award with this name already exists for this category' },
        { status: 400 }
      )
    }

    const award = new AwardTemplate({
      name,
      slug,
      description,
      categoryId,
      badgeIcon: badgeIcon || '🏆',
      badgeColor: badgeColor || '#3B82F6',
      minimumScore,
      rarity,
      benefits: Array.isArray(benefits) ? benefits : [],
      certificateTemplate: certificateTemplate || '',
      isActive: true
    })

    await award.save()

    return NextResponse.json({
      success: true,
      data: {
        award: {
          ...award.toObject(),
          id: award._id.toString()
        }
      }
    }, { status: 201 })
  } catch (error) {
    console.error('Error creating award:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create award' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    await connectDB()
    
    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Award ID is required' },
        { status: 400 }
      )
    }

    // If name is being updated, regenerate slug
    if (updateData.name) {
      updateData.slug = updateData.name.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '')
    }

    const award = await AwardTemplate.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    )

    if (!award) {
      return NextResponse.json(
        { success: false, error: 'Award not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        award: {
          ...award.toObject(),
          id: award._id.toString()
        }
      }
    })
  } catch (error) {
    console.error('Error updating award:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update award' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await connectDB()
    
    const searchParams = request.nextUrl.searchParams
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Award ID is required' },
        { status: 400 }
      )
    }

    const award = await AwardTemplate.findByIdAndDelete(id)

    if (!award) {
      return NextResponse.json(
        { success: false, error: 'Award not found' },
        { status: 404 }
      )
    }

    // TODO: Also delete related user awards
    // await UserAward.deleteMany({ awardTemplateId: id })

    return NextResponse.json({
      success: true,
      message: 'Award deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting award:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete award' },
      { status: 500 }
    )
  }
}

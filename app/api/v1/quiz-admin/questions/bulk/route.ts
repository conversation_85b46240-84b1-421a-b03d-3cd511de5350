import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { QuizQuestion, QuizCategory } from '@/lib/models/quiz.model'
import { QuizValidator } from '@/lib/validation/quiz-validation'

export async function POST(request: NextRequest) {
  try {
    await connectDB()
    
    const body = await request.json()
    const { questions, mode = 'create', categoryId } = body // mode: 'create', 'update', 'upsert'

    if (!Array.isArray(questions) || questions.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Questions array is required and cannot be empty' },
        { status: 400 }
      )
    }

    const results = {
      created: 0,
      updated: 0,
      failed: 0,
      errors: [] as string[],
      warnings: [] as string[]
    }

    // Get all categories for validation
    const categories = await QuizCategory.find({}).lean()
    const categoryMap = new Map(categories.map(cat => [cat.name, cat._id.toString()]))

    // Validate all questions using enhanced validation
    const bulkValidation = QuizValidator.validateBulkData(questions, 'questions', categoryMap)

    if (!bulkValidation.isValid) {
      return NextResponse.json({
        success: false,
        error: 'Validation failed',
        data: {
          results: {
            ...results,
            failed: questions.length,
            errors: bulkValidation.errors,
            warnings: bulkValidation.warnings
          }
        }
      }, { status: 400 })
    }

    const validatedQuestions = bulkValidation.data || []
    results.warnings = bulkValidation.warnings

    // Process validated questions
    for (const questionData of validatedQuestions) {
      try {
        const existingQuestion = await QuizQuestion.findOne({ 
          question: questionData.question,
          categoryId: questionData.categoryId
        })

        if (existingQuestion) {
          if (mode === 'create') {
            results.failed++
            results.errors.push(`Question '${questionData.question.substring(0, 50)}...' already exists in this category`)
            continue
          } else if (mode === 'update' || mode === 'upsert') {
            await QuizQuestion.findByIdAndUpdate(existingQuestion._id, questionData, {
              new: true,
              runValidators: true
            })
            results.updated++
          }
        } else {
          if (mode === 'update') {
            results.failed++
            results.errors.push(`Question '${questionData.question.substring(0, 50)}...' not found for update`)
            continue
          } else {
            const newQuestion = new QuizQuestion(questionData)
            await newQuestion.save()
            results.created++
          }
        }
      } catch (error) {
        results.failed++
        results.errors.push(`Failed to process question '${questionData.question.substring(0, 50)}...': ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        results,
        summary: `Created: ${results.created}, Updated: ${results.updated}, Failed: ${results.failed}`,
        warnings: results.warnings.length > 0 ? results.warnings : undefined
      }
    }, { status: 200 })

  } catch (error) {
    console.error('Error in bulk question upload:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to process bulk question upload' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    const searchParams = request.nextUrl.searchParams
    const format = searchParams.get('format') || 'json' // json, csv
    const categoryId = searchParams.get('categoryId')
    
    // Build query
    const query: any = {}
    if (categoryId && categoryId !== 'all') {
      query.categoryId = categoryId
    }

    const questions = await QuizQuestion.find(query)
      .populate('categoryId', 'name')
      .sort({ createdAt: -1 })
      .lean()

    const exportData = questions.map(question => ({
      categoryName: (question.categoryId as any)?.name || 'Unknown',
      question: question.question,
      option1: question.options[0] || '',
      option2: question.options[1] || '',
      option3: question.options[2] || '',
      option4: question.options[3] || '',
      option5: question.options[4] || '',
      option6: question.options[5] || '',
      correctAnswer: question.correctAnswer + 1, // Convert to 1-based index
      explanation: question.explanation || '',
      difficulty: question.difficulty,
      points: question.points,
      tags: Array.isArray(question.tags) ? question.tags.join(', ') : question.tags,
      isActive: question.isActive
    }))

    if (format === 'csv') {
      const csv = convertToCSV(exportData)
      return new NextResponse(csv, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename="quiz-questions.csv"'
        }
      })
    }

    return NextResponse.json({
      success: true,
      data: {
        questions: exportData,
        count: exportData.length
      }
    }, { status: 200 })

  } catch (error) {
    console.error('Error exporting questions:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to export questions' },
      { status: 500 }
    )
  }
}

// Old validation function removed - now using QuizValidator

function convertToCSV(data: any[]): string {
  if (data.length === 0) return ''
  
  const headers = Object.keys(data[0])
  const csvHeaders = headers.join(',')
  
  const csvRows = data.map(row => 
    headers.map(header => {
      const value = row[header]
      // Escape commas and quotes in CSV
      if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
        return `"${value.replace(/"/g, '""')}"`
      }
      return value
    }).join(',')
  )
  
  return [csvHeaders, ...csvRows].join('\n')
}

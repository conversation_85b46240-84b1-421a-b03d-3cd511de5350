import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { QuizQuestion, QuizCategory, QuizAttempt } from '@/lib/models/quiz.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const searchParams = request.nextUrl.searchParams
    const categoryId = searchParams.get('categoryId')
    const difficulty = searchParams.get('difficulty')
    const includeStats = searchParams.get('includeStats') === 'true'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = (page - 1) * limit

    // Build query
    const query: any = {}
    if (categoryId && categoryId !== 'all') {
      query.categoryId = categoryId
    }
    if (difficulty && difficulty !== 'all') {
      query.difficulty = difficulty
    }

    const questions = await QuizQuestion.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean()

    const total = await QuizQuestion.countDocuments(query)

    // Get category information for each question
    const categoryIds = [...new Set(questions.map(q => q.categoryId.toString()))]
    const categories = categoryIds.length > 0
      ? await QuizCategory.find({ _id: { $in: categoryIds } }).lean()
      : []
    const categoryMap = categories.reduce((acc, cat) => {
      acc[cat._id.toString()] = cat
      return acc
    }, {} as any)

    // Enhance questions with category info and real stats
    const questionsWithDetails = await Promise.all(
      questions.map(async (question) => {
        const category = categoryMap[question.categoryId.toString()]
        let attempts = 0
        let correctRate = 0

        if (includeStats) {
          // Note: In a real implementation, you would need to track individual question
          // performance in quiz attempts. For now, we'll calculate based on category performance
          const categoryAttempts = await QuizAttempt.find({
            categoryId: question.categoryId
          }).lean()

          // Estimate question attempts based on category attempts and question count
          const categoryQuestionCount = await QuizQuestion.countDocuments({
            categoryId: question.categoryId,
            isActive: true
          })

          attempts = categoryQuestionCount > 0
            ? Math.floor(categoryAttempts.length / categoryQuestionCount)
            : 0

          // Estimate correct rate based on category average score and question difficulty
          const categoryAvgScore = categoryAttempts.length > 0
            ? categoryAttempts.reduce((sum, attempt) => sum + attempt.score, 0) / categoryAttempts.length
            : 0

          // Adjust based on difficulty (harder questions have lower correct rates)
          const difficultyMultipliers: Record<string, number> = {
            'beginner': 1.1,
            'intermediate': 1.0,
            'advanced': 0.9,
            'expert': 0.8
          }
          const difficultyMultiplier = difficultyMultipliers[question.difficulty] || 1.0

          correctRate = Math.round(categoryAvgScore * difficultyMultiplier * 10) / 10
        }

        return {
          ...question,
          id: question._id.toString(),
          categoryName: category?.name || 'Unknown',
          categoryIcon: category?.icon || '📚',
          attempts,
          correctRate
        }
      })
    )

    return NextResponse.json({
      success: true,
      data: {
        questions: questionsWithDetails,
        total,
        page,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching questions:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch questions' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()
    
    const body = await request.json()
    const {
      categoryId,
      question,
      options,
      correctAnswer,
      explanation,
      difficulty,
      points,
      tags
    } = body

    // Validate required fields
    if (!categoryId || !question || !options || correctAnswer === undefined || !difficulty) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate options array
    if (!Array.isArray(options) || options.length < 2) {
      return NextResponse.json(
        { success: false, error: 'At least 2 options are required' },
        { status: 400 }
      )
    }

    // Validate correct answer index
    if (correctAnswer < 0 || correctAnswer >= options.length) {
      return NextResponse.json(
        { success: false, error: 'Invalid correct answer index' },
        { status: 400 }
      )
    }

    // Verify category exists
    const category = await QuizCategory.findById(categoryId)
    if (!category) {
      return NextResponse.json(
        { success: false, error: 'Category not found' },
        { status: 404 }
      )
    }

    const newQuestion = new QuizQuestion({
      categoryId,
      question,
      options,
      correctAnswer,
      explanation: explanation || '',
      difficulty,
      points: points || (difficulty === 'beginner' ? 5 : difficulty === 'intermediate' ? 10 : 15),
      tags: Array.isArray(tags) ? tags : [],
      isActive: true
    })

    await newQuestion.save()

    return NextResponse.json({
      success: true,
      data: {
        question: {
          ...newQuestion.toObject(),
          id: newQuestion._id.toString()
        }
      }
    }, { status: 201 })
  } catch (error) {
    console.error('Error creating question:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create question' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    await connectDB()
    
    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Question ID is required' },
        { status: 400 }
      )
    }

    // Validate correct answer if provided
    if (updateData.correctAnswer !== undefined && updateData.options) {
      if (updateData.correctAnswer < 0 || updateData.correctAnswer >= updateData.options.length) {
        return NextResponse.json(
          { success: false, error: 'Invalid correct answer index' },
          { status: 400 }
        )
      }
    }

    const question = await QuizQuestion.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    )

    if (!question) {
      return NextResponse.json(
        { success: false, error: 'Question not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        question: {
          ...question.toObject(),
          id: question._id.toString()
        }
      }
    })
  } catch (error) {
    console.error('Error updating question:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update question' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await connectDB()
    
    const searchParams = request.nextUrl.searchParams
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Question ID is required' },
        { status: 400 }
      )
    }

    const question = await QuizQuestion.findByIdAndDelete(id)

    if (!question) {
      return NextResponse.json(
        { success: false, error: 'Question not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Question deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting question:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete question' },
      { status: 500 }
    )
  }
}

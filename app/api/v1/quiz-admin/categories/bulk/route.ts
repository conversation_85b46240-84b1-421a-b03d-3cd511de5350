import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { QuizCategory } from '@/lib/models/quiz.model'
import { QuizValidator } from '@/lib/validation/quiz-validation'

export async function POST(request: NextRequest) {
  try {
    await connectDB()
    
    const body = await request.json()
    const { categories, mode = 'create' } = body // mode: 'create', 'update', 'upsert'

    if (!Array.isArray(categories) || categories.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Categories array is required and cannot be empty' },
        { status: 400 }
      )
    }

    const results = {
      created: 0,
      updated: 0,
      failed: 0,
      errors: [] as string[],
      warnings: [] as string[]
    }

    // Validate all categories using enhanced validation
    const bulkValidation = QuizValidator.validateBulkData(categories, 'categories')

    if (!bulkValidation.isValid) {
      return NextResponse.json({
        success: false,
        error: 'Validation failed',
        data: {
          results: {
            ...results,
            failed: categories.length,
            errors: bulkValidation.errors,
            warnings: bulkValidation.warnings
          }
        }
      }, { status: 400 })
    }

    const validatedCategories = bulkValidation.data || []
    results.warnings = bulkValidation.warnings

    // Process validated categories
    for (const categoryData of validatedCategories) {
      try {
        const existingCategory = await QuizCategory.findOne({ 
          $or: [
            { slug: categoryData.slug },
            { name: categoryData.name }
          ]
        })

        if (existingCategory) {
          if (mode === 'create') {
            results.failed++
            results.errors.push(`Category '${categoryData.name}' already exists`)
            continue
          } else if (mode === 'update' || mode === 'upsert') {
            await QuizCategory.findByIdAndUpdate(existingCategory._id, categoryData, {
              new: true,
              runValidators: true
            })
            results.updated++
          }
        } else {
          if (mode === 'update') {
            results.failed++
            results.errors.push(`Category '${categoryData.name}' not found for update`)
            continue
          } else {
            const newCategory = new QuizCategory(categoryData)
            await newCategory.save()
            results.created++
          }
        }
      } catch (error) {
        results.failed++
        results.errors.push(`Failed to process category '${categoryData.name}': ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        results,
        summary: `Created: ${results.created}, Updated: ${results.updated}, Failed: ${results.failed}`,
        warnings: results.warnings.length > 0 ? results.warnings : undefined
      }
    }, { status: 200 })

  } catch (error) {
    console.error('Error in bulk category upload:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to process bulk category upload' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    const searchParams = request.nextUrl.searchParams
    const format = searchParams.get('format') || 'json' // json, csv
    
    const categories = await QuizCategory.find({})
      .sort({ createdAt: -1 })
      .lean()

    const exportData = categories.map(category => ({
      name: category.name,
      description: category.description,
      icon: category.icon,
      color: category.color,
      skills: Array.isArray(category.skills) ? category.skills.join(', ') : category.skills,
      difficulty: category.difficulty,
      estimatedTime: category.estimatedTime,
      totalQuestions: category.totalQuestions,
      passingScore: category.passingScore,
      isActive: category.isActive
    }))

    if (format === 'csv') {
      const csv = convertToCSV(exportData)
      return new NextResponse(csv, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename="quiz-categories.csv"'
        }
      })
    }

    return NextResponse.json({
      success: true,
      data: {
        categories: exportData,
        count: exportData.length
      }
    }, { status: 200 })

  } catch (error) {
    console.error('Error exporting categories:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to export categories' },
      { status: 500 }
    )
  }
}

// Old validation function removed - now using QuizValidator

function convertToCSV(data: any[]): string {
  if (data.length === 0) return ''
  
  const headers = Object.keys(data[0])
  const csvHeaders = headers.join(',')
  
  const csvRows = data.map(row => 
    headers.map(header => {
      const value = row[header]
      // Escape commas and quotes in CSV
      if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
        return `"${value.replace(/"/g, '""')}"`
      }
      return value
    }).join(',')
  )
  
  return [csvHeaders, ...csvRows].join('\n')
}

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { QuizCategory, QuizAttempt, QuizQuestion } from '@/lib/models/quiz.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const searchParams = request.nextUrl.searchParams
    const includeStats = searchParams.get('includeStats') === 'true'

    const categories = await QuizCategory.find({})
      .sort({ createdAt: -1 })
      .lean()

    // If stats are requested, calculate real statistics
    const categoriesWithStats = await Promise.all(
      categories.map(async (category) => {
        let attempts = 0
        let averageScore = 0
        let availableQuestions = 0

        if (includeStats) {
          // Get real attempt statistics for this category
          const categoryAttempts = await QuizAttempt.find({
            categoryId: category._id
          }).lean()

          attempts = categoryAttempts.length
          averageScore = attempts > 0
            ? Math.round((categoryAttempts.reduce((sum, attempt) => sum + attempt.score, 0) / attempts) * 10) / 10
            : 0

          // Get actual available questions count
          availableQuestions = await QuizQuestion.countDocuments({
            categoryId: category._id,
            isActive: true
          })
        }

        return {
          ...category,
          id: category._id.toString(),
          attempts,
          averageScore,
          availableQuestions: includeStats ? availableQuestions : (category.totalQuestions || 0)
        }
      })
    )

    return NextResponse.json({
      success: true,
      data: {
        categories: categoriesWithStats,
        total: categories.length
      }
    })
  } catch (error) {
    console.error('Error fetching categories:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch categories' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()
    
    const body = await request.json()
    const {
      name,
      description,
      icon,
      color,
      skills,
      difficulty,
      estimatedTime,
      totalQuestions,
      passingScore
    } = body

    // Validate required fields
    if (!name || !description || !skills || !difficulty) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Generate slug from name
    const slug = name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')

    // Check if slug already exists
    const existingCategory = await QuizCategory.findOne({ slug })
    if (existingCategory) {
      return NextResponse.json(
        { success: false, error: 'Category with this name already exists' },
        { status: 400 }
      )
    }

    const category = new QuizCategory({
      name,
      slug,
      description,
      icon: icon || '📚',
      color: color || 'bg-blue-500',
      skills: Array.isArray(skills) ? skills : [skills],
      difficulty,
      estimatedTime: estimatedTime || 15,
      totalQuestions: totalQuestions || 10,
      passingScore: passingScore || 70,
      isActive: true
    })

    await category.save()

    return NextResponse.json({
      success: true,
      data: {
        category: {
          ...category.toObject(),
          id: category._id.toString()
        }
      }
    }, { status: 201 })
  } catch (error) {
    console.error('Error creating category:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create category' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    await connectDB()
    
    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Category ID is required' },
        { status: 400 }
      )
    }

    // If name is being updated, regenerate slug
    if (updateData.name) {
      updateData.slug = updateData.name.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '')
    }

    const category = await QuizCategory.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    )

    if (!category) {
      return NextResponse.json(
        { success: false, error: 'Category not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        category: {
          ...category.toObject(),
          id: category._id.toString()
        }
      }
    })
  } catch (error) {
    console.error('Error updating category:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update category' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await connectDB()
    
    const searchParams = request.nextUrl.searchParams
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Category ID is required' },
        { status: 400 }
      )
    }

    const category = await QuizCategory.findByIdAndDelete(id)

    if (!category) {
      return NextResponse.json(
        { success: false, error: 'Category not found' },
        { status: 404 }
      )
    }

    // TODO: Also delete related questions and awards
    // await QuizQuestion.deleteMany({ categoryId: id })
    // await AwardTemplate.deleteMany({ categoryId: id })

    return NextResponse.json({
      success: true,
      message: 'Category deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting category:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete category' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { QuizAttempt, QuizCategory, QuizQuestion } from '@/lib/models/quiz.model'
import { AwardTemplate } from '@/lib/models/award.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    const searchParams = request.nextUrl.searchParams
    const timeRange = searchParams.get('timeRange') || '30d'
    
    // Calculate date range
    const now = new Date()
    let startDate: Date
    
    switch (timeRange) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    }

    // Get basic counts
    const [totalAttempts, totalCategories, totalQuestions, totalAwards] = await Promise.all([
      QuizAttempt.countDocuments({ completedAt: { $gte: startDate } }),
      QuizCategory.countDocuments({ isActive: true }),
      QuizQuestion.countDocuments({ isActive: true }),
      AwardTemplate.countDocuments({ isActive: true })
    ])

    // Get attempts for analysis
    const attempts = await QuizAttempt.find({ 
      completedAt: { $gte: startDate } 
    }).lean()

    // Calculate basic metrics
    const averageScore = attempts.length > 0 
      ? attempts.reduce((sum, attempt) => sum + attempt.score, 0) / attempts.length 
      : 0

    const passRate = attempts.length > 0 
      ? (attempts.filter(attempt => attempt.score >= 70).length / attempts.length) * 100 
      : 0

    const averageTime = attempts.length > 0 
      ? attempts.reduce((sum, attempt) => sum + attempt.timeSpent, 0) / attempts.length / 60 
      : 0

    // Get category performance
    const categories = await QuizCategory.find({ isActive: true }).lean()
    const categoryPerformance = await Promise.all(
      categories.map(async (category) => {
        const categoryAttempts = attempts.filter(
          attempt => attempt.categoryId.toString() === (category._id as string).toString()
        )
        
        const avgScore = categoryAttempts.length > 0 
          ? categoryAttempts.reduce((sum, attempt) => sum + attempt.score, 0) / categoryAttempts.length 
          : 0

        return {
          name: category.name,
          icon: category.icon,
          attempts: categoryAttempts.length,
          averageScore: Math.round(avgScore * 10) / 10
        }
      })
    )

    // Score distribution
    const scoreRanges = [
      { range: '90-100%', min: 90, max: 100 },
      { range: '80-89%', min: 80, max: 89 },
      { range: '70-79%', min: 70, max: 79 },
      { range: '60-69%', min: 60, max: 69 },
      { range: '0-59%', min: 0, max: 59 }
    ]

    const scoreDistribution = scoreRanges.map(range => {
      const count = attempts.filter(
        attempt => attempt.score >= range.min && attempt.score <= range.max
      ).length
      
      return {
        range: range.range,
        count,
        percentage: attempts.length > 0 ? Math.round((count / attempts.length) * 100 * 10) / 10 : 0
      }
    })

    // Time distribution
    const timeRanges = [
      { range: '0-5 min', min: 0, max: 300 },
      { range: '5-10 min', min: 300, max: 600 },
      { range: '10-15 min', min: 600, max: 900 },
      { range: '15-20 min', min: 900, max: 1200 },
      { range: '20+ min', min: 1200, max: Infinity }
    ]

    const timeDistribution = timeRanges.map(range => {
      const count = attempts.filter(
        attempt => attempt.timeSpent >= range.min && attempt.timeSpent < range.max
      ).length
      
      return {
        range: range.range,
        count,
        percentage: attempts.length > 0 ? Math.round((count / attempts.length) * 100 * 10) / 10 : 0
      }
    })

    // Daily attempts for the last 7 days
    const dailyAttempts = []
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
      const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate())
      const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000)
      
      const dayAttempts = attempts.filter(
        attempt => new Date(attempt.completedAt) >= dayStart && new Date(attempt.completedAt) < dayEnd
      )
      
      const dayAvgScore = dayAttempts.length > 0 
        ? dayAttempts.reduce((sum, attempt) => sum + attempt.score, 0) / dayAttempts.length 
        : 0

      dailyAttempts.push({
        date: dayStart.toISOString().split('T')[0],
        attempts: dayAttempts.length,
        averageScore: Math.round(dayAvgScore * 10) / 10
      })
    }

    // Calculate real question performance
    const questions = await QuizQuestion.find({ isActive: true }).limit(10).lean()
    const questionPerformance = await Promise.all(
      questions.map(async (question) => {
        // Get category info for this question
        const category = categories.find(cat =>
          (cat._id as string).toString() === question.categoryId.toString()
        )

        // Calculate question performance based on category attempts
        const categoryAttempts = attempts.filter(
          attempt => attempt.categoryId.toString() === question.categoryId.toString()
        )

        // Estimate question attempts (in real app, you'd track individual question responses)
        const categoryQuestionCount = await QuizQuestion.countDocuments({
          categoryId: question.categoryId,
          isActive: true
        })

        const questionAttempts = categoryQuestionCount > 0
          ? Math.floor(categoryAttempts.length / categoryQuestionCount)
          : 0

        // Estimate correct rate based on category performance and question difficulty
        const categoryAvgScore = categoryAttempts.length > 0
          ? categoryAttempts.reduce((sum, attempt) => sum + attempt.score, 0) / categoryAttempts.length
          : 0

        const difficultyMultipliers: Record<string, number> = {
          'beginner': 1.1,
          'intermediate': 1.0,
          'advanced': 0.9,
          'expert': 0.8
        }
        const difficultyMultiplier = difficultyMultipliers[question.difficulty] || 1.0

        const correctRate = Math.round(categoryAvgScore * difficultyMultiplier * 10) / 10

        return {
          questionId: (question._id as string).toString(),
          question: question.question,
          category: category?.name || 'Unknown Category',
          correctRate: Math.min(100, Math.max(0, correctRate)),
          attempts: questionAttempts
        }
      })
    )

    const analytics = {
      totalAttempts,
      totalCategories,
      totalQuestions,
      totalAwards,
      averageScore: Math.round(averageScore * 10) / 10,
      passRate: Math.round(passRate * 10) / 10,
      averageTime: Math.round(averageTime * 10) / 10,
      topCategories: categoryPerformance.sort((a, b) => b.attempts - a.attempts),
      scoreDistribution,
      timeDistribution,
      dailyAttempts,
      questionPerformance
    }

    return NextResponse.json({
      success: true,
      data: analytics
    })
  } catch (error) {
    console.error('Error fetching analytics:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch analytics' },
      { status: 500 }
    )
  }
}

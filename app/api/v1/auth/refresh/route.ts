import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'
import { connectDB } from '@/lib/database/connection'
import { User } from '@/lib/models/user.model'
import { Company } from '@/lib/models/company.model'
import { generateTokens } from '@/lib/middleware/auth.middleware'
import { ApiResponse, AuthResponse, AuthUser, DatabaseError } from '@/lib/types/api.types'

interface RefreshTokenRequest {
  refreshToken: string
}

interface RefreshTokenPayload {
  userId: string
  type: 'refresh'
  iat: number
  exp: number
}

export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<AuthResponse>>> {
  try {
    // Debug environment variables
    console.log('🔍 Environment check:', {
      hasJwtSecret: !!process.env.JWT_SECRET,
      hasJwtRefreshSecret: !!process.env.JWT_REFRESH_SECRET,
      jwtRefreshSecretLength: process.env.JWT_REFRESH_SECRET?.length,
      nodeEnv: process.env.NODE_ENV
    })

    await connectDB()

    let body: RefreshTokenRequest
    try {
      body = await request.json()
    } catch {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'INVALID_REQUEST_BODY',
            message: 'Invalid JSON in request body',
            statusCode: 400
          },
          timestamp: new Date().toISOString()
        },
        { status: 400 }
      )
    }

    const { refreshToken } = body

    if (!refreshToken) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'MISSING_REFRESH_TOKEN',
            message: 'Refresh token is required',
            field: 'refreshToken',
            statusCode: 400
          },
          timestamp: new Date().toISOString()
        },
        { status: 400 }
      )
    }

    // Verify refresh token
    let decoded: RefreshTokenPayload
    try {
      console.log('🔍 Verifying refresh token with secret length:', process.env.JWT_REFRESH_SECRET?.length)
      decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET!) as RefreshTokenPayload
      console.log('🔍 Refresh token verified successfully for user:', decoded.userId)
    } catch (jwtError) {
      console.error('🔍 JWT verification failed:', jwtError)
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'INVALID_REFRESH_TOKEN',
            message: 'Invalid or expired refresh token',
            statusCode: 401
          },
          timestamp: new Date().toISOString()
        },
        { status: 401 }
      )
    }

    // Get user from database
    let user
    try {
      user = await User.findById(decoded.userId)
        .populate('companyId', 'name slug')
        .select('-password')
        .lean()
    } catch (populateError) {
      console.error('User populate error:', populateError)
      // Try without populate if it fails
      user = await User.findById(decoded.userId)
        .select('-password')
        .lean()
    }

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found',
            statusCode: 404
          },
          timestamp: new Date().toISOString()
        },
        { status: 404 }
      )
    }

    if (!user.isActive) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'ACCOUNT_DEACTIVATED',
            message: 'Account is deactivated',
            statusCode: 401
          },
          timestamp: new Date().toISOString()
        },
        { status: 401 }
      )
    }

    // Generate new tokens
    console.log('🔍 Generating tokens for user:', {
      userId: user._id,
      email: user.email,
      role: user.role,
      hasCompanyId: !!user.companyId
    })

    let tokens
    try {
      tokens = generateTokens(user)
      console.log('🔍 Tokens generated successfully')
    } catch (tokenError) {
      console.error('🔍 Token generation failed:', tokenError)
      throw new Error(`Token generation failed: ${tokenError instanceof Error ? tokenError.message : 'Unknown error'}`)
    }

    // Format user data - simplified approach
    console.log('🔍 Formatting user data:', {
      userStructure: Object.keys(user),
      companyIdType: typeof user.companyId,
      companyIdValue: user.companyId
    })

    // Create a safe profile object
    const profileData = user.profile || {}
    const safeProfile = {
      firstName: profileData.firstName || '',
      lastName: profileData.lastName || '',
      fullName: `${profileData.firstName || ''} ${profileData.lastName || ''}`.trim() || 'User',
      avatar: profileData.avatar,
      phone: profileData.phone,
      location: profileData.location
    }

    // Create a safe preferences object
    const preferencesData = user.preferences || {}
    const safePreferences = {
      emailNotifications: preferencesData.emailNotifications ?? true,
      jobAlerts: preferencesData.jobAlerts ?? true,
      marketingEmails: preferencesData.marketingEmails ?? false,
      theme: preferencesData.theme || 'system',
      language: preferencesData.language || 'en',
      timezone: preferencesData.timezone || 'UTC',
      remoteWork: preferencesData.remoteWork ?? false
    }

    const userData: AuthUser = {
      id: user._id.toString(),
      email: user.email,
      role: user.role,
      profile: safeProfile,
      preferences: safePreferences,
      companyId: user.companyId?._id?.toString() || user.companyId?.toString(),
      isEmailVerified: user.isEmailVerified || false,
      isActive: user.isActive !== false, // Default to true if undefined
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    }

    const authResponse: AuthResponse = {
      user: userData,
      token: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      expiresIn: 3600 // 1 hour
    }

    return NextResponse.json(
      {
        success: true,
        data: authResponse,
        message: 'Token refreshed successfully',
        timestamp: new Date().toISOString()
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('Token refresh error:', error)
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace')

    const dbError = error as DatabaseError
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to refresh token',
          statusCode: 500,
          details: process.env.NODE_ENV === 'development' ? {
            originalError: dbError.message,
            errorType: error instanceof Error ? error.constructor.name : typeof error,
            stack: error instanceof Error ? error.stack : undefined
          } : undefined
        },
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// Method not allowed for other HTTP methods
export async function GET(): Promise<NextResponse<ApiResponse>> {
  return NextResponse.json(
    {
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'GET method not allowed. Use POST to refresh tokens.',
        statusCode: 405
      },
      timestamp: new Date().toISOString()
    },
    { status: 405 }
  )
}

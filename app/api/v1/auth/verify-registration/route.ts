import { NextRequest } from 'next/server'
import { User } from '@/lib/models/user.model'
import { Company } from '@/lib/models/company.model'
import { Client } from '@/lib/models/client.model'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import { withErrorHandler, createSuccessResponse, validateMethod } from '@/lib/api/route-handler'
import { connectToDatabase } from '@/lib/database/connection'
import { registrationTracker } from '@/lib/services/registration-tracker.service'

interface VerificationRequest {
  email: string
  trackingId?: string
}

interface VerificationResult {
  user: {
    id: string
    email: string
    role: string
    isActive: boolean
    isEmailVerified: boolean
    createdAt: Date
  }
  company?: {
    id: string
    name: string
    slug: string
    isUserInAdmins: boolean
    isUserInTeamMembers: boolean
    userRole?: string
    verification: {
      isVerified: boolean
      status: string
    }
  }
  client?: {
    id: string
    headline: string
    isActive: boolean
  }
  relationships: {
    userCompanyId?: string
    companyAdminsCount: number
    companyTeamMembersCount: number
    isConsistent: boolean
  }
  tracking?: {
    sessionId: string
    status: string
    completedSteps: number
    totalSteps: number
    duration?: number
  }
  issues: string[]
  isValid: boolean
}

/**
 * POST /api/v1/auth/verify-registration - Verify registration completion
 * 
 * This endpoint provides comprehensive verification of user registration,
 * especially for company admin registrations to ensure all relationships
 * are properly established.
 */
export const POST = withErrorHandler<VerificationResult>(async (request: NextRequest) => {
  validateMethod(request, ['POST'])

  const requestBody = await request.json()

  if (typeof requestBody !== 'object' || requestBody === null) {
    throw errorService.createError(ErrorCode.VALIDATION_ERROR, 'Invalid request data')
  }

  const requestData = requestBody as Record<string, unknown>

  if (!requestData.email || typeof requestData.email !== 'string') {
    throw errorService.createError(ErrorCode.VALIDATION_ERROR, 'Email is required')
  }

  const { email, trackingId } = {
    email: String(requestData.email).toLowerCase().trim(),
    trackingId: requestData.trackingId ? String(requestData.trackingId) : undefined
  } as VerificationRequest

  await connectToDatabase()

  console.log('🔍 Starting registration verification:', { email, trackingId })

  try {
    // Find user
    const user = await User.findOne({ email })
      .select('_id email role isActive isEmailVerified companyId createdAt')
      .lean()

    if (!user) {
      throw errorService.createError(
        ErrorCode.NOT_FOUND,
        'User not found',
        'email'
      )
    }

    const result: VerificationResult = {
      user: {
        id: user._id.toString(),
        email: user.email,
        role: user.role,
        isActive: user.isActive,
        isEmailVerified: user.isEmailVerified,
        createdAt: user.createdAt
      },
      relationships: {
        userCompanyId: user.companyId?.toString(),
        companyAdminsCount: 0,
        companyTeamMembersCount: 0,
        isConsistent: true
      },
      issues: [],
      isValid: true
    }

    // Verify company relationships for company admins
    if (user.role === 'company_admin') {
      if (!user.companyId) {
        result.issues.push('Company admin user does not have a company assigned')
        result.isValid = false
      } else {
        // Find and verify company
        const company = await Company.findById(user.companyId)
          .populate('admins', '_id email')
          .populate('teamMembers.user', '_id email')
          .lean()

        if (!company) {
          result.issues.push('Company not found for company admin user')
          result.isValid = false
        } else {
          // Check if user is in company admins
          const isUserInAdmins = company.admins.some((admin: any) =>
            admin._id.toString() === user._id.toString()
          )

          // Check if user is in team members
          const teamMember = company.teamMembers.find((member: any) =>
            member.user._id.toString() === user._id.toString()
          )

          result.company = {
            id: company._id.toString(),
            name: company.name,
            slug: company.slug,
            isUserInAdmins,
            isUserInTeamMembers: !!teamMember,
            userRole: teamMember?.role,
            verification: {
              isVerified: company.verification?.isVerified || false,
              status: company.verification?.status || 'pending'
            }
          }

          result.relationships.companyAdminsCount = company.admins.length
          result.relationships.companyTeamMembersCount = company.teamMembers.length

          // Validate relationships
          if (!isUserInAdmins) {
            result.issues.push('User is not in company admins array')
            result.isValid = false
            result.relationships.isConsistent = false
          }

          if (!teamMember) {
            result.issues.push('User is not in company team members')
            result.isValid = false
            result.relationships.isConsistent = false
          } else if (teamMember.role !== 'owner') {
            result.issues.push(`User role in team members is "${teamMember.role}", expected "owner"`)
          }

          if (!teamMember?.isActive) {
            result.issues.push('User is not active in company team members')
            result.isValid = false
          }
        }
      }
    }

    // Verify client profile for job seekers
    if (user.role === 'job_seeker') {
      const client = await Client.findOne({ user: user._id })
        .select('_id headline isActive')
        .lean()

      if (client) {
        result.client = {
          id: client._id.toString(),
          headline: client.headline,
          isActive: client.isActive
        }

        if (!client.isActive) {
          result.issues.push('Client profile is not active')
        }
      } else {
        result.issues.push('Job seeker does not have a client profile')
      }
    }

    // Add tracking information if available
    if (trackingId) {
      const tracking = registrationTracker.getProgress(trackingId)
      if (tracking) {
        const completedSteps = tracking.steps.filter(s => s.status === 'completed').length
        const duration = tracking.endTime 
          ? tracking.endTime.getTime() - tracking.startTime.getTime()
          : Date.now() - tracking.startTime.getTime()

        result.tracking = {
          sessionId: tracking.sessionId,
          status: tracking.status,
          completedSteps,
          totalSteps: tracking.steps.length,
          duration: Math.round(duration / 1000) // Convert to seconds
        }

        if (tracking.status === 'failed') {
          result.issues.push(`Registration tracking shows failure: ${tracking.error || 'Unknown error'}`)
          result.isValid = false
        }
      }
    }

    // Final validation
    if (result.issues.length === 0) {
      result.issues.push('All verification checks passed')
    }

    console.log('✅ Registration verification completed:', {
      email,
      isValid: result.isValid,
      issuesCount: result.issues.length,
      role: user.role
    })

    return createSuccessResponse(result, {
      message: result.isValid
        ? 'Registration verification passed'
        : 'Registration verification found issues'
    })

  } catch (error: unknown) {
    console.error('❌ Registration verification failed:', {
      email,
      trackingId,
      error: error instanceof Error ? error.message : 'Unknown error'
    })

    throw error
  }
}, {
  requireDatabase: true,
  rateLimit: {
    requests: 20, // Allow frequent verification checks
    windowMs: 5 * 60 * 1000 // per 5 minutes
  }
})

// Method not allowed for other HTTP methods
export async function GET() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'GET method not allowed for registration verification'
  )
}

export async function PUT() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PUT method not allowed for registration verification'
  )
}

export async function DELETE() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'DELETE method not allowed for registration verification'
  )
}

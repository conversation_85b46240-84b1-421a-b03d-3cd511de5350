import { NextRequest } from 'next/server'
import { authService, AuthResponse } from '@/lib/services/auth.service'
import { validationService } from '@/lib/services/validation.service'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import { withErrorHandler, createSuccessResponse, validateMethod } from '@/lib/api/route-handler'

/**
 * Enhanced Company Admin Registration Endpoint
 * 
 * This endpoint provides atomic registration for company admins with comprehensive
 * error handling and detailed feedback. It ensures that either both the user and
 * company are created successfully, or neither is created (atomic transaction).
 */

// POST /api/v1/auth/register-company-admin - Enhanced company admin registration
export const POST = withErrorHandler<AuthResponse>(async (request: NextRequest) => {
  validateMethod(request, ['POST'])

  // Validate request body with enhanced validation
  const registerData = validationService.validateCompanyAdminRegisterRequest(await request.json())

  // Ensure this is a company admin registration
  if (registerData.role !== 'company_admin') {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      'This endpoint is specifically for company admin registration',
      'role'
    )
  }

  // Validate company data is present
  if (!registerData.company) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      'Company information is required for company admin registration',
      'company'
    )
  }

  // Additional company data validation
  const requiredCompanyFields = ['name', 'industry', 'size', 'description']
  for (const field of requiredCompanyFields) {
    if (!registerData.company[field as keyof typeof registerData.company]) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        `Company ${field} is required`,
        `company.${field}`
      )
    }
  }

  // Validate company name length and format
  if (registerData.company.name.trim().length < 2) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      'Company name must be at least 2 characters long',
      'company.name'
    )
  }

  if (registerData.company.name.trim().length > 100) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      'Company name must be less than 100 characters',
      'company.name'
    )
  }

  // Validate company description length
  if (registerData.company.description.trim().length < 10) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      'Company description must be at least 10 characters long',
      'company.description'
    )
  }

  if (registerData.company.description.trim().length > 1000) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      'Company description must be less than 1000 characters',
      'company.description'
    )
  }

  // Validate company size
  const validSizes = ['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+']
  if (!validSizes.includes(registerData.company.size)) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      'Invalid company size. Must be one of: ' + validSizes.join(', '),
      'company.size'
    )
  }

  // Validate website URL if provided
  if (registerData.company.website) {
    try {
      new URL(registerData.company.website.startsWith('http') ? 
        registerData.company.website : 
        `https://${registerData.company.website}`)
    } catch {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        'Invalid website URL format',
        'company.website'
      )
    }
  }

  console.log('🚀 Starting enhanced company admin registration:', {
    email: registerData.email,
    companyName: registerData.company.name,
    companyIndustry: registerData.company.industry,
    companySize: registerData.company.size,
    timestamp: new Date().toISOString()
  })

  try {
    // Use the atomic registration service
    const result = await authService.registerWithTransaction(registerData)

    console.log('✅ Company admin registration completed successfully:', {
      userId: result.user.id,
      email: result.user.email,
      companyId: result.user.companyId,
      timestamp: new Date().toISOString()
    })

    return createSuccessResponse(result, { message: 'Company admin registration completed successfully' })

  } catch (error: unknown) {
    console.error('❌ Company admin registration failed:', {
      email: registerData.email,
      companyName: registerData.company.name,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    })

    // Re-throw the error to be handled by the error middleware
    throw error
  }
}, {
  requireDatabase: true,
  rateLimit: {
    requests: 3, // More restrictive for company admin registration
    windowMs: 15 * 60 * 1000 // per 15 minutes
  }
})

// Method not allowed for other HTTP methods
export async function GET() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'GET method not allowed for company admin registration'
  )
}

export async function PUT() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PUT method not allowed for company admin registration'
  )
}

export async function DELETE() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'DELETE method not allowed for company admin registration'
  )
}

export async function PATCH() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PATCH method not allowed for company admin registration'
  )
}

import { NextRequest, NextResponse } from 'next/server'
import <PERSON><PERSON> from 'stripe'
import { connectDB } from '@/lib/db'
import { Subscription } from '@/lib/models/subscription.model'
import { Payment } from '@/lib/models/payment.model'
import { Invoice } from '@/lib/models/invoice.model'
import { EmailService } from '@/lib/services/email.service'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20'
})

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = request.headers.get('stripe-signature')!

    let event: Stripe.Event

    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
    } catch (err) {
      console.error('Webhook signature verification failed:', err)
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      )
    }

    await connectDB()

    // Handle the event
    switch (event.type) {
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object as Stripe.Subscription)
        break

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription)
        break

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription)
        break

      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice)
        break

      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object as Stripe.Invoice)
        break

      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent)
        break

      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent)
        break

      case 'charge.dispute.created':
        await handleChargeDisputeCreated(event.data.object as Stripe.Dispute)
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })

  } catch (error) {
    console.error('Webhook error:', error)
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    )
  }
}

async function handleSubscriptionCreated(stripeSubscription: Stripe.Subscription) {
  try {
    const subscriptionId = stripeSubscription.metadata.subscriptionId
    if (!subscriptionId) return

    await Subscription.findByIdAndUpdate(subscriptionId, {
      stripeSubscriptionId: stripeSubscription.id,
      stripeCustomerId: stripeSubscription.customer as string,
      status: mapStripeStatus(stripeSubscription.status),
      currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
      currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000)
    })

    console.log(`Subscription created: ${subscriptionId}`)
  } catch (error) {
    console.error('Error handling subscription created:', error)
  }
}

async function handleSubscriptionUpdated(stripeSubscription: Stripe.Subscription) {
  try {
    const subscription = await Subscription.findOne({
      stripeSubscriptionId: stripeSubscription.id
    }).populate('company')

    if (!subscription) return

    const updates: any = {
      status: mapStripeStatus(stripeSubscription.status),
      currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
      currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000)
    }

    if (stripeSubscription.canceled_at) {
      updates.canceledAt = new Date(stripeSubscription.canceled_at * 1000)
    }

    if (stripeSubscription.cancel_at_period_end) {
      updates.cancelAtPeriodEnd = true
    }

    await Subscription.findByIdAndUpdate(subscription._id, updates)

    // Send email notifications for status changes
    if (subscription.status !== updates.status) {
      await EmailService.sendSubscriptionStatusUpdate(
        subscription.company.email,
        subscription.company.name,
        updates.status,
        subscription
      )
    }

    console.log(`Subscription updated: ${subscription._id}`)
  } catch (error) {
    console.error('Error handling subscription updated:', error)
  }
}

async function handleSubscriptionDeleted(stripeSubscription: Stripe.Subscription) {
  try {
    const subscription = await Subscription.findOne({
      stripeSubscriptionId: stripeSubscription.id
    }).populate('company')

    if (!subscription) return

    await Subscription.findByIdAndUpdate(subscription._id, {
      status: 'canceled',
      canceledAt: new Date()
    })

    // Send cancellation email
    await EmailService.sendSubscriptionCanceled(
      subscription.company.email,
      subscription.company.name,
      subscription
    )

    console.log(`Subscription deleted: ${subscription._id}`)
  } catch (error) {
    console.error('Error handling subscription deleted:', error)
  }
}

async function handleInvoicePaymentSucceeded(stripeInvoice: Stripe.Invoice) {
  try {
    // Create payment record
    const payment = new Payment({
      company: stripeInvoice.metadata?.companyId,
      subscription: stripeInvoice.metadata?.subscriptionId,
      amount: stripeInvoice.amount_paid / 100, // Convert from cents
      currency: stripeInvoice.currency.toUpperCase(),
      status: 'succeeded',
      paymentMethod: 'stripe',
      description: stripeInvoice.description || 'Subscription payment',
      stripePaymentId: stripeInvoice.payment_intent as string,
      stripeChargeId: stripeInvoice.charge as string,
      paidAt: new Date(stripeInvoice.status_transitions.paid_at! * 1000),
      metadata: {
        stripeInvoiceId: stripeInvoice.id
      }
    })

    await payment.save()

    // Update invoice if exists
    if (stripeInvoice.metadata?.invoiceId) {
      await Invoice.findByIdAndUpdate(stripeInvoice.metadata.invoiceId, {
        status: 'paid',
        paidAt: new Date(),
        stripeInvoiceId: stripeInvoice.id
      })
    }

    console.log(`Payment succeeded: ${payment._id}`)
  } catch (error) {
    console.error('Error handling invoice payment succeeded:', error)
  }
}

async function handleInvoicePaymentFailed(stripeInvoice: Stripe.Invoice) {
  try {
    // Create failed payment record
    const payment = new Payment({
      company: stripeInvoice.metadata?.companyId,
      subscription: stripeInvoice.metadata?.subscriptionId,
      amount: stripeInvoice.amount_due / 100, // Convert from cents
      currency: stripeInvoice.currency.toUpperCase(),
      status: 'failed',
      paymentMethod: 'stripe',
      description: stripeInvoice.description || 'Subscription payment',
      failureReason: 'Payment failed',
      metadata: {
        stripeInvoiceId: stripeInvoice.id
      }
    })

    await payment.save()

    // Update subscription status
    if (stripeInvoice.metadata?.subscriptionId) {
      await Subscription.findByIdAndUpdate(stripeInvoice.metadata.subscriptionId, {
        status: 'past_due'
      })
    }

    console.log(`Payment failed: ${payment._id}`)
  } catch (error) {
    console.error('Error handling invoice payment failed:', error)
  }
}

async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  try {
    // Update existing payment record or create new one
    let payment = await Payment.findOne({
      stripePaymentId: paymentIntent.id
    })

    if (payment) {
      await Payment.findByIdAndUpdate(payment._id, {
        status: 'succeeded',
        paidAt: new Date(),
        stripeChargeId: paymentIntent.latest_charge as string
      })
    } else {
      payment = new Payment({
        company: paymentIntent.metadata?.companyId,
        amount: paymentIntent.amount / 100, // Convert from cents
        currency: paymentIntent.currency.toUpperCase(),
        status: 'succeeded',
        paymentMethod: 'stripe',
        description: paymentIntent.description || 'One-time payment',
        stripePaymentId: paymentIntent.id,
        stripeChargeId: paymentIntent.latest_charge as string,
        paidAt: new Date(),
        metadata: paymentIntent.metadata
      })

      await payment.save()
    }

    console.log(`Payment intent succeeded: ${payment._id}`)
  } catch (error) {
    console.error('Error handling payment intent succeeded:', error)
  }
}

async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
  try {
    let payment = await Payment.findOne({
      stripePaymentId: paymentIntent.id
    })

    if (payment) {
      await Payment.findByIdAndUpdate(payment._id, {
        status: 'failed',
        failureReason: paymentIntent.last_payment_error?.message || 'Payment failed'
      })
    }

    console.log(`Payment intent failed: ${paymentIntent.id}`)
  } catch (error) {
    console.error('Error handling payment intent failed:', error)
  }
}

async function handleChargeDisputeCreated(dispute: Stripe.Dispute) {
  try {
    // Find the payment and mark it as disputed
    const payment = await Payment.findOne({
      stripeChargeId: dispute.charge
    })

    if (payment) {
      await Payment.findByIdAndUpdate(payment._id, {
        status: 'disputed',
        metadata: {
          ...payment.metadata,
          disputeId: dispute.id,
          disputeReason: dispute.reason,
          disputeAmount: dispute.amount / 100
        }
      })
    }

    console.log(`Charge dispute created: ${dispute.id}`)
  } catch (error) {
    console.error('Error handling charge dispute created:', error)
  }
}

function mapStripeStatus(stripeStatus: string): string {
  switch (stripeStatus) {
    case 'active':
      return 'active'
    case 'trialing':
      return 'trialing'
    case 'past_due':
      return 'past_due'
    case 'canceled':
    case 'cancelled':
      return 'canceled'
    case 'paused':
      return 'paused'
    default:
      return 'active'
  }
}

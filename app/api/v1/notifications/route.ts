import { NextRequest } from 'next/server'
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, createSuccessResponse, validate<PERSON>ethod, AuthenticatedRequest } from '@/lib/middleware/auth.middleware'
import { notificationService } from '@/lib/services'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'

// GET /api/v1/notifications - Get user notifications
export const GET = withErrorHandler(async (request: AuthenticatedRequest) => {
  validateMethod(request, ['GET'])

  // Get user ID from authenticated request
  const userId = request.user?.id
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }
  
  const { searchParams } = new URL(request.url)
  
  // Pagination
  const page = parseInt(searchParams.get('page') || '1')
  const limit = parseInt(searchParams.get('limit') || '20')
  
  // Filters
  const unreadOnly = searchParams.get('unreadOnly') === 'true'
  
  // Check if requesting stats
  if (searchParams.get('stats') === 'true') {
    const stats = await notificationService.getNotificationStats(userId)
    return createSuccessResponse(stats)
  }
  
  const result = await notificationService.getUserNotifications(userId, page, limit, unreadOnly)
  
  return createSuccessResponse(result)
}, {
  requireDatabase: true,
  requireAuth: true
})

// PUT /api/v1/notifications - Mark all notifications as read
export const PUT = withErrorHandler(async (request: AuthenticatedRequest) => {
  validateMethod(request, ['PUT'])

  // Get user ID from authenticated request
  const userId = request.user?.id
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }
  
  const { searchParams } = new URL(request.url)
  const action = searchParams.get('action')
  
  if (action === 'markAllRead') {
    await notificationService.markAllAsRead(userId)
    return createSuccessResponse({ message: 'All notifications marked as read' })
  } else {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      'Invalid action. Use action=markAllRead to mark all notifications as read',
      'action'
    )
  }
}, {
  requireDatabase: true,
  requireAuth: true
})

// Method not allowed for other HTTP methods
export async function POST() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'POST method not allowed for notifications collection. Notifications are created automatically by the system.'
  )
}

export async function DELETE() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'DELETE method not allowed for notifications collection. Use individual notification endpoints to delete specific notifications.'
  )
}

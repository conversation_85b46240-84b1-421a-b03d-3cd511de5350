import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { Job } from '@/lib/models/job.model'
import { Company } from '@/lib/models/company.model'
import { User } from '@/lib/models/user.model'
import { Client } from '@/lib/models/client.model'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

// GET /api/v1/stats - Get platform statistics
export async function GET(request: NextRequest) {
  try {
    await connectDB()

    // Get counts from different collections
    const [
      totalJobs,
      activeJobs,
      totalCompanies,
      verifiedCompanies,
      totalUsers,
      totalClients,
      activeClients,
      jobCategories,
      jobLocations,
      clientSkills
    ] = await Promise.all([
      Job.countDocuments(),
      Job.countDocuments({ status: 'active', isActive: true }),
      Company.countDocuments({ isActive: true }),
      Company.countDocuments({ 'verification.isVerified': true, isActive: true }),
      User.countDocuments({ isActive: true }),
      Client.countDocuments({ isActive: true }),
      Client.countDocuments({ isActive: true, isPublic: true }),
      Job.distinct('category'),
      Job.distinct('location.city'),
      Client.distinct('skills.name')
    ])

    // Get top job categories
    const topCategories = await Job.aggregate([
      { $match: { status: 'active', isActive: true } },
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ])

    // Get top locations
    const topLocations = await Job.aggregate([
      { $match: { status: 'active', isActive: true } },
      { $group: { _id: '$location.city', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ])

    return NextResponse.json({
      success: true,
      data: {
        jobs: {
          total: totalJobs,
          active: activeJobs
        },
        companies: {
          total: totalCompanies,
          verified: verifiedCompanies
        },
        users: {
          total: totalUsers
        },
        clients: {
          total: totalClients,
          active: activeClients
        },
        categories: {
          total: jobCategories.length,
          top: topCategories.map(cat => ({
            name: cat._id,
            count: cat.count
          }))
        },
        skills: {
          total: clientSkills.length
        },
        locations: {
          total: jobLocations.length,
          top: topLocations.map(loc => ({
            name: loc._id,
            count: loc.count
          }))
        }
      }
    }, { status: 200 })

  } catch (error) {
    console.error('Stats GET error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch platform statistics'
      },
      { status: 500 }
    )
  }
}

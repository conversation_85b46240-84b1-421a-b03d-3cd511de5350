"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { useQuizAdminStore, type AwardTemplate } from "@/stores/quiz-admin.store"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Trophy,
  Star,
  Gift,
  Sparkles,
  Users,
  Target
} from "lucide-react"

export default function AwardsPage() {
  const {
    awards,
    awardsLoading,
    awardsError,
    categories,
    fetchAwards,
    fetchCategories,
    deleteAward
  } = useQuizAdminStore()

  const [filteredAwards, setFilteredAwards] = useState<AwardTemplate[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [selectedRarity, setSelectedRarity] = useState<string>("all")

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    let filtered = awards

    if (searchTerm) {
      filtered = filtered.filter(award =>
        award.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        award.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (selectedCategory !== "all") {
      filtered = filtered.filter(award => award.categoryId === selectedCategory)
    }

    if (selectedRarity !== "all") {
      filtered = filtered.filter(award => award.rarity === selectedRarity)
    }

    setFilteredAwards(filtered)
  }, [awards, searchTerm, selectedCategory, selectedRarity])

  const loadData = async () => {
    try {
      // Load categories
      const categoriesResponse = await fetch('/api/v1/quiz/categories')
      const categoriesResult = await categoriesResponse.json()
      
      if (categoriesResult.success) {
        setCategories(categoriesResult.data.categories)
        
        // Mock awards data (in real app, this would be an API call)
        const mockAwards: AwardTemplate[] = [
          {
            id: '1',
            name: 'Frontend Developer',
            slug: 'frontend-developer',
            description: 'Certified Frontend Development Skills',
            categoryId: categoriesResult.data.categories[0]?.id || '1',
            categoryName: 'Frontend Development',
            categoryIcon: '💻',
            badgeIcon: '🏆',
            badgeColor: '#3B82F6',
            minimumScore: 70,
            rarity: 'common',
            benefits: [
              'Digital certificate download',
              'Profile badge display',
              'Access to React tutorials'
            ],
            isActive: true,
            createdAt: new Date().toISOString(),
            timesEarned: 23,
            averageScoreOfEarners: 78
          },
          {
            id: '2',
            name: 'Frontend Expert',
            slug: 'frontend-expert',
            description: 'Expert-level Frontend Development Mastery',
            categoryId: categoriesResult.data.categories[0]?.id || '1',
            categoryName: 'Frontend Development',
            categoryIcon: '💻',
            badgeIcon: '🌟',
            badgeColor: '#10B981',
            minimumScore: 90,
            rarity: 'rare',
            benefits: [
              'Premium certificate download',
              'Expert badge display',
              'Free AI training course access',
              'Priority job matching'
            ],
            isActive: true,
            createdAt: new Date().toISOString(),
            timesEarned: 7,
            averageScoreOfEarners: 93
          }
        ]
        
        setAwards(mockAwards)
      }
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'bg-gray-100 text-gray-800'
      case 'uncommon': return 'bg-green-100 text-green-800'
      case 'rare': return 'bg-blue-100 text-blue-800'
      case 'epic': return 'bg-purple-100 text-purple-800'
      case 'legendary': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getRarityIcon = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return <Sparkles className="w-4 h-4" />
      case 'epic': return <Star className="w-4 h-4" />
      case 'rare': return <Gift className="w-4 h-4" />
      default: return <Trophy className="w-4 h-4" />
    }
  }

  const handleDeleteAward = async (awardId: string) => {
    if (confirm('Are you sure you want to delete this award? This action cannot be undone.')) {
      try {
        console.log('Deleting award:', awardId)
        loadData()
      } catch (error) {
        console.error('Error deleting award:', error)
      }
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Award Templates</h1>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-20 bg-gray-200 rounded"></div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Award Templates</h1>
          <p className="text-muted-foreground">Manage award templates and their criteria</p>
        </div>
        <Button asChild className="button-enhanced">
          <Link href="/quizadmin/awards/new">
            <Plus className="w-4 h-4 mr-2" />
            Create Award
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Trophy className="w-4 h-4 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Awards</p>
                <p className="text-2xl font-bold">{awards.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-green-100 rounded-lg">
                <Star className="w-4 h-4 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Active Awards</p>
                <p className="text-2xl font-bold">{awards.filter(a => a.isActive).length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Times Earned</p>
                <p className="text-2xl font-bold">{awards.reduce((sum, a) => sum + a.timesEarned, 0)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Target className="w-4 h-4 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Avg Min Score</p>
                <p className="text-2xl font-bold">
                  {awards.length > 0 ? Math.round(awards.reduce((sum, a) => sum + a.minimumScore, 0) / awards.length) : 0}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search awards..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.icon} {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedRarity} onValueChange={setSelectedRarity}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Rarities" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Rarities</SelectItem>
                <SelectItem value="common">Common</SelectItem>
                <SelectItem value="uncommon">Uncommon</SelectItem>
                <SelectItem value="rare">Rare</SelectItem>
                <SelectItem value="epic">Epic</SelectItem>
                <SelectItem value="legendary">Legendary</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Award</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Rarity</TableHead>
                <TableHead>Min Score</TableHead>
                <TableHead>Times Earned</TableHead>
                <TableHead>Avg Score</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAwards.map((award) => (
                <TableRow key={award.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div 
                        className="w-10 h-10 rounded-full flex items-center justify-center text-lg"
                        style={{ backgroundColor: award.badgeColor + '20', color: award.badgeColor }}
                      >
                        {award.badgeIcon}
                      </div>
                      <div>
                        <div className="font-medium">{award.name}</div>
                        <div className="text-sm text-gray-500">{award.description}</div>
                        <div className="text-xs text-gray-400 mt-1">
                          {award.benefits.length} benefits
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{award.categoryIcon}</span>
                      <span className="text-sm">{award.categoryName}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getRarityColor(award.rarity)}>
                      {getRarityIcon(award.rarity)}
                      <span className="ml-1 capitalize">{award.rarity}</span>
                    </Badge>
                  </TableCell>
                  <TableCell>{award.minimumScore}%</TableCell>
                  <TableCell>{award.timesEarned}</TableCell>
                  <TableCell>{award.averageScoreOfEarners}%</TableCell>
                  <TableCell>
                    <Badge variant={award.isActive ? "default" : "secondary"}>
                      {award.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/quizadmin/awards/${award.id}`}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/quizadmin/awards/${award.id}/edit`}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDeleteAward(award.id)}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}

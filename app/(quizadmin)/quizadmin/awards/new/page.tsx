"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { useQuizAdminStore } from "@/stores/quiz-admin.store"
import { ArrowLeft, Save, Loader2, Plus, X, Trophy, Award, Star, Crown } from "lucide-react"
import { toast } from "sonner"

const rarityOptions = [
  { value: "common", label: "Common", color: "bg-gray-100 text-gray-800", icon: Award },
  { value: "uncommon", label: "Uncommon", color: "bg-green-100 text-green-800", icon: Star },
  { value: "rare", label: "Rare", color: "bg-blue-100 text-blue-800", icon: Trophy },
  { value: "epic", label: "Epic", color: "bg-purple-100 text-purple-800", icon: Crown },
  { value: "legendary", label: "Legendary", color: "bg-yellow-100 text-yellow-800", icon: Crown }
]

const badgeIcons = [
  "🏆", "🥇", "🥈", "🥉", "⭐", "🌟", "💎", "👑", "🎖️", "🏅",
  "🎯", "🚀", "⚡", "🔥", "💪", "🧠", "💡", "🎨", "🔧", "📚"
]

const badgeColors = [
  "#FFD700", "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
  "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9", "#F8C471"
]

export default function NewAwardPage() {
  const router = useRouter()
  const { 
    createAward, 
    awardsLoading, 
    categories, 
    fetchCategories 
  } = useQuizAdminStore()
  
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    categoryId: "",
    badgeIcon: "🏆",
    badgeColor: "#FFD700",
    minimumScore: 70,
    rarity: "",
    benefits: [] as string[],
    certificateTemplate: "",
    isActive: true
  })
  
  const [newBenefit, setNewBenefit] = useState("")
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    fetchCategories()
  }, [fetchCategories])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.name.trim()) {
      newErrors.name = "Award name is required"
    }
    
    if (!formData.description.trim()) {
      newErrors.description = "Description is required"
    }
    
    if (!formData.categoryId) {
      newErrors.categoryId = "Category is required"
    }
    
    if (!formData.rarity) {
      newErrors.rarity = "Rarity level is required"
    }
    
    if (formData.minimumScore < 50 || formData.minimumScore > 100) {
      newErrors.minimumScore = "Minimum score must be between 50 and 100"
    }
    
    if (formData.benefits.length === 0) {
      newErrors.benefits = "At least one benefit is required"
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error("Please fix the form errors")
      return
    }
    
    try {
      await createAward(formData)
      toast.success("Award created successfully!")
      router.push("/quizadmin/awards")
    } catch (error) {
      toast.error("Failed to create award")
      console.error("Error creating award:", error)
    }
  }

  const addBenefit = () => {
    if (newBenefit.trim() && !formData.benefits.includes(newBenefit.trim())) {
      setFormData(prev => ({
        ...prev,
        benefits: [...prev.benefits, newBenefit.trim()]
      }))
      setNewBenefit("")
    }
  }

  const removeBenefit = (benefitToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      benefits: prev.benefits.filter(benefit => benefit !== benefitToRemove)
    }))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault()
      addBenefit()
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/quizadmin/awards">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Awards
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Create New Award</h1>
            <p className="text-muted-foreground">Create an award template for quiz achievements</p>
          </div>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Information */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Award Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Award Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., JavaScript Master"
                    className={errors.name ? "border-red-500" : ""}
                  />
                  {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe what this award represents..."
                    rows={3}
                    className={errors.description ? "border-red-500" : ""}
                  />
                  {errors.description && <p className="text-sm text-red-500">{errors.description}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="categoryId">Category *</Label>
                  <Select value={formData.categoryId} onValueChange={(value) => setFormData(prev => ({ ...prev, categoryId: value }))}>
                    <SelectTrigger className={errors.categoryId ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          <div className="flex items-center space-x-2">
                            <span className="text-lg">{category.icon}</span>
                            <span>{category.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.categoryId && <p className="text-sm text-red-500">{errors.categoryId}</p>}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Badge Icon</Label>
                    <div className="grid grid-cols-5 gap-2">
                      {badgeIcons.map((icon) => (
                        <Button
                          key={icon}
                          type="button"
                          variant={formData.badgeIcon === icon ? "default" : "outline"}
                          size="sm"
                          onClick={() => setFormData(prev => ({ ...prev, badgeIcon: icon }))}
                          className="h-10 text-lg"
                        >
                          {icon}
                        </Button>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Badge Color</Label>
                    <div className="grid grid-cols-4 gap-2">
                      {badgeColors.map((color) => (
                        <Button
                          key={color}
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => setFormData(prev => ({ ...prev, badgeColor: color }))}
                          className={`h-10 ${formData.badgeColor === color ? 'ring-2 ring-offset-2 ring-primary' : ''}`}
                          style={{ backgroundColor: color }}
                        >
                          {formData.badgeColor === color && "✓"}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Benefits */}
            <Card>
              <CardHeader>
                <CardTitle>Award Benefits</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Benefits *</Label>
                  <div className="flex space-x-2">
                    <Input
                      value={newBenefit}
                      onChange={(e) => setNewBenefit(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Add a benefit..."
                      className="flex-1"
                    />
                    <Button type="button" onClick={addBenefit} size="sm">
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>
                  {errors.benefits && <p className="text-sm text-red-500">{errors.benefits}</p>}
                  
                  <div className="space-y-2 mt-3">
                    {formData.benefits.map((benefit, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm">{benefit}</span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeBenefit(benefit)}
                          className="h-6 w-6 p-0"
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Settings Sidebar */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Award Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="rarity">Rarity Level *</Label>
                  <Select value={formData.rarity} onValueChange={(value) => setFormData(prev => ({ ...prev, rarity: value }))}>
                    <SelectTrigger className={errors.rarity ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select rarity" />
                    </SelectTrigger>
                    <SelectContent>
                      {rarityOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center space-x-2">
                            <option.icon className="w-4 h-4" />
                            <Badge className={option.color} variant="secondary">
                              {option.label}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.rarity && <p className="text-sm text-red-500">{errors.rarity}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="minimumScore">Minimum Score (%)</Label>
                  <Input
                    id="minimumScore"
                    type="number"
                    min="50"
                    max="100"
                    value={formData.minimumScore}
                    onChange={(e) => setFormData(prev => ({ ...prev, minimumScore: parseInt(e.target.value) || 70 }))}
                    className={errors.minimumScore ? "border-red-500" : ""}
                  />
                  {errors.minimumScore && <p className="text-sm text-red-500">{errors.minimumScore}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="certificateTemplate">Certificate Template (Optional)</Label>
                  <Input
                    id="certificateTemplate"
                    value={formData.certificateTemplate}
                    onChange={(e) => setFormData(prev => ({ ...prev, certificateTemplate: e.target.value }))}
                    placeholder="Certificate template URL or ID"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="isActive">Active Award</Label>
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Preview */}
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="p-4 border rounded-lg space-y-3">
                  <div className="flex items-center space-x-3">
                    <div
                      className="w-12 h-12 rounded-full flex items-center justify-center text-xl"
                      style={{ backgroundColor: formData.badgeColor + '20', color: formData.badgeColor }}
                    >
                      {formData.badgeIcon}
                    </div>
                    <div>
                      <h3 className="font-semibold">{formData.name || "Award Name"}</h3>
                      <p className="text-sm text-muted-foreground">
                        {categories.find(c => c.id === formData.categoryId)?.name || "Category"}
                      </p>
                    </div>
                  </div>

                  <p className="text-sm text-muted-foreground">
                    {formData.description || "Award description..."}
                  </p>

                  <div className="flex items-center justify-between text-sm">
                    <span>Min Score: {formData.minimumScore}%</span>
                    {formData.rarity && (
                      <Badge className={rarityOptions.find(r => r.value === formData.rarity)?.color}>
                        {rarityOptions.find(r => r.value === formData.rarity)?.label}
                      </Badge>
                    )}
                  </div>

                  {formData.benefits.length > 0 && (
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Benefits:</p>
                      <ul className="text-xs text-muted-foreground space-y-1">
                        {formData.benefits.slice(0, 3).map((benefit, index) => (
                          <li key={index}>• {benefit}</li>
                        ))}
                        {formData.benefits.length > 3 && (
                          <li>• +{formData.benefits.length - 3} more...</li>
                        )}
                      </ul>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" asChild>
            <Link href="/quizadmin/awards">Cancel</Link>
          </Button>
          <Button type="submit" disabled={awardsLoading}>
            {awardsLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Create Award
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}

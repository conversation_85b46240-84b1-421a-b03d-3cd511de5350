"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { useQuizAdminStore, type QuizAttempt } from "@/stores/quiz-admin.store"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Search,
  MoreHorizontal,
  Eye,
  Download,
  Users,
  TrendingUp,
  Clock,
  Target,
  Calendar,
  Filter
} from "lucide-react"

interface QuizAttempt {
  id: string
  sessionId: string
  userId?: string
  userEmail?: string
  categoryId: string
  categoryName: string
  categoryIcon: string
  score: number
  totalQuestions: number
  correctAnswers: number
  timeSpent: number
  completedAt: string
  earnedAwards: string[]
  isAnonymous: boolean
  ipAddress: string
  userAgent: string
}

export default function AttemptsPage() {
  const [attempts, setAttempts] = useState<QuizAttempt[]>([])
  const [filteredAttempts, setFilteredAttempts] = useState<QuizAttempt[]>([])
  const [categories, setCategories] = useState<any[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>("all")
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    let filtered = attempts

    if (searchTerm) {
      filtered = filtered.filter(attempt =>
        attempt.userEmail?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        attempt.sessionId.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (selectedCategory !== "all") {
      filtered = filtered.filter(attempt => attempt.categoryId === selectedCategory)
    }

    if (selectedTimeRange !== "all") {
      const now = new Date()
      const timeRanges = {
        today: new Date(now.getFullYear(), now.getMonth(), now.getDate()),
        week: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
        month: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      }
      
      if (timeRanges[selectedTimeRange as keyof typeof timeRanges]) {
        filtered = filtered.filter(attempt => 
          new Date(attempt.completedAt) >= timeRanges[selectedTimeRange as keyof typeof timeRanges]
        )
      }
    }

    setFilteredAttempts(filtered)
  }, [attempts, searchTerm, selectedCategory, selectedTimeRange])

  const loadData = async () => {
    try {
      // Load categories
      const categoriesResponse = await fetch('/api/v1/quiz/categories')
      const categoriesResult = await categoriesResponse.json()
      
      if (categoriesResult.success) {
        setCategories(categoriesResult.data.categories)
        
        // Mock attempts data (in real app, this would be an API call)
        const mockAttempts: QuizAttempt[] = [
          {
            id: '1',
            sessionId: 'sess_abc123',
            userId: 'user_123',
            userEmail: '<EMAIL>',
            categoryId: categoriesResult.data.categories[0]?.id || '1',
            categoryName: 'Frontend Development',
            categoryIcon: '💻',
            score: 85,
            totalQuestions: 5,
            correctAnswers: 4,
            timeSpent: 420, // 7 minutes
            completedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
            earnedAwards: ['Frontend Developer'],
            isAnonymous: false,
            ipAddress: '***********',
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          },
          {
            id: '2',
            sessionId: 'sess_def456',
            categoryId: categoriesResult.data.categories[1]?.id || '2',
            categoryName: 'Backend Development',
            categoryIcon: '⚙️',
            score: 92,
            totalQuestions: 5,
            correctAnswers: 5,
            timeSpent: 380, // 6.3 minutes
            completedAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(), // 5 hours ago
            earnedAwards: ['Backend Developer', 'Backend Architect'],
            isAnonymous: true,
            ipAddress: '***********',
            userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
          }
        ]
        
        setAttempts(mockAttempts)
      }
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 75) return 'text-blue-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const formatTimeSpent = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
      return `${diffInMinutes}m ago`
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  const exportAttempts = () => {
    // In a real app, this would generate and download a CSV/Excel file
    console.log('Exporting attempts data...')
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Quiz Attempts</h1>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Quiz Attempts</h1>
          <p className="text-muted-foreground">Monitor and analyze quiz attempts and performance</p>
        </div>
        <Button onClick={exportAttempts} className="button-enhanced">
          <Download className="w-4 h-4 mr-2" />
          Export Data
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Attempts</p>
                <p className="text-2xl font-bold">{attempts.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-green-100 rounded-lg">
                <TrendingUp className="w-4 h-4 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Average Score</p>
                <p className="text-2xl font-bold">
                  {attempts.length > 0 ? Math.round(attempts.reduce((sum, a) => sum + a.score, 0) / attempts.length) : 0}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Clock className="w-4 h-4 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Avg Time</p>
                <p className="text-2xl font-bold">
                  {attempts.length > 0 ? Math.round(attempts.reduce((sum, a) => sum + a.timeSpent, 0) / attempts.length / 60) : 0}m
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Target className="w-4 h-4 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Pass Rate</p>
                <p className="text-2xl font-bold">
                  {attempts.length > 0 ? Math.round(attempts.filter(a => a.score >= 70).length / attempts.length * 100) : 0}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search by email or session ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.icon} {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Time" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">Last Week</SelectItem>
                <SelectItem value="month">Last Month</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Score</TableHead>
                <TableHead>Questions</TableHead>
                <TableHead>Time</TableHead>
                <TableHead>Awards</TableHead>
                <TableHead>Completed</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAttempts.map((attempt) => (
                <TableRow key={attempt.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">
                        {attempt.isAnonymous ? (
                          <span className="text-gray-500">Anonymous User</span>
                        ) : (
                          attempt.userEmail
                        )}
                      </div>
                      <div className="text-sm text-gray-500">
                        Session: {attempt.sessionId.slice(0, 12)}...
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{attempt.categoryIcon}</span>
                      <span className="text-sm">{attempt.categoryName}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <span className={`font-bold ${getScoreColor(attempt.score)}`}>
                        {attempt.score}%
                      </span>
                      {attempt.score >= 70 && (
                        <Badge variant="secondary" className="text-xs">
                          Pass
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div>{attempt.correctAnswers}/{attempt.totalQuestions}</div>
                      <div className="text-gray-500">
                        {Math.round((attempt.correctAnswers / attempt.totalQuestions) * 100)}% correct
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{formatTimeSpent(attempt.timeSpent)}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {attempt.earnedAwards.map((award) => (
                        <Badge key={award} variant="secondary" className="text-xs">
                          🏆 {award}
                        </Badge>
                      ))}
                      {attempt.earnedAwards.length === 0 && (
                        <span className="text-gray-400 text-sm">None</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div>{formatDate(attempt.completedAt)}</div>
                      <div className="text-gray-500 flex items-center">
                        <Calendar className="w-3 h-3 mr-1" />
                        {new Date(attempt.completedAt).toLocaleDateString()}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/quizadmin/attempts/${attempt.id}`}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Download className="mr-2 h-4 w-4" />
                          Export Attempt
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}

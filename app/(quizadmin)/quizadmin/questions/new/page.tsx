"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Switch } from "@/components/ui/switch"
import { useQuizAdminStore } from "@/stores/quiz-admin.store"
import { ArrowLeft, Save, Loader2, Plus, X, CheckCircle } from "lucide-react"
import { toast } from "sonner"

const difficultyOptions = [
  { value: "beginner", label: "Beginner", color: "bg-green-100 text-green-800" },
  { value: "intermediate", label: "Intermediate", color: "bg-blue-100 text-blue-800" },
  { value: "advanced", label: "Advanced", color: "bg-purple-100 text-purple-800" },
  { value: "expert", label: "Expert", color: "bg-red-100 text-red-800" }
]

export default function NewQuestionPage() {
  const router = useRouter()
  const { 
    createQuestion, 
    questionsLoading, 
    categories, 
    fetchCategories 
  } = useQuizAdminStore()
  
  const [formData, setFormData] = useState({
    categoryId: "",
    question: "",
    options: ["", "", "", ""],
    correctAnswer: 0,
    explanation: "",
    difficulty: "",
    points: 1,
    tags: [] as string[],
    isActive: true
  })
  
  const [newTag, setNewTag] = useState("")
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    fetchCategories()
  }, [fetchCategories])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.categoryId) {
      newErrors.categoryId = "Category is required"
    }
    
    if (!formData.question.trim()) {
      newErrors.question = "Question is required"
    }
    
    if (!formData.difficulty) {
      newErrors.difficulty = "Difficulty level is required"
    }
    
    // Validate options
    const validOptions = formData.options.filter(opt => opt.trim())
    if (validOptions.length < 2) {
      newErrors.options = "At least 2 options are required"
    }
    
    // Check if correct answer is valid
    if (!formData.options[formData.correctAnswer]?.trim()) {
      newErrors.correctAnswer = "Please select a valid correct answer"
    }
    
    if (formData.points < 1 || formData.points > 10) {
      newErrors.points = "Points must be between 1 and 10"
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error("Please fix the form errors")
      return
    }
    
    // Filter out empty options
    const filteredOptions = formData.options.filter(opt => opt.trim())
    
    try {
      await createQuestion({
        ...formData,
        options: filteredOptions
      })
      toast.success("Question created successfully!")
      router.push("/quizadmin/questions")
    } catch (error) {
      toast.error("Failed to create question")
      console.error("Error creating question:", error)
    }
  }

  const updateOption = (index: number, value: string) => {
    const newOptions = [...formData.options]
    newOptions[index] = value
    setFormData(prev => ({ ...prev, options: newOptions }))
  }

  const addOption = () => {
    if (formData.options.length < 6) {
      setFormData(prev => ({
        ...prev,
        options: [...prev.options, ""]
      }))
    }
  }

  const removeOption = (index: number) => {
    if (formData.options.length > 2) {
      const newOptions = formData.options.filter((_, i) => i !== index)
      setFormData(prev => ({
        ...prev,
        options: newOptions,
        correctAnswer: prev.correctAnswer >= index ? Math.max(0, prev.correctAnswer - 1) : prev.correctAnswer
      }))
    }
  }

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }))
      setNewTag("")
    }
  }

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault()
      addTag()
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/quizadmin/questions">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Questions
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Create New Question</h1>
            <p className="text-muted-foreground">Add a new quiz question with multiple choice answers</p>
          </div>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Question Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="categoryId">Category *</Label>
                  <Select value={formData.categoryId} onValueChange={(value) => setFormData(prev => ({ ...prev, categoryId: value }))}>
                    <SelectTrigger className={errors.categoryId ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          <div className="flex items-center space-x-2">
                            <span className="text-lg">{category.icon}</span>
                            <span>{category.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.categoryId && <p className="text-sm text-red-500">{errors.categoryId}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="question">Question *</Label>
                  <Textarea
                    id="question"
                    value={formData.question}
                    onChange={(e) => setFormData(prev => ({ ...prev, question: e.target.value }))}
                    placeholder="Enter your question here..."
                    rows={3}
                    className={errors.question ? "border-red-500" : ""}
                  />
                  {errors.question && <p className="text-sm text-red-500">{errors.question}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="explanation">Explanation (Optional)</Label>
                  <Textarea
                    id="explanation"
                    value={formData.explanation}
                    onChange={(e) => setFormData(prev => ({ ...prev, explanation: e.target.value }))}
                    placeholder="Explain the correct answer..."
                    rows={2}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Answer Options */}
            <Card>
              <CardHeader>
                <CardTitle>Answer Options</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <RadioGroup 
                  value={formData.correctAnswer.toString()} 
                  onValueChange={(value) => setFormData(prev => ({ ...prev, correctAnswer: parseInt(value) }))}
                >
                  {formData.options.map((option, index) => (
                    <div key={index} className="flex items-center space-x-3 p-3 border rounded-lg">
                      <RadioGroupItem value={index.toString()} id={`option-${index}`} />
                      <Input
                        value={option}
                        onChange={(e) => updateOption(index, e.target.value)}
                        placeholder={`Option ${index + 1}`}
                        className="flex-1"
                      />
                      {formData.correctAnswer === index && (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      )}
                      {formData.options.length > 2 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeOption(index)}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                </RadioGroup>
                
                {errors.options && <p className="text-sm text-red-500">{errors.options}</p>}
                {errors.correctAnswer && <p className="text-sm text-red-500">{errors.correctAnswer}</p>}
                
                {formData.options.length < 6 && (
                  <Button type="button" variant="outline" onClick={addOption} className="w-full">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Option
                  </Button>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Settings Sidebar */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Question Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="difficulty">Difficulty Level *</Label>
                  <Select value={formData.difficulty} onValueChange={(value) => setFormData(prev => ({ ...prev, difficulty: value }))}>
                    <SelectTrigger className={errors.difficulty ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select difficulty" />
                    </SelectTrigger>
                    <SelectContent>
                      {difficultyOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center space-x-2">
                            <Badge className={option.color} variant="secondary">
                              {option.label}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.difficulty && <p className="text-sm text-red-500">{errors.difficulty}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="points">Points</Label>
                  <Input
                    id="points"
                    type="number"
                    min="1"
                    max="10"
                    value={formData.points}
                    onChange={(e) => setFormData(prev => ({ ...prev, points: parseInt(e.target.value) || 1 }))}
                    className={errors.points ? "border-red-500" : ""}
                  />
                  {errors.points && <p className="text-sm text-red-500">{errors.points}</p>}
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="isActive">Active Question</Label>
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Tags */}
            <Card>
              <CardHeader>
                <CardTitle>Tags</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Question Tags</Label>
                  <div className="flex space-x-2">
                    <Input
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Add a tag..."
                      className="flex-1"
                    />
                    <Button type="button" onClick={addTag} size="sm">
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>

                  <div className="flex flex-wrap gap-2 mt-3">
                    {formData.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="flex items-center space-x-1">
                        <span>{tag}</span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeTag(tag)}
                          className="h-4 w-4 p-0 hover:bg-transparent"
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Preview */}
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="p-4 border rounded-lg space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-sm">
                      {formData.question || "Your question will appear here..."}
                    </h3>
                    <Badge variant="outline" className="text-xs">
                      {formData.points} pts
                    </Badge>
                  </div>

                  <div className="space-y-2">
                    {formData.options.filter(opt => opt.trim()).map((option, index) => (
                      <div
                        key={index}
                        className={`p-2 border rounded text-sm ${
                          formData.correctAnswer === index ? 'bg-green-50 border-green-200' : 'bg-gray-50'
                        }`}
                      >
                        <div className="flex items-center space-x-2">
                          <div className={`w-3 h-3 rounded-full border ${
                            formData.correctAnswer === index ? 'bg-green-500 border-green-500' : 'border-gray-300'
                          }`} />
                          <span>{option || `Option ${index + 1}`}</span>
                          {formData.correctAnswer === index && (
                            <CheckCircle className="w-3 h-3 text-green-500" />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>

                  {formData.explanation && (
                    <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded text-sm">
                      <strong>Explanation:</strong> {formData.explanation}
                    </div>
                  )}

                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>
                      {categories.find(c => c.id === formData.categoryId)?.name || "Category"}
                    </span>
                    {formData.difficulty && (
                      <Badge className={difficultyOptions.find(d => d.value === formData.difficulty)?.color}>
                        {difficultyOptions.find(d => d.value === formData.difficulty)?.label}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" asChild>
            <Link href="/quizadmin/questions">Cancel</Link>
          </Button>
          <Button type="submit" disabled={questionsLoading}>
            {questionsLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Create Question
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}

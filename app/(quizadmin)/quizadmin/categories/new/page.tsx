"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { useQuizAdminStore } from "@/stores/quiz-admin.store"
import { ArrowLeft, Save, Loader2, Plus, X } from "lucide-react"
import { toast } from "sonner"

const difficultyOptions = [
  { value: "beginner", label: "Beginner", color: "bg-green-100 text-green-800" },
  { value: "intermediate", label: "Intermediate", color: "bg-blue-100 text-blue-800" },
  { value: "advanced", label: "Advanced", color: "bg-purple-100 text-purple-800" },
  { value: "expert", label: "Expert", color: "bg-red-100 text-red-800" }
]

const iconOptions = [
  "💻", "🌐", "📱", "🎨", "📊", "🔧", "🚀", "⚡", "🎯", "🔍",
  "📚", "🧠", "💡", "🔬", "📈", "🎮", "🎵", "📝", "🏆", "⭐"
]

const colorOptions = [
  "bg-blue-500", "bg-green-500", "bg-purple-500", "bg-red-500",
  "bg-yellow-500", "bg-indigo-500", "bg-pink-500", "bg-teal-500",
  "bg-orange-500", "bg-cyan-500"
]

export default function NewCategoryPage() {
  const router = useRouter()
  const { createCategory, categoriesLoading } = useQuizAdminStore()
  
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    icon: "📚",
    color: "bg-blue-500",
    skills: [] as string[],
    difficulty: "",
    estimatedTime: 15,
    totalQuestions: 10,
    passingScore: 70,
    isActive: true
  })
  
  const [newSkill, setNewSkill] = useState("")
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.name.trim()) {
      newErrors.name = "Category name is required"
    }
    
    if (!formData.description.trim()) {
      newErrors.description = "Description is required"
    }
    
    if (!formData.difficulty) {
      newErrors.difficulty = "Difficulty level is required"
    }
    
    if (formData.skills.length === 0) {
      newErrors.skills = "At least one skill is required"
    }
    
    if (formData.estimatedTime < 5 || formData.estimatedTime > 120) {
      newErrors.estimatedTime = "Estimated time must be between 5 and 120 minutes"
    }
    
    if (formData.totalQuestions < 5 || formData.totalQuestions > 100) {
      newErrors.totalQuestions = "Total questions must be between 5 and 100"
    }
    
    if (formData.passingScore < 50 || formData.passingScore > 100) {
      newErrors.passingScore = "Passing score must be between 50 and 100"
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error("Please fix the form errors")
      return
    }
    
    try {
      await createCategory(formData)
      toast.success("Category created successfully!")
      router.push("/quizadmin/categories")
    } catch (error) {
      toast.error("Failed to create category")
      console.error("Error creating category:", error)
    }
  }

  const addSkill = () => {
    if (newSkill.trim() && !formData.skills.includes(newSkill.trim())) {
      setFormData(prev => ({
        ...prev,
        skills: [...prev.skills, newSkill.trim()]
      }))
      setNewSkill("")
    }
  }

  const removeSkill = (skillToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.filter(skill => skill !== skillToRemove)
    }))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault()
      addSkill()
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/quizadmin/categories">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Categories
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Create New Category</h1>
            <p className="text-muted-foreground">Add a new quiz category with skills and settings</p>
          </div>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Information */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Category Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., JavaScript Development"
                    className={errors.name ? "border-red-500" : ""}
                  />
                  {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe what this category covers..."
                    rows={3}
                    className={errors.description ? "border-red-500" : ""}
                  />
                  {errors.description && <p className="text-sm text-red-500">{errors.description}</p>}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Icon</Label>
                    <div className="grid grid-cols-5 gap-2">
                      {iconOptions.map((icon) => (
                        <Button
                          key={icon}
                          type="button"
                          variant={formData.icon === icon ? "default" : "outline"}
                          size="sm"
                          onClick={() => setFormData(prev => ({ ...prev, icon }))}
                          className="h-10 text-lg"
                        >
                          {icon}
                        </Button>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Color Theme</Label>
                    <div className="grid grid-cols-5 gap-2">
                      {colorOptions.map((color) => (
                        <Button
                          key={color}
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => setFormData(prev => ({ ...prev, color }))}
                          className={`h-10 ${color} ${formData.color === color ? 'ring-2 ring-offset-2 ring-primary' : ''}`}
                        >
                          {formData.color === color && "✓"}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Skills */}
            <Card>
              <CardHeader>
                <CardTitle>Skills & Tags</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Skills *</Label>
                  <div className="flex space-x-2">
                    <Input
                      value={newSkill}
                      onChange={(e) => setNewSkill(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Add a skill..."
                      className="flex-1"
                    />
                    <Button type="button" onClick={addSkill} size="sm">
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>
                  {errors.skills && <p className="text-sm text-red-500">{errors.skills}</p>}

                  <div className="flex flex-wrap gap-2 mt-3">
                    {formData.skills.map((skill) => (
                      <Badge key={skill} variant="secondary" className="flex items-center space-x-1">
                        <span>{skill}</span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeSkill(skill)}
                          className="h-4 w-4 p-0 hover:bg-transparent"
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Settings Sidebar */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Category Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="difficulty">Difficulty Level *</Label>
                  <Select value={formData.difficulty} onValueChange={(value) => setFormData(prev => ({ ...prev, difficulty: value }))}>
                    <SelectTrigger className={errors.difficulty ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select difficulty" />
                    </SelectTrigger>
                    <SelectContent>
                      {difficultyOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center space-x-2">
                            <Badge className={option.color} variant="secondary">
                              {option.label}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.difficulty && <p className="text-sm text-red-500">{errors.difficulty}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="estimatedTime">Estimated Time (minutes)</Label>
                  <Input
                    id="estimatedTime"
                    type="number"
                    min="5"
                    max="120"
                    value={formData.estimatedTime}
                    onChange={(e) => setFormData(prev => ({ ...prev, estimatedTime: parseInt(e.target.value) || 15 }))}
                    className={errors.estimatedTime ? "border-red-500" : ""}
                  />
                  {errors.estimatedTime && <p className="text-sm text-red-500">{errors.estimatedTime}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="totalQuestions">Total Questions</Label>
                  <Input
                    id="totalQuestions"
                    type="number"
                    min="5"
                    max="100"
                    value={formData.totalQuestions}
                    onChange={(e) => setFormData(prev => ({ ...prev, totalQuestions: parseInt(e.target.value) || 10 }))}
                    className={errors.totalQuestions ? "border-red-500" : ""}
                  />
                  {errors.totalQuestions && <p className="text-sm text-red-500">{errors.totalQuestions}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="passingScore">Passing Score (%)</Label>
                  <Input
                    id="passingScore"
                    type="number"
                    min="50"
                    max="100"
                    value={formData.passingScore}
                    onChange={(e) => setFormData(prev => ({ ...prev, passingScore: parseInt(e.target.value) || 70 }))}
                    className={errors.passingScore ? "border-red-500" : ""}
                  />
                  {errors.passingScore && <p className="text-sm text-red-500">{errors.passingScore}</p>}
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="isActive">Active Category</Label>
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Preview */}
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="p-4 border rounded-lg space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">{formData.icon}</div>
                    <div>
                      <h3 className="font-semibold">{formData.name || "Category Name"}</h3>
                      <p className="text-sm text-muted-foreground">
                        {formData.totalQuestions} questions • {formData.estimatedTime} min
                      </p>
                    </div>
                  </div>

                  <p className="text-sm text-muted-foreground">
                    {formData.description || "Category description..."}
                  </p>

                  <div className="flex flex-wrap gap-1">
                    {formData.skills.slice(0, 3).map((skill) => (
                      <Badge key={skill} variant="secondary" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                    {formData.skills.length > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{formData.skills.length - 3}
                      </Badge>
                    )}
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <span>Pass: {formData.passingScore}%</span>
                    {formData.difficulty && (
                      <Badge className={difficultyOptions.find(d => d.value === formData.difficulty)?.color}>
                        {difficultyOptions.find(d => d.value === formData.difficulty)?.label}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" asChild>
            <Link href="/quizadmin/categories">Cancel</Link>
          </Button>
          <Button type="submit" disabled={categoriesLoading}>
            {categoriesLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Create Category
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}

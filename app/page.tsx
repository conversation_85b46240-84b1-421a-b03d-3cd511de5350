//app/page.tsx
"use client"
import { SharedLayout } from "@/components/layouts/shared-layout"
import { HeroSection } from "@/components/hero-section"
import { SkillsAssessment } from "@/components/skills-assessment"
import { CompanyShowcase } from "@/components/company-showcase"
import { TestimonialsSection } from "@/components/testimonials-section"
import { LocationBanner } from "@/components/location/location-banner"
import { LocationBasedJobsSection } from "@/components/home/<USER>"
import { SearchedJobsResults } from "@/components/search/searched-jobs-results"
import { useHomeJobsStore } from "@/stores/home-jobs.store"
import { useLocationStore } from "@/stores/location-store"

export default function HomePage() {
  const { currentLocation } = useLocationStore()
  const {
    searchState,
    isLoadingSearch,
    searchError,
    performAdvancedSearch,
    clearSearch
  } = useHomeJobsStore()



  // Enhanced search function with dynamic backend integration
  const handleSearch = async (query: string, location: string): Promise<void> => {
    const trimmedQuery = query.trim()
    const trimmedLocation = location.trim()

    if (!trimmedQuery && !trimmedLocation) {
      clearSearch()
      return
    }

    // Get user location data for search context
    const userLocationData = currentLocation ? {
      city: currentLocation.city,
      region: currentLocation.region,
      country: currentLocation.country,
      continent: currentLocation.continent
    } : undefined

    // Perform advanced search using the store
    await performAdvancedSearch(
      trimmedQuery,
      trimmedLocation,
      userLocationData
    )
  }

  const handleClearSearch = (): void => {
    clearSearch()
  }

  return (
    <SharedLayout>
      <LocationBanner />
      <HeroSection
        onSearch={handleSearch}
        isSearchActive={searchState.isActive}
        isSearchLoading={isLoadingSearch}
      />

      {searchState.isActive ? (
        <SearchedJobsResults
          jobs={searchState.results}
          searchQuery={searchState.query}
          onClearSearch={handleClearSearch}
          isLoading={isLoadingSearch}
          error={searchError}
          totalResults={searchState.totalResults}
          searchTime={searchState.searchTime}
          appliedFilters={searchState.appliedFilters}
        />
      ) : (
        <>
          <LocationBasedJobsSection />
          {/* <PremiumFeaturesSection /> */}
          <SkillsAssessment />
          {/* <CareerPathVisualizer /> */}
          <CompanyShowcase />
          <TestimonialsSection />
        </>
      )}
    </SharedLayout>
  )
}

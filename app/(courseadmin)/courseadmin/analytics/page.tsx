'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  BookOpen,
  DollarSign,
  Award,
  Clock,
  Star,
  Download,
  Calendar,
  Target,
  Activity
} from 'lucide-react'

export default function CourseAnalytics() {
  const [timeRange, setTimeRange] = useState('30d')
  const [activeTab, setActiveTab] = useState('overview')

  // Mock analytics data - in real app, this would come from API
  const analyticsData = {
    overview: {
      totalRevenue: 245000,
      revenueGrowth: 12.5,
      totalEnrollments: 1250,
      enrollmentGrowth: 8.3,
      completionRate: 78.5,
      completionGrowth: 5.2,
      averageRating: 4.6,
      ratingGrowth: 2.1
    },
    coursePerformance: [
      {
        id: '1',
        title: 'React from the Start',
        enrollments: 450,
        completions: 380,
        revenue: 31455,
        rating: 4.8,
        completionRate: 84.4
      },
      {
        id: '2',
        title: 'Basics of Python for Beginners',
        enrollments: 380,
        completions: 290,
        revenue: 18962,
        rating: 4.6,
        completionRate: 76.3
      },
      {
        id: '3',
        title: 'Advanced JavaScript Concepts',
        enrollments: 220,
        completions: 165,
        revenue: 21780,
        rating: 4.7,
        completionRate: 75.0
      }
    ],
    monthlyData: [
      { month: 'Jan', enrollments: 85, revenue: 8500, completions: 65 },
      { month: 'Feb', enrollments: 120, revenue: 12000, completions: 95 },
      { month: 'Mar', enrollments: 150, revenue: 15500, completions: 118 },
      { month: 'Apr', enrollments: 180, revenue: 18200, completions: 142 },
      { month: 'May', enrollments: 210, revenue: 21800, completions: 168 },
      { month: 'Jun', enrollments: 245, revenue: 25200, completions: 195 }
    ]
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`
  }

  const getGrowthIcon = (growth: number) => {
    return growth > 0 ? (
      <TrendingUp className="w-4 h-4 text-green-600" />
    ) : (
      <TrendingDown className="w-4 h-4 text-red-600" />
    )
  }

  const getGrowthColor = (growth: number) => {
    return growth > 0 ? 'text-green-600' : 'text-red-600'
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Course Analytics</h1>
          <p className="text-muted-foreground">Track performance and insights across all courses</p>
        </div>
        <div className="flex space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                <p className="text-2xl font-bold">{formatCurrency(analyticsData.overview.totalRevenue)}</p>
                <div className="flex items-center space-x-1 mt-1">
                  {getGrowthIcon(analyticsData.overview.revenueGrowth)}
                  <span className={`text-sm ${getGrowthColor(analyticsData.overview.revenueGrowth)}`}>
                    {formatPercentage(analyticsData.overview.revenueGrowth)}
                  </span>
                </div>
              </div>
              <DollarSign className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Enrollments</p>
                <p className="text-2xl font-bold">{analyticsData.overview.totalEnrollments.toLocaleString()}</p>
                <div className="flex items-center space-x-1 mt-1">
                  {getGrowthIcon(analyticsData.overview.enrollmentGrowth)}
                  <span className={`text-sm ${getGrowthColor(analyticsData.overview.enrollmentGrowth)}`}>
                    {formatPercentage(analyticsData.overview.enrollmentGrowth)}
                  </span>
                </div>
              </div>
              <Users className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completion Rate</p>
                <p className="text-2xl font-bold">{analyticsData.overview.completionRate}%</p>
                <div className="flex items-center space-x-1 mt-1">
                  {getGrowthIcon(analyticsData.overview.completionGrowth)}
                  <span className={`text-sm ${getGrowthColor(analyticsData.overview.completionGrowth)}`}>
                    {formatPercentage(analyticsData.overview.completionGrowth)}
                  </span>
                </div>
              </div>
              <Target className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Average Rating</p>
                <p className="text-2xl font-bold">{analyticsData.overview.averageRating}</p>
                <div className="flex items-center space-x-1 mt-1">
                  {getGrowthIcon(analyticsData.overview.ratingGrowth)}
                  <span className={`text-sm ${getGrowthColor(analyticsData.overview.ratingGrowth)}`}>
                    {formatPercentage(analyticsData.overview.ratingGrowth)}
                  </span>
                </div>
              </div>
              <Star className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="courses">Course Performance</TabsTrigger>
          <TabsTrigger value="students">Student Analytics</TabsTrigger>
          <TabsTrigger value="revenue">Revenue Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Monthly Trends</CardTitle>
                <CardDescription>Enrollments and revenue over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analyticsData.monthlyData.map((data, index) => (
                    <div key={data.month} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-blue-600">{data.month}</span>
                        </div>
                        <div>
                          <p className="font-medium">{data.enrollments} enrollments</p>
                          <p className="text-sm text-muted-foreground">{formatCurrency(data.revenue)}</p>
                        </div>
                      </div>
                      <Badge variant="secondary">{data.completions} completed</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Performing Courses</CardTitle>
                <CardDescription>Courses with highest engagement</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analyticsData.coursePerformance.map((course, index) => (
                    <div key={course.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-br from-primary/10 to-primary/20 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-primary">#{index + 1}</span>
                        </div>
                        <div>
                          <p className="font-medium">{course.title}</p>
                          <p className="text-sm text-muted-foreground">
                            {course.enrollments} students • {course.completionRate}% completion
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{formatCurrency(course.revenue)}</p>
                        <div className="flex items-center space-x-1">
                          <Star className="w-3 h-3 text-yellow-500 fill-current" />
                          <span className="text-sm">{course.rating}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="courses" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Course Performance Metrics</CardTitle>
              <CardDescription>Detailed analytics for each course</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {analyticsData.coursePerformance.map((course) => (
                  <div key={course.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold">{course.title}</h3>
                      <Badge variant="outline">{course.rating} ⭐</Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mx-auto mb-2">
                          <Users className="w-6 h-6 text-blue-600" />
                        </div>
                        <p className="text-2xl font-bold">{course.enrollments}</p>
                        <p className="text-sm text-muted-foreground">Enrollments</p>
                      </div>
                      
                      <div className="text-center">
                        <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mx-auto mb-2">
                          <Award className="w-6 h-6 text-green-600" />
                        </div>
                        <p className="text-2xl font-bold">{course.completions}</p>
                        <p className="text-sm text-muted-foreground">Completions</p>
                      </div>
                      
                      <div className="text-center">
                        <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mx-auto mb-2">
                          <Target className="w-6 h-6 text-purple-600" />
                        </div>
                        <p className="text-2xl font-bold">{course.completionRate}%</p>
                        <p className="text-sm text-muted-foreground">Completion Rate</p>
                      </div>
                      
                      <div className="text-center">
                        <div className="flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-full mx-auto mb-2">
                          <DollarSign className="w-6 h-6 text-yellow-600" />
                        </div>
                        <p className="text-2xl font-bold">{formatCurrency(course.revenue)}</p>
                        <p className="text-sm text-muted-foreground">Revenue</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="students" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Student Analytics</CardTitle>
              <CardDescription>Student engagement and behavior insights</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Activity className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">Student analytics dashboard coming soon</p>
                <p className="text-sm text-muted-foreground mt-2">
                  Track student engagement, learning patterns, and success metrics
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Analysis</CardTitle>
              <CardDescription>Financial performance and trends</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <BarChart3 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">Revenue analytics dashboard coming soon</p>
                <p className="text-sm text-muted-foreground mt-2">
                  Detailed revenue breakdowns, forecasting, and financial insights
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </motion.div>
  )
}

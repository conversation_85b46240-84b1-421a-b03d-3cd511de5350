'use client'

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft, BookOpen, Settings } from 'lucide-react'
import { CourseContentEditor } from '@/components/course-admin/course-content-editor'
import { useAuthStore } from '@/stores/auth.store'
import { useToast } from '@/hooks/use-toast'

interface Course {
  _id: string
  title: string
  slug: string
  description: string
  isPublished: boolean
  totalLessons: number
  totalStudents: number
}

export default function CourseContentPage() {
  const params = useParams()
  const router = useRouter()
  const slug = params.slug as string
  
  const [course, setCourse] = useState<Course | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  
  const { token } = useAuthStore()
  const { toast } = useToast()

  useEffect(() => {
    fetchCourse()
  }, [slug])

  const fetchCourse = async () => {
    try {
      const headers: Record<string, string> = {}
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch(`/api/v1/courses/${slug}`, {
        headers
      })

      if (response.ok) {
        const result = await response.json()
        setCourse(result.data.course)
      } else {
        toast({
          title: "Error",
          description: "Course not found",
          variant: "destructive"
        })
        router.push('/courseadmin/courses')
      }
    } catch (error) {
      console.error('Error fetching course:', error)
      toast({
        title: "Error",
        description: "Failed to load course",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <div className="h-10 w-10 bg-muted rounded animate-pulse"></div>
            <div className="space-y-2">
              <div className="h-6 w-48 bg-muted rounded animate-pulse"></div>
              <div className="h-4 w-32 bg-muted rounded animate-pulse"></div>
            </div>
          </div>
          
          <Card>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-muted rounded w-1/3"></div>
                <div className="h-3 bg-muted rounded w-2/3"></div>
                <div className="space-y-2">
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-12 text-center">
            <BookOpen className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Course not found</h3>
            <p className="text-muted-foreground mb-4">
              The course you're looking for doesn't exist or you don't have permission to access it.
            </p>
            <Button onClick={() => router.push('/courseadmin/courses')}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Courses
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-6"
      >
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              onClick={() => router.push('/courseadmin/courses')}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Courses
            </Button>
            <div>
              <h1 className="text-3xl font-bold">Course Content</h1>
              <p className="text-muted-foreground">
                Manage sections, lessons, and quizzes for "{course.title}"
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              onClick={() => router.push(`/courseadmin/courses/${slug}`)}
            >
              <Settings className="w-4 h-4 mr-2" />
              Course Settings
            </Button>
            <Button
              onClick={() => router.push(`/courses/${slug}`)}
              target="_blank"
            >
              <BookOpen className="w-4 h-4 mr-2" />
              Preview Course
            </Button>
          </div>
        </div>

        {/* Course Info */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>{course.title}</CardTitle>
                <CardDescription>{course.description}</CardDescription>
              </div>
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                <div className="text-center">
                  <div className="font-semibold text-lg text-foreground">
                    {course.totalLessons}
                  </div>
                  <div>Lessons</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-lg text-foreground">
                    {course.totalStudents}
                  </div>
                  <div>Students</div>
                </div>
                <div className="text-center">
                  <div className={`font-semibold text-lg ${course.isPublished ? 'text-green-600' : 'text-yellow-600'}`}>
                    {course.isPublished ? 'Published' : 'Draft'}
                  </div>
                  <div>Status</div>
                </div>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Content Editor */}
        <CourseContentEditor 
          courseSlug={course.slug} 
          courseTitle={course.title}
        />
      </motion.div>
    </div>
  )
}

'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Eye,
  Mail,
  Phone,
  BookOpen,
  Users,
  Star,
  DollarSign,
  TrendingUp,
  UserCheck,
  UserX,
  Award
} from 'lucide-react'
import { useCourses } from '@/stores/course.store'

interface Instructor {
  _id: string
  name: string
  email: string
  avatar?: string
  bio?: string
  expertise: string[]
  totalCourses: number
  totalStudents: number
  averageRating: number
  totalEarnings: number
  isActive: boolean
  joinedAt: Date
  lastActive: Date
}

export default function InstructorsManagement() {
  const [searchQuery, setSearchQuery] = useState('')
  const [instructors, setInstructors] = useState<Instructor[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const { courses, fetchCourses } = useCourses()

  useEffect(() => {
    fetchCourses()
    // Mock instructor data - in real app, this would come from API
    setInstructors([
      {
        _id: '1',
        name: 'Dr. Sarah Johnson',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        bio: 'Full-stack developer with 10+ years experience in React, Node.js, and Python.',
        expertise: ['React', 'Node.js', 'Python', 'JavaScript', 'TypeScript'],
        totalCourses: 8,
        totalStudents: 12450,
        averageRating: 4.8,
        totalEarnings: 125000,
        isActive: true,
        joinedAt: new Date('2022-01-15'),
        lastActive: new Date()
      },
      {
        _id: '2',
        name: 'Prof. Michael Chen',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        bio: 'Data Science expert specializing in Machine Learning and AI applications.',
        expertise: ['Python', 'Machine Learning', 'Data Science', 'TensorFlow', 'PyTorch'],
        totalCourses: 5,
        totalStudents: 8200,
        averageRating: 4.9,
        totalEarnings: 98000,
        isActive: true,
        joinedAt: new Date('2021-08-20'),
        lastActive: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
      },
      {
        _id: '3',
        name: 'Emily Rodriguez',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        bio: 'UX/UI Designer with expertise in modern design systems and user research.',
        expertise: ['UI/UX Design', 'Figma', 'Adobe Creative Suite', 'Design Systems'],
        totalCourses: 3,
        totalStudents: 5600,
        averageRating: 4.7,
        totalEarnings: 67000,
        isActive: false,
        joinedAt: new Date('2023-03-10'),
        lastActive: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      }
    ])
    setIsLoading(false)
  }, [fetchCourses])

  // Filter instructors based on search
  const filteredInstructors = instructors.filter(instructor =>
    instructor.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    instructor.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    instructor.expertise.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  const handleToggleStatus = async (instructorId: string) => {
    // TODO: Implement API call to toggle instructor status
    setInstructors(prev => prev.map(instructor => 
      instructor._id === instructorId 
        ? { ...instructor, isActive: !instructor.isActive }
        : instructor
    ))
  }

  const getStatusBadge = (instructor: Instructor) => {
    if (!instructor.isActive) {
      return <Badge variant="secondary">Inactive</Badge>
    }
    
    const daysSinceActive = Math.floor((Date.now() - instructor.lastActive.getTime()) / (1000 * 60 * 60 * 24))
    
    if (daysSinceActive === 0) {
      return <Badge className="bg-green-100 text-green-800">Online</Badge>
    } else if (daysSinceActive <= 7) {
      return <Badge className="bg-yellow-100 text-yellow-800">Recent</Badge>
    } else {
      return <Badge variant="outline">Away</Badge>
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Instructor Management</h1>
          <p className="text-muted-foreground">Manage course instructors and their performance</p>
        </div>
        <Button className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700">
          <Plus className="w-4 h-4 mr-2" />
          Invite Instructor
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Instructors</p>
                <p className="text-2xl font-bold">{instructors.length}</p>
              </div>
              <Users className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Instructors</p>
                <p className="text-2xl font-bold">{instructors.filter(i => i.isActive).length}</p>
              </div>
              <UserCheck className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Students</p>
                <p className="text-2xl font-bold">
                  {instructors.reduce((sum, instructor) => sum + instructor.totalStudents, 0).toLocaleString()}
                </p>
              </div>
              <BookOpen className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Earnings</p>
                <p className="text-2xl font-bold">
                  ${instructors.reduce((sum, instructor) => sum + instructor.totalEarnings, 0).toLocaleString()}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Search instructors by name, email, or expertise..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Instructors Table */}
      <Card>
        <CardHeader>
          <CardTitle>Instructors ({filteredInstructors.length})</CardTitle>
          <CardDescription>Manage instructor profiles and performance</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Instructor</TableHead>
                <TableHead>Expertise</TableHead>
                <TableHead>Courses</TableHead>
                <TableHead>Students</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead>Earnings</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredInstructors.map((instructor) => (
                <TableRow key={instructor._id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar className="w-10 h-10">
                        <AvatarImage src={instructor.avatar} alt={instructor.name} />
                        <AvatarFallback>
                          {instructor.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{instructor.name}</p>
                        <p className="text-sm text-muted-foreground">{instructor.email}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {instructor.expertise.slice(0, 3).map((skill, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {skill}
                        </Badge>
                      ))}
                      {instructor.expertise.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{instructor.expertise.length - 3}
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <BookOpen className="w-4 h-4 text-muted-foreground" />
                      <span>{instructor.totalCourses}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4 text-muted-foreground" />
                      <span>{instructor.totalStudents.toLocaleString()}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-500 fill-current" />
                      <span>{instructor.averageRating.toFixed(1)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <DollarSign className="w-4 h-4 text-muted-foreground" />
                      <span>${instructor.totalEarnings.toLocaleString()}</span>
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(instructor)}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem>
                          <Eye className="w-4 h-4 mr-2" />
                          View Profile
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit Instructor
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Mail className="w-4 h-4 mr-2" />
                          Send Message
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleToggleStatus(instructor._id)}>
                          {instructor.isActive ? (
                            <>
                              <UserX className="w-4 h-4 mr-2" />
                              Deactivate
                            </>
                          ) : (
                            <>
                              <UserCheck className="w-4 h-4 mr-2" />
                              Activate
                            </>
                          )}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredInstructors.length === 0 && (
            <div className="text-center py-8">
              <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No instructors found matching your criteria</p>
              <Button className="mt-4">
                <Plus className="w-4 h-4 mr-2" />
                Invite Your First Instructor
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}

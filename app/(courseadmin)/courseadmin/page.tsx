'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  BookOpen, 
  Users, 
  GraduationCap, 
  TrendingUp, 
  DollarSign,
  Plus,
  BarChart3,
  Clock,
  Star,
  Award,
  PlayCircle,
  FileText,
  AlertCircle,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { useCourseCategories, useCourses } from '@/stores/course.store'
import { useAuthStore } from '@/stores/auth.store'
import { useToast } from '@/hooks/use-toast'

interface CourseStats {
  totalCourses: number
  publishedCourses: number
  draftCourses: number
  featuredCourses: number
  totalStudents: number
  totalRevenue: number
  recentEnrollments: number
  totalSections: number
  totalLessons: number
  enrollmentGrowth: number
}

interface TopCourse {
  _id: string
  title: string
  slug: string
  totalStudents: number
  rating: number
  totalRevenue: number
}

export default function CourseAdminDashboard() {
  const [activeTab, setActiveTab] = useState('overview')
  const [stats, setStats] = useState<CourseStats>({
    totalCourses: 0,
    publishedCourses: 0,
    draftCourses: 0,
    featuredCourses: 0,
    totalStudents: 0,
    totalRevenue: 0,
    recentEnrollments: 0,
    totalSections: 0,
    totalLessons: 0,
    enrollmentGrowth: 0
  })
  const [topCourses, setTopCourses] = useState<TopCourse[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const { toast } = useToast()

  const { categories, fetchCategories } = useCourseCategories()
  const { courses, fetchCourses } = useCourses()

  useEffect(() => {
    fetchCategories()
    fetchCourses()
    fetchStats()
  }, [fetchCategories, fetchCourses])

  const fetchStats = async () => {
    try {
      const { token, isAuthenticated } = useAuthStore.getState()

      if (!isAuthenticated || !token) {
        console.log('User not authenticated, using mock data')
        // Set mock data for demonstration
        setStats({
          totalCourses: 12,
          publishedCourses: 8,
          draftCourses: 4,
          featuredCourses: 3,
          totalStudents: 245,
          totalRevenue: 12450,
          recentEnrollments: 23,
          totalSections: 45,
          totalLessons: 156,
          enrollmentGrowth: 15.5
        })
        setTopCourses([
          { _id: '1', title: 'React Fundamentals', slug: 'react-fundamentals', totalStudents: 89, rating: 4.8, totalRevenue: 4450 },
          { _id: '2', title: 'JavaScript Mastery', slug: 'javascript-mastery', totalStudents: 67, rating: 4.6, totalRevenue: 3350 },
          { _id: '3', title: 'Node.js Backend', slug: 'nodejs-backend', totalStudents: 45, rating: 4.7, totalRevenue: 2250 }
        ])
        setIsLoading(false)
        return
      }

      const headers: Record<string, string> = {
        'Authorization': `Bearer ${token}`
      }

      const response = await fetch('/api/v1/courseadmin/stats', {
        headers
      })

      if (response.ok) {
        const result = await response.json()
        setStats(result.data.overview)
        setTopCourses(result.data.topCourses || [])
      } else if (response.status === 401) {
        console.log('Authentication failed, user may need to log in again')
        toast({
          title: "Authentication Required",
          description: "Please log in to view dashboard statistics",
          variant: "destructive"
        })
      } else {
        throw new Error('Failed to fetch stats')
      }
    } catch (error) {
      console.error('Error fetching stats:', error)
      toast({
        title: "Error",
        description: "Failed to load dashboard statistics",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  return (
    <motion.div 
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header */}
      <motion.div variants={itemVariants} className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Course Management Dashboard</h1>
          <p className="text-muted-foreground">Manage courses, instructors, and student enrollments</p>
        </div>
        <div className="flex space-x-3">
          <Button asChild className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700">
            <Link href="/courseadmin/courses/new">
              <Plus className="w-4 h-4 mr-2" />
              New Course
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/courseadmin/analytics">
              <BarChart3 className="w-4 h-4 mr-2" />
              View Analytics
            </Link>
          </Button>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-border/50 hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Courses</p>
                {isLoading ? (
                  <div className="flex items-center space-x-2">
                    <div className="h-8 w-16 bg-muted rounded animate-pulse"></div>
                  </div>
                ) : (
                  <p className="text-2xl font-bold">{stats.totalCourses}</p>
                )}
                <p className="text-xs text-green-600">
                  {stats.publishedCourses} published, {stats.draftCourses} drafts
                  {stats.featuredCourses > 0 && `, ${stats.featuredCourses} featured`}
                </p>
              </div>
              <BookOpen className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-border/50 hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Students</p>
                <p className="text-2xl font-bold">{stats.totalStudents.toLocaleString()}</p>
                <p className="text-xs text-green-600">
                  {stats.recentEnrollments > 0 && `+${stats.recentEnrollments} this month`}
                  {stats.enrollmentGrowth !== 0 && (
                    <span className={stats.enrollmentGrowth > 0 ? 'text-green-600' : 'text-red-600'}>
                      {stats.enrollmentGrowth > 0 ? ' ↗' : ' ↘'} {Math.abs(stats.enrollmentGrowth)}%
                    </span>
                  )}
                </p>
              </div>
              <GraduationCap className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-border/50 hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                <p className="text-2xl font-bold">${stats.totalRevenue.toLocaleString()}</p>
                <p className="text-xs text-green-600">From course sales</p>
              </div>
              <DollarSign className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-border/50 hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Course Content</p>
                <p className="text-2xl font-bold">{stats.totalLessons}</p>
                <p className="text-xs text-green-600">
                  {stats.totalSections} sections, {stats.totalLessons} lessons
                </p>
              </div>
              <FileText className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Quick Actions */}
      <motion.div variants={itemVariants}>
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common course management tasks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button 
                variant="outline" 
                className="h-auto p-4 flex flex-col items-center space-y-2 hover:bg-blue-50 hover:border-blue-200"
                asChild
              >
                <Link href="/courseadmin/courses">
                  <BookOpen className="w-6 h-6 text-blue-600" />
                  <span>Manage Courses</span>
                </Link>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-auto p-4 flex flex-col items-center space-y-2 hover:bg-green-50 hover:border-green-200"
                asChild
              >
                <Link href="/courseadmin/instructors">
                  <Users className="w-6 h-6 text-green-600" />
                  <span>Manage Instructors</span>
                </Link>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-auto p-4 flex flex-col items-center space-y-2 hover:bg-purple-50 hover:border-purple-200"
                asChild
              >
                <Link href="/courseadmin/enrollments">
                  <GraduationCap className="w-6 h-6 text-purple-600" />
                  <span>View Enrollments</span>
                </Link>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-auto p-4 flex flex-col items-center space-y-2 hover:bg-orange-50 hover:border-orange-200"
                asChild
              >
                <Link href="/courseadmin/categories">
                  <FileText className="w-6 h-6 text-orange-600" />
                  <span>Manage Categories</span>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Tabs for detailed views */}
      <motion.div variants={itemVariants}>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="recent">Recent Activity</TabsTrigger>
            <TabsTrigger value="courses">Top Courses</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Course Categories</CardTitle>
                  <CardDescription>Distribution of courses by category</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {categories.map((category) => (
                      <div key={category._id} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{category.icon}</span>
                          <span className="font-medium">{category.name}</span>
                        </div>
                        <Badge variant="secondary">
                          {courses.filter(course => course.categoryId._id === category._id).length} courses
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Course Status</CardTitle>
                  <CardDescription>Current status of all courses</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                        <span>Published Courses</span>
                      </div>
                      <Badge className="bg-green-100 text-green-800">
                        {stats.publishedCourses}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Clock className="w-5 h-5 text-yellow-600" />
                        <span>Draft Courses</span>
                      </div>
                      <Badge className="bg-yellow-100 text-yellow-800">
                        {stats.draftCourses}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Star className="w-5 h-5 text-blue-600" />
                        <span>Featured Courses</span>
                      </div>
                      <Badge className="bg-blue-100 text-blue-800">
                        {courses.filter(course => course.isFeatured).length}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="recent" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Latest course management activities</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Recent activity tracking will be displayed here</p>
                  <p className="text-sm text-muted-foreground mt-2">Implementation in progress...</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="courses" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Courses</CardTitle>
                <CardDescription>Courses with highest enrollment and ratings</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {courses
                    .sort((a, b) => b.totalStudents - a.totalStudents)
                    .slice(0, 5)
                    .map((course) => (
                      <div key={course._id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <h4 className="font-medium">{course.title}</h4>
                          <p className="text-sm text-muted-foreground">
                            {course.totalStudents} students • {course.rating.toFixed(1)} ⭐
                          </p>
                        </div>
                        <Badge variant={course.isPublished ? "default" : "secondary"}>
                          {course.isPublished ? "Published" : "Draft"}
                        </Badge>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>
    </motion.div>
  )
}

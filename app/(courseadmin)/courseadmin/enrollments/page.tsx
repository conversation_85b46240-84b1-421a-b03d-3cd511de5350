'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Mail,
  Award,
  Clock,
  CheckCircle,
  XCircle,
  Users,
  BookOpen,
  TrendingUp,
  Calendar,
  DollarSign
} from 'lucide-react'
import { useCourses } from '@/stores/course.store'

interface Enrollment {
  _id: string
  student: {
    _id: string
    name: string
    email: string
    avatar?: string
  }
  course: {
    _id: string
    title: string
    thumbnail: string
    price: number
  }
  enrolledAt: Date
  progress: number
  completedAt?: Date
  lastAccessedAt: Date
  certificateIssued: boolean
  paymentStatus: 'pending' | 'completed' | 'failed' | 'refunded'
  paymentAmount: number
}

export default function EnrollmentsManagement() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCourse, setSelectedCourse] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [enrollments, setEnrollments] = useState<Enrollment[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const { courses, fetchCourses } = useCourses()

  useEffect(() => {
    fetchCourses()
    // Mock enrollment data - in real app, this would come from API
    setEnrollments([
      {
        _id: '1',
        student: {
          _id: 'student1',
          name: 'John Doe',
          email: '<EMAIL>',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
        },
        course: {
          _id: 'course1',
          title: 'React from the Start',
          thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=100&h=100&fit=crop',
          price: 69.99
        },
        enrolledAt: new Date('2024-01-15'),
        progress: 85,
        lastAccessedAt: new Date(),
        certificateIssued: false,
        paymentStatus: 'completed',
        paymentAmount: 69.99
      },
      {
        _id: '2',
        student: {
          _id: 'student2',
          name: 'Jane Smith',
          email: '<EMAIL>',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
        },
        course: {
          _id: 'course2',
          title: 'Basics of Python for Beginners',
          thumbnail: 'https://images.unsplash.com/photo-1526379879527-8559ecfcaec0?w=100&h=100&fit=crop',
          price: 49.99
        },
        enrolledAt: new Date('2024-02-01'),
        progress: 100,
        completedAt: new Date('2024-02-28'),
        lastAccessedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        certificateIssued: true,
        paymentStatus: 'completed',
        paymentAmount: 49.99
      },
      {
        _id: '3',
        student: {
          _id: 'student3',
          name: 'Mike Johnson',
          email: '<EMAIL>'
        },
        course: {
          _id: 'course1',
          title: 'React from the Start',
          thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=100&h=100&fit=crop',
          price: 69.99
        },
        enrolledAt: new Date('2024-01-20'),
        progress: 45,
        lastAccessedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
        certificateIssued: false,
        paymentStatus: 'pending',
        paymentAmount: 69.99
      }
    ])
    setIsLoading(false)
  }, [fetchCourses])

  // Filter enrollments based on search and filters
  const filteredEnrollments = enrollments.filter(enrollment => {
    const matchesSearch = enrollment.student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         enrollment.student.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         enrollment.course.title.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesCourse = selectedCourse === 'all' || enrollment.course._id === selectedCourse
    
    const matchesStatus = selectedStatus === 'all' ||
                         (selectedStatus === 'completed' && enrollment.progress === 100) ||
                         (selectedStatus === 'in-progress' && enrollment.progress > 0 && enrollment.progress < 100) ||
                         (selectedStatus === 'not-started' && enrollment.progress === 0) ||
                         (selectedStatus === 'certified' && enrollment.certificateIssued)
    
    return matchesSearch && matchesCourse && matchesStatus
  })

  const getStatusBadge = (enrollment: Enrollment) => {
    if (enrollment.progress === 100) {
      return <Badge className="bg-green-100 text-green-800">Completed</Badge>
    } else if (enrollment.progress > 0) {
      return <Badge className="bg-blue-100 text-blue-800">In Progress</Badge>
    } else {
      return <Badge variant="secondary">Not Started</Badge>
    }
  }

  const getPaymentBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Paid</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>
      case 'refunded':
        return <Badge variant="outline">Refunded</Badge>
      default:
        return <Badge variant="secondary">Unknown</Badge>
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date)
  }

  const totalRevenue = enrollments
    .filter(e => e.paymentStatus === 'completed')
    .reduce((sum, e) => sum + e.paymentAmount, 0)

  const completionRate = enrollments.length > 0 
    ? (enrollments.filter(e => e.progress === 100).length / enrollments.length) * 100 
    : 0

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Student Enrollments</h1>
          <p className="text-muted-foreground">Track student progress and course completions</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Enrollments</p>
                <p className="text-2xl font-bold">{enrollments.length}</p>
              </div>
              <Users className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold">{enrollments.filter(e => e.progress === 100).length}</p>
                <p className="text-xs text-green-600">{completionRate.toFixed(1)}% completion rate</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Certificates Issued</p>
                <p className="text-2xl font-bold">{enrollments.filter(e => e.certificateIssued).length}</p>
              </div>
              <Award className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                <p className="text-2xl font-bold">${totalRevenue.toLocaleString()}</p>
              </div>
              <DollarSign className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search students or courses..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={selectedCourse} onValueChange={setSelectedCourse}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="All Courses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Courses</SelectItem>
                {courses.map((course) => (
                  <SelectItem key={course._id} value={course._id}>
                    {course.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="in-progress">In Progress</SelectItem>
                <SelectItem value="not-started">Not Started</SelectItem>
                <SelectItem value="certified">Certified</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Enrollments Table */}
      <Card>
        <CardHeader>
          <CardTitle>Enrollments ({filteredEnrollments.length})</CardTitle>
          <CardDescription>Monitor student progress and engagement</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student</TableHead>
                <TableHead>Course</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>Enrolled</TableHead>
                <TableHead>Last Active</TableHead>
                <TableHead>Payment</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredEnrollments.map((enrollment) => (
                <TableRow key={enrollment._id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar className="w-10 h-10">
                        <AvatarImage src={enrollment.student.avatar} alt={enrollment.student.name} />
                        <AvatarFallback>
                          {enrollment.student.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{enrollment.student.name}</p>
                        <p className="text-sm text-muted-foreground">{enrollment.student.email}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <img 
                        src={enrollment.course.thumbnail} 
                        alt={enrollment.course.title}
                        className="w-10 h-10 rounded object-cover"
                      />
                      <div>
                        <p className="font-medium">{enrollment.course.title}</p>
                        <p className="text-sm text-muted-foreground">${enrollment.course.price}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{enrollment.progress}%</span>
                        {enrollment.certificateIssued && (
                          <Award className="w-4 h-4 text-yellow-500" />
                        )}
                      </div>
                      <Progress value={enrollment.progress} className="w-20" />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">{formatDate(enrollment.enrolledAt)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">{formatDate(enrollment.lastAccessedAt)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {getPaymentBadge(enrollment.paymentStatus)}
                      <p className="text-xs text-muted-foreground">${enrollment.paymentAmount}</p>
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(enrollment)}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem>
                          <Eye className="w-4 h-4 mr-2" />
                          View Progress
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Mail className="w-4 h-4 mr-2" />
                          Contact Student
                        </DropdownMenuItem>
                        {enrollment.progress === 100 && !enrollment.certificateIssued && (
                          <DropdownMenuItem>
                            <Award className="w-4 h-4 mr-2" />
                            Issue Certificate
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600">
                          <XCircle className="w-4 h-4 mr-2" />
                          Revoke Access
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredEnrollments.length === 0 && (
            <div className="text-center py-8">
              <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No enrollments found matching your criteria</p>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}

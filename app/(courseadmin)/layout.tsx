'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/auth.store'
import { CourseAdminSidebar } from '@/components/course-admin/course-admin-sidebar'
import { CourseAdminTopbar } from '@/components/course-admin/course-admin-topbar'
import { Toaster } from '@/components/ui/toaster'
import { cn } from '@/lib/utils'

interface CourseAdminLayoutProps {
  children: React.ReactNode
}

export default function CourseAdminLayout({ children }: CourseAdminLayoutProps) {
  const router = useRouter()
  const { user, isAuthenticated, isLoading } = useAuthStore()

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push('/login?redirect=/courseadmin')
        return
      }

      // Check if user has course admin permissions
      // Allow admin users and course instructors to access course admin
      if (user?.role !== 'admin' && user?.role !== 'instructor') {
        router.push('/client-dashboard')
        return
      }
    }
  }, [isAuthenticated, isLoading, user, router])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!isAuthenticated || (user?.role !== 'admin' && user?.role !== 'instructor')) {
    return null
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="flex h-screen overflow-hidden">
        {/* Sidebar */}
        <CourseAdminSidebar />
        
        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Topbar */}
          <CourseAdminTopbar />
          
          {/* Page Content */}
          <main className="flex-1 overflow-y-auto bg-background">
            <div className="container mx-auto p-6">
              {children}
            </div>
          </main>
        </div>
      </div>
      
      <Toaster />
    </div>
  )
}

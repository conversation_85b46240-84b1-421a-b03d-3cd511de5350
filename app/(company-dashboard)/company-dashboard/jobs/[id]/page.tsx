// app/(company-dashboard)/company-dashboard/jobs/[id]/page.tsx
"use client"

import React, { useEffect, useState } from "react"
import { useRouter, useParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useToast } from "@/hooks/use-toast"
import { useCompanyDashboardStore } from "@/stores/company-dashboard.store"
import {
  ArrowLeft,
  Edit,
  Share2,
  Eye,
  Users,
  Calendar,
  MapPin,
  DollarSign,
  Clock,
  Briefcase,
  MoreHorizontal,
  Download,
  MessageSquare,
  Star,
  TrendingUp
} from "lucide-react"

export default function JobDetailsPage() {
  const router = useRouter()
  const params = useParams()
  const jobId = params?.id as string
  const { toast } = useToast()

  const {
    currentJob,
    jobApplications,
    jobLoading,
    fetchJobDetails,
    fetchJobApplications,
    toggleJobStatus,
    deleteJob
  } = useCompanyDashboardStore()

  const [activeTab, setActiveTab] = useState("overview")
  const [isToggling, setIsToggling] = useState(false)

  // Helper function to safely get current job status
  const getCurrentStatus = () => currentJob?.status || 'draft'

  // Helper function to safely get job ID
  const getJobId = () => currentJob?._id || currentJob?.id || jobId

  useEffect(() => {
    if (jobId) {
      fetchJobDetails(jobId)
      fetchJobApplications(jobId)
    }
  }, [jobId, fetchJobDetails, fetchJobApplications])

  // Debug current job data
  useEffect(() => {
    if (currentJob) {
      console.log('Current job data:', currentJob)
      console.log('Current job _id:', currentJob._id)
      console.log('Current job id:', currentJob.id)
      console.log('Current job status:', currentJob.status)
    }
  }, [currentJob])

  const handleStatusToggle = async () => {
    const currentJobId = getJobId()
    if (!currentJob || !currentJobId) {
      console.error('No current job or job ID available', { currentJob })
      toast({
        title: "Error",
        description: "Job information is not available. Please refresh the page.",
        variant: "destructive"
      })
      return
    }

    setIsToggling(true)
    const currentStatus = getCurrentStatus()
    const newStatus = currentStatus === 'active' ? 'closed' : 'active'
    const actionText = newStatus === 'active' ? 'activated' : 'closed'

    console.log('Toggling job status:', {
      jobId: currentJobId,
      currentStatus,
      newStatus
    })

    try {
      await toggleJobStatus(currentJobId, newStatus)

      toast({
        title: `Job ${actionText} successfully!`,
        description: `${currentJob.title} has been ${actionText}.`,
      })
    } catch (error) {
      console.error('Failed to toggle job status:', error)
      toast({
        title: "Failed to update job status",
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: "destructive"
      })
    } finally {
      setIsToggling(false)
    }
  }

  const handleDelete = async () => {
    const currentJobId = getJobId()
    if (currentJob && currentJobId && confirm('Are you sure you want to delete this job posting?')) {
      try {
        await deleteJob(currentJobId)
        toast({
          title: "Job deleted successfully",
          description: `${currentJob.title} has been deleted.`,
        })
        router.push('/company-dashboard/jobs')
      } catch (error) {
        console.error('Failed to delete job:', error)
        toast({
          title: "Failed to delete job",
          description: error instanceof Error ? error.message : 'An unexpected error occurred',
          variant: "destructive"
        })
      }
    }
  }

  // Early return if no jobId (after all hooks)
  if (!jobId) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Invalid Job ID</h1>
          <p className="text-gray-600 mt-2">The job ID is missing or invalid.</p>
          <Button
            onClick={() => router.push('/company-dashboard/jobs')}
            className="mt-4"
          >
            Back to Jobs
          </Button>
        </div>
      </div>
    )
  }

  const getStatusColor = (status: string | undefined) => {
    if (!status) return 'bg-gray-100 text-gray-800 border-gray-200'

    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200'
      case 'draft': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'closed': return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'paused': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'expired': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string | undefined) => {
    if (!status) return '⚫'

    switch (status) {
      case 'active': return '🟢'
      case 'draft': return '🟡'
      case 'closed': return '⚫'
      case 'paused': return '🟠'
      case 'expired': return '🔴'
      default: return '⚫'
    }
  }

  const getStatusText = (status: string | undefined) => {
    if (!status) return 'Unknown'

    switch (status) {
      case 'active': return 'Active'
      case 'draft': return 'Draft'
      case 'closed': return 'Closed'
      case 'paused': return 'Paused'
      case 'expired': return 'Expired'
      default: return status.charAt(0).toUpperCase() + status.slice(1)
    }
  }

  // Loading state
  if (jobLoading) {
    return (
      <div className="w-full h-full">
        <div className="w-full p-6">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  // Job not found after loading
  if (!currentJob) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Job Not Found</h1>
          <p className="text-gray-600 mt-2">The requested job could not be found or has been removed.</p>
          <Button
            onClick={() => router.push('/company-dashboard/jobs')}
            className="mt-4"
          >
            Back to Jobs
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full h-full">
      {/* Full Width Container with Padding */}
      <div className="w-full p-6">
        {/* Header Section */}
        <div className="w-full flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => router.back()}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Jobs
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">{currentJob.title}</h1>
              <div className="flex items-center space-x-4 mt-2">
                <Badge className={getStatusColor(getCurrentStatus())}>
                  <span className="mr-1">{getStatusIcon(getCurrentStatus())}</span>
                  {getStatusText(getCurrentStatus())}
                </Badge>
                <span className="text-muted-foreground">
                  Posted {new Date(currentJob.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline">
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
            <Button variant="outline">
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </Button>
            <Button 
              variant="outline"
              onClick={() => router.push(`/company-dashboard/jobs/${jobId}/edit`)}
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Button>
            <Button
              variant={getCurrentStatus() === 'active' ? 'outline' : 'default'}
              onClick={handleStatusToggle}
              disabled={isToggling}
            >
              {isToggling ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                  {getCurrentStatus() === 'active' ? 'Closing...' : 'Activating...'}
                </>
              ) : (
                <>
                  <Clock className="w-4 h-4 mr-2" />
                  {getCurrentStatus() === 'active' ? 'Close Job' : 'Activate Job'}
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Job Details Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="applications">
              Applications ({Array.isArray(jobApplications) ? jobApplications.length : 0})
            </TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="w-full">
            <div className="w-full grid gap-6 grid-cols-1 lg:grid-cols-3">
              {/* Job Information */}
              <div className="lg:col-span-2 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Job Description</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="prose max-w-none">
                      <p className="text-muted-foreground whitespace-pre-wrap">
                        {currentJob.description || 'No description provided'}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Requirements</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="prose max-w-none">
                      {(() => {
                        if (Array.isArray(currentJob.requirements) && currentJob.requirements.length > 0) {
                          return (
                            <ul className="list-disc list-inside space-y-2">
                              {currentJob.requirements.map((req, index) => (
                                <li key={index} className="text-muted-foreground">{req}</li>
                              ))}
                            </ul>
                          )
                        } else if (typeof currentJob.requirements === 'string' && currentJob.requirements.trim()) {
                          return (
                            <p className="text-muted-foreground whitespace-pre-wrap">
                              {currentJob.requirements}
                            </p>
                          )
                        } else {
                          return (
                            <p className="text-muted-foreground">
                              No requirements specified
                            </p>
                          )
                        }
                      })()}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Responsibilities</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="prose max-w-none">
                      {(() => {
                        if (Array.isArray(currentJob.responsibilities) && currentJob.responsibilities.length > 0) {
                          return (
                            <ul className="list-disc list-inside space-y-2">
                              {currentJob.responsibilities.map((resp, index) => (
                                <li key={index} className="text-muted-foreground">{resp}</li>
                              ))}
                            </ul>
                          )
                        } else if (typeof currentJob.responsibilities === 'string' && currentJob.responsibilities.trim()) {
                          return (
                            <p className="text-muted-foreground whitespace-pre-wrap">
                              {currentJob.responsibilities}
                            </p>
                          )
                        } else {
                          return (
                            <p className="text-muted-foreground">
                              No responsibilities specified
                            </p>
                          )
                        }
                      })()}
                    </div>
                  </CardContent>
                </Card>

                {currentJob.skills && Array.isArray(currentJob.skills) && currentJob.skills.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Required Skills</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {currentJob.skills.map((skill, index) => (
                          <Badge key={index} variant="secondary">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {currentJob.tags && Array.isArray(currentJob.tags) && currentJob.tags.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Tags</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {currentJob.tags.map((tag, index) => (
                          <Badge key={index} variant="outline">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* Job Stats & Info */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Job Statistics</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Users className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm">Applications</span>
                      </div>
                      <span className="text-sm font-medium">
                        {currentJob.applicationsCount || 0}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Eye className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm">Views</span>
                      </div>
                      <span className="text-sm font-medium">
                        {currentJob.viewsCount || 0}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <TrendingUp className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm">Conversion Rate</span>
                      </div>
                      <span className="text-sm font-medium">
                        {currentJob.conversionRate || 0}%
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm">Days Active</span>
                      </div>
                      <span className="text-sm font-medium">
                        {Math.floor((Date.now() - new Date(currentJob.createdAt).getTime()) / (1000 * 60 * 60 * 24))}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Job Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Location</span>
                      <span className="text-sm text-muted-foreground">
                        {(() => {
                          if (typeof currentJob.location === 'object' && currentJob.location) {
                            const parts = []
                            if (currentJob.location.city) parts.push(currentJob.location.city)
                            if (currentJob.location.state) parts.push(currentJob.location.state)
                            if (currentJob.location.country) parts.push(currentJob.location.country)
                            const locationStr = parts.length > 0 ? parts.join(', ') : 'Remote'
                            return currentJob.location.remote ? `${locationStr} (Remote)` : locationStr
                          }
                          return currentJob.location || 'Not specified'
                        })()}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Job Type</span>
                      <span className="text-sm text-muted-foreground">
                        {currentJob.type || 'Not specified'}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Experience Level</span>
                      <span className="text-sm text-muted-foreground">
                        {currentJob.level || 'Not specified'}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Department</span>
                      <span className="text-sm text-muted-foreground">
                        {currentJob.department || 'Not specified'}
                      </span>
                    </div>

                    {(currentJob.salary || currentJob.salaryRange) && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Salary Range</span>
                        <span className="text-sm text-muted-foreground">
                          {currentJob.salary && typeof currentJob.salary === 'object'
                            ? `${currentJob.salary.currency || '$'}${(currentJob.salary.min || 0).toLocaleString()} - ${currentJob.salary.currency || '$'}${(currentJob.salary.max || 0).toLocaleString()} ${currentJob.salary.period || 'yearly'}`
                            : currentJob.salaryRange || 'Not specified'
                          }
                        </span>
                      </div>
                    )}

                    {currentJob.applicationDeadline && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Application Deadline</span>
                        <span className="text-sm text-muted-foreground">
                          {new Date(currentJob.applicationDeadline).toLocaleDateString()}
                        </span>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button 
                      variant="outline" 
                      className="w-full justify-start"
                      onClick={() => router.push(`/company-dashboard/jobs/${jobId}/edit`)}
                    >
                      <Edit className="w-4 h-4 mr-2" />
                      Edit Job
                    </Button>
                    
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={handleStatusToggle}
                      disabled={isToggling}
                    >
                      {isToggling ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                          {getCurrentStatus() === 'active' ? 'Closing...' : 'Activating...'}
                        </>
                      ) : (
                        <>
                          <Clock className="w-4 h-4 mr-2" />
                          {getCurrentStatus() === 'active' ? 'Close Job' : 'Activate Job'}
                        </>
                      )}
                    </Button>
                    
                    <Button 
                      variant="outline" 
                      className="w-full justify-start"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Export Applications
                    </Button>
                    
                    <Button 
                      variant="destructive" 
                      className="w-full justify-start"
                      onClick={handleDelete}
                    >
                      <MoreHorizontal className="w-4 h-4 mr-2" />
                      Delete Job
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="applications" className="w-full">
            <Card>
              <CardHeader>
                <CardTitle>Recent Applications</CardTitle>
                <CardDescription>
                  Latest applications for this job posting
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!Array.isArray(jobApplications) || jobApplications.length === 0 ? (
                  <div className="text-center py-12">
                    <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No applications yet</h3>
                    <p className="text-muted-foreground">
                      Applications will appear here when candidates apply to this job
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {(jobApplications || []).slice(0, 10).map((application) => (
                      <div 
                        key={application._id} 
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 cursor-pointer"
                        onClick={() => router.push(`/company-dashboard/applications/${application._id}`)}
                      >
                        <div className="flex items-center space-x-4">
                          <Avatar>
                            <AvatarImage src={application.candidate?.avatar} />
                            <AvatarFallback>
                              {application.candidate?.name?.split(' ').map((n: string) => n[0]).join('') || 'CN'}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <h4 className="font-medium">
                              {application.candidate?.name || 'Anonymous Candidate'}
                            </h4>
                            <p className="text-sm text-muted-foreground">
                              Applied {new Date(application.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <Badge variant="secondary">
                          {application.status.replace('_', ' ')}
                        </Badge>
                      </div>
                    ))}
                    
                    {Array.isArray(jobApplications) && jobApplications.length > 10 && (
                      <div className="text-center pt-4">
                        <Button
                          variant="outline"
                          onClick={() => router.push(`/company-dashboard/applications?job=${jobId}`)}
                        >
                          View All Applications ({jobApplications.length})
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="w-full">
            <div className="w-full grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Views</CardTitle>
                  <Eye className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{currentJob.viewsCount || 0}</div>
                  <p className="text-xs text-muted-foreground">
                    +12% from last week
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Applications</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{currentJob.applicationsCount || 0}</div>
                  <p className="text-xs text-muted-foreground">
                    +5% from last week
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{currentJob.conversionRate || 0}%</div>
                  <p className="text-xs text-muted-foreground">
                    +2% from last week
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg. Time to Apply</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">2.4 days</div>
                  <p className="text-xs text-muted-foreground">
                    -0.5 days from last week
                  </p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="w-full">
            <Card>
              <CardHeader>
                <CardTitle>Job Settings</CardTitle>
                <CardDescription>
                  Manage job posting settings and preferences
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Auto-close applications</h4>
                      <p className="text-sm text-muted-foreground">
                        Automatically close applications when deadline is reached
                      </p>
                    </div>
                    <Badge variant="secondary">Enabled</Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Email notifications</h4>
                      <p className="text-sm text-muted-foreground">
                        Receive email notifications for new applications
                      </p>
                    </div>
                    <Badge variant="secondary">Enabled</Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Public visibility</h4>
                      <p className="text-sm text-muted-foreground">
                        Job is visible on public job board
                      </p>
                    </div>
                    <Badge variant={getCurrentStatus() === 'active' ? 'default' : 'secondary'}>
                      {getCurrentStatus() === 'active' ? 'Visible' : 'Hidden'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Status History */}
            <Card>
              <CardHeader>
                <CardTitle>Status History</CardTitle>
                <CardDescription>
                  Track of all status changes for this job posting
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {currentJob.statusHistory && currentJob.statusHistory.length > 0 ? (
                    currentJob.statusHistory.map((entry, index: number) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-2">
                            <span>{getStatusIcon(entry.fromStatus)}</span>
                            <span className="text-sm text-muted-foreground">
                              {getStatusText(entry.fromStatus)}
                            </span>
                            <span className="text-muted-foreground">→</span>
                            <span>{getStatusIcon(entry.toStatus)}</span>
                            <span className="text-sm font-medium">
                              {getStatusText(entry.toStatus)}
                            </span>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-muted-foreground">
                            {new Date(entry.timestamp).toLocaleDateString()} at{' '}
                            {new Date(entry.timestamp).toLocaleTimeString()}
                          </div>
                          {entry.reason && (
                            <div className="text-xs text-muted-foreground mt-1">
                              Reason: {entry.reason}
                            </div>
                          )}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <Clock className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No status changes yet</h3>
                      <p className="text-muted-foreground">
                        Status changes will appear here when the job status is updated
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

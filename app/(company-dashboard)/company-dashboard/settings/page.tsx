'use client'

import { useParams } from 'next/navigation'
import { CompanySettingsForm } from '@/components/company/settings/company-settings-form'

export default function CompanySettingsPage() {
  const params = useParams()
  const companyId = params?.id as string || 'current' // Use 'current' for current company

  return (
    <div className="container mx-auto py-6">
      <CompanySettingsForm companyId={companyId} />
    </div>
  )
}

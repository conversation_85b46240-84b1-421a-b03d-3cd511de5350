// app/(company-dashboard)/company-dashboard/company/page.tsx
'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { motion } from 'framer-motion'
import { useCompanyStore } from '@/stores/company.store'
import { useAuthStore } from '@/stores/auth.store'
import { toast } from 'sonner'

// Add this interface if not already defined elsewhere
interface Company {
  name?: string;
  description?: string;
  website?: string;
  locations?: Array<{
    city: string;
    country: string;
    isHeadquarters?: boolean;
    address?: string;
  }>;
  industry?: string[] | string;
  size?: string;
  founded?: number | string;
  email?: string;
  phone?: string;
  contact?: {
    email?: string;
    phone?: string;
    hrEmail?: string;
  };
  logo?: string;
  tagline?: string;
  verification?: {
    isVerified?: boolean;
  };
  isFeatured?: boolean;
  stats?: {
    totalJobs?: number;
    totalApplications?: number;
    profileViews?: number;
  };
  culture?: {
    mission?: string;
    vision?: string;
    values?: string[];
    benefits?: string[];
  };
  socialLinks?: {
    linkedin?: string;
    twitter?: string;
    facebook?: string;
    instagram?: string;
    github?: string;
    youtube?: string;
    glassdoor?: string;
  };
  isActive?: boolean;
  createdAt?: string | Date;
  updatedAt?: string | Date;
  // Add the missing rating property
  rating?: {
    average?: number;
  };
}
import {
  Building2,
  MapPin,
  Globe,
  Users,
  Calendar,
  Edit,
  Save,
  Upload,
  Eye,
  Star,
  Briefcase,
  Mail,
  Phone,
  ExternalLink,
  Award,
  Target,
  TrendingUp,
  Shield,
  CheckCircle,
  AlertCircle,
  Clock,
  DollarSign,
  Linkedin,
  Twitter,
  Facebook,
  Instagram,
  Github,
  Youtube,
  Heart,
  Coffee,
  Zap,
  Lightbulb,
  MapPinIcon
} from 'lucide-react'

export default function CompanyProfilePage() {
  const router = useRouter()
  const { user } = useAuthStore()
  const {
    company,
    needsCompanySetup,
    profileLoading,
    updateLoading,
    fetchCompanyProfile,
    updateCompanyProfile,
    uploadCompanyLogo
  } = useCompanyStore()

  const [isEditing, setIsEditing] = useState(false)
  const [activeTab, setActiveTab] = useState("overview")
  const [isFixingRelationship, setIsFixingRelationship] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    website: '',
    location: '',
    industry: '',
    size: '',
    founded: '',
    email: '',
    phone: ''
  })

  useEffect(() => {
    console.log('🔍 Company Profile Page - Fetching company profile...')
    fetchCompanyProfile()
  }, [fetchCompanyProfile])

  // Debug logging
  useEffect(() => {
    console.log('🔍 Company Profile Page - Company data:', company)
    console.log('🔍 Company Profile Page - Profile loading:', profileLoading)
    console.log('🔍 Company Profile Page - User:', user)
  }, [company, profileLoading, user])

  useEffect(() => {
    if (company) {
      setFormData({
        name: company.name || '',
        description: company.description || '',
        website: company.website || '',
        location: company.locations?.[0] ? `${company.locations[0].city}, ${company.locations[0].country}` : '',
        industry: Array.isArray(company.industry) ? company.industry.join(', ') : company.industry || '',
        size: company.size || '',
        founded: company.founded?.toString() || '',
        email: company.contact?.email || '',
        phone: company.contact?.phone || ''
      })
    }
  }, [company])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSave = async () => {
    try {
      await updateCompanyProfile(formData)
      setIsEditing(false)
    } catch (error) {
      console.error('Failed to update company profile:', error)
    }
  }

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      try {
        await uploadCompanyLogo(file)
      } catch (error) {
        console.error('Failed to upload logo:', error)
      }
    }
  }

  const handleFixRelationship = async () => {
    setIsFixingRelationship(true)
    try {
      const token = useAuthStore.getState().token
      const response = await fetch('/api/v1/companies/me/fix-relationships', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        // Refresh the company profile after fixing
        await fetchCompanyProfile()
        toast.success('Company relationship fixed successfully!')
      } else {
        const error = await response.json()
        throw new Error(error.error || 'Failed to fix relationship')
      }
    } catch (error) {
      console.error('Fix relationship error:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to fix relationship')
    } finally {
      setIsFixingRelationship(false)
    }
  }

  if (profileLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!company || needsCompanySetup) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Company Setup Required</h2>
          <p className="text-muted-foreground mb-4">
            {needsCompanySetup
              ? "You need to complete your company profile setup to continue."
              : "No company is associated with your account. You can create a new company profile or contact support if you should have access to an existing company."
            }
          </p>
          <div className="space-x-2">
            <Button onClick={() => router.push('/company-dashboard')}>
              Back to Dashboard
            </Button>
            {needsCompanySetup && (
              <Button onClick={() => router.push('/company-dashboard/setup')} variant="default">
                Complete Setup
              </Button>
            )}
            {!needsCompanySetup && (
              <Button
                onClick={handleFixRelationship}
                variant="outline"
                disabled={isFixingRelationship}
              >
                {isFixingRelationship ? 'Fixing...' : 'Try Fix Relationship'}
              </Button>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full h-full">
      {/* Full Width Container with Padding */}
      <div className="w-full p-6">
        {/* Header Section */}
        <div className="w-full flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Company Profile</h1>
            <p className="text-muted-foreground mt-2">
              Manage your company information and public profile
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" onClick={() => router.push('/companies')}>
              <Eye className="w-4 h-4 mr-2" />
              View Public Profile
            </Button>
            {isEditing ? (
              <>
                <Button variant="outline" onClick={() => setIsEditing(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSave} disabled={updateLoading}>
                  <Save className="w-4 h-4 mr-2" />
                  {updateLoading ? 'Saving...' : 'Save Changes'}
                </Button>
              </>
            ) : (
              <Button onClick={() => setIsEditing(true)}>
                <Edit className="w-4 h-4 mr-2" />
                Edit Profile
              </Button>
            )}
          </div>
        </div>

        {/* Company Profile Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="details">Company Details</TabsTrigger>
            <TabsTrigger value="branding">Branding</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="w-full">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="w-full space-y-6"
            >
              {/* Company Header */}
              <Card className="w-full">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-6">
                    <Avatar className="w-24 h-24 border-4 border-background shadow-lg">
                      <AvatarImage src={company?.logo} />
                      <AvatarFallback className="bg-gradient-to-br from-primary to-primary/80">
                        <Building2 className="w-12 h-12 text-primary-foreground" />
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 space-y-4">
                      <div>
                        <div className="flex items-center space-x-3 mb-2">
                          <h1 className="text-3xl font-bold">{company?.name || 'Company Name'}</h1>
                          {company?.verification?.isVerified && (
                            <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Verified
                            </Badge>
                          )}
                          {company?.isFeatured && (
                            <Badge variant="default" className="bg-yellow-100 text-yellow-800 border-yellow-200">
                              <Star className="w-3 h-3 mr-1" />
                              Featured
                            </Badge>
                          )}
                        </div>
                        {company?.tagline && (
                          <p className="text-lg text-muted-foreground italic">{company.tagline}</p>
                        )}
                      </div>

                      {/* Quick Stats */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center p-3 bg-muted/30 rounded-lg">
                          <div className="text-2xl font-bold text-primary">{company?.stats?.totalJobs || 0}</div>
                          <div className="text-xs text-muted-foreground">Active Jobs</div>
                        </div>
                        <div className="text-center p-3 bg-muted/30 rounded-lg">
                          <div className="text-2xl font-bold text-green-600">{company?.stats?.totalApplications || 0}</div>
                          <div className="text-xs text-muted-foreground">Applications</div>
                        </div>
                        <div className="text-center p-3 bg-muted/30 rounded-lg">
                          <div className="text-2xl font-bold text-blue-600">{company?.stats?.profileViews || 0}</div>
                          <div className="text-xs text-muted-foreground">Profile Views</div>
                        </div>
                        <div className="text-center p-3 bg-muted/30 rounded-lg">
                          <div className="text-2xl font-bold text-purple-600">{company?.stats?.averageRating?.toFixed(1) || 'N/A'}</div>
                          <div className="text-xs text-muted-foreground">Rating</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Main Content Grid */}
              <div className="grid gap-6 grid-cols-1 lg:grid-cols-3">
                {/* Company Details */}
                <Card className="lg:col-span-2">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Building2 className="w-5 h-5" />
                      <span>Company Information</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Basic Info */}
                    <div className="space-y-4">
                      {company?.description && (
                        <div>
                          <h4 className="font-semibold mb-2">About Us</h4>
                          <p className="text-muted-foreground leading-relaxed">{company.description}</p>
                        </div>
                      )}

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Industry */}
                        {company?.industry && Array.isArray(company.industry) && company.industry.length > 0 && (
                          <div>
                            <h4 className="font-semibold mb-2 flex items-center">
                              <Target className="w-4 h-4 mr-2" />
                              Industry
                            </h4>
                            <div className="flex flex-wrap gap-1">
                              {company.industry.map((ind, index) => (
                                <Badge key={index} variant="secondary">{ind}</Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Company Size */}
                        {company?.size && (
                          <div>
                            <h4 className="font-semibold mb-2 flex items-center">
                              <Users className="w-4 h-4 mr-2" />
                              Company Size
                            </h4>
                            <Badge variant="outline" className="capitalize">{company.size}</Badge>
                          </div>
                        )}

                        {/* Founded */}
                        {company?.founded && (
                          <div>
                            <h4 className="font-semibold mb-2 flex items-center">
                              <Calendar className="w-4 h-4 mr-2" />
                              Founded
                            </h4>
                            <p className="text-muted-foreground">{company.founded}</p>
                          </div>
                        )}

                        {/* Website */}
                        {company?.website && (
                          <div>
                            <h4 className="font-semibold mb-2 flex items-center">
                              <Globe className="w-4 h-4 mr-2" />
                              Website
                            </h4>
                            <a
                              href={company.website}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-primary hover:underline flex items-center"
                            >
                              {company.website}
                              <ExternalLink className="w-3 h-3 ml-1" />
                            </a>
                          </div>
                        )}
                      </div>
                    </div>

                    <Separator />

                    {/* Locations */}
                    {company?.locations && company.locations.length > 0 && (
                      <div>
                        <h4 className="font-semibold mb-3 flex items-center">
                          <MapPin className="w-4 h-4 mr-2" />
                          Locations
                        </h4>
                        <div className="space-y-2">
                          {company.locations.map((location, index) => (
                            <div key={index} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                              <div>
                                <div className="font-medium">
                                  {location.city}, {location.country}
                                  {location.isHeadquarters && (
                                    <Badge variant="secondary" className="ml-2 text-xs">HQ</Badge>
                                  )}
                                </div>
                                {location.address && (
                                  <div className="text-sm text-muted-foreground">{location.address}</div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Sidebar */}
                <div className="space-y-6">
                  {/* Contact Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Mail className="w-5 h-5" />
                        <span>Contact Information</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {company?.contact?.email && (
                        <div className="flex items-center space-x-3">
                          <Mail className="w-4 h-4 text-muted-foreground" />
                          <div>
                            <div className="text-sm font-medium">Email</div>
                            <a href={`mailto:${company.contact.email}`} className="text-sm text-primary hover:underline">
                              {company.contact.email}
                            </a>
                          </div>
                        </div>
                      )}

                      {company?.contact?.phone && (
                        <div className="flex items-center space-x-3">
                          <Phone className="w-4 h-4 text-muted-foreground" />
                          <div>
                            <div className="text-sm font-medium">Phone</div>
                            <a href={`tel:${company.contact.phone}`} className="text-sm text-primary hover:underline">
                              {company.contact.phone}
                            </a>
                          </div>
                        </div>
                      )}

                      {company?.contact?.hrEmail && (
                        <div className="flex items-center space-x-3">
                          <Briefcase className="w-4 h-4 text-muted-foreground" />
                          <div>
                            <div className="text-sm font-medium">HR Email</div>
                            <a href={`mailto:${company.contact.hrEmail}`} className="text-sm text-primary hover:underline">
                              {company.contact.hrEmail}
                            </a>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Company Culture */}
                  {company?.culture && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                          <Heart className="w-5 h-5" />
                          <span>Company Culture</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {company.culture.mission && (
                          <div>
                            <h4 className="font-semibold mb-2 flex items-center">
                              <Target className="w-4 h-4 mr-2" />
                              Mission
                            </h4>
                            <p className="text-sm text-muted-foreground">{company.culture.mission}</p>
                          </div>
                        )}

                        {company.culture.vision && (
                          <div>
                            <h4 className="font-semibold mb-2 flex items-center">
                              <Lightbulb className="w-4 h-4 mr-2" />
                              Vision
                            </h4>
                            <p className="text-sm text-muted-foreground">{company.culture.vision}</p>
                          </div>
                        )}

                        {company.culture.values && company.culture.values.length > 0 && (
                          <div>
                            <h4 className="font-semibold mb-2 flex items-center">
                              <Award className="w-4 h-4 mr-2" />
                              Values
                            </h4>
                            <div className="space-y-1">
                              {company.culture.values.map((value, index) => (
                                <div key={index} className="text-sm text-muted-foreground flex items-center">
                                  <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                                  {value}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {company.culture.benefits && company.culture.benefits.length > 0 && (
                          <div>
                            <h4 className="font-semibold mb-2 flex items-center">
                              <Coffee className="w-4 h-4 mr-2" />
                              Benefits
                            </h4>
                            <div className="flex flex-wrap gap-1">
                              {company.culture.benefits.slice(0, 6).map((benefit, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {benefit}
                                </Badge>
                              ))}
                              {company.culture.benefits.length > 6 && (
                                <Badge variant="secondary" className="text-xs">
                                  +{company.culture.benefits.length - 6} more
                                </Badge>
                              )}
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  )}

                  {/* Social Links */}
                  {company?.socialLinks && 
                    (['linkedin', 'twitter', 'facebook', 'instagram', 'github', 'youtube', 'glassdoor'] as const)
                      .some(key => company.socialLinks[key as keyof typeof company.socialLinks]) && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                          <Zap className="w-5 h-5" />
                          <span>Social Media</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-3">
                          {company.socialLinks.linkedin && (
                            <a
                              href={company.socialLinks.linkedin}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center space-x-2 p-2 rounded-lg hover:bg-muted/50 transition-colors"
                            >
                              <Linkedin className="w-4 h-4 text-blue-600" />
                              <span className="text-sm">LinkedIn</span>
                            </a>
                          )}

                          {company.socialLinks.twitter && (
                            <a
                              href={company.socialLinks.twitter}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center space-x-2 p-2 rounded-lg hover:bg-muted/50 transition-colors"
                            >
                              <Twitter className="w-4 h-4 text-blue-400" />
                              <span className="text-sm">Twitter</span>
                            </a>
                          )}

                          {company.socialLinks.facebook && (
                            <a
                              href={company.socialLinks.facebook}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center space-x-2 p-2 rounded-lg hover:bg-muted/50 transition-colors"
                            >
                              <Facebook className="w-4 h-4 text-blue-700" />
                              <span className="text-sm">Facebook</span>
                            </a>
                          )}

                          {company.socialLinks.instagram && (
                            <a
                              href={company.socialLinks.instagram}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center space-x-2 p-2 rounded-lg hover:bg-muted/50 transition-colors"
                            >
                              <Instagram className="w-4 h-4 text-pink-600" />
                              <span className="text-sm">Instagram</span>
                            </a>
                          )}

                          {company.socialLinks.github && (
                            <a
                              href={company.socialLinks.github}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center space-x-2 p-2 rounded-lg hover:bg-muted/50 transition-colors"
                            >
                              <Github className="w-4 h-4 text-gray-800 dark:text-gray-200" />
                              <span className="text-sm">GitHub</span>
                            </a>
                          )}

                          {company.socialLinks.youtube && (
                            <a
                              href={company.socialLinks.youtube}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center space-x-2 p-2 rounded-lg hover:bg-muted/50 transition-colors"
                            >
                              <Youtube className="w-4 h-4 text-red-600" />
                              <span className="text-sm">YouTube</span>
                            </a>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Company Stats */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <TrendingUp className="w-5 h-5" />
                        <span>Company Stats</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-muted-foreground">Profile Completion</span>
                          <span className="text-sm font-medium">
                            {Math.round(((company?.name ? 1 : 0) +
                                       (company?.description ? 1 : 0) +
                                       (company?.website ? 1 : 0) +
                                       (company?.industry?.length ? 1 : 0) +
                                       (company?.locations?.length ? 1 : 0)) / 5 * 100)}%
                          </span>
                        </div>
                        <Progress
                          value={((company?.name ? 1 : 0) +
                                 (company?.description ? 1 : 0) +
                                 (company?.website ? 1 : 0) +
                                 (company?.industry?.length ? 1 : 0) +
                                 (company?.locations?.length ? 1 : 0)) / 5 * 100}
                          className="h-2"
                        />
                      </div>

                      <Separator />

                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Member Since</span>
                          <span className="text-sm font-medium">
                            {company?.createdAt ? new Date(company.createdAt).getFullYear() : 'N/A'}
                          </span>
                        </div>

                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Last Updated</span>
                          <span className="text-sm font-medium">
                            {company?.updatedAt ? new Date(company.updatedAt).toLocaleDateString() : 'N/A'}
                          </span>
                        </div>

                        {company?.verification?.isVerified && (
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Verified</span>
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </motion.div>
          </TabsContent>

          {/* The duplicate TabsContent for "details" has been removed to fix the JSX closing tag error */}

          <TabsContent value="details" className="w-full">
            <Card>
              <CardHeader>
                <CardTitle>Detailed Information</CardTitle>
                <CardDescription>
                  Complete company details and contact information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="name">Company Name</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        disabled={!isEditing}
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor="location">Location</Label>
                      <Input
                        id="location"
                        value={formData.location}
                        onChange={(e) => handleInputChange('location', e.target.value)}
                        disabled={!isEditing}
                        placeholder="City, Country"
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor="size">Company Size</Label>
                      <Input
                        id="size"
                        value={formData.size}
                        onChange={(e) => handleInputChange('size', e.target.value)}
                        disabled={!isEditing}
                        placeholder="1-10, 11-50, 51-200, etc."
                        className="mt-1"
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="email">Contact Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        disabled={!isEditing}
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        disabled={!isEditing}
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor="founded">Founded Year</Label>
                      <Input
                        id="founded"
                        value={formData.founded}
                        onChange={(e) => handleInputChange('founded', e.target.value)}
                        disabled={!isEditing}
                        placeholder="2020"
                        className="mt-1"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="branding" className="w-full">
            <Card>
              <CardHeader>
                <CardTitle>Company Branding</CardTitle>
                <CardDescription>
                  Manage your company logo and visual identity
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label>Company Logo</Label>
                  <div className="mt-2 flex items-center space-x-4">
                    <Avatar className="w-20 h-20">
                      <AvatarImage src={company?.logo} />
                      <AvatarFallback>
                        <Building2 className="w-10 h-10" />
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleLogoUpload}
                        className="hidden"
                        id="logo-upload"
                      />
                      <Button
                        variant="outline"
                        onClick={() => document.getElementById('logo-upload')?.click()}
                      >
                        <Upload className="w-4 h-4 mr-2" />
                        Upload Logo
                      </Button>
                      <p className="text-xs text-muted-foreground mt-1">
                        Recommended: Square image, at least 200x200px
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="w-full">
            <Card>
              <CardHeader>
                <CardTitle>Profile Settings</CardTitle>
                <CardDescription>
                  Manage your company profile visibility and settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Public Profile</h4>
                      <p className="text-sm text-muted-foreground">
                        Make your company profile visible to job seekers
                      </p>
                    </div>
                    <Badge variant={company?.isActive ? "default" : "secondary"}>
                      {company?.isActive ? "Public" : "Private"}
                    </Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Verification Status</h4>
                      <p className="text-sm text-muted-foreground">
                        Company verification status
                      </p>
                    </div>
                    <Badge variant={company?.verification?.isVerified ? "default" : "secondary"}>
                      {company?.verification?.isVerified ? "Verified" : "Pending"}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

'use client'

import { useEffect, useState } from 'react'
import { useAuthStore } from '@/stores/auth.store'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function DebugAuthPage() {
  const { 
    user, 
    token, 
    refreshToken, 
    isAuthenticated, 
    checkAuthStatus, 
    hydrateFromStorage,
    logout 
  } = useAuthStore()
  
  const [localStorageData, setLocalStorageData] = useState<any>(null)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    
    // Check localStorage
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem('auth-storage')
        if (stored) {
          setLocalStorageData(JSON.parse(stored))
        }
      } catch (error) {
        console.error('Failed to parse localStorage:', error)
      }
    }
  }, [])

  const handleCheckAuth = async () => {
    try {
      await checkAuthStatus()
      console.log('Auth status checked')
    } catch (error) {
      console.error('Auth check failed:', error)
    }
  }

  const handleHydrate = () => {
    hydrateFromStorage()
    console.log('Hydrated from storage')
  }

  const handleLogout = async () => {
    await logout()
    setLocalStorageData(null)
  }

  if (!isClient) {
    return <div>Loading...</div>
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <h1 className="text-3xl font-bold">Authentication Debug Page</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Current Auth State */}
        <Card>
          <CardHeader>
            <CardTitle>Current Auth State</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div><strong>Is Authenticated:</strong> {isAuthenticated ? '✅ Yes' : '❌ No'}</div>
            <div><strong>Has User:</strong> {user ? '✅ Yes' : '❌ No'}</div>
            <div><strong>Has Token:</strong> {token ? '✅ Yes' : '❌ No'}</div>
            <div><strong>Has Refresh Token:</strong> {refreshToken ? '✅ Yes' : '❌ No'}</div>
            
            {user && (
              <div className="mt-4 p-3 bg-muted rounded">
                <div><strong>User Email:</strong> {user.email}</div>
                <div><strong>User Role:</strong> {user.role}</div>
                <div><strong>User Name:</strong> {user.profile?.firstName} {user.profile?.lastName}</div>
              </div>
            )}
            
            {token && (
              <div className="mt-4 p-3 bg-muted rounded">
                <div><strong>Token (first 50 chars):</strong></div>
                <div className="text-xs font-mono break-all">{token.substring(0, 50)}...</div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* LocalStorage Data */}
        <Card>
          <CardHeader>
            <CardTitle>LocalStorage Data</CardTitle>
          </CardHeader>
          <CardContent>
            {localStorageData ? (
              <div className="space-y-2">
                <div><strong>Version:</strong> {localStorageData.version}</div>
                <div><strong>Has State:</strong> {localStorageData.state ? '✅ Yes' : '❌ No'}</div>
                
                {localStorageData.state && (
                  <div className="mt-4 p-3 bg-muted rounded">
                    <div><strong>Stored User:</strong> {localStorageData.state.user ? '✅ Yes' : '❌ No'}</div>
                    <div><strong>Stored Token:</strong> {localStorageData.state.token ? '✅ Yes' : '❌ No'}</div>
                    <div><strong>Stored Refresh Token:</strong> {localStorageData.state.refreshToken ? '✅ Yes' : '❌ No'}</div>
                    <div><strong>Stored Is Authenticated:</strong> {localStorageData.state.isAuthenticated ? '✅ Yes' : '❌ No'}</div>
                    
                    {localStorageData.state.user && (
                      <div className="mt-2">
                        <div><strong>Stored Email:</strong> {localStorageData.state.user.email}</div>
                        <div><strong>Stored Role:</strong> {localStorageData.state.user.role}</div>
                      </div>
                    )}
                  </div>
                )}
                
                <details className="mt-4">
                  <summary className="cursor-pointer font-medium">Raw Data</summary>
                  <pre className="mt-2 p-3 bg-muted rounded text-xs overflow-auto">
                    {JSON.stringify(localStorageData, null, 2)}
                  </pre>
                </details>
              </div>
            ) : (
              <div>❌ No auth data in localStorage</div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Debug Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-x-4">
          <Button onClick={handleCheckAuth}>
            Check Auth Status
          </Button>
          <Button onClick={handleHydrate} variant="outline">
            Hydrate from Storage
          </Button>
          <Button onClick={handleLogout} variant="destructive">
            Logout
          </Button>
          <Button onClick={() => window.location.reload()} variant="secondary">
            Refresh Page
          </Button>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div>1. Login at <a href="/login" className="text-blue-500 underline">/login</a></div>
          <div>2. Come back to this page to see if auth state persists</div>
          <div>3. Refresh the page to test persistence across page loads</div>
          <div>4. Check browser console for detailed logs</div>
        </CardContent>
      </Card>
    </div>
  )
}

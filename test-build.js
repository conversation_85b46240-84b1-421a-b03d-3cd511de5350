// Simple test to verify dynamic imports work
async function testDynamicImports() {
  console.log('Testing dynamic imports...')
  
  // Test cache middleware
  try {
    const cacheModule = await import('./lib/middleware/cache.middleware.ts')
    console.log('✅ Cache middleware loaded successfully')
  } catch (error) {
    console.log('❌ Cache middleware failed:', error.message)
  }
  
  // Test security utils
  try {
    const securityModule = await import('./lib/utils/security.ts')
    console.log('✅ Security utils loaded successfully')
  } catch (error) {
    console.log('❌ Security utils failed:', error.message)
  }
  
  console.log('Dynamic import test completed')
}

testDynamicImports().catch(console.error)

# TCM Advanced Authentication & User Management System

## Overview

This document outlines the comprehensive authentication and user management system from the TCM (lasttcmsuit) project. This system provides enterprise-grade security with advanced features including device tracking, session management, IP/country blocking, and multi-role authorization.

## Core Features

### 🔐 **Advanced Authentication**
- JWT-based authentication with secure token management
- Persistent session management across devices
- Device fingerprinting and tracking
- Single device login enforcement (configurable per user)
- Automatic session cleanup and expiration
- Password reset with secure token generation

### 🛡️ **Security & Access Control**
- Multi-role authorization with granular permissions
- IP address and country-based blocking
- Device blocking and trust management
- Failed login attempt tracking and account locking
- Real-time session monitoring and management
- Comprehensive audit logging

### 📱 **Device Management**
- Device fingerprinting using multiple parameters
- Trusted device management
- New device verification workflow
- Device-specific session tracking
- Cross-platform device detection

### 👥 **User Management**
- 25+ predefined user roles with hierarchical permissions
- User status management (active, suspended, blocked, etc.)
- Department and position-based access control
- Emergency contact and profile management
- Account lifecycle management

## System Architecture

### **Backend Components**

#### **1. Authentication Core (`lib/backend/auth/`)**
- `auth.ts` - Main authentication service
- `token.ts` - JWT token generation and verification
- `session.ts` - Server-side session management
- `permissions.ts` - Role-based permission checking
- `role-permissions.ts` - Permission matrix definitions

#### **2. Services (`lib/backend/services/auth/`)**
- `SessionService.ts` - Session lifecycle management
- `BlockingService.ts` - IP/device/country blocking
- Device fingerprinting and tracking

#### **3. Models (`models/`)**
- `User.ts` - User schema with authentication methods
- `UserSession.ts` - Session tracking and management
- `BlockedEntity.ts` - Blocked IPs, devices, countries

#### **4. Utilities (`lib/backend/utils/`)**
- `device-detector.ts` - Device information extraction
- `device-fingerprint.ts` - Device fingerprinting
- `logger.ts` - Comprehensive logging system

### **Frontend Components**

#### **1. Authentication Store (`lib/frontend/`)**
- `authStore.ts` - Zustand-based state management
- `frontendauth.ts` - API communication layer
- Persistent authentication state

#### **2. UI Components (`components/auth/`)**
- `device-verification.tsx` - New device verification
- Login/logout components
- Password reset components

#### **3. Security Components (`components/security/`)**
- `device-management.tsx` - Device management interface
- Session monitoring components
- Security settings interface

### **Middleware System**

#### **Route Protection (`middleware.ts`)**
- Comprehensive route-based access control
- Role-based route restrictions
- Automatic redirections for unauthorized access
- Public route definitions
- API route protection

## Data Models

### **User Model**
```typescript
interface IUser {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  status: UserStatus;
  department?: string;
  position?: string;
  
  // Security settings
  allowMultipleDevices: boolean;
  trustedDevicesOnly: boolean;
  singleDeviceLogin: boolean;
  
  // Authentication tracking
  lastLogin?: Date;
  failedLoginAttempts?: number;
  lastFailedLogin?: Date;
  accountLockTime?: Date;
  
  // Password reset
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  
  // Methods
  comparePassword(password: string): Promise<boolean>;
  createPasswordResetToken(): string;
}
```

### **User Session Model**
```typescript
interface IUserSession {
  userId: ObjectId;
  deviceId: string;
  deviceName: string;
  deviceType: string;
  browser: string;
  operatingSystem: string;
  ipAddress: string;
  macAddress?: string;
  location?: {
    country?: string;
    region?: string;
    city?: string;
    latitude?: number;
    longitude?: number;
  };
  isTrusted: boolean;
  isActive: boolean;
  lastActive: Date;
  loginTime: Date;
  logoutTime?: Date;
  expiresAt: Date;
  token: string;
}
```

### **Blocked Entity Model**
```typescript
interface IBlockedEntity {
  type: 'IP_ADDRESS' | 'IP_RANGE' | 'DEVICE' | 'COUNTRY';
  value: string;
  description: string;
  reason: string;
  isActive: boolean;
  expiresAt?: Date;
  blockedBy: ObjectId;
}
```

## User Roles & Permissions

### **Administrative Roles**
- `SUPER_ADMIN` - Full system access
- `SYSTEM_ADMIN` - System administration
- `ADMIN` - General administration
- `AUDITOR` - Audit and compliance

### **HR Roles**
- `HR_DIRECTOR` - HR leadership
- `HR_MANAGER` - HR management
- `HR_SPECIALIST` - HR operations
- `RECRUITER` - Recruitment activities
- `TRAINING_COORDINATOR` - Training management

### **Management Roles**
- `DIRECTOR` - Executive level
- `MANAGER` - Department management
- `DEPARTMENT_HEAD` - Department leadership
- `TEAM_LEADER` - Team management
- `PROJECT_MANAGER` - Project oversight

### **Finance Roles**
- `FINANCE_DIRECTOR` - Financial leadership
- `FINANCE_MANAGER` - Financial management
- `ACCOUNTANT` - Accounting operations
- `PAYROLL_SPECIALIST` - Payroll processing

### **Operational Roles**
- `EMPLOYEE` - Standard employee
- `CONTRACTOR` - Contract worker
- `INTERN` - Internship position

## Authentication Workflow

### **1. Login Process**
```mermaid
sequenceDiagram
    participant C as Client
    participant M as Middleware
    participant A as Auth Service
    participant S as Session Service
    participant B as Blocking Service
    participant D as Database

    C->>A: Login Request (email, password)
    A->>D: Validate User Credentials
    A->>B: Check IP/Country/Device Blocks
    B-->>A: Block Status
    A->>S: Create/Update Session
    S->>D: Store Session Data
    A->>C: Return JWT Token + Session Info
    C->>M: Subsequent Requests with Token
    M->>A: Verify Token & Session
    A-->>M: User Info
    M->>C: Allow/Deny Access
```

### **2. Device Verification**
```mermaid
sequenceDiagram
    participant C as Client
    participant A as Auth Service
    participant S as Session Service
    participant D as Device Detector

    C->>A: Login from New Device
    A->>D: Extract Device Info
    D-->>A: Device Fingerprint
    A->>S: Check Device Trust Status
    S-->>A: Device Not Trusted
    A->>C: Require Device Verification
    C->>A: Verify Device Request
    A->>S: Mark Device as Trusted
    S->>A: Device Trusted
    A->>C: Full Access Granted
```

### **3. Session Management**
```mermaid
sequenceDiagram
    participant U as User
    participant S as Session Service
    participant D as Database
    participant C as Cleanup Service

    U->>S: Login Request
    S->>D: Create Session Record
    S->>S: Generate Device ID
    S->>D: Store Session with Expiry
    
    loop Every Request
        U->>S: API Request
        S->>D: Update Last Active
    end
    
    C->>D: Cleanup Expired Sessions
    D-->>C: Sessions Cleaned
```

## API Routes

### **Authentication Endpoints**
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `POST /api/auth/register` - User registration
- `GET /api/auth/status` - Check auth status
- `POST /api/auth/password-reset` - Request password reset
- `POST /api/auth/password-reset/confirm` - Confirm password reset
- `POST /api/auth/verify-device` - Verify new device

### **Session Management**
- `GET /api/auth/sessions` - Get user sessions
- `DELETE /api/auth/sessions/:id` - End specific session
- `DELETE /api/auth/sessions/all` - End all sessions
- `POST /api/auth/sessions/:id/trust` - Trust device

### **Security Management**
- `POST /api/security/block-ip` - Block IP address
- `POST /api/security/block-device` - Block device
- `POST /api/security/block-country` - Block country
- `GET /api/security/blocked-entities` - List blocked entities
- `DELETE /api/security/blocked-entities/:id` - Unblock entity

## Frontend Integration

### **Authentication Store (Zustand)**
```typescript
interface AuthStore {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  resetAuthError: () => void;
}
```

### **Device Management**
```typescript
interface DeviceInfo {
  deviceId: string;
  deviceName: string;
  deviceType: string;
  browser: string;
  operatingSystem: string;
  ipAddress: string;
  isTrusted: boolean;
  lastActive: Date;
}
```

## Security Features

### **Device Fingerprinting**
- IP address tracking
- Browser and OS detection
- Device name and type identification
- MAC address collection (when available)
- Geographic location detection

### **Blocking System**
- IP address blocking (individual and ranges)
- Device-specific blocking
- Country-based restrictions
- Temporary and permanent blocks
- Admin-controlled unblocking

### **Session Security**
- Automatic session expiration
- Concurrent session limits
- Device-specific sessions
- Real-time session monitoring
- Force logout capabilities

## Implementation Guide

### **1. Database Setup**
```javascript
// MongoDB collections required
- users
- userSessions
- blockedEntities
- auditLogs (optional)
```

### **2. Environment Variables**
```env
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=7d
JWT_COOKIE_EXPIRES_IN=7
MONGODB_URI=mongodb://localhost:27017/tcm
```

### **3. Middleware Configuration**
```typescript
// Configure protected routes
const roleBasedRoutes = {
  '/admin': [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN],
  '/dashboard': [/* all authenticated roles */],
  // ... more routes
};
```

### **4. Frontend Setup**
```typescript
// Initialize auth store
const authStore = useAuthStore();

// Check authentication on app start
useEffect(() => {
  authStore.checkAuth();
}, []);
```

## Advanced Features

### **Single Device Login**
- Configurable per user
- Automatic logout from other devices
- Device conflict resolution
- Admin override capabilities

### **Trusted Device Management**
- First device auto-trusted
- Manual device verification
- Device trust revocation
- Trust expiration policies

### **Geographic Restrictions**
- Country-based blocking
- IP range restrictions
- VPN detection (optional)
- Location-based alerts

### **Audit & Monitoring**
- Comprehensive logging
- Login attempt tracking
- Session activity monitoring
- Security event alerts

## Benefits

### **Security**
- Enterprise-grade authentication
- Multi-layer security controls
- Real-time threat detection
- Comprehensive audit trails

### **User Experience**
- Seamless device management
- Persistent sessions
- Intuitive verification process
- Minimal friction for trusted devices

### **Administration**
- Granular access control
- Real-time session monitoring
- Flexible blocking system
- Comprehensive user management

### **Scalability**
- Efficient session management
- Optimized database queries
- Horizontal scaling support
- Performance monitoring

This authentication system provides a robust foundation for any enterprise application requiring advanced security, user management, and session control capabilities.

## Technical Implementation Details

### **Core Services Architecture**

#### **SessionService Class**
```typescript
class SessionService {
  // Session lifecycle management
  async createSession(user: IUser, deviceInfo: DeviceInfo): Promise<IUserSession>
  async getSessionByToken(token: string): Promise<IUserSession | null>
  async updateSessionActivity(sessionId: string): Promise<void>
  async endSession(token: string): Promise<boolean>
  async endAllUserSessions(userId: string, exceptSessionId?: string): Promise<number>

  // Device management
  async trustDevice(sessionId: string): Promise<boolean>
  generateDeviceId(ipAddress: string, deviceName: string, browser: string, os: string): string

  // Maintenance
  async cleanupExpiredSessions(): Promise<number>
  async getUserActiveSessions(userId: string): Promise<IUserSession[]>
  async getAllActiveSessions(limit: number, skip: number): Promise<{sessions: IUserSession[], total: number}>
}
```

#### **BlockingService Class**
```typescript
class BlockingService {
  // Blocking operations
  async blockIpAddress(ip: string, reason: string, adminId: string, expiresAt?: Date): Promise<IBlockedEntity>
  async blockIpRange(cidr: string, reason: string, adminId: string, expiresAt?: Date): Promise<IBlockedEntity>
  async blockDevice(deviceId: string, reason: string, adminId: string, expiresAt?: Date): Promise<IBlockedEntity>
  async blockCountry(countryCode: string, reason: string, adminId: string, expiresAt?: Date): Promise<IBlockedEntity>

  // Checking operations
  async isIpBlocked(ipAddress: string): Promise<{isBlocked: boolean, entity?: IBlockedEntity}>
  async isDeviceBlocked(deviceId: string): Promise<{isBlocked: boolean, entity?: IBlockedEntity}>
  async isCountryBlocked(countryCode: string): Promise<{isBlocked: boolean, entity?: IBlockedEntity}>

  // Management operations
  async unblockEntity(entityId: string, adminId: string): Promise<IBlockedEntity | null>
  async getAllBlockedEntities(limit: number, skip: number, filter?: any): Promise<{entities: IBlockedEntity[], total: number}>
  async cleanupExpiredBlocks(): Promise<number>
}
```

### **Device Detection System**

#### **DeviceDetector Class**
```typescript
class DeviceDetector {
  static detectDevice(req: NextRequest): DeviceInfo {
    // Extract user agent and parse device information
    const userAgent = req.headers.get('user-agent') || 'Unknown';
    const parser = new UAParser(userAgent);

    return {
      deviceName: `${deviceResult.vendor} ${deviceResult.model}`,
      deviceType: deviceResult.type || 'desktop',
      browser: browserResult.name || 'Unknown Browser',
      browserVersion: browserResult.version || '',
      operatingSystem: osResult.name || 'Unknown OS',
      osVersion: osResult.version || '',
      ipAddress: this.getIpAddress(req),
      location: this.getLocationFromRequest(req)
    };
  }

  private static getIpAddress(req: NextRequest): string {
    // Handle various proxy headers
    const forwardedFor = req.headers.get('x-forwarded-for');
    const realIp = req.headers.get('x-real-ip');
    const cfConnectingIp = req.headers.get('cf-connecting-ip');

    return forwardedFor?.split(',')[0].trim() ||
           realIp ||
           cfConnectingIp ||
           req.ip ||
           '0.0.0.0';
  }
}
```

### **Authentication Flow Implementation**

#### **Login Process**
```typescript
export const loginUser = async (email: string, password: string, req?: NextRequest) => {
  // 1. Validate user credentials
  const user = await User.findByEmail(email).select('+password');
  if (!user || !await user.comparePassword(password)) {
    throw new Error('Invalid credentials');
  }

  // 2. Check user status
  if (user.status !== UserStatus.ACTIVE) {
    throw new Error('Account not active');
  }

  // 3. Extract device information
  const deviceInfo = req ? DeviceDetector.detectDevice(req) : defaultDeviceInfo;

  // 4. Check blocking rules
  await blockingService.isIpBlocked(deviceInfo.ipAddress);
  await blockingService.isDeviceBlocked(deviceInfo.deviceId);
  await blockingService.isCountryBlocked(deviceInfo.location?.country);

  // 5. Handle single device login enforcement
  if (user.singleDeviceLogin) {
    const activeSessions = await sessionService.getUserActiveSessions(user._id.toString());
    if (activeSessions.length > 0) {
      throw new Error('Single device login enforced');
    }
  }

  // 6. Create session
  const session = await sessionService.createSession(user, deviceInfo);

  // 7. Update user login timestamp
  user.lastLogin = new Date();
  await user.save();

  return {
    user,
    token: session.token,
    requiresDeviceVerification: !session.isTrusted,
    sessionId: session._id.toString()
  };
};
```

#### **Token Verification**
```typescript
export const verifyToken = (token: string): JwtPayload | null => {
  try {
    return jwt.verify(token, JWT_SECRET) as JwtPayload;
  } catch (error) {
    return null;
  }
};

export const getCurrentUser = async (req: NextRequest): Promise<IUser | null> => {
  // 1. Extract token from cookies
  const token = req.cookies.get('token')?.value;
  if (!token) return null;

  // 2. Verify session exists
  const session = await sessionService.getSessionByToken(token);
  if (!session) return null;

  // 3. Verify JWT token
  const decoded = verifyToken(token);
  if (!decoded) {
    await sessionService.endSession(token);
    return null;
  }

  // 4. Get user from database
  const user = await User.findById(decoded.id);
  if (!user || user.status !== UserStatus.ACTIVE) return null;

  // 5. Update session activity
  await sessionService.updateSessionActivity(session._id.toString());

  return user;
};
```

### **Middleware Implementation**

#### **Route Protection**
```typescript
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 1. Check if route is public
  if (publicRoutes.some(route => pathname.startsWith(route))) {
    return NextResponse.next();
  }

  // 2. Get and verify token
  const token = request.cookies.get('token')?.value;
  const isAuthenticated = token ? !!verifyToken(token) : false;

  // 3. Handle unauthenticated users
  if (!isAuthenticated) {
    return NextResponse.redirect(new URL('/loading', request.url));
  }

  // 4. Check role-based access
  const decoded = verifyToken(token!);
  const userRole = decoded!.role as UserRole;

  // 5. Super admin bypass
  if (userRole === UserRole.SUPER_ADMIN) {
    return NextResponse.next();
  }

  // 6. Check specific route permissions
  for (const [routePrefix, allowedRoles] of Object.entries(roleBasedRoutes)) {
    if (pathname.startsWith(routePrefix)) {
      if (!allowedRoles.includes(userRole)) {
        return NextResponse.redirect(new URL('/unauthorized', request.url));
      }
      break;
    }
  }

  return NextResponse.next();
}
```

### **Database Schema Definitions**

#### **User Schema**
```typescript
const userSchema = new Schema<IUser>({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    validate: {
      validator: (v: string) => /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/.test(v),
      message: 'Invalid email format'
    }
  },
  password: {
    type: String,
    required: true,
    minlength: 8,
    select: false
  },
  firstName: { type: String, required: true, trim: true },
  lastName: { type: String, required: true, trim: true },
  role: {
    type: String,
    enum: Object.values(UserRole),
    default: UserRole.EMPLOYEE
  },
  status: {
    type: String,
    enum: Object.values(UserStatus),
    default: UserStatus.ACTIVE
  },

  // Security settings
  allowMultipleDevices: { type: Boolean, default: true },
  trustedDevicesOnly: { type: Boolean, default: false },
  singleDeviceLogin: { type: Boolean, default: false },

  // Authentication tracking
  lastLogin: Date,
  failedLoginAttempts: { type: Number, default: 0 },
  lastFailedLogin: Date,
  accountLockTime: Date,

  // Password reset
  passwordResetToken: String,
  passwordResetExpires: Date
}, {
  timestamps: true
});

// Pre-save middleware for password hashing
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  this.password = await bcrypt.hash(this.password, 12);
  next();
});

// Instance methods
userSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password);
};

userSchema.methods.createPasswordResetToken = function(): string {
  const resetToken = crypto.randomBytes(32).toString('hex');
  this.passwordResetToken = crypto.createHash('sha256').update(resetToken).digest('hex');
  this.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
  return resetToken;
};

// Static methods
userSchema.statics.findByEmail = function(email: string) {
  return this.findOne({ email: email.toLowerCase() });
};
```

#### **UserSession Schema**
```typescript
const userSessionSchema = new Schema<IUserSession>({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  deviceId: { type: String, required: true, index: true },
  deviceName: { type: String, required: true },
  deviceType: { type: String, required: true },
  browser: { type: String, required: true },
  operatingSystem: { type: String, required: true },
  ipAddress: { type: String, required: true },
  macAddress: String,
  location: {
    country: String,
    region: String,
    city: String,
    latitude: Number,
    longitude: Number
  },
  isTrusted: { type: Boolean, default: false },
  isActive: { type: Boolean, default: true, index: true },
  lastActive: { type: Date, default: Date.now },
  loginTime: { type: Date, default: Date.now },
  logoutTime: Date,
  expiresAt: { type: Date, required: true, index: true },
  token: { type: String, required: true, index: true }
}, {
  timestamps: true
});

// Compound indexes for efficient queries
userSessionSchema.index({ userId: 1, isActive: 1 });
userSessionSchema.index({ token: 1, isActive: 1, expiresAt: 1 });
userSessionSchema.index({ deviceId: 1, isActive: 1 });

// Static methods
userSessionSchema.statics.findActiveSessionsByUser = function(userId: string) {
  return this.find({
    userId: new mongoose.Types.ObjectId(userId),
    isActive: true,
    expiresAt: { $gt: new Date() }
  }).sort({ lastActive: -1 });
};

userSessionSchema.statics.findByToken = function(token: string) {
  return this.findOne({
    token,
    isActive: true,
    expiresAt: { $gt: new Date() }
  });
};
```

#### **BlockedEntity Schema**
```typescript
const blockedEntitySchema = new Schema<IBlockedEntity>({
  type: {
    type: String,
    enum: ['IP_ADDRESS', 'IP_RANGE', 'DEVICE', 'COUNTRY'],
    required: true,
    index: true
  },
  value: { type: String, required: true, index: true },
  description: { type: String, required: true },
  reason: { type: String, required: true },
  isActive: { type: Boolean, default: true, index: true },
  expiresAt: { type: Date, index: true },
  blockedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Compound indexes
blockedEntitySchema.index({ type: 1, value: 1, isActive: 1 });
blockedEntitySchema.index({ isActive: 1, expiresAt: 1 });

// Static methods for checking blocks
blockedEntitySchema.statics.findActiveByIp = function(ipAddress: string) {
  return this.findOne({
    $or: [
      { type: 'IP_ADDRESS', value: ipAddress },
      { type: 'IP_RANGE', value: { $regex: this.buildCidrRegex(ipAddress) } }
    ],
    isActive: true,
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ]
  });
};

blockedEntitySchema.statics.findActiveByDevice = function(deviceId: string) {
  return this.findOne({
    type: 'DEVICE',
    value: deviceId,
    isActive: true,
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ]
  });
};

blockedEntitySchema.statics.findActiveByCountry = function(countryCode: string) {
  return this.findOne({
    type: 'COUNTRY',
    value: countryCode.toUpperCase(),
    isActive: true,
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ]
  });
};
```

### **Frontend Integration Patterns**

#### **Authentication Hook**
```typescript
export const useAuth = () => {
  const authStore = useAuthStore();
  const router = useRouter();

  const login = async (credentials: LoginCredentials) => {
    try {
      await authStore.login(credentials);
      router.push('/dashboard');
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await authStore.logout();
      router.push('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const checkAuth = useCallback(async () => {
    await authStore.checkAuth();
  }, [authStore]);

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  return {
    user: authStore.user,
    isAuthenticated: authStore.isAuthenticated,
    isLoading: authStore.isLoading,
    error: authStore.error,
    login,
    logout,
    checkAuth
  };
};
```

#### **Protected Route Component**
```typescript
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: UserRole[];
  fallback?: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRoles,
  fallback = <div>Loading...</div>
}) => {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
      return;
    }

    if (user && requiredRoles && !requiredRoles.includes(user.role)) {
      router.push('/unauthorized');
      return;
    }
  }, [user, isAuthenticated, isLoading, requiredRoles, router]);

  if (isLoading) return fallback;
  if (!isAuthenticated) return null;
  if (requiredRoles && user && !requiredRoles.includes(user.role)) return null;

  return <>{children}</>;
};
```

### **Performance Optimizations**

#### **Session Update Throttling**
```typescript
class SessionService {
  private sessionUpdateThrottle = new Map<string, number>();
  private readonly SESSION_UPDATE_INTERVAL = 60000; // 1 minute

  async updateSessionActivity(sessionId: string): Promise<void> {
    const lastUpdate = this.sessionUpdateThrottle.get(sessionId);
    const now = Date.now();

    if (lastUpdate && (now - lastUpdate) < this.SESSION_UPDATE_INTERVAL) {
      return; // Skip update if within throttle window
    }

    await UserSession.findByIdAndUpdate(sessionId, { lastActive: new Date() });
    this.sessionUpdateThrottle.set(sessionId, now);
  }
}
```

#### **Database Indexing Strategy**
```typescript
// Critical indexes for performance
userSchema.index({ email: 1 }, { unique: true });
userSchema.index({ status: 1 });
userSchema.index({ role: 1 });

userSessionSchema.index({ userId: 1, isActive: 1 });
userSessionSchema.index({ token: 1, isActive: 1, expiresAt: 1 });
userSessionSchema.index({ deviceId: 1 });
userSessionSchema.index({ expiresAt: 1 }); // For cleanup operations

blockedEntitySchema.index({ type: 1, value: 1, isActive: 1 });
blockedEntitySchema.index({ isActive: 1, expiresAt: 1 });
```

### **Monitoring & Maintenance**

#### **Cleanup Jobs**
```typescript
// Scheduled cleanup operations
export const cleanupExpiredSessions = async () => {
  const count = await sessionService.cleanupExpiredSessions();
  logger.info(`Cleaned up ${count} expired sessions`);
};

export const cleanupExpiredBlocks = async () => {
  const count = await blockingService.cleanupExpiredBlocks();
  logger.info(`Cleaned up ${count} expired blocks`);
};

// Run cleanup every hour
setInterval(cleanupExpiredSessions, 60 * 60 * 1000);
setInterval(cleanupExpiredBlocks, 60 * 60 * 1000);
```

#### **Health Monitoring**
```typescript
export const getSystemHealth = async () => {
  const [activeSessions, blockedEntities, activeUsers] = await Promise.all([
    UserSession.countDocuments({ isActive: true, expiresAt: { $gt: new Date() } }),
    BlockedEntity.countDocuments({ isActive: true }),
    User.countDocuments({ status: UserStatus.ACTIVE })
  ]);

  return {
    activeSessions,
    blockedEntities,
    activeUsers,
    timestamp: new Date()
  };
};
```

This comprehensive authentication system provides enterprise-grade security with advanced features for device management, session control, and user access management. The modular design allows for easy integration into any application while maintaining high security standards and optimal performance.

{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "cleanup": "node scripts/cleanup-database.js", "cleanup:quick": "node scripts/quick-cleanup.js", "cleanup:all": "node scripts/quick-cleanup.js --all", "db:stats": "node scripts/quick-cleanup.js --stats"}, "dependencies": {"@emotion/is-prop-valid": "latest", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/nodemailer": "^6.4.17", "@types/pdfkit": "^0.17.0", "autoprefixer": "^10.4.20", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "express-rate-limit": "^7.5.1", "framer-motion": "latest", "glob": "^11.0.0", "gridfs-stream": "^1.1.1", "input-otp": "1.4.1", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.454.0", "mongodb": "^6.17.0", "mongoose": "^8.16.1", "multer": "^2.0.1", "next": "14.2.16", "next-themes": "^0.4.4", "nodemailer": "^7.0.5", "pdfkit": "^0.17.1", "react": "^18", "react-day-picker": "8.10.1", "react-dom": "^18", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "latest", "sonner": "^1.7.1", "stripe": "^18.3.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1", "zustand": "^5.0.6"}, "devDependencies": {"@types/express-rate-limit": "^6.0.0", "@types/glob": "^8.1.0", "@types/ioredis": "^5.0.0", "@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8.57.1", "eslint-config-next": "15.3.5", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}
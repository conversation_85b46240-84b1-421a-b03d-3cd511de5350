const mongoose = require('mongoose')

// MongoDB connection - using the actual URI from .env.local
const MONGODB_URI = 'mongodb+srv://winstonmhango23:<EMAIL>/?retryWrites=true&w=majority&appName=jobs'
const BASE_URL = 'http://localhost:3000/api/v1'

async function testRegistrationDebug() {
  console.log('🧪 Testing Registration Debug - Company Creation Issue')
  console.log('=====================================================')

  // Test data for company admin registration
  const testCompanyData = {
    email: `test-debug-${Date.now()}@example.com`,
    password: 'testpassword123',
    firstName: 'Debug',
    lastName: 'User',
    role: 'company_admin',
    phone: '+1234567890',
    location: {
      city: 'San Francisco',
      state: 'CA',
      country: 'United States'
    },
    company: {
      name: 'Debug Test Company',
      website: 'https://debug-test.com',
      industry: 'technology',
      size: '11-50',
      description: 'A test company for debugging the registration flow.'
    }
  }

  try {
    // Connect to MongoDB to check before/after state
    await mongoose.connect(MONGODB_URI)
    console.log('✅ Connected to MongoDB')

    // Check initial state
    const Company = mongoose.connection.db.collection('companies')
    const User = mongoose.connection.db.collection('users')
    
    const initialCompanyCount = await Company.countDocuments()
    const initialUserCount = await User.countDocuments()
    
    console.log('\n📊 Initial State:')
    console.log(`   Companies: ${initialCompanyCount}`)
    console.log(`   Users: ${initialUserCount}`)

    // Test registration
    console.log('\n🚀 Testing Company Admin Registration...')
    console.log('Registration Data:', JSON.stringify({
      email: testCompanyData.email,
      role: testCompanyData.role,
      company: testCompanyData.company
    }, null, 2))

    const registrationResponse = await fetch(`${BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testCompanyData)
    })

    const registrationResult = await registrationResponse.json()
    console.log('\n📝 Registration Response Status:', registrationResponse.status)
    console.log('📝 Registration Response:', JSON.stringify(registrationResult, null, 2))

    // Check final state
    const finalCompanyCount = await Company.countDocuments()
    const finalUserCount = await User.countDocuments()
    
    console.log('\n📊 Final State:')
    console.log(`   Companies: ${finalCompanyCount} (${finalCompanyCount - initialCompanyCount > 0 ? '+' : ''}${finalCompanyCount - initialCompanyCount})`)
    console.log(`   Users: ${finalUserCount} (${finalUserCount - initialUserCount > 0 ? '+' : ''}${finalUserCount - initialUserCount})`)

    if (registrationResponse.status === 200 || registrationResponse.status === 201) {
      // Find the created user
      const createdUser = await User.findOne({ email: testCompanyData.email })
      console.log('\n👤 Created User:')
      console.log(`   ID: ${createdUser?._id}`)
      console.log(`   Email: ${createdUser?.email}`)
      console.log(`   Role: ${createdUser?.role}`)
      console.log(`   Company ID: ${createdUser?.companyId || 'NONE'}`)

      if (createdUser?.companyId) {
        // Find the created company
        const createdCompany = await Company.findOne({ _id: createdUser.companyId })
        console.log('\n🏢 Created Company:')
        console.log(`   ID: ${createdCompany?._id}`)
        console.log(`   Name: ${createdCompany?.name}`)
        console.log(`   Slug: ${createdCompany?.slug}`)
        console.log(`   Industry: ${createdCompany?.industry}`)
        console.log(`   Size: ${createdCompany?.size}`)
        console.log(`   Admins: ${createdCompany?.admins?.length || 0}`)
        console.log(`   Team Members: ${createdCompany?.teamMembers?.length || 0}`)
      } else {
        console.log('\n❌ NO COMPANY CREATED - This is the problem!')
      }
    } else {
      console.log('\n❌ Registration failed!')
      console.log('Error details:', registrationResult)
    }

    // List all companies to see what's in the database
    console.log('\n📋 All Companies in Database:')
    const allCompanies = await Company.find({}).toArray()
    if (allCompanies.length === 0) {
      console.log('   No companies found')
    } else {
      allCompanies.forEach((company, index) => {
        console.log(`   ${index + 1}. ${company.name} (ID: ${company._id})`)
      })
    }

    // List all users to see what's in the database
    console.log('\n📋 All Users in Database:')
    const allUsers = await User.find({}).toArray()
    allUsers.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.email} (Role: ${user.role}, Company: ${user.companyId || 'None'})`)
    })

  } catch (error) {
    console.error('❌ Error:', error)
  } finally {
    await mongoose.disconnect()
    console.log('\n✅ Disconnected from MongoDB')
  }
}

// Run the test
testRegistrationDebug()

// types/companies-frontend.types.ts

export type CompanySize = 'startup' | 'small' | 'medium' | 'large' | 'enterprise'
export type FundingStage = 'pre_seed' | 'seed' | 'series_a' | 'series_b' | 'series_c' | 'ipo' | 'acquired'

export interface CompanyLocation {
  city: string
  state?: string
  country: string
  isHeadquarters?: boolean
  displayName: string
}

export interface CompanyFrontend {
  id: string
  name: string
  slug: string
  description: string
  logo?: string
  website?: string
  industries: string[]
  size: CompanySize
  founded?: number
  primaryLocation: {
    city: string
    state?: string
    country: string
    displayName: string
  }
  allLocations: CompanyLocation[]
  rating: number
  stats: {
    activeJobs: number
    totalJobs: number
    totalApplications: number
    totalHires: number
    followerCount: number
    profileViews: number
  }
  culture: {
    benefits: string[]
    values: string[]
    workEnvironment?: string
  }
  specialties: string[]
  technologies: string[]
  socialLinks: {
    linkedin?: string
    twitter?: string
    github?: string
  }
  verification: {
    isVerified: boolean
    verifiedAt?: string
  }
  funding?: {
    stage: FundingStage
    amount?: number
    investors: string[]
  }
  isActive?: boolean
  isFeatured?: boolean
  createdAt?: string
  updatedAt?: string
}

export interface CompanySearchQuery {
  search?: string
  industries?: string[]
  sizes?: CompanySize[]
  locations?: string[]
  fundingStages?: FundingStage[]
  verified?: boolean
  hasJobs?: boolean
  page?: number
  limit?: number
  sortBy?: 'relevance' | 'name' | 'founded' | 'size' | 'jobs' | 'rating'
  sortOrder?: 'asc' | 'desc'
}

export interface CompanySearchResult {
  companies: CompanyFrontend[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  aggregations: {
    industries: string[]
    sizes: string[]
    locations: string[]
    fundingStages: string[]
  }
  searchMeta: {
    query: string
    totalResults: number
    searchTime: number
  }
}

export interface CompanyFilters {
  industries: string[]
  sizes: CompanySize[]
  locations: string[]
  fundingStages: FundingStage[]
  verified: boolean
  hasJobs: boolean
  rating: {
    min: number
    max: number
  }
}

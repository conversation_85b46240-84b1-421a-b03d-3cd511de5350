import { z } from 'zod'

// Company Location Types
export interface CompanyLocation {
  id?: string
  city: string
  state?: string
  country: string
  isHeadquarters: boolean
  address?: string
  postalCode?: string
  coordinates?: [number, number]
}

// Company Culture Types
export interface CompanyCulture {
  values?: string[]
  benefits?: string[]
  workEnvironment?: string
  diversity?: string
  mission?: string
  vision?: string
  perks?: string[]
}

// Social Links Types
export interface SocialLinks {
  linkedin?: string
  twitter?: string
  facebook?: string
  instagram?: string
  github?: string
  youtube?: string
  glassdoor?: string
}

// Team Member Types
export interface TeamMember {
  id: string
  user: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  role: string
  department?: string
  joinedAt: Date
  isActive: boolean
  permissions: string[]
}

// Job Types
export interface Job {
  id: string
  title: string
  description: string
  department?: string
  location: string
  type: 'full-time' | 'part-time' | 'contract' | 'internship'
  level: 'entry' | 'mid' | 'senior' | 'lead' | 'executive'
  salary?: {
    min: number
    max: number
    currency: string
  }
  skills: string[]
  requirements: string[]
  benefits: string[]
  status: 'draft' | 'active' | 'paused' | 'closed'
  applicationsCount: number
  viewsCount: number
  createdAt: Date
  updatedAt: Date
  expiresAt?: Date
}

// Company Stats Types
export interface CompanyStats {
  totalJobs: number
  activeJobs: number
  totalApplications: number
  totalHires: number
  averageTimeToHire?: number
  responseRate?: number
  profileViews: number
  followerCount: number
}

// Subscription Types
export interface Subscription {
  plan: 'starter' | 'professional' | 'enterprise'
  status: 'active' | 'inactive' | 'cancelled' | 'trial'
  currentPeriodEnd?: Date
  jobPostingLimit: number
  jobPostingsUsed: number
  featuredJobsLimit: number
  featuredJobsUsed: number
}

// Verification Types
export interface Verification {
  isVerified: boolean
  verifiedAt?: Date
  documents?: string[]
  businessRegistration?: string
  taxId?: string
  status: 'pending' | 'approved' | 'rejected'
}

// Enhanced Company Interface
export interface EnhancedCompany {
  _id: string
  name: string
  slug: string
  description: string
  tagline?: string
  logo?: string
  coverImage?: string
  website?: string
  industry: string[]
  size: 'startup' | 'small' | 'medium' | 'large' | 'enterprise'
  founded?: number
  employeeCount?: {
    min: number
    max: number
  }
  locations: CompanyLocation[]
  contact: {
    email: string
    phone?: string
    address?: string
    supportEmail?: string
    hrEmail?: string
  }
  culture: CompanyCulture
  socialLinks: SocialLinks
  subscription: Subscription
  stats: CompanyStats
  verification: Verification
  isActive: boolean
  isFeatured: boolean
  createdAt: Date
  updatedAt: Date
}

// Request/Response Types
export interface UpdateCompanyProfileRequest {
  name?: string
  description?: string
  tagline?: string
  website?: string
  industry?: string[]
  size?: string
  founded?: number
  contact?: Partial<EnhancedCompany['contact']>
}

export interface UpdateCompanyCultureRequest {
  culture: Partial<CompanyCulture>
}

export interface UpdateSocialLinksRequest {
  socialLinks: Partial<SocialLinks>
}

export interface AddLocationRequest {
  location: Omit<CompanyLocation, 'id'>
}

export interface UpdateLocationRequest {
  locationId: string
  location: Partial<CompanyLocation>
}

export interface InviteTeamMemberRequest {
  email: string
  role: string
  department?: string
  permissions: string[]
}

export interface UpdateTeamMemberRequest {
  memberId: string
  role?: string
  department?: string
  permissions?: string[]
  isActive?: boolean
}

export interface CreateJobRequest {
  title: string
  description: string
  department?: string
  location: string
  type: Job['type']
  level: Job['level']
  salary?: Job['salary']
  skills: string[]
  requirements: string[]
  benefits: string[]
  expiresAt?: Date
}

export interface UpdateJobRequest {
  jobId: string
  data: Partial<Omit<Job, 'id' | 'createdAt' | 'updatedAt' | 'applicationsCount' | 'viewsCount'>>
}

// Validation Schemas
export const companyLocationSchema = z.object({
  city: z.string().min(1, 'City is required'),
  state: z.string().optional(),
  country: z.string().min(1, 'Country is required'),
  isHeadquarters: z.boolean().default(false),
  address: z.string().optional(),
  postalCode: z.string().optional(),
  coordinates: z.tuple([z.number(), z.number()]).optional()
})

export const companyCultureSchema = z.object({
  values: z.array(z.string()).optional(),
  benefits: z.array(z.string()).optional(),
  workEnvironment: z.string().optional(),
  diversity: z.string().optional(),
  mission: z.string().optional(),
  vision: z.string().optional(),
  perks: z.array(z.string()).optional()
})

export const socialLinksSchema = z.object({
  linkedin: z.string().url().optional().or(z.literal('')),
  twitter: z.string().url().optional().or(z.literal('')),
  facebook: z.string().url().optional().or(z.literal('')),
  instagram: z.string().url().optional().or(z.literal('')),
  github: z.string().url().optional().or(z.literal('')),
  youtube: z.string().url().optional().or(z.literal('')),
  glassdoor: z.string().url().optional().or(z.literal(''))
})

export const updateCompanyProfileSchema = z.object({
  name: z.string().min(1, 'Company name is required').optional(),
  description: z.string().min(10, 'Description must be at least 10 characters').optional(),
  tagline: z.string().max(100, 'Tagline must be less than 100 characters').optional(),
  website: z.string().url('Invalid website URL').optional().or(z.literal('')),
  industry: z.array(z.string()).optional(),
  size: z.enum(['startup', 'small', 'medium', 'large', 'enterprise']).optional(),
  founded: z.number().min(1800).max(new Date().getFullYear()).optional(),
  contact: z.object({
    email: z.string().email('Invalid email address').optional(),
    phone: z.string().optional(),
    address: z.string().optional(),
    supportEmail: z.string().email('Invalid support email').optional(),
    hrEmail: z.string().email('Invalid HR email').optional()
  }).optional()
})

export const inviteTeamMemberSchema = z.object({
  email: z.string().email('Invalid email address'),
  role: z.string().min(1, 'Role is required'),
  department: z.string().optional(),
  permissions: z.array(z.string()).min(1, 'At least one permission is required')
})

export const createJobSchema = z.object({
  title: z.string().min(1, 'Job title is required'),
  description: z.string().min(50, 'Job description must be at least 50 characters'),
  department: z.string().optional(),
  location: z.string().min(1, 'Location is required'),
  type: z.enum(['full-time', 'part-time', 'contract', 'internship']),
  level: z.enum(['entry', 'mid', 'senior', 'lead', 'executive']),
  salary: z.object({
    min: z.number().min(0),
    max: z.number().min(0),
    currency: z.string().length(3)
  }).optional(),
  skills: z.array(z.string()).min(1, 'At least one skill is required'),
  requirements: z.array(z.string()).min(1, 'At least one requirement is required'),
  benefits: z.array(z.string()).optional(),
  expiresAt: z.date().optional()
})

// API Response Types
export interface CompanyProfileResponse {
  success: boolean
  data: EnhancedCompany
  message?: string
}

export interface TeamMembersResponse {
  success: boolean
  data: TeamMember[]
  message?: string
}

export interface JobsResponse {
  success: boolean
  data: Job[]
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  message?: string
}

export interface AnalyticsResponse {
  success: boolean
  data: {
    profileViews: Array<{ date: string; views: number }>
    applicationTrends: Array<{ date: string; applications: number }>
    jobPerformance: Array<{ jobId: string; title: string; views: number; applications: number }>
    hiringMetrics: {
      totalHires: number
      averageTimeToHire: number
      responseRate: number
    }
  }
  message?: string
}

// types/client-detail.types.ts
export interface ClientDetailProfile {
  id: string
  user: {
    id: string
    profile: {
      firstName: string
      lastName: string
      email: string
      phone?: string
      avatar?: string
      dateOfBirth?: string
      gender?: string
      nationality?: string
    }
    preferences: {
      theme: string
      language: string
      timezone: string
      notifications: {
        email: boolean
        sms: boolean
        push: boolean
      }
    }
  }
  headline: string
  summary: string
  
  // Experience & Career
  experience: {
    level: 'entry' | 'junior' | 'mid' | 'senior' | 'lead' | 'executive'
    yearsOfExperience: number
    industries: string[]
    currentSalary: {
      amount?: number
      currency: string
      period: 'hourly' | 'monthly' | 'yearly'
    }
  }
  
  // Job Preferences
  jobPreferences: {
    desiredRoles: string[]
    industries: string[]
    locations: Array<{
      city: string
      state: string
      country: string
      remote: boolean
      relocationWilling: boolean
    }>
    salaryExpectation: {
      min: number
      max: number
      currency: string
      period: 'hourly' | 'monthly' | 'yearly'
      negotiable: boolean
    }
    jobTypes: Array<'full-time' | 'part-time' | 'contract' | 'freelance' | 'internship' | 'temporary'>
    workArrangement: Array<'onsite' | 'remote' | 'hybrid'>
    availability: 'immediately' | '2-weeks' | '1-month' | '2-months' | '3-months' | 'not-looking'
    benefits: string[]
    companySize: Array<'startup' | 'small' | 'medium' | 'large' | 'enterprise'>
  }
  
  // Privacy Settings
  privacy: {
    profileVisibility: 'public' | 'private' | 'recruiter-only'
    showSalaryExpectation: boolean
    showCurrentCompany: boolean
    allowRecruiterContact: boolean
    showProfileToCurrentEmployer: boolean
  }
  
  // Work History
  workHistory: Array<{
    id: string
    company: string
    position: string
    location: string
    startDate: string
    endDate?: string
    current: boolean
    description: string
    achievements: string[]
    skills: string[]
    employmentType: 'full-time' | 'part-time' | 'contract' | 'freelance' | 'internship'
  }>
  
  // Education
  education: Array<{
    id: string
    institution: string
    degree: string
    fieldOfStudy: string
    startDate: string
    endDate?: string
    current: boolean
    gpa?: number
    description?: string
    achievements: string[]
  }>
  
  // Skills & Expertise
  skills: Array<{
    name: string
    category: string
    level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
    yearsOfExperience: number
    endorsed: boolean
    endorsements: number
  }>
  
  // Certifications
  certifications: Array<{
    id: string
    name: string
    issuer: string
    issueDate: string
    expiryDate?: string
    credentialId?: string
    credentialUrl?: string
    description?: string
  }>
  
  // Portfolio
  portfolio: Array<{
    id: string
    title: string
    description: string
    url?: string
    images: string[]
    technologies: string[]
    category: string
    featured: boolean
    completionDate: string
  }>
  
  // Documents
  documents: {
    resume: Array<{
      id: string
      name: string
      url: string
      uploadDate: string
      size: number
      type: string
      primary: boolean
    }>
    coverLetter: Array<{
      id: string
      name: string
      url: string
      uploadDate: string
      size: number
      type: string
    }>
    portfolio: Array<{
      id: string
      name: string
      url: string
      uploadDate: string
      size: number
      type: string
    }>
    certificate: Array<{
      id: string
      name: string
      url: string
      uploadDate: string
      size: number
      type: string
    }>
    other: Array<{
      id: string
      name: string
      url: string
      uploadDate: string
      size: number
      type: string
    }>
  }
  
  // Languages
  languages: Array<{
    name: string
    proficiency: 'basic' | 'conversational' | 'fluent' | 'native'
    certified: boolean
  }>
  
  // Activity & Analytics
  activity: {
    profileViews: number
    searchAppearances: number
    recruiterViews: number
    profileCompleteness: number
    lastProfileUpdate: string
    viewHistory: Array<{
      date: string
      views: number
      source: string
    }>
  }
  
  // Application Statistics
  applicationStats: {
    totalApplications: number
    pendingApplications: number
    interviewsReceived: number
    offersReceived: number
    successRate: number
    averageResponseTime: number
    recentApplications: Array<{
      jobId: string
      jobTitle: string
      company: string
      appliedDate: string
      status: 'pending' | 'reviewed' | 'interview' | 'offer' | 'rejected' | 'withdrawn'
    }>
  }
  
  // Saved Items
  savedJobs: string[]
  followedCompanies: string[]
  followedRecruiters: string[]
  
  // Messages
  messages: {
    unreadCount: number
    sent: Array<{
      id: string
      recipientId: string
      subject: string
      content: string
      sentDate: string
      read: boolean
    }>
    received: Array<{
      id: string
      senderId: string
      subject: string
      content: string
      receivedDate: string
      read: boolean
    }>
  }
  
  // Job Alerts
  jobAlerts: Array<{
    id: string
    name: string
    criteria: {
      keywords: string[]
      location: string
      salaryMin?: number
      jobType: string[]
      industries: string[]
    }
    frequency: 'daily' | 'weekly' | 'monthly'
    active: boolean
    createdDate: string
  }>
  
  // Search History
  searchHistory: Array<{
    query: string
    filters: Record<string, unknown>
    timestamp: string
    resultsCount: number
  }>
  
  // Verification
  verification: {
    emailVerified: boolean
    phoneVerified: boolean
    identityVerified: boolean
    backgroundCheckCompleted: boolean
    references: Array<{
      id: string
      name: string
      position: string
      company: string
      email: string
      phone?: string
      relationship: string
      verified: boolean
    }>
  }
  
  // Metadata
  isActive: boolean
  isPublic: boolean
  lastLogin: string
  createdAt: string
  updatedAt: string
}

// API Request/Response Types
export interface UpdateClientDetailRequest {
  headline?: string
  summary?: string
  experience?: Partial<ClientDetailProfile['experience']>
  jobPreferences?: Partial<ClientDetailProfile['jobPreferences']>
  privacy?: Partial<ClientDetailProfile['privacy']>
  workHistory?: ClientDetailProfile['workHistory']
  education?: ClientDetailProfile['education']
  skills?: ClientDetailProfile['skills']
  certifications?: ClientDetailProfile['certifications']
  portfolio?: ClientDetailProfile['portfolio']
  languages?: ClientDetailProfile['languages']
}

export interface ClientDetailResponse {
  success: boolean
  data: ClientDetailProfile
  message?: string
}

export interface ClientDetailAnalytics {
  profileViews: {
    total: number
    thisWeek: number
    thisMonth: number
    trend: 'up' | 'down' | 'stable'
  }
  searchAppearances: {
    total: number
    thisWeek: number
    thisMonth: number
    trend: 'up' | 'down' | 'stable'
  }
  applicationSuccess: {
    rate: number
    totalApplications: number
    interviews: number
    offers: number
  }
  profileStrength: {
    score: number
    completeness: number
    suggestions: string[]
  }
}

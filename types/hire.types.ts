// types/hire.types.ts
import { ObjectId } from 'mongodb'

export type HireStatus = 
  | 'pending'           // Initial hire request sent
  | 'reviewing'         // Company is reviewing the hire request
  | 'negotiating'       // Terms are being negotiated
  | 'contract_sent'     // Contract has been sent to talent
  | 'contract_signed'   // Contract signed by talent
  | 'hired'             // Successfully hired
  | 'rejected'          // Hire request rejected
  | 'cancelled'         // Hire request cancelled
  | 'expired'           // Hire request expired

export type HireType = 
  | 'full_time'         // Full-time employment
  | 'part_time'         // Part-time employment
  | 'contract'          // Contract work
  | 'freelance'         // Freelance project
  | 'internship'        // Internship
  | 'temporary'         // Temporary position

export type ContractType = 
  | 'employment'        // Employment contract
  | 'service'           // Service agreement
  | 'nda'               // Non-disclosure agreement
  | 'freelance'         // Freelance contract

export interface HireTimelineEntry {
  status: HireStatus
  date: Date
  notes?: string
  updatedBy: string | ObjectId
  updatedByRole: 'company' | 'talent' | 'system'
  metadata?: Record<string, any>
}

export interface ProjectDetails {
  title: string
  description: string
  requirements: string[]
  deliverables: string[]
  timeline: {
    startDate: Date
    endDate?: Date
    milestones?: Array<{
      title: string
      description: string
      dueDate: Date
      deliverables: string[]
    }>
  }
  budget?: {
    amount: number
    currency: string
    paymentTerms: string
    paymentSchedule?: 'hourly' | 'weekly' | 'monthly' | 'milestone' | 'completion'
  }
}

export interface ContractTerms {
  type: ContractType
  duration?: {
    startDate: Date
    endDate?: Date
    isIndefinite: boolean
  }
  compensation: {
    amount: number
    currency: string
    frequency: 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'project'
    benefits?: string[]
    bonuses?: Array<{
      type: string
      amount: number
      criteria: string
    }>
  }
  workArrangement: {
    type: 'remote' | 'onsite' | 'hybrid'
    location?: string
    hoursPerWeek?: number
    flexibleHours: boolean
    timezone?: string
  }
  terms: {
    probationPeriod?: number // in days
    noticePeriod?: number // in days
    nonCompete?: boolean
    nonDisclosure?: boolean
    intellectualProperty?: string
    terminationClause?: string
    additionalTerms?: string[]
  }
}

export interface HireRequest {
  _id?: ObjectId
  id?: string
  
  // Core Information
  companyId: string | ObjectId
  talentId: string | ObjectId
  hireType: HireType
  status: HireStatus
  
  // Project & Contract Details
  projectDetails: ProjectDetails
  contractTerms: ContractTerms
  
  // Communication
  message?: string
  companyNotes?: string
  talentResponse?: string
  
  // Timeline & Status Tracking
  timeline: HireTimelineEntry[]
  
  // Metadata
  priority: 'low' | 'medium' | 'high' | 'urgent'
  source: 'talent_search' | 'application' | 'referral' | 'direct'
  referenceId?: string // Link to job application if applicable
  
  // Contract Management
  contractDocuments?: Array<{
    type: ContractType
    filename: string
    url: string
    uploadDate: Date
    signedBy?: Array<{
      party: 'company' | 'talent'
      signedAt: Date
      signature?: string
    }>
  }>
  
  // Expiration & Deadlines
  expiresAt?: Date
  responseDeadline?: Date
  
  // System Fields
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  createdBy: string | ObjectId
  lastUpdatedBy: string | ObjectId
}

export interface CreateHireRequest {
  talentId: string
  hireType: HireType
  projectDetails: Omit<ProjectDetails, 'timeline'> & {
    timeline: {
      startDate: string
      endDate?: string
      milestones?: Array<{
        title: string
        description: string
        dueDate: string
        deliverables: string[]
      }>
    }
  }
  contractTerms: Omit<ContractTerms, 'duration'> & {
    duration?: {
      startDate: string
      endDate?: string
      isIndefinite: boolean
    }
  }
  message?: string
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  responseDeadline?: string
}

export interface UpdateHireRequest {
  status?: HireStatus
  message?: string
  companyNotes?: string
  talentResponse?: string
  contractTerms?: Partial<ContractTerms>
  projectDetails?: Partial<ProjectDetails>
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  responseDeadline?: string
}

export interface HireSearchQuery {
  companyId?: string
  talentId?: string
  status?: HireStatus | HireStatus[]
  hireType?: HireType | HireType[]
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  dateRange?: {
    from: string
    to: string
  }
  search?: string
  page?: number
  limit?: number
  sortBy?: 'createdAt' | 'updatedAt' | 'priority' | 'status'
  sortOrder?: 'asc' | 'desc'
}

export interface HireSearchResult {
  hireRequests: HireFrontend[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  filters: {
    statuses: Array<{ status: HireStatus; count: number }>
    types: Array<{ type: HireType; count: number }>
    priorities: Array<{ priority: string; count: number }>
  }
}

export interface HireStats {
  total: number
  byStatus: Record<HireStatus, number>
  byType: Record<HireType, number>
  byPriority: Record<string, number>
  successRate: number
  averageTimeToHire: number // in days
  activeRequests: number
  expiringSoon: number // expiring within 7 days
}

export interface HireNotification {
  id: string
  type: 'hire_request' | 'status_update' | 'contract_signed' | 'deadline_reminder'
  title: string
  message: string
  hireRequestId: string
  recipientId: string
  recipientType: 'company' | 'talent'
  isRead: boolean
  createdAt: Date
  metadata?: Record<string, any>
}

// Frontend types for UI components
export interface HireFrontend extends Omit<HireRequest, '_id' | 'companyId' | 'talentId' | 'createdBy' | 'lastUpdatedBy'> {
  id: string
  company: {
    id: string
    name: string
    logo?: string
    industry?: string
  }
  talent: {
    id: string
    name: string
    avatar?: string
    title: string
    location: string
    skills: string[]
  }
  createdBy: {
    id: string
    name: string
    role: string
  }
  lastUpdatedBy: {
    id: string
    name: string
    role: string
  }
}

export interface HireFormData {
  // Step 1: Hire Type & Basic Info
  hireType: HireType
  priority: 'low' | 'medium' | 'high' | 'urgent'
  
  // Step 2: Project Details
  projectTitle: string
  projectDescription: string
  requirements: string[]
  deliverables: string[]
  startDate: string
  endDate?: string
  milestones: Array<{
    title: string
    description: string
    dueDate: string
    deliverables: string[]
  }>
  
  // Step 3: Contract Terms
  contractType: ContractType
  compensationAmount: number
  compensationCurrency: string
  compensationFrequency: 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'project'
  benefits: string[]
  workArrangementType: 'remote' | 'onsite' | 'hybrid'
  workLocation?: string
  hoursPerWeek?: number
  flexibleHours: boolean
  probationPeriod?: number
  noticePeriod?: number
  nonCompete: boolean
  nonDisclosure: boolean
  
  // Step 4: Message & Timeline
  message?: string
  responseDeadline?: string
  
  // Agreements
  termsAccepted: boolean
  privacyAccepted: boolean
}

export interface HireValidationErrors {
  hireType?: string
  projectTitle?: string
  projectDescription?: string
  requirements?: string
  deliverables?: string
  startDate?: string
  endDate?: string
  compensationAmount?: string
  compensationFrequency?: string
  workArrangementType?: string
  responseDeadline?: string
  termsAccepted?: string
  privacyAccepted?: string
  general?: string
}

// stores/quiz.store.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { 
  quizService, 
  type QuizCategory,
  type QuizSession,
  type QuizQuestion,
  type QuizAnswer,
  type QuizResult 
} from '@/lib/services/quiz.service'

interface QuizState {
  // Categories
  categories: QuizCategory[]
  isLoadingCategories: boolean
  categoriesError: string | null

  // Current Quiz Session
  currentSession: QuizSession | null
  currentQuestionIndex: number
  answers: QuizAnswer[]
  selectedAnswer: number | null
  timeLeft: number
  isQuizActive: boolean
  isSubmitting: boolean

  // Progressive Quiz System
  quizPhase: 'first' | 'second' | 'completed'
  firstPhaseAnswers: QuizAnswer[]
  wrongAnswers: QuizAnswer[]
  totalQuestionsAnswered: number
  showPhaseTransition: boolean

  // Results
  lastResult: QuizResult | null
  showResults: boolean

  // UI State
  showRegistrationPrompt: boolean
  showAwardModal: boolean
  showLoginForm: boolean
}

interface QuizActions {
  // Category actions
  fetchCategories: () => Promise<void>
  clearCategoriesError: () => void

  // Quiz session actions
  startQuiz: (categoryId: string) => Promise<void>
  selectAnswer: (answerIndex: number) => void
  nextQuestion: () => void
  previousQuestion: () => void
  submitQuiz: (userId?: string) => Promise<void>
  resetQuiz: () => void

  // Progressive quiz actions
  startSecondPhase: () => Promise<void>
  completeQuiz: () => void
  proceedToSecondPhase: () => void
  skipSecondPhase: () => void

  // Timer actions
  updateTimer: (timeLeft: number) => void
  startTimer: () => void
  stopTimer: () => void

  // UI actions
  setShowRegistrationPrompt: (show: boolean) => void
  setShowAwardModal: (show: boolean) => void
  setShowResults: (show: boolean) => void

  // Utility actions
  clearSession: () => void
  getProgress: () => number
}

export const useQuizStore = create<QuizState & QuizActions>()(
  persist(
    (set, get) => ({
      // Initial state
      categories: [],
      isLoadingCategories: false,
      categoriesError: null,

      currentSession: null,
      currentQuestionIndex: 0,
      answers: [],
      selectedAnswer: null,
      timeLeft: 0,
      isQuizActive: false,
      isSubmitting: false,

      // Progressive quiz state
      quizPhase: 'first',
      firstPhaseAnswers: [],
      wrongAnswers: [],
      totalQuestionsAnswered: 0,
      showPhaseTransition: false,

      lastResult: null,
      showResults: false,

      showRegistrationPrompt: false,
      showAwardModal: false,
      showLoginForm: false,

      // Actions
      fetchCategories: async () => {
        set({ isLoadingCategories: true, categoriesError: null })
        
        try {
          const categories = await quizService.getCategories()
          set({ 
            categories, 
            isLoadingCategories: false,
            categoriesError: null 
          })
        } catch (error) {
          set({
            categoriesError: error instanceof Error ? error.message : 'Failed to fetch categories',
            isLoadingCategories: false
          })
        }
      },

      clearCategoriesError: () => set({ categoriesError: null }),

      startQuiz: async (categoryId: string) => {
        try {
          const sessionId = quizService.generateSessionId()
          const session = await quizService.startQuiz(categoryId, sessionId)

          // Limit first phase to 10 questions
          const firstPhaseQuestions = session.questions.slice(0, 10)
          const updatedSession = {
            ...session,
            questions: firstPhaseQuestions
          }

          set({
            currentSession: updatedSession,
            currentQuestionIndex: 0,
            answers: [],
            selectedAnswer: null,
            timeLeft: session.timeLimit,
            isQuizActive: true,
            showResults: false,
            lastResult: null,
            showRegistrationPrompt: false,
            showAwardModal: false,
            showLoginForm: false,
            // Reset progressive quiz state
            quizPhase: 'first',
            firstPhaseAnswers: [],
            wrongAnswers: [],
            totalQuestionsAnswered: 0,
            showPhaseTransition: false
          })
        } catch (error) {
          console.error('Failed to start quiz:', error)
          throw error
        }
      },

      selectAnswer: (answerIndex: number) => {
        set({ selectedAnswer: answerIndex })
      },

      nextQuestion: () => {
        const state = get()
        const { currentSession, currentQuestionIndex, selectedAnswer, answers, quizPhase } = state

        if (!currentSession || selectedAnswer === null) return

        // Record the answer with time spent
        const currentQuestion = currentSession.questions[currentQuestionIndex]
        const timeSpent = Math.max(1, currentSession.timeLimit - state.timeLeft)

        const newAnswer: QuizAnswer = {
          questionId: currentQuestion.id,
          selectedAnswer,
          timeSpent
        }

        const newAnswers = [...answers, newAnswer]
        const nextIndex = currentQuestionIndex + 1

        if (nextIndex >= currentSession.questions.length) {
          // Phase completed
          if (quizPhase === 'first') {
            // First phase completed - show transition
            set({
              answers: newAnswers,
              firstPhaseAnswers: newAnswers,
              selectedAnswer: null,
              isQuizActive: false,
              showPhaseTransition: true,
              totalQuestionsAnswered: newAnswers.length
            })
          } else {
            // Second phase completed - show final results
            set({
              answers: newAnswers,
              selectedAnswer: null,
              isQuizActive: false,
              quizPhase: 'completed',
              totalQuestionsAnswered: state.totalQuestionsAnswered + newAnswers.length - state.firstPhaseAnswers.length
            })

            // Auto-submit quiz with all answers
            const allAnswers = [...state.firstPhaseAnswers, ...newAnswers.slice(state.firstPhaseAnswers.length)]
            get().submitQuiz()
          }
        } else {
          // Move to next question
          set({
            answers: newAnswers,
            currentQuestionIndex: nextIndex,
            selectedAnswer: null
          })
        }
      },

      previousQuestion: () => {
        const state = get()
        if (state.currentQuestionIndex > 0) {
          set({
            currentQuestionIndex: state.currentQuestionIndex - 1,
            selectedAnswer: null
          })
        }
      },

      submitQuiz: async (userId?: string) => {
        const state = get()
        const { currentSession, answers } = state

        if (!currentSession) return

        set({ isSubmitting: true })

        try {
          const totalTimeSpent = currentSession.timeLimit - state.timeLeft
          
          const result = await quizService.submitQuiz(
            currentSession.sessionId,
            currentSession.category.id,
            answers,
            totalTimeSpent,
            userId
          )

          set({
            lastResult: result,
            showResults: true,
            isSubmitting: false,
            isQuizActive: false
          })

          // Show registration prompt for anonymous users with good scores
          if (!userId && result.score >= result.passingScore) {
            set({ showRegistrationPrompt: true })
          }

          // Show award modal if awards were earned
          if (result.earnedAwards.length > 0) {
            set({ showAwardModal: true })
          }

        } catch (error) {
          console.error('Failed to submit quiz:', error)
          set({ isSubmitting: false })
          throw error
        }
      },

      resetQuiz: () => {
        quizService.clearSession()
        set({
          currentSession: null,
          currentQuestionIndex: 0,
          answers: [],
          selectedAnswer: null,
          timeLeft: 0,
          isQuizActive: false,
          isSubmitting: false,
          showResults: false,
          lastResult: null,
          showRegistrationPrompt: false,
          showAwardModal: false,
          showLoginForm: false,
          // Reset progressive quiz state
          quizPhase: 'first',
          firstPhaseAnswers: [],
          wrongAnswers: [],
          totalQuestionsAnswered: 0,
          showPhaseTransition: false
        })
      },

      // Progressive quiz actions
      proceedToSecondPhase: () => {
        set({ showPhaseTransition: false })
        get().startSecondPhase()
      },

      skipSecondPhase: () => {
        const state = get()
        set({
          showPhaseTransition: false,
          quizPhase: 'completed',
          showResults: true
        })
        // Submit with first phase answers only
        get().submitQuiz()
      },

      startSecondPhase: async () => {
        const state = get()
        if (!state.currentSession) return

        try {
          // Get wrong answers from first phase
          const wrongAnswers = await get().identifyWrongAnswers(state.firstPhaseAnswers)

          // Get additional questions to make up 10 total
          const additionalQuestionsNeeded = Math.max(0, 10 - wrongAnswers.length)
          const additionalQuestions = await get().getAdditionalQuestions(
            state.currentSession.category.id,
            additionalQuestionsNeeded,
            [...state.firstPhaseAnswers.map(a => a.questionId), ...wrongAnswers.map(a => a.questionId)]
          )

          // Combine wrong answers and new questions
          const secondPhaseQuestions = [...wrongAnswers, ...additionalQuestions].slice(0, 10)

          set({
            currentSession: {
              ...state.currentSession,
              questions: secondPhaseQuestions
            },
            currentQuestionIndex: 0,
            answers: [],
            selectedAnswer: null,
            timeLeft: state.currentSession.timeLimit,
            isQuizActive: true,
            quizPhase: 'second',
            wrongAnswers
          })
        } catch (error) {
          console.error('Failed to start second phase:', error)
          // Fallback to completing quiz
          get().skipSecondPhase()
        }
      },

      completeQuiz: () => {
        set({
          quizPhase: 'completed',
          showResults: true,
          isQuizActive: false
        })
      },

      // Helper methods
      identifyWrongAnswers: async (answers: QuizAnswer[]) => {
        try {
          const response = await fetch('/api/v1/quiz/check-answers', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ answers })
          })
          const result = await response.json()
          return result.data.wrongQuestions || []
        } catch (error) {
          console.error('Failed to identify wrong answers:', error)
          return []
        }
      },

      getAdditionalQuestions: async (categoryId: string, count: number, excludeIds: string[]) => {
        try {
          const response = await fetch('/api/v1/quiz/additional-questions', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ categoryId, count, excludeIds })
          })
          const result = await response.json()
          return result.data.questions || []
        } catch (error) {
          console.error('Failed to get additional questions:', error)
          return []
        }
      },

      updateTimer: (timeLeft: number) => {
        set({ timeLeft })
        
        // Auto-submit when time runs out
        if (timeLeft <= 0) {
          const state = get()
          if (state.isQuizActive) {
            get().submitQuiz()
          }
        }
      },

      startTimer: () => {
        // Timer logic would be handled by the component
        set({ isQuizActive: true })
      },

      stopTimer: () => {
        set({ isQuizActive: false })
      },

      setShowRegistrationPrompt: (show: boolean) => {
        set({ showRegistrationPrompt: show })
      },

      setShowAwardModal: (show: boolean) => {
        set({ showAwardModal: show })
      },

      setShowResults: (show: boolean) => {
        set({ showResults: show })
      },

      setShowLoginForm: (show: boolean) => {
        set({ showLoginForm: show })
      },

      clearSession: () => {
        quizService.clearSession()
        set({
          currentSession: null,
          currentQuestionIndex: 0,
          answers: [],
          selectedAnswer: null,
          timeLeft: 0,
          isQuizActive: false,
          // Reset progressive quiz state
          quizPhase: 'first',
          firstPhaseAnswers: [],
          wrongAnswers: [],
          totalQuestionsAnswered: 0,
          showPhaseTransition: false,
          showLoginForm: false
        })
      },

      getProgress: () => {
        const state = get()
        if (!state.currentSession) return 0
        return ((state.currentQuestionIndex + 1) / state.currentSession.questions.length) * 100
      }
    }),
    {
      name: 'quiz-storage',
      partialize: (state) => ({
        lastResult: state.lastResult,
        categories: state.categories
      })
    }
  )
)

// Selector hooks for better performance
export const useQuizCategories = () => {
  const store = useQuizStore()
  return {
    categories: store.categories,
    isLoading: store.isLoadingCategories,
    error: store.categoriesError,
    fetchCategories: store.fetchCategories,
    clearError: store.clearCategoriesError
  }
}

export const useQuizSession = () => {
  const store = useQuizStore()
  return {
    session: store.currentSession,
    currentQuestionIndex: store.currentQuestionIndex,
    selectedAnswer: store.selectedAnswer,
    timeLeft: store.timeLeft,
    isActive: store.isQuizActive,
    isSubmitting: store.isSubmitting,
    progress: store.getProgress(),
    
    startQuiz: store.startQuiz,
    selectAnswer: store.selectAnswer,
    nextQuestion: store.nextQuestion,
    previousQuestion: store.previousQuestion,
    submitQuiz: store.submitQuiz,
    resetQuiz: store.resetQuiz,
    updateTimer: store.updateTimer
  }
}

export const useQuizResults = () => {
  const store = useQuizStore()
  return {
    result: store.lastResult,
    showResults: store.showResults,
    showRegistrationPrompt: store.showRegistrationPrompt,
    showAwardModal: store.showAwardModal,
    showLoginForm: store.showLoginForm,

    setShowResults: store.setShowResults,
    setShowRegistrationPrompt: store.setShowRegistrationPrompt,
    setShowAwardModal: store.setShowAwardModal,
    setShowLoginForm: store.setShowLoginForm
  }
}

export const useQuizProgression = () => {
  const store = useQuizStore()
  return {
    quizPhase: store.quizPhase,
    firstPhaseAnswers: store.firstPhaseAnswers,
    wrongAnswers: store.wrongAnswers,
    totalQuestionsAnswered: store.totalQuestionsAnswered,
    showPhaseTransition: store.showPhaseTransition,

    proceedToSecondPhase: store.proceedToSecondPhase,
    skipSecondPhase: store.skipSecondPhase,
    startSecondPhase: store.startSecondPhase,
    completeQuiz: store.completeQuiz
  }
}

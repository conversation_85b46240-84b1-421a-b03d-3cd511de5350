// stores/quiz.store.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { 
  quizService, 
  type QuizCategory,
  type QuizSession,
  type QuizQuestion,
  type QuizAnswer,
  type QuizResult 
} from '@/lib/services/quiz.service'

interface QuizState {
  // Categories
  categories: QuizCategory[]
  isLoadingCategories: boolean
  categoriesError: string | null

  // Current Quiz Session
  currentSession: QuizSession | null
  currentQuestionIndex: number
  answers: QuizAnswer[]
  selectedAnswer: number | null
  timeLeft: number
  isQuizActive: boolean
  isSubmitting: boolean

  // Results
  lastResult: QuizResult | null
  showResults: boolean

  // UI State
  showRegistrationPrompt: boolean
  showAwardModal: boolean
}

interface QuizActions {
  // Category actions
  fetchCategories: () => Promise<void>
  clearCategoriesError: () => void

  // Quiz session actions
  startQuiz: (categoryId: string) => Promise<void>
  selectAnswer: (answerIndex: number) => void
  nextQuestion: () => void
  previousQuestion: () => void
  submitQuiz: (userId?: string) => Promise<void>
  resetQuiz: () => void

  // Timer actions
  updateTimer: (timeLeft: number) => void
  startTimer: () => void
  stopTimer: () => void

  // UI actions
  setShowRegistrationPrompt: (show: boolean) => void
  setShowAwardModal: (show: boolean) => void
  setShowResults: (show: boolean) => void

  // Utility actions
  clearSession: () => void
  getProgress: () => number
}

export const useQuizStore = create<QuizState & QuizActions>()(
  persist(
    (set, get) => ({
      // Initial state
      categories: [],
      isLoadingCategories: false,
      categoriesError: null,

      currentSession: null,
      currentQuestionIndex: 0,
      answers: [],
      selectedAnswer: null,
      timeLeft: 0,
      isQuizActive: false,
      isSubmitting: false,

      lastResult: null,
      showResults: false,

      showRegistrationPrompt: false,
      showAwardModal: false,

      // Actions
      fetchCategories: async () => {
        set({ isLoadingCategories: true, categoriesError: null })
        
        try {
          const categories = await quizService.getCategories()
          set({ 
            categories, 
            isLoadingCategories: false,
            categoriesError: null 
          })
        } catch (error) {
          set({
            categoriesError: error instanceof Error ? error.message : 'Failed to fetch categories',
            isLoadingCategories: false
          })
        }
      },

      clearCategoriesError: () => set({ categoriesError: null }),

      startQuiz: async (categoryId: string) => {
        try {
          const sessionId = quizService.generateSessionId()
          const session = await quizService.startQuiz(categoryId, sessionId)
          
          set({
            currentSession: session,
            currentQuestionIndex: 0,
            answers: [],
            selectedAnswer: null,
            timeLeft: session.timeLimit,
            isQuizActive: true,
            showResults: false,
            lastResult: null,
            showRegistrationPrompt: false,
            showAwardModal: false
          })
        } catch (error) {
          console.error('Failed to start quiz:', error)
          throw error
        }
      },

      selectAnswer: (answerIndex: number) => {
        set({ selectedAnswer: answerIndex })
      },

      nextQuestion: () => {
        const state = get()
        const { currentSession, currentQuestionIndex, selectedAnswer, answers } = state

        if (!currentSession || selectedAnswer === null) return

        // Record the answer with time spent
        const currentQuestion = currentSession.questions[currentQuestionIndex]
        const timeSpent = Math.max(1, currentSession.timeLimit - state.timeLeft)
        
        const newAnswer: QuizAnswer = {
          questionId: currentQuestion.id,
          selectedAnswer,
          timeSpent
        }

        const newAnswers = [...answers, newAnswer]
        const nextIndex = currentQuestionIndex + 1

        if (nextIndex >= currentSession.questions.length) {
          // Quiz completed, show results
          set({
            answers: newAnswers,
            selectedAnswer: null,
            isQuizActive: false
          })
          
          // Auto-submit quiz
          get().submitQuiz()
        } else {
          // Move to next question
          set({
            answers: newAnswers,
            currentQuestionIndex: nextIndex,
            selectedAnswer: null
          })
        }
      },

      previousQuestion: () => {
        const state = get()
        if (state.currentQuestionIndex > 0) {
          set({
            currentQuestionIndex: state.currentQuestionIndex - 1,
            selectedAnswer: null
          })
        }
      },

      submitQuiz: async (userId?: string) => {
        const state = get()
        const { currentSession, answers } = state

        if (!currentSession) return

        set({ isSubmitting: true })

        try {
          const totalTimeSpent = currentSession.timeLimit - state.timeLeft
          
          const result = await quizService.submitQuiz(
            currentSession.sessionId,
            currentSession.category.id,
            answers,
            totalTimeSpent,
            userId
          )

          set({
            lastResult: result,
            showResults: true,
            isSubmitting: false,
            isQuizActive: false
          })

          // Show registration prompt for anonymous users with good scores
          if (!userId && result.score >= result.passingScore) {
            set({ showRegistrationPrompt: true })
          }

          // Show award modal if awards were earned
          if (result.earnedAwards.length > 0) {
            set({ showAwardModal: true })
          }

        } catch (error) {
          console.error('Failed to submit quiz:', error)
          set({ isSubmitting: false })
          throw error
        }
      },

      resetQuiz: () => {
        quizService.clearSession()
        set({
          currentSession: null,
          currentQuestionIndex: 0,
          answers: [],
          selectedAnswer: null,
          timeLeft: 0,
          isQuizActive: false,
          isSubmitting: false,
          showResults: false,
          lastResult: null,
          showRegistrationPrompt: false,
          showAwardModal: false
        })
      },

      updateTimer: (timeLeft: number) => {
        set({ timeLeft })
        
        // Auto-submit when time runs out
        if (timeLeft <= 0) {
          const state = get()
          if (state.isQuizActive) {
            get().submitQuiz()
          }
        }
      },

      startTimer: () => {
        // Timer logic would be handled by the component
        set({ isQuizActive: true })
      },

      stopTimer: () => {
        set({ isQuizActive: false })
      },

      setShowRegistrationPrompt: (show: boolean) => {
        set({ showRegistrationPrompt: show })
      },

      setShowAwardModal: (show: boolean) => {
        set({ showAwardModal: show })
      },

      setShowResults: (show: boolean) => {
        set({ showResults: show })
      },

      clearSession: () => {
        quizService.clearSession()
        set({
          currentSession: null,
          currentQuestionIndex: 0,
          answers: [],
          selectedAnswer: null,
          timeLeft: 0,
          isQuizActive: false
        })
      },

      getProgress: () => {
        const state = get()
        if (!state.currentSession) return 0
        return ((state.currentQuestionIndex + 1) / state.currentSession.questions.length) * 100
      }
    }),
    {
      name: 'quiz-storage',
      partialize: (state) => ({
        lastResult: state.lastResult,
        categories: state.categories
      })
    }
  )
)

// Selector hooks for better performance
export const useQuizCategories = () => {
  const store = useQuizStore()
  return {
    categories: store.categories,
    isLoading: store.isLoadingCategories,
    error: store.categoriesError,
    fetchCategories: store.fetchCategories,
    clearError: store.clearCategoriesError
  }
}

export const useQuizSession = () => {
  const store = useQuizStore()
  return {
    session: store.currentSession,
    currentQuestionIndex: store.currentQuestionIndex,
    selectedAnswer: store.selectedAnswer,
    timeLeft: store.timeLeft,
    isActive: store.isQuizActive,
    isSubmitting: store.isSubmitting,
    progress: store.getProgress(),
    
    startQuiz: store.startQuiz,
    selectAnswer: store.selectAnswer,
    nextQuestion: store.nextQuestion,
    previousQuestion: store.previousQuestion,
    submitQuiz: store.submitQuiz,
    resetQuiz: store.resetQuiz,
    updateTimer: store.updateTimer
  }
}

export const useQuizResults = () => {
  const store = useQuizStore()
  return {
    result: store.lastResult,
    showResults: store.showResults,
    showRegistrationPrompt: store.showRegistrationPrompt,
    showAwardModal: store.showAwardModal,
    
    setShowResults: store.setShowResults,
    setShowRegistrationPrompt: store.setShowRegistrationPrompt,
    setShowAwardModal: store.setShowAwardModal
  }
}

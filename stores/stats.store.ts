import { create } from 'zustand'
import { errorService, type AppError } from '@/lib/error-service'

// Types
export interface PlatformStats {
  jobs: {
    total: number
    active: number
  }
  companies: {
    total: number
    verified: number
  }
  users: {
    total: number
  }
  clients: {
    total: number
    active: number
  }
  categories: {
    total: number
    top: Array<{
      name: string
      count: number
    }>
  }
  skills: {
    total: number
  }
  locations: {
    total: number
    top: Array<{
      name: string
      count: number
    }>
  }
}

interface StatsState {
  stats: PlatformStats | null
  loading: boolean
  error: AppError | null
  lastUpdated: Date | null
}

interface StatsActions {
  getStats: () => Promise<void>
  clearError: () => void
}

// API Service functions
const StatsAPI = {
  async getStats() {
    const response = await fetch('/api/v1/stats')
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to fetch stats')
    }
    return response.json()
  }
}

export const useStatsStore = create<StatsState & StatsActions>((set, get) => ({
  // State
  stats: null,
  loading: false,
  error: null,
  lastUpdated: null,

  // Actions
  getStats: async () => {
    // Check if we have recent data (less than 5 minutes old)
    const { stats, lastUpdated } = get()
    if (stats && lastUpdated && Date.now() - lastUpdated.getTime() < 5 * 60 * 1000) {
      return // Use cached data
    }

    set({ loading: true, error: null })
    
    try {
      const response = await StatsAPI.getStats()
      set({
        stats: response.data,
        loading: false,
        lastUpdated: new Date()
      })
    } catch (error) {
      const appError = errorService.logError(error as Error, 'stats_get')
      set({ 
        error: appError, 
        loading: false 
      })
      throw appError
    }
  },

  clearError: () => set({ error: null })
}))

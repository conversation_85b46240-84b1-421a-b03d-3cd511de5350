// atores/companies.store.ts
import { create } from 'zustand'
import { errorService, type AppError } from '@/lib/error-service'

// Types
export interface Company {
  _id: string
  name: string
  description: string
  logo?: string
  website?: string
  industry: string
  size: 'startup' | 'small' | 'medium' | 'large' | 'enterprise'
  location: {
    headquarters: {
      city: string
      state: string
      country: string
    }
    offices: Array<{
      city: string
      state: string
      country: string
      isPrimary: boolean
    }>
  }
  contact: {
    email: string
    phone?: string
    address?: string
  }
  social: {
    linkedin?: string
    twitter?: string
    facebook?: string
    instagram?: string
  }
  benefits: string[]
  culture: {
    values: string[]
    perks: string[]
    workEnvironment: string
  }
  stats: {
    employeeCount: number
    foundedYear: number
    jobsCount: number
    applicationsCount: number
  }
  verification: {
    isVerified: boolean
    verifiedAt?: Date
    documents: string[]
  }
  subscription: {
    plan: 'free' | 'basic' | 'premium' | 'enterprise'
    status: 'active' | 'inactive' | 'suspended'
    expiresAt?: Date
  }
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface CompanySearchQuery {
  q?: string
  industry?: string[]
  size?: string[]
  location?: string
  verified?: boolean
  page?: number
  limit?: number
  sortBy?: 'name' | 'size' | 'jobs' | 'founded'
  sortOrder?: 'asc' | 'desc'
}

export interface CompanyFilters {
  industries: string[]
  sizes: string[]
  locations: string[]
  verified: boolean
  hasJobs: boolean
}

interface CompaniesState {
  companies: Company[]
  currentCompany: Company | null
  searchQuery: CompanySearchQuery
  filters: Partial<CompanyFilters>
  followedCompanies: string[]

  // Job-related state
  companyJobs: Record<string, any[]> // Jobs by company ID
  jobStats: Record<string, any> // Job statistics by company ID

  // Loading states
  searchLoading: boolean
  companyLoading: boolean
  followLoading: boolean
  jobsLoading: boolean
  jobStatsLoading: boolean

  // Error states
  error: AppError | null
  searchError: AppError | null
  jobsError: AppError | null

  // Meta
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

interface CompaniesActions {
  searchCompanies: (query: CompanySearchQuery) => Promise<void>
  getCompanyById: (companyId: string) => Promise<void>
  getCompanyJobs: (companyId: string, page?: number, limit?: number) => Promise<void>
  getCompanyJobStats: (companyId: string) => Promise<void>
  followCompany: (companyId: string) => Promise<void>
  unfollowCompany: (companyId: string) => Promise<void>
  getFollowedCompanies: () => Promise<void>
  updateFilters: (filters: Partial<CompanyFilters>) => void
  updateSearchQuery: (query: Partial<CompanySearchQuery>) => void
  clearSearch: () => void
  clearError: () => void
  clearSearchError: () => void
  clearJobsError: () => void
}

// API Service functions
const CompaniesAPI = {
  async searchCompanies(query: CompanySearchQuery) {
    const params = new URLSearchParams()
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(key, v.toString()))
        } else {
          params.append(key, value.toString())
        }
      }
    })

    const response = await fetch(`/api/v1/companies/search?${params}`)
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Company search failed')
    }
    return response.json()
  },

  async getCompanyById(companyId: string) {
    const response = await fetch(`/api/v1/companies/${companyId}`)
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to fetch company')
    }
    return response.json()
  },

  async getCompanyJobs(companyId: string, page: number = 1, limit: number = 10) {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString()
    })

    const response = await fetch(`/api/v1/companies/${companyId}/jobs?${params}`)
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to fetch company jobs')
    }
    return response.json()
  },

  async getCompanyJobStats(companyId: string) {
    const response = await fetch(`/api/v1/companies/${companyId}/jobs/stats`)
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to fetch company job stats')
    }
    return response.json()
  },

  async followCompany(companyId: string, token: string) {
    const response = await fetch(`/api/v1/companies/${companyId}/follow`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to follow company')
    }
    return response.json()
  },

  async unfollowCompany(companyId: string, token: string) {
    const response = await fetch(`/api/v1/companies/${companyId}/follow`, {
      method: 'DELETE',
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to unfollow company')
    }
    return response.json()
  },

  async getFollowedCompanies(token: string) {
    const response = await fetch('/api/v1/companies/followed', {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to get followed companies')
    }
    return response.json()
  }
}

export const useCompaniesStore = create<CompaniesState & CompaniesActions>((set) => ({
  // State
  companies: [],
  currentCompany: null,
  searchQuery: { page: 1, limit: 20 },
  filters: {},
  followedCompanies: [],

  // Job-related state
  companyJobs: {},
  jobStats: {},

  // Loading states
  searchLoading: false,
  companyLoading: false,
  followLoading: false,
  jobsLoading: false,
  jobStatsLoading: false,

  // Error states
  error: null,
  searchError: null,
  jobsError: null,

  // Meta
  pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },

  // Actions
  searchCompanies: async (query) => {
    set({ searchLoading: true, searchError: null })
    
    try {
      const response = await CompaniesAPI.searchCompanies(query)
      
      set({
        companies: response.data.companies,
        pagination: response.data.pagination,
        searchQuery: query,
        searchLoading: false
      })
    } catch (error) {
      const appError = errorService.logError(error as Error, 'companies_search')
      set({ 
        searchError: appError, 
        searchLoading: false 
      })
      throw appError
    }
  },

  getCompanyById: async (companyId) => {
    set({ companyLoading: true, error: null })
    try {
      const response = await CompaniesAPI.getCompanyById(companyId)
      set({
        currentCompany: response.data,
        companyLoading: false
      })
    } catch (error) {
      const appError = errorService.logError(error as Error, 'companies_get_by_id')
      set({
        error: appError,
        companyLoading: false
      })
      throw appError
    }
  },

  getCompanyJobs: async (companyId, page = 1, limit = 10) => {
    set({ jobsLoading: true, jobsError: null })

    try {
      const response = await CompaniesAPI.getCompanyJobs(companyId, page, limit)
      const jobs = response.data?.jobs || response.jobs || []

      // Update company jobs in state
      set(state => ({
        companyJobs: {
          ...state.companyJobs,
          [companyId]: jobs
        },
        jobsLoading: false
      }))

      return jobs
    } catch (error) {
      const appError = errorService.logError(error as Error, 'companies_get_jobs')
      set({
        jobsError: appError,
        jobsLoading: false
      })
      throw appError
    }
  },

  getCompanyJobStats: async (companyId) => {
    set({ jobStatsLoading: true, jobsError: null })

    try {
      const response = await CompaniesAPI.getCompanyJobStats(companyId)
      const stats = response.data || response

      // Update job stats in state
      set(state => ({
        jobStats: {
          ...state.jobStats,
          [companyId]: stats
        },
        jobStatsLoading: false
      }))

      return stats
    } catch (error) {
      const appError = errorService.logError(error as Error, 'companies_get_job_stats')
      set({
        jobsError: appError,
        jobStatsLoading: false
      })
      throw appError
    }
  },

  followCompany: async (companyId) => {
    const token = localStorage.getItem('auth-token')
    if (!token) {
      throw new Error('Authentication required')
    }

    set({ followLoading: true, error: null })
    try {
      await CompaniesAPI.followCompany(companyId, token)
      
      set(state => ({
        followedCompanies: [...state.followedCompanies, companyId],
        followLoading: false
      }))
    } catch (error) {
      const appError = errorService.logError(error as Error, 'companies_follow')
      set({
        error: appError,
        followLoading: false
      })
      throw appError
    }
  },

  unfollowCompany: async (companyId) => {
    const token = localStorage.getItem('auth-token')
    if (!token) {
      throw new Error('Authentication required')
    }

    set({ followLoading: true, error: null })
    try {
      await CompaniesAPI.unfollowCompany(companyId, token)
      
      set(state => ({
        followedCompanies: state.followedCompanies.filter(id => id !== companyId),
        followLoading: false
      }))
    } catch (error) {
      const appError = errorService.logError(error as Error, 'companies_unfollow')
      set({
        error: appError,
        followLoading: false
      })
      throw appError
    }
  },

  getFollowedCompanies: async () => {
    const token = localStorage.getItem('auth-token')
    if (!token) {
      return // Skip if not authenticated
    }

    try {
      const followedCompanies = await CompaniesAPI.getFollowedCompanies(token)
      set({ followedCompanies: followedCompanies.map((company: Company) => company._id) })
    } catch (error) {
      const appError = errorService.logError(error as Error, 'companies_get_followed')
      set({ error: appError })
    }
  },

  updateFilters: (filters) => {
    set(state => ({
      filters: { ...state.filters, ...filters }
    }))
  },

  updateSearchQuery: (query) => {
    set(state => ({
      searchQuery: { ...state.searchQuery, ...query }
    }))
  },

  clearSearch: () => {
    set({
      companies: [],
      searchQuery: { page: 1, limit: 20 },
      filters: {},
      pagination: { page: 1, limit: 20, total: 0, totalPages: 0 }
    })
  },

  clearError: () => set({ error: null }),
  clearSearchError: () => set({ searchError: null }),
  clearJobsError: () => set({ jobsError: null })
}))

// stores/home-jobs.store.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { 
  homeJobsFrontendService, 
  type LocationBasedJobQuery, 
  type HomeJobSearchResult,
  type JobInteraction 
} from '@/lib/services/home-jobs-frontend.service'
import { type JobFrontend } from '@/lib/services/jobs-frontend.service'

export interface LocationStats {
  local: number
  regional: number
  national: number
  continental: number
  international: number
}

export interface SearchState {
  isActive: boolean
  query: string
  locationQuery: string
  results: JobFrontend[]
  totalResults: number
  searchTime: number
  appliedFilters: string[]
  suggestions: string[]
  isLoadingSuggestions: boolean
}

export interface JobsByLocationLevel {
  local: JobFrontend[]
  regional: JobFrontend[]
  national: JobFrontend[]
  continental: JobFrontend[]
  international: JobFrontend[]
}

export interface UserJobInteractions {
  likedJobs: Set<string>
  bookmarkedJobs: Set<string>
  viewedJobs: Set<string>
  appliedJobs: Set<string>
}

interface HomeJobsState {
  // Job data
  jobsByLocation: JobsByLocationLevel
  featuredJobs: JobFrontend[]
  urgentJobs: JobFrontend[]
  recentJobs: JobFrontend[]
  recommendations: JobFrontend[]
  
  // Location and stats
  locationStats: LocationStats
  currentLocationLevel: 'local' | 'regional' | 'national' | 'continental' | 'international'
  
  // Search state
  searchState: SearchState
  
  // User interactions
  userInteractions: UserJobInteractions
  
  // Loading states
  isLoadingLocationJobs: boolean
  isLoadingFeatured: boolean
  isLoadingSearch: boolean
  isLoadingStats: boolean
  isLoadingRecommendations: boolean
  
  // Error states
  error: string | null
  searchError: string | null
  
  // Pagination
  pagination: {
    [key in keyof JobsByLocationLevel]: {
      page: number
      hasMore: boolean
      total: number
    }
  }
}

interface HomeJobsActions {
  // Location-based job fetching
  fetchJobsByLocationLevel: (
    level: 'local' | 'regional' | 'national' | 'continental' | 'international',
    userLocation: LocationBasedJobQuery['userLocation'],
    options?: { page?: number; limit?: number; refresh?: boolean }
  ) => Promise<void>
  
  fetchAllLocationLevels: (
    userLocation: LocationBasedJobQuery['userLocation']
  ) => Promise<void>
  
  // Featured and special jobs
  fetchFeaturedJobs: (userLocation?: LocationBasedJobQuery['userLocation']) => Promise<void>
  fetchUrgentJobs: (userLocation?: LocationBasedJobQuery['userLocation']) => Promise<void>
  fetchRecentJobs: (userLocation?: LocationBasedJobQuery['userLocation']) => Promise<void>
  
  // Search functionality
  performAdvancedSearch: (
    searchTerm: string,
    locationTerm: string,
    userLocation?: LocationBasedJobQuery['userLocation'],
    filters?: Partial<LocationBasedJobQuery>
  ) => Promise<void>
  
  clearSearch: () => void
  
  // Location stats
  fetchLocationStats: (userLocation: LocationBasedJobQuery['userLocation']) => Promise<void>
  
  // User interactions
  toggleLikeJob: (jobId: string) => Promise<void>
  toggleBookmarkJob: (jobId: string) => Promise<void>
  trackJobView: (jobId: string) => Promise<void>
  trackJobShare: (jobId: string, platform?: string) => Promise<void>
  
  // Recommendations
  fetchRecommendations: (
    userLocation: LocationBasedJobQuery['userLocation'],
    userPreferences: {
      jobTypes?: string[]
      experienceLevels?: string[]
      skills?: string[]
      salaryRange?: { min?: number; max?: number }
      workModels?: string[]
    }
  ) => Promise<void>
  
  // State management
  setCurrentLocationLevel: (level: 'local' | 'regional' | 'national' | 'continental' | 'international') => void
  clearError: () => void
  clearSearchError: () => void
  reset: () => void
}

const initialState: HomeJobsState = {
  jobsByLocation: {
    local: [],
    regional: [],
    national: [],
    continental: [],
    international: []
  },
  featuredJobs: [],
  urgentJobs: [],
  recentJobs: [],
  recommendations: [],
  locationStats: {
    local: 0,
    regional: 0,
    national: 0,
    continental: 0,
    international: 0
  },
  currentLocationLevel: 'local',
  searchState: {
    isActive: false,
    query: '',
    locationQuery: '',
    results: [],
    totalResults: 0,
    searchTime: 0,
    appliedFilters: [],
    suggestions: [],
    isLoadingSuggestions: false
  },
  userInteractions: {
    likedJobs: new Set(),
    bookmarkedJobs: new Set(),
    viewedJobs: new Set(),
    appliedJobs: new Set()
  },
  isLoadingLocationJobs: false,
  isLoadingFeatured: false,
  isLoadingSearch: false,
  isLoadingStats: false,
  isLoadingRecommendations: false,
  error: null,
  searchError: null,
  pagination: {
    local: { page: 1, hasMore: true, total: 0 },
    regional: { page: 1, hasMore: true, total: 0 },
    national: { page: 1, hasMore: true, total: 0 },
    continental: { page: 1, hasMore: true, total: 0 },
    international: { page: 1, hasMore: true, total: 0 }
  }
}

export const useHomeJobsStore = create<HomeJobsState & HomeJobsActions>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Location-based job fetching
      fetchJobsByLocationLevel: async (level, userLocation, options = {}) => {
        const { page = 1, limit = 6, refresh = false } = options
        
        try {
          set({ isLoadingLocationJobs: true, error: null })
          
          const result = await homeJobsFrontendService.getJobsByLocationLevel(
            level,
            userLocation,
            { page, limit }
          )
          
          set(state => {
            const currentJobs = refresh ? [] : state.jobsByLocation[level]
            const newJobs = page === 1 ? result.jobs : [...currentJobs, ...result.jobs]
            
            return {
              jobsByLocation: {
                ...state.jobsByLocation,
                [level]: newJobs
              },
              pagination: {
                ...state.pagination,
                [level]: {
                  page: result.pagination.page,
                  hasMore: result.pagination.page < result.pagination.totalPages,
                  total: result.pagination.total
                }
              },
              isLoadingLocationJobs: false
            }
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch jobs',
            isLoadingLocationJobs: false 
          })
        }
      },

      fetchAllLocationLevels: async (userLocation) => {
        const levels: Array<'local' | 'regional' | 'national' | 'continental' | 'international'> = 
          ['local', 'regional', 'national', 'continental', 'international']
        
        await Promise.all(
          levels.map(level => 
            get().fetchJobsByLocationLevel(level, userLocation, { limit: 6, refresh: true })
          )
        )
      },

      // Featured and special jobs
      fetchFeaturedJobs: async (userLocation) => {
        try {
          set({ isLoadingFeatured: true, error: null })
          const jobs = await homeJobsFrontendService.getFeaturedJobs(userLocation, 6)
          set({ featuredJobs: jobs, isLoadingFeatured: false })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch featured jobs',
            isLoadingFeatured: false 
          })
        }
      },

      fetchUrgentJobs: async (userLocation) => {
        try {
          const jobs = await homeJobsFrontendService.getUrgentJobs(userLocation, 6)
          set({ urgentJobs: jobs })
        } catch (error) {
          console.error('Failed to fetch urgent jobs:', error)
        }
      },

      fetchRecentJobs: async (userLocation) => {
        try {
          const jobs = await homeJobsFrontendService.getRecentJobs(userLocation, 12)
          set({ recentJobs: jobs })
        } catch (error) {
          console.error('Failed to fetch recent jobs:', error)
        }
      },

      // Search functionality
      performAdvancedSearch: async (searchTerm, locationTerm, userLocation, filters) => {
        try {
          set({ isLoadingSearch: true, searchError: null })
          
          const startTime = Date.now()
          const result = await homeJobsFrontendService.advancedSearch(
            searchTerm,
            locationTerm,
            userLocation,
            filters
          )
          const searchTime = Date.now() - startTime
          
          set({
            searchState: {
              isActive: true,
              query: searchTerm,
              locationQuery: locationTerm,
              results: result.jobs,
              totalResults: result.pagination.total,
              searchTime,
              appliedFilters: result.searchMeta.appliedFilters
            },
            isLoadingSearch: false
          })
        } catch (error) {
          set({ 
            searchError: error instanceof Error ? error.message : 'Search failed',
            isLoadingSearch: false 
          })
        }
      },

      clearSearch: () => {
        set({
          searchState: {
            isActive: false,
            query: '',
            locationQuery: '',
            results: [],
            totalResults: 0,
            searchTime: 0,
            appliedFilters: [],
            suggestions: [],
            isLoadingSuggestions: false
          }
        })
      },

      // Search suggestions
      fetchSearchSuggestions: async (query: string) => {
        if (!query.trim() || query.length < 2) {
          set(state => ({
            searchState: {
              ...state.searchState,
              suggestions: [],
              isLoadingSuggestions: false
            }
          }))
          return
        }

        try {
          set(state => ({
            searchState: {
              ...state.searchState,
              isLoadingSuggestions: true
            }
          }))

          // Simple suggestions based on common job titles and skills
          const commonSuggestions = [
            'Software Engineer', 'Frontend Developer', 'Backend Developer', 'Full Stack Developer',
            'Data Scientist', 'Product Manager', 'UX Designer', 'UI Designer', 'DevOps Engineer',
            'Marketing Manager', 'Sales Representative', 'Business Analyst', 'Project Manager',
            'React', 'JavaScript', 'Python', 'Java', 'Node.js', 'TypeScript', 'AWS', 'Docker'
          ]

          const filteredSuggestions = commonSuggestions
            .filter(suggestion =>
              suggestion.toLowerCase().includes(query.toLowerCase())
            )
            .slice(0, 5)

          set(state => ({
            searchState: {
              ...state.searchState,
              suggestions: filteredSuggestions,
              isLoadingSuggestions: false
            }
          }))
        } catch (error) {
          set(state => ({
            searchState: {
              ...state.searchState,
              suggestions: [],
              isLoadingSuggestions: false
            }
          }))
        }
      },

      // Location stats
      fetchLocationStats: async (userLocation) => {
        try {
          set({ isLoadingStats: true })
          const stats = await homeJobsFrontendService.getLocationStats(userLocation)
          set({ locationStats: stats, isLoadingStats: false })
        } catch (error) {
          console.error('Failed to fetch location stats:', error)
          set({ isLoadingStats: false })
        }
      },

      // User interactions
      toggleLikeJob: async (jobId) => {
        const { userInteractions } = get()
        const newLikedJobs = new Set(userInteractions.likedJobs)
        
        if (newLikedJobs.has(jobId)) {
          newLikedJobs.delete(jobId)
        } else {
          newLikedJobs.add(jobId)
        }
        
        set({
          userInteractions: {
            ...userInteractions,
            likedJobs: newLikedJobs
          }
        })
        
        // Track interaction
        await homeJobsFrontendService.trackJobInteraction({
          jobId,
          type: 'like',
          timestamp: new Date()
        })
      },

      toggleBookmarkJob: async (jobId) => {
        const { userInteractions } = get()
        const newBookmarkedJobs = new Set(userInteractions.bookmarkedJobs)
        
        if (newBookmarkedJobs.has(jobId)) {
          newBookmarkedJobs.delete(jobId)
        } else {
          newBookmarkedJobs.add(jobId)
        }
        
        set({
          userInteractions: {
            ...userInteractions,
            bookmarkedJobs: newBookmarkedJobs
          }
        })
        
        // Track interaction
        await homeJobsFrontendService.trackJobInteraction({
          jobId,
          type: 'bookmark',
          timestamp: new Date()
        })
      },

      trackJobView: async (jobId) => {
        const { userInteractions } = get()
        const newViewedJobs = new Set(userInteractions.viewedJobs)
        newViewedJobs.add(jobId)
        
        set({
          userInteractions: {
            ...userInteractions,
            viewedJobs: newViewedJobs
          }
        })
        
        await homeJobsFrontendService.trackJobInteraction({
          jobId,
          type: 'view',
          timestamp: new Date()
        })
      },

      trackJobShare: async (jobId, platform) => {
        await homeJobsFrontendService.trackJobInteraction({
          jobId,
          type: 'share',
          timestamp: new Date(),
          metadata: { platform }
        })
      },

      // Recommendations
      fetchRecommendations: async (userLocation, userPreferences) => {
        try {
          set({ isLoadingRecommendations: true })
          const jobs = await homeJobsFrontendService.getJobRecommendations(
            userLocation,
            userPreferences,
            10
          )
          set({ recommendations: jobs, isLoadingRecommendations: false })
        } catch (error) {
          console.error('Failed to fetch recommendations:', error)
          set({ isLoadingRecommendations: false })
        }
      },

      // State management
      setCurrentLocationLevel: (level) => {
        set({ currentLocationLevel: level })
      },

      clearError: () => {
        set({ error: null })
      },

      clearSearchError: () => {
        set({ searchError: null })
      },

      reset: () => {
        set(initialState)
      }
    }),
    {
      name: 'home-jobs-store',
      partialize: (state) => ({
        userInteractions: {
          likedJobs: Array.from(state.userInteractions.likedJobs),
          bookmarkedJobs: Array.from(state.userInteractions.bookmarkedJobs),
          viewedJobs: Array.from(state.userInteractions.viewedJobs),
          appliedJobs: Array.from(state.userInteractions.appliedJobs)
        },
        currentLocationLevel: state.currentLocationLevel
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          // Convert arrays back to Sets
          state.userInteractions = {
            likedJobs: new Set(state.userInteractions.likedJobs as unknown as string[]),
            bookmarkedJobs: new Set(state.userInteractions.bookmarkedJobs as unknown as string[]),
            viewedJobs: new Set(state.userInteractions.viewedJobs as unknown as string[]),
            appliedJobs: new Set(state.userInteractions.appliedJobs as unknown as string[])
          }
        }
      }
    }
  )
)

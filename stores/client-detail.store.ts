// stores/client-detail.store.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { 
  ClientDetailProfile, 
  UpdateClientDetailRequest,
  ClientDetailAnalytics
} from '@/types/client-detail.types'
import { clientDetailFrontendService } from '@/lib/services/client-detail-frontend.service'

interface ClientDetailState {
  // Data
  currentClient: ClientDetailProfile | null
  analytics: ClientDetailAnalytics | null
  
  // UI State
  isEditing: boolean
  editingSection: string | null
  showAnalytics: boolean
  
  // Loading States
  isLoading: boolean
  isUpdating: boolean
  isLoadingAnalytics: boolean
  isSaving: boolean
  
  // Error States
  error: string | null
  updateError: string | null
  analyticsError: string | null
  
  // Cache
  lastFetchTime: number
  cacheExpiry: number
}

interface ClientDetailActions {
  // Data Actions
  fetchClientDetail: (clientId: string) => Promise<void>
  updateClientDetail: (clientId: string, updateData: UpdateClientDetailRequest) => Promise<void>
  fetchAnalytics: (clientId: string) => Promise<void>
  
  // Profile Management
  updateBasicInfo: (clientId: string, basicInfo: { headline?: string; summary?: string }) => Promise<void>
  updateJobPreferences: (clientId: string, jobPreferences: Partial<ClientDetailProfile['jobPreferences']>) => Promise<void>
  updatePrivacySettings: (clientId: string, privacy: Partial<ClientDetailProfile['privacy']>) => Promise<void>
  updateExperience: (clientId: string, experience: Partial<ClientDetailProfile['experience']>) => Promise<void>
  
  // Experience & Education
  addWorkExperience: (clientId: string, workData: ClientDetailProfile['workHistory'][0]) => Promise<void>
  addEducation: (clientId: string, educationData: ClientDetailProfile['education'][0]) => Promise<void>
  addSkill: (clientId: string, skillData: ClientDetailProfile['skills'][0]) => Promise<void>
  
  // UI Actions
  setEditing: (isEditing: boolean, section?: string) => void
  toggleAnalytics: () => void
  clearErrors: () => void
  
  // Utility Actions
  getProfileSuggestions: () => string[]
  getProfileStrength: () => number
  clearCache: () => void
  reset: () => void
}

const initialState: ClientDetailState = {
  currentClient: null,
  analytics: null,
  
  isEditing: false,
  editingSection: null,
  showAnalytics: false,
  
  isLoading: false,
  isUpdating: false,
  isLoadingAnalytics: false,
  isSaving: false,
  
  error: null,
  updateError: null,
  analyticsError: null,
  
  lastFetchTime: 0,
  cacheExpiry: 5 * 60 * 1000, // 5 minutes
}

export const useClientDetailStore = create<ClientDetailState & ClientDetailActions>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Data Actions
      fetchClientDetail: async (clientId: string) => {
        const state = get()
        
        // Check cache
        const isCacheValid = state.currentClient && 
          state.currentClient.id === clientId &&
          Date.now() - state.lastFetchTime < state.cacheExpiry

        if (isCacheValid) return

        set({ isLoading: true, error: null })

        try {
          const client = await clientDetailFrontendService.getClientDetail(clientId)
          set({
            currentClient: client,
            isLoading: false,
            lastFetchTime: Date.now()
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch client details',
            isLoading: false
          })
        }
      },

      updateClientDetail: async (clientId: string, updateData: UpdateClientDetailRequest) => {
        set({ isUpdating: true, updateError: null })

        try {
          const updatedClient = await clientDetailFrontendService.updateClientDetail(clientId, updateData)
          set({
            currentClient: updatedClient,
            isUpdating: false,
            isEditing: false,
            editingSection: null,
            lastFetchTime: Date.now()
          })
        } catch (error) {
          set({
            updateError: error instanceof Error ? error.message : 'Failed to update client',
            isUpdating: false
          })
        }
      },

      fetchAnalytics: async (clientId: string) => {
        set({ isLoadingAnalytics: true, analyticsError: null })

        try {
          const analytics = await clientDetailFrontendService.getClientAnalytics(clientId)
          set({
            analytics,
            isLoadingAnalytics: false
          })
        } catch (error) {
          set({
            analyticsError: error instanceof Error ? error.message : 'Failed to fetch analytics',
            isLoadingAnalytics: false
          })
        }
      },

      // Profile Management
      updateBasicInfo: async (clientId: string, basicInfo: { headline?: string; summary?: string }) => {
        await get().updateClientDetail(clientId, basicInfo)
      },

      updateJobPreferences: async (clientId: string, jobPreferences: Partial<ClientDetailProfile['jobPreferences']>) => {
        await get().updateClientDetail(clientId, { jobPreferences })
      },

      updatePrivacySettings: async (clientId: string, privacy: Partial<ClientDetailProfile['privacy']>) => {
        await get().updateClientDetail(clientId, { privacy })
      },

      updateExperience: async (clientId: string, experience: Partial<ClientDetailProfile['experience']>) => {
        await get().updateClientDetail(clientId, { experience })
      },

      // Experience & Education
      addWorkExperience: async (clientId: string, workData: ClientDetailProfile['workHistory'][0]) => {
        set({ isSaving: true, updateError: null })

        try {
          const updatedClient = await clientDetailFrontendService.addWorkExperience(clientId, workData)
          set({
            currentClient: updatedClient,
            isSaving: false,
            lastFetchTime: Date.now()
          })
        } catch (error) {
          set({
            updateError: error instanceof Error ? error.message : 'Failed to add work experience',
            isSaving: false
          })
        }
      },

      addEducation: async (clientId: string, educationData: ClientDetailProfile['education'][0]) => {
        set({ isSaving: true, updateError: null })

        try {
          const updatedClient = await clientDetailFrontendService.addEducation(clientId, educationData)
          set({
            currentClient: updatedClient,
            isSaving: false,
            lastFetchTime: Date.now()
          })
        } catch (error) {
          set({
            updateError: error instanceof Error ? error.message : 'Failed to add education',
            isSaving: false
          })
        }
      },

      addSkill: async (clientId: string, skillData: ClientDetailProfile['skills'][0]) => {
        set({ isSaving: true, updateError: null })

        try {
          const updatedClient = await clientDetailFrontendService.addSkill(clientId, skillData)
          set({
            currentClient: updatedClient,
            isSaving: false,
            lastFetchTime: Date.now()
          })
        } catch (error) {
          set({
            updateError: error instanceof Error ? error.message : 'Failed to add skill',
            isSaving: false
          })
        }
      },

      // UI Actions
      setEditing: (isEditing: boolean, section?: string) => {
        set({ 
          isEditing, 
          editingSection: isEditing ? section || null : null 
        })
      },

      toggleAnalytics: () => {
        const state = get()
        set({ showAnalytics: !state.showAnalytics })
      },

      clearErrors: () => {
        set({ 
          error: null, 
          updateError: null, 
          analyticsError: null 
        })
      },

      // Utility Actions
      getProfileSuggestions: () => {
        const state = get()
        if (!state.currentClient) return []
        return clientDetailFrontendService.getProfileSuggestions(state.currentClient)
      },

      getProfileStrength: () => {
        const state = get()
        if (!state.currentClient) return 0
        return clientDetailFrontendService.calculateProfileStrength(state.currentClient)
      },

      clearCache: () => {
        clientDetailFrontendService.clearCache()
        set({ lastFetchTime: 0 })
      },

      reset: () => {
        set(initialState)
        clientDetailFrontendService.clearCache()
      }
    }),
    {
      name: 'client-detail-store',
      partialize: (state) => ({
        // Only persist essential data, not loading states
        currentClient: state.currentClient,
        lastFetchTime: state.lastFetchTime,
        showAnalytics: state.showAnalytics
      })
    }
  )
)

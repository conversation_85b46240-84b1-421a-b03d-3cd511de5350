import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { 
  HireRequest, 
  HireFrontend, 
  HireFormData, 
  HireSearchQuery, 
  HireSearchResult, 
  HireStats, 
  HireStatus, 
  HireType,
  CreateHireRequest,
  UpdateHireRequest,
  HireValidationErrors
} from '@/types/hire.types'

interface HireState {
  // Data
  hireRequests: HireFrontend[]
  currentHireRequest: HireFrontend | null
  searchResult: HireSearchResult | null
  stats: HireStats | null
  
  // Search & Filters
  searchQuery: HireSearchQuery
  activeFilters: Partial<HireSearchQuery>
  
  // UI State
  selectedHireRequestId: string | null
  showHireModal: boolean
  showFilters: boolean
  currentStep: number
  
  // Form State
  formData: HireFormData
  validationErrors: HireValidationErrors
  
  // Loading States
  isLoading: boolean
  isSearching: boolean
  isCreating: boolean
  isUpdating: boolean
  isLoadingStats: boolean
  
  // Error States
  error: string | null
  searchError: string | null
  createError: string | null
  updateError: string | null
  
  // Cache
  lastSearchTime: number
  cacheExpiry: number
}

interface HireActions {
  // Search & Filter Actions
  searchHireRequests: (query?: Partial<HireSearchQuery>) => Promise<void>
  updateFilters: (filters: Partial<HireSearchQuery>) => void
  clearFilters: () => void
  toggleFilters: () => void
  
  // CRUD Actions
  createHireRequest: (data: CreateHireRequest) => Promise<HireFrontend | null>
  updateHireRequest: (id: string, data: UpdateHireRequest) => Promise<HireFrontend | null>
  getHireRequestById: (id: string) => Promise<HireFrontend | null>
  deleteHireRequest: (id: string) => Promise<boolean>
  
  // Status Management
  updateHireStatus: (id: string, status: HireStatus, notes?: string) => Promise<boolean>
  approveHireRequest: (id: string, notes?: string) => Promise<boolean>
  rejectHireRequest: (id: string, reason: string) => Promise<boolean>
  
  // UI Actions
  selectHireRequest: (id: string | null) => void
  openHireModal: (talentId?: string) => void
  closeHireModal: () => void
  setCurrentStep: (step: number) => void
  nextStep: () => void
  previousStep: () => void
  
  // Form Actions
  updateFormData: (data: Partial<HireFormData>) => void
  validateForm: () => boolean
  resetForm: () => void
  
  // Stats Actions
  loadStats: (companyId?: string) => Promise<void>
  
  // Utility Actions
  clearErrors: () => void
  clearCache: () => void
  refreshData: () => Promise<void>
}

type HireStore = HireState & HireActions

// Initial form data
const initialFormData: HireFormData = {
  hireType: 'full_time',
  priority: 'medium',
  projectTitle: '',
  projectDescription: '',
  requirements: [],
  deliverables: [],
  startDate: '',
  endDate: '',
  milestones: [],
  contractType: 'employment',
  compensationAmount: 0,
  compensationCurrency: 'USD',
  compensationFrequency: 'monthly',
  benefits: [],
  workArrangementType: 'remote',
  workLocation: '',
  hoursPerWeek: 40,
  flexibleHours: false,
  probationPeriod: 90,
  noticePeriod: 30,
  nonCompete: false,
  nonDisclosure: true,
  message: '',
  responseDeadline: '',
  termsAccepted: false,
  privacyAccepted: false
}

// Initial search query
const initialSearchQuery: HireSearchQuery = {
  page: 1,
  limit: 20,
  sortBy: 'createdAt',
  sortOrder: 'desc'
}

export const useHireStore = create<HireStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial State
        hireRequests: [],
        currentHireRequest: null,
        searchResult: null,
        stats: null,
        
        searchQuery: initialSearchQuery,
        activeFilters: {},
        
        selectedHireRequestId: null,
        showHireModal: false,
        showFilters: false,
        currentStep: 1,
        
        formData: initialFormData,
        validationErrors: {},
        
        isLoading: false,
        isSearching: false,
        isCreating: false,
        isUpdating: false,
        isLoadingStats: false,
        
        error: null,
        searchError: null,
        createError: null,
        updateError: null,
        
        lastSearchTime: 0,
        cacheExpiry: 5 * 60 * 1000, // 5 minutes
        
        // Search & Filter Actions
        searchHireRequests: async (queryUpdate) => {
          const state = get()
          const newQuery = { ...state.searchQuery, ...queryUpdate }
          
          set({ isSearching: true, searchError: null, searchQuery: newQuery })
          
          try {
            // This will be implemented when we create the frontend service
            const response = await fetch(`/api/v1/hire-requests?${new URLSearchParams(newQuery as any)}`)
            
            if (!response.ok) {
              throw new Error(`Search failed: ${response.statusText}`)
            }
            
            const data = await response.json()
            const result: HireSearchResult = data.success ? data.data : data
            
            set({
              searchResult: result,
              hireRequests: result.hireRequests || [],
              isSearching: false,
              lastSearchTime: Date.now()
            })
          } catch (error) {
            set({
              searchError: error instanceof Error ? error.message : 'Search failed',
              isSearching: false
            })
          }
        },
        
        updateFilters: (filters) => {
          set({ activeFilters: { ...get().activeFilters, ...filters } })
        },
        
        clearFilters: () => {
          set({ activeFilters: {}, searchQuery: initialSearchQuery })
        },
        
        toggleFilters: () => {
          set({ showFilters: !get().showFilters })
        },
        
        // CRUD Actions
        createHireRequest: async (data) => {
          set({ isCreating: true, createError: null })
          
          try {
            const response = await fetch('/api/v1/hire-requests', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data)
            })
            
            if (!response.ok) {
              const errorData = await response.json()
              throw new Error(errorData.error || 'Failed to create hire request')
            }
            
            const result = await response.json()
            const hireRequest: HireFrontend = result.success ? result.data : result
            
            set({
              hireRequests: [hireRequest, ...get().hireRequests],
              currentHireRequest: hireRequest,
              isCreating: false,
              showHireModal: false,
              formData: initialFormData,
              currentStep: 1
            })
            
            return hireRequest
          } catch (error) {
            set({
              createError: error instanceof Error ? error.message : 'Failed to create hire request',
              isCreating: false
            })
            return null
          }
        },
        
        updateHireRequest: async (id, data) => {
          set({ isUpdating: true, updateError: null })
          
          try {
            const response = await fetch(`/api/v1/hire-requests/${id}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data)
            })
            
            if (!response.ok) {
              const errorData = await response.json()
              throw new Error(errorData.error || 'Failed to update hire request')
            }
            
            const result = await response.json()
            const updatedHireRequest: HireFrontend = result.success ? result.data : result
            
            set({
              hireRequests: get().hireRequests.map(hr => 
                hr.id === id ? updatedHireRequest : hr
              ),
              currentHireRequest: get().currentHireRequest?.id === id ? updatedHireRequest : get().currentHireRequest,
              isUpdating: false
            })
            
            return updatedHireRequest
          } catch (error) {
            set({
              updateError: error instanceof Error ? error.message : 'Failed to update hire request',
              isUpdating: false
            })
            return null
          }
        },
        
        getHireRequestById: async (id) => {
          set({ isLoading: true, error: null })
          
          try {
            const response = await fetch(`/api/v1/hire-requests/${id}`)
            
            if (!response.ok) {
              throw new Error(`Failed to fetch hire request: ${response.statusText}`)
            }
            
            const result = await response.json()
            const hireRequest: HireFrontend = result.success ? result.data : result
            
            set({
              currentHireRequest: hireRequest,
              isLoading: false
            })
            
            return hireRequest
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to fetch hire request',
              isLoading: false
            })
            return null
          }
        },
        
        deleteHireRequest: async (id) => {
          set({ isUpdating: true, updateError: null })
          
          try {
            const response = await fetch(`/api/v1/hire-requests/${id}`, {
              method: 'DELETE'
            })
            
            if (!response.ok) {
              throw new Error(`Failed to delete hire request: ${response.statusText}`)
            }
            
            set({
              hireRequests: get().hireRequests.filter(hr => hr.id !== id),
              currentHireRequest: get().currentHireRequest?.id === id ? null : get().currentHireRequest,
              selectedHireRequestId: get().selectedHireRequestId === id ? null : get().selectedHireRequestId,
              isUpdating: false
            })
            
            return true
          } catch (error) {
            set({
              updateError: error instanceof Error ? error.message : 'Failed to delete hire request',
              isUpdating: false
            })
            return false
          }
        },
        
        // Status Management
        updateHireStatus: async (id, status, notes) => {
          return await get().updateHireRequest(id, { status, companyNotes: notes })
            .then(result => !!result)
        },
        
        approveHireRequest: async (id, notes) => {
          return await get().updateHireStatus(id, 'hired', notes)
        },
        
        rejectHireRequest: async (id, reason) => {
          return await get().updateHireStatus(id, 'rejected', reason)
        },
        
        // UI Actions
        selectHireRequest: (id) => {
          set({ selectedHireRequestId: id })
        },
        
        openHireModal: (talentId) => {
          set({ 
            showHireModal: true, 
            currentStep: 1,
            formData: talentId ? { ...initialFormData } : initialFormData
          })
        },
        
        closeHireModal: () => {
          set({ 
            showHireModal: false, 
            currentStep: 1,
            formData: initialFormData,
            validationErrors: {},
            createError: null
          })
        },
        
        setCurrentStep: (step) => {
          set({ currentStep: step })
        },
        
        nextStep: () => {
          const currentStep = get().currentStep
          if (currentStep < 4) {
            set({ currentStep: currentStep + 1 })
          }
        },
        
        previousStep: () => {
          const currentStep = get().currentStep
          if (currentStep > 1) {
            set({ currentStep: currentStep - 1 })
          }
        },
        
        // Form Actions
        updateFormData: (data) => {
          set({ 
            formData: { ...get().formData, ...data },
            validationErrors: {} // Clear validation errors when form data changes
          })
        },
        
        validateForm: () => {
          const { formData } = get()
          const errors: HireValidationErrors = {}
          
          // Step 1 validation
          if (!formData.hireType) {
            errors.hireType = 'Hire type is required'
          }
          
          // Step 2 validation
          if (!formData.projectTitle.trim()) {
            errors.projectTitle = 'Project title is required'
          }
          
          if (!formData.projectDescription.trim()) {
            errors.projectDescription = 'Project description is required'
          }
          
          if (formData.requirements.length === 0) {
            errors.requirements = 'At least one requirement is needed'
          }
          
          if (formData.deliverables.length === 0) {
            errors.deliverables = 'At least one deliverable is needed'
          }
          
          if (!formData.startDate) {
            errors.startDate = 'Start date is required'
          }
          
          // Step 3 validation
          if (formData.compensationAmount <= 0) {
            errors.compensationAmount = 'Compensation amount must be greater than 0'
          }
          
          if (!formData.compensationFrequency) {
            errors.compensationFrequency = 'Compensation frequency is required'
          }
          
          if (!formData.workArrangementType) {
            errors.workArrangementType = 'Work arrangement type is required'
          }
          
          // Step 4 validation
          if (formData.responseDeadline && new Date(formData.responseDeadline) <= new Date()) {
            errors.responseDeadline = 'Response deadline must be in the future'
          }
          
          if (!formData.termsAccepted) {
            errors.termsAccepted = 'You must accept the terms and conditions'
          }
          
          if (!formData.privacyAccepted) {
            errors.privacyAccepted = 'You must accept the privacy policy'
          }
          
          set({ validationErrors: errors })
          return Object.keys(errors).length === 0
        },
        
        resetForm: () => {
          set({ 
            formData: initialFormData,
            validationErrors: {},
            currentStep: 1
          })
        },
        
        // Stats Actions
        loadStats: async (companyId) => {
          set({ isLoadingStats: true, error: null })
          
          try {
            const url = companyId 
              ? `/api/v1/hire-requests/stats?companyId=${companyId}`
              : '/api/v1/hire-requests/stats'
              
            const response = await fetch(url)
            
            if (!response.ok) {
              throw new Error(`Failed to load stats: ${response.statusText}`)
            }
            
            const result = await response.json()
            const stats: HireStats = result.success ? result.data : result
            
            set({
              stats,
              isLoadingStats: false
            })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to load stats',
              isLoadingStats: false
            })
          }
        },
        
        // Utility Actions
        clearErrors: () => {
          set({
            error: null,
            searchError: null,
            createError: null,
            updateError: null
          })
        },
        
        clearCache: () => {
          set({
            hireRequests: [],
            searchResult: null,
            stats: null,
            lastSearchTime: 0
          })
        },
        
        refreshData: async () => {
          const { searchQuery } = get()
          await get().searchHireRequests(searchQuery)
        }
      }),
      {
        name: 'hire-storage',
        partialize: (state) => ({
          activeFilters: state.activeFilters,
          showFilters: state.showFilters,
          searchQuery: state.searchQuery
        })
      }
    )
  )
)

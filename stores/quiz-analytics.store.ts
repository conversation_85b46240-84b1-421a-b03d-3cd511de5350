import { create } from 'zustand'

// Analytics Types
export interface AnalyticsData {
  totalAttempts: number
  totalCategories: number
  totalQuestions: number
  totalAwards: number
  averageScore: number
  passRate: number
  averageTime: number
  topCategories: Array<{
    name: string
    icon: string
    attempts: number
    averageScore: number
  }>
  scoreDistribution: Array<{
    range: string
    count: number
    percentage: number
  }>
  timeDistribution: Array<{
    range: string
    count: number
    percentage: number
  }>
  dailyAttempts: Array<{
    date: string
    attempts: number
    averageScore: number
  }>
  questionPerformance: Array<{
    questionId: string
    question: string
    category: string
    correctRate: number
    attempts: number
  }>
}

// Quiz Analytics Store
interface QuizAnalyticsState {
  analytics: AnalyticsData | null
  isLoading: boolean
  error: string | null
  selectedTimeRange: string
  
  // Actions
  fetchAnalytics: (timeRange?: string) => Promise<void>
  setTimeRange: (timeRange: string) => void
  clearError: () => void
  reset: () => void
}

export const useQuizAnalyticsStore = create<QuizAnalyticsState>((set, get) => ({
  // Initial state
  analytics: null,
  isLoading: false,
  error: null,
  selectedTimeRange: '30d',
  
  // Actions
  fetchAnalytics: async (timeRange?: string) => {
    const currentTimeRange = timeRange || get().selectedTimeRange
    set({ isLoading: true, error: null })
    
    try {
      const response = await fetch(`/api/v1/quiz-admin/analytics?timeRange=${currentTimeRange}`)
      const result = await response.json()
      
      if (result.success) {
        set({ 
          analytics: result.data,
          isLoading: false,
          selectedTimeRange: currentTimeRange
        })
      } else {
        set({ 
          error: result.error || 'Failed to fetch analytics',
          isLoading: false 
        })
      }
    } catch (error) {
      set({ 
        error: 'Network error while fetching analytics',
        isLoading: false 
      })
    }
  },
  
  setTimeRange: (timeRange: string) => {
    set({ selectedTimeRange: timeRange })
    get().fetchAnalytics(timeRange)
  },
  
  clearError: () => {
    set({ error: null })
  },
  
  reset: () => {
    set({
      analytics: null,
      isLoading: false,
      error: null,
      selectedTimeRange: '30d'
    })
  }
}))

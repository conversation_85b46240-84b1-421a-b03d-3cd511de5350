import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { talentFrontendService } from '@/lib/services/talent-frontend.service'
import {
  TalentFrontend,
  TalentSearchQuery,
  TalentSearchResult
} from '@/lib/services/talent-frontend.service'

// Re-export types for backward compatibility
export type { TalentFrontend, TalentSearchQuery, TalentSearchResult }

// Store-specific filter types
export interface TalentSearchFilters {
  skills: string[]
  industries: string[]
  locations: string[]
  experienceLevels: string[]
  jobTypes: string[]
  workArrangements: string[]
  availability: string[]
  salaryRange: {
    min?: number
    max?: number
    currency?: string
  }
  remote?: boolean
  verified?: boolean
  featured?: boolean
  premium?: boolean
  minRating?: number
  profileCompleteness?: number
}

interface TalentState {
  // Data
  talent: TalentFrontend[]
  currentTalent: TalentFrontend | null
  searchResult: TalentSearchResult | null
  followedTalent: Set<string>
  savedSearches: Array<TalentSearchQuery & { name: string }>
  
  // Search & Filters
  searchQuery: TalentSearchQuery
  activeFilters: Partial<TalentSearchFilters>
  quickFilters: {
    available: boolean
    verified: boolean
    featured: boolean
    remote: boolean
    premium: boolean
  }
  
  // UI State
  viewMode: 'grid' | 'list'
  showFilters: boolean
  selectedTalentId: string | null
  
  // Loading States
  isLoading: boolean
  isSearching: boolean
  isLoadingMore: boolean
  isFollowing: boolean
  
  // Error States
  error: string | null
  searchError: string | null
  
  // Cache
  lastSearchTime: number
  cacheExpiry: number
}

interface TalentActions {
  // Search & Filter Actions
  searchTalent: (query: Partial<TalentSearchQuery>) => Promise<void>
  updateFilters: (filters: Partial<TalentSearchFilters>) => void
  clearFilters: () => void
  applyQuickFilter: (filter: keyof TalentState['quickFilters']) => void
  
  // Talent Actions
  getTalentById: (id: string) => Promise<TalentFrontend | null>
  followTalent: (talentId: string) => Promise<void>
  unfollowTalent: (talentId: string) => Promise<void>
  
  // UI Actions
  setViewMode: (mode: 'grid' | 'list') => void
  toggleFilters: () => void
  selectTalent: (talentId: string | null) => void
  
  // Pagination
  loadMore: () => Promise<void>
  goToPage: (page: number) => Promise<void>
  
  // Saved Searches
  saveCurrentSearch: (name: string) => void
  loadSavedSearch: (index: number) => void
  deleteSavedSearch: (index: number) => void
  
  // Error Handling
  clearError: () => void
  clearSearchError: () => void
}

// Initial search query
const initialSearchQuery: TalentSearchQuery = {
  query: '',
  page: 1,
  limit: 20,
  sortBy: 'relevance',
  sortOrder: 'desc'
}

type TalentStore = TalentState & TalentActions

export const useTalentFrontendStore = create<TalentStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial State
        talent: [],
        currentTalent: null,
        searchResult: null,
        followedTalent: new Set(),
        savedSearches: [],
        
        searchQuery: initialSearchQuery,
        activeFilters: {},
        quickFilters: {
          available: false,
          verified: false,
          featured: false,
          remote: false,
          premium: false
        },
        
        viewMode: 'list',
        showFilters: true,
        selectedTalentId: null,
        
        isLoading: false,
        isSearching: false,
        isLoadingMore: false,
        isFollowing: false,
        
        error: null,
        searchError: null,
        
        lastSearchTime: 0,
        cacheExpiry: 5 * 60 * 1000,

        // Actions
        searchTalent: async (queryUpdate) => {
          const state = get()
          const newQuery = { ...state.searchQuery, ...queryUpdate }
          
          set({ isSearching: true, searchError: null, searchQuery: newQuery })
          
          try {
            const result = await talentFrontendService.searchTalent(newQuery)

            set({
              searchResult: result,
              talent: result.talent,
              isSearching: false,
              lastSearchTime: Date.now()
            })
          } catch (error) {
            set({
              searchError: error instanceof Error ? error.message : 'Search failed',
              isSearching: false
            })
          }
        },

        updateFilters: (filters) => {
          const state = get()
          const newFilters = { ...state.activeFilters, ...filters }
          set({ activeFilters: newFilters })
          
          // Auto-search when filters change
          state.searchTalent({ page: 1 })
        },

        clearFilters: () => {
          set({ 
            activeFilters: {},
            quickFilters: {
              available: false,
              verified: false,
              featured: false,
              remote: false,
              premium: false
            }
          })
          
          // Reset search to show all results
          get().searchTalent({ page: 1 })
        },

        applyQuickFilter: (filter) => {
          const state = get()
          const newQuickFilters = {
            ...state.quickFilters,
            [filter]: !state.quickFilters[filter]
          }
          
          set({ quickFilters: newQuickFilters })
          
          // Convert quick filters to search filters
          const searchFilters: Partial<TalentSearchFilters> = {}
          
          if (newQuickFilters.available) {
            searchFilters.availability = ['available', 'immediately']
          }
          if (newQuickFilters.verified) {
            searchFilters.verified = true
          }
          if (newQuickFilters.featured) {
            searchFilters.featured = true
          }
          if (newQuickFilters.remote) {
            searchFilters.remote = true
          }
          if (newQuickFilters.premium) {
            searchFilters.premium = true
          }
          
          state.updateFilters(searchFilters)
        },

        getTalentById: async (id) => {
          set({ isLoading: true, error: null })
          
          try {
            const talent = await talentFrontendService.getTalentById(id)
            set({ currentTalent: talent, isLoading: false })
            return talent
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to fetch talent',
              isLoading: false
            })
            return null
          }
        },

        followTalent: async (talentId) => {
          set({ isFollowing: true, error: null })
          
          try {
            // TODO: Implement follow API call
            // await talentFrontendService.followTalent(talentId)
            
            const state = get()
            const newFollowed = new Set(state.followedTalent)
            newFollowed.add(talentId)
            
            set({ followedTalent: newFollowed, isFollowing: false })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to follow talent',
              isFollowing: false
            })
          }
        },

        unfollowTalent: async (talentId) => {
          set({ isFollowing: true, error: null })
          
          try {
            // TODO: Implement unfollow API call
            // await talentFrontendService.unfollowTalent(talentId)
            
            const state = get()
            const newFollowed = new Set(state.followedTalent)
            newFollowed.delete(talentId)
            
            set({ followedTalent: newFollowed, isFollowing: false })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to unfollow talent',
              isFollowing: false
            })
          }
        },

        setViewMode: (mode) => set({ viewMode: mode }),
        
        toggleFilters: () => {
          const state = get()
          set({ showFilters: !state.showFilters })
        },
        
        selectTalent: (talentId) => set({ selectedTalentId: talentId }),

        loadMore: async () => {
          const state = get()
          if (state.isLoadingMore || !state.searchResult) return
          
          const nextPage = state.searchQuery.page! + 1
          const totalPages = state.searchResult.pagination.totalPages
          
          if (nextPage > totalPages) return
          
          set({ isLoadingMore: true })
          
          try {
            const result = await talentFrontendService.searchTalent({
              ...state.searchQuery,
              page: nextPage
            })
            
            set({
              talent: [...state.talent, ...result.talent],
              searchQuery: { ...state.searchQuery, page: nextPage },
              isLoadingMore: false
            })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to load more',
              isLoadingMore: false
            })
          }
        },

        goToPage: async (page) => {
          const state = get()
          await state.searchTalent({ page })
        },

        saveCurrentSearch: (name) => {
          const state = get()
          const searchToSave = { ...state.searchQuery, name }
          const newSavedSearches = [...state.savedSearches, searchToSave]
          set({ savedSearches: newSavedSearches })
        },

        loadSavedSearch: (index) => {
          const state = get()
          const savedSearch = state.savedSearches[index]
          if (savedSearch) {
            const { name, ...query } = savedSearch
            state.searchTalent(query)
          }
        },

        deleteSavedSearch: (index) => {
          const state = get()
          const newSavedSearches = state.savedSearches.filter((_, i) => i !== index)
          set({ savedSearches: newSavedSearches })
        },

        clearError: () => set({ error: null }),
        clearSearchError: () => set({ searchError: null })
      }),
      {
        name: 'talent-frontend-store',
        partialize: (state) => ({
          followedTalent: Array.from(state.followedTalent),
          savedSearches: state.savedSearches,
          viewMode: state.viewMode,
          showFilters: state.showFilters
        }),
        onRehydrateStorage: () => (state) => {
          if (state?.followedTalent) {
            state.followedTalent = new Set(state.followedTalent as string[])
          }
        }
      }
    ),
    { name: 'talent-frontend' }
  )
)

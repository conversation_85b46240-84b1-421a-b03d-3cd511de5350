// stores/course.store.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { courseService, CourseFilters, CourseListOptions } from '@/lib/services/course.service'
import { ICourse, ICourseCategory, ICourseEnrollment, ICourseProgress, ICourseSection, ICourseLesson } from '@/lib/models/course.model'

// Payment data interface
interface PaymentData extends Record<string, unknown> {
  paymentMethod?: string
  amount?: number
  currency?: string
  metadata?: Record<string, unknown>
}

// Progress update data interface
interface ProgressUpdateData {
  isCompleted?: boolean
  timeSpent?: number
  lastPosition?: number
  notes?: string
}

interface CourseState {
  // Categories
  categories: ICourseCategory[]
  isLoadingCategories: boolean
  categoriesError: string | null

  // Courses
  courses: ICourse[]
  isLoadingCourses: boolean
  coursesError: string | null
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }

  // Current Course
  currentCourse: ICourse | null
  courseContent: {
    sections: ICourseSection[]
    lessons: ICourseLesson[]
    enrollment?: ICourseEnrollment
    progress: ICourseProgress[]
  }
  isLoadingCourse: boolean
  courseError: string | null

  // User Enrollments
  userEnrollments: ICourseEnrollment[]
  isLoadingEnrollments: boolean
  enrollmentsError: string | null

  // Filters
  activeFilters: CourseFilters
  searchQuery: string

  // UI State
  viewMode: 'grid' | 'list'
  selectedCategory: string | null
}

interface CourseActions {
  // Category actions
  fetchCategories: () => Promise<void>
  clearCategoriesError: () => void

  // Course actions
  fetchCourses: (filters?: CourseFilters, options?: CourseListOptions) => Promise<void>
  fetchCourse: (courseId: string) => Promise<void>
  fetchCourseContent: (courseId: string) => Promise<void>
  searchCourses: (query: string) => Promise<void>
  clearCoursesError: () => void
  clearCourseError: () => void

  // Enrollment actions
  enrollInCourse: (courseId: string, paymentData?: PaymentData) => Promise<{ success: boolean; enrollmentId?: string; paymentUrl?: string; error?: string }>
  fetchUserEnrollments: () => Promise<void>
  updateProgress: (courseId: string, lessonId: string, progressData: ProgressUpdateData) => Promise<boolean>

  // Filter actions
  setFilters: (filters: CourseFilters) => void
  clearFilters: () => void
  setSearchQuery: (query: string) => void
  setSelectedCategory: (categoryId: string | null) => void

  // UI actions
  setViewMode: (mode: 'grid' | 'list') => void
}

type CourseStore = CourseState & CourseActions

export const useCourseStore = create<CourseStore>()(
  persist(
    (set, get) => ({
      // Initial state
      categories: [],
      isLoadingCategories: false,
      categoriesError: null,

      courses: [],
      isLoadingCourses: false,
      coursesError: null,
      pagination: {
        page: 1,
        limit: 12,
        total: 0,
        pages: 0
      },

      currentCourse: null,
      courseContent: {
        sections: [],
        lessons: [],
        progress: []
      },
      isLoadingCourse: false,
      courseError: null,

      userEnrollments: [],
      isLoadingEnrollments: false,
      enrollmentsError: null,

      activeFilters: {},
      searchQuery: '',

      viewMode: 'grid',
      selectedCategory: null,

      // Actions
      fetchCategories: async () => {
        set({ isLoadingCategories: true, categoriesError: null })
        try {
          const categories = await courseService.getCategories()
          set({ categories, isLoadingCategories: false })
        } catch (error) {
          set({ 
            categoriesError: error instanceof Error ? error.message : 'Failed to fetch categories',
            isLoadingCategories: false 
          })
        }
      },

      clearCategoriesError: () => {
        set({ categoriesError: null })
      },

      fetchCourses: async (filters = {}, options = {}) => {
        set({ isLoadingCourses: true, coursesError: null })
        try {
          const result = await courseService.getCourses(filters, options)
          set({ 
            courses: result.courses,
            pagination: result.pagination,
            isLoadingCourses: false 
          })
        } catch (error) {
          set({ 
            coursesError: error instanceof Error ? error.message : 'Failed to fetch courses',
            isLoadingCourses: false 
          })
        }
      },

      fetchCourse: async (courseId: string) => {
        set({ isLoadingCourse: true, courseError: null })
        try {
          const course = await courseService.getCourse(courseId)
          set({ currentCourse: course, isLoadingCourse: false })
        } catch (error) {
          set({ 
            courseError: error instanceof Error ? error.message : 'Failed to fetch course',
            isLoadingCourse: false 
          })
        }
      },

      fetchCourseContent: async (courseId: string) => {
        set({ isLoadingCourse: true, courseError: null })
        try {
          const content = await courseService.getCourseContent(courseId)
          set({ courseContent: content, isLoadingCourse: false })
        } catch (error) {
          set({ 
            courseError: error instanceof Error ? error.message : 'Failed to fetch course content',
            isLoadingCourse: false 
          })
        }
      },

      searchCourses: async (query: string) => {
        set({ searchQuery: query, isLoadingCourses: true, coursesError: null })
        try {
          const result = await courseService.getCourses({ ...get().activeFilters, search: query })
          set({
            courses: result.courses,
            pagination: result.pagination,
            isLoadingCourses: false
          })
        } catch (error) {
          set({
            coursesError: error instanceof Error ? error.message : 'Failed to search courses',
            isLoadingCourses: false
          })
        }
      },

      clearCoursesError: () => {
        set({ coursesError: null })
      },

      clearCourseError: () => {
        set({ courseError: null })
      },

      enrollInCourse: async (courseId: string, paymentData?: PaymentData) => {
        try {
          const result = await courseService.enrollInCourse(courseId, paymentData)

          if (result.success) {
            // Refresh user enrollments
            get().fetchUserEnrollments()
          }

          return result
        } catch (error) {
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Failed to enroll in course'
          }
        }
      },

      fetchUserEnrollments: async () => {
        set({ isLoadingEnrollments: true, enrollmentsError: null })
        try {
          const enrollments = await courseService.getUserEnrollments()
          set({ userEnrollments: enrollments, isLoadingEnrollments: false })
        } catch (error) {
          set({ 
            enrollmentsError: error instanceof Error ? error.message : 'Failed to fetch enrollments',
            isLoadingEnrollments: false 
          })
        }
      },

      updateProgress: async (courseId: string, lessonId: string, progressData: ProgressUpdateData) => {
        try {
          const success = await courseService.updateProgress(courseId, lessonId, progressData)

          if (success) {
            // Refresh course content to get updated progress
            get().fetchCourseContent(courseId)
          }

          return success
        } catch (error) {
          console.error('Failed to update progress:', error)
          return false
        }
      },

      setFilters: (filters: CourseFilters) => {
        set({ activeFilters: filters })
        get().fetchCourses(filters)
      },

      clearFilters: () => {
        set({ activeFilters: {}, searchQuery: '', selectedCategory: null })
        get().fetchCourses()
      },

      setSearchQuery: (query: string) => {
        set({ searchQuery: query })
      },

      setSelectedCategory: (categoryId: string | null) => {
        set({ selectedCategory: categoryId })
        const filters = categoryId ? { category: categoryId } : {}
        get().setFilters(filters)
      },

      setViewMode: (mode: 'grid' | 'list') => {
        set({ viewMode: mode })
      }
    }),
    {
      name: 'course-storage',
      partialize: (state) => ({
        viewMode: state.viewMode,
        activeFilters: state.activeFilters
        // Note: Don't persist courses/categories to always fetch fresh data
      })
    }
  )
)

// Selector hooks for better performance
export const useCourseCategories = () => {
  const store = useCourseStore()
  return {
    categories: store.categories,
    isLoading: store.isLoadingCategories,
    error: store.categoriesError,
    fetchCategories: store.fetchCategories,
    clearError: store.clearCategoriesError
  }
}

export const useCourses = () => {
  const store = useCourseStore()
  return {
    courses: store.courses,
    isLoading: store.isLoadingCourses,
    error: store.coursesError,
    pagination: store.pagination,
    fetchCourses: store.fetchCourses,
    searchCourses: store.searchCourses,
    clearError: store.clearCoursesError
  }
}

export const useCurrentCourse = () => {
  const store = useCourseStore()
  return {
    course: store.currentCourse,
    content: store.courseContent,
    isLoading: store.isLoadingCourse,
    error: store.courseError,
    fetchCourse: store.fetchCourse,
    fetchContent: store.fetchCourseContent,
    clearError: store.clearCourseError
  }
}

export const useCourseEnrollments = () => {
  const store = useCourseStore()
  return {
    enrollments: store.userEnrollments,
    isLoading: store.isLoadingEnrollments,
    error: store.enrollmentsError,
    fetchEnrollments: store.fetchUserEnrollments,
    enrollInCourse: store.enrollInCourse,
    updateProgress: store.updateProgress
  }
}

export const useCourseFilters = () => {
  const store = useCourseStore()
  return {
    filters: store.activeFilters,
    searchQuery: store.searchQuery,
    selectedCategory: store.selectedCategory,
    viewMode: store.viewMode,
    setFilters: store.setFilters,
    clearFilters: store.clearFilters,
    setSearchQuery: store.setSearchQuery,
    setSelectedCategory: store.setSelectedCategory,
    setViewMode: store.setViewMode
  }
}

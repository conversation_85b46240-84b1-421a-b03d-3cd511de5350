import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import {
  ClientPro<PERSON>le,
  UpdateClientRequest,
  ClientSearchFilters,
  ClientSearchResult
} from '@/types/client.types'
import { useAuthStore } from './auth.store'

interface ClientState {
  // Current client data
  client: ClientProfile | null
  
  // Loading states
  loading: boolean
  profileLoading: boolean
  updateLoading: boolean
  searchLoading: boolean
  
  // Error state
  error: string | null
  
  // Search results
  searchResults: ClientSearchResult | null
  searchFilters: ClientSearchFilters
  
  // Documents
  documents: Record<string, any[]>
  documentsLoading: boolean

  // Profile completeness
  profileCompleteness: {
    percentage: number
    completed: string[]
    missing: string[]
    recommendations: string[]
  } | null

  // Privacy settings
  privacySettings: {
    profileVisibility: 'public' | 'private' | 'recruiters_only'
    showSalaryExpectation: boolean
    showCurrentCompany: boolean
    allowRecruiterContact: boolean
    showProfileToCurrentEmployer: boolean
  } | null

  // Social features
  followedCompanies: any[]
  followedRecruiters: any[]
  conversations: any[]
  socialLoading: boolean

  // Saved jobs
  savedJobs: string[]

  // Application stats
  applicationStats: {
    totalApplications: number
    pendingApplications: number
    interviewsReceived: number
    offersReceived: number
    successRate: number
  }
}

interface ClientActions {
  // Profile management
  fetchClientProfile: () => Promise<void>
  updateClientProfile: (data: UpdateClientRequest) => Promise<void>
  calculateProfileCompleteness: () => Promise<{ percentage: number; completed: string[]; missing: string[]; recommendations: string[] }>
  updateProfileVisibility: (settings: {
    profileVisibility: 'public' | 'private' | 'recruiters_only'
    showSalaryExpectation?: boolean
    showCurrentCompany?: boolean
    allowRecruiterContact?: boolean
    showProfileToCurrentEmployer?: boolean
  }) => Promise<void>
  exportProfile: (format: 'json' | 'pdf') => Promise<any>

  // Document management
  fetchDocuments: () => Promise<Record<string, any[]>>
  uploadDocument: (file: File, type: string, description?: string, isDefault?: boolean) => Promise<void>
  updateDocument: (documentId: string, updates: { description?: string; isDefault?: boolean }) => Promise<void>
  deleteDocument: (documentId: string) => Promise<void>

  // Social features - Company following
  followCompany: (companyId: string) => Promise<void>
  unfollowCompany: (companyId: string) => Promise<void>
  fetchFollowedCompanies: () => Promise<void>
  checkCompanyFollowStatus: (companyId: string) => Promise<boolean>

  // Social features - Recruiter following
  followRecruiter: (recruiterId: string) => Promise<void>
  unfollowRecruiter: (recruiterId: string) => Promise<void>
  fetchFollowedRecruiters: () => Promise<void>
  checkRecruiterFollowStatus: (recruiterId: string) => Promise<boolean>

  // Messaging
  fetchConversations: (type?: string) => Promise<void>
  sendMessage: (recipientId: string, subject: string, content: string, replyTo?: string) => Promise<void>
  markMessageAsRead: (messageId: string) => Promise<void>
  fetchConversation: (threadId: string) => Promise<any>

  // Job management
  saveJob: (jobId: string) => Promise<void>
  unsaveJob: (jobId: string) => Promise<void>
  
  // Company management
  followCompany: (companyId: string) => Promise<void>
  unfollowCompany: (companyId: string) => Promise<void>
  
  // Search
  searchClients: (filters: ClientSearchFilters, page?: number, limit?: number) => Promise<void>
  updateSearchFilters: (filters: Partial<ClientSearchFilters>) => void
  clearSearchResults: () => void
  
  // Activity tracking
  updateProfileViews: () => Promise<void>
  
  // Utility
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
  reset: () => void
}

type ClientStore = ClientState & ClientActions

const initialState: ClientState = {
  client: null,
  loading: false,
  profileLoading: false,
  updateLoading: false,
  searchLoading: false,
  error: null,
  searchResults: null,
  searchFilters: {},
  documents: {},
  documentsLoading: false,
  profileCompleteness: null,
  privacySettings: null,
  followedCompanies: [],
  followedRecruiters: [],
  conversations: [],
  socialLoading: false,
  savedJobs: [],
  applicationStats: {
    totalApplications: 0,
    pendingApplications: 0,
    interviewsReceived: 0,
    offersReceived: 0,
    successRate: 0
  }
}

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = useAuthStore.getState().token
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  }
}

export const useClientStore = create<ClientStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Profile management
      fetchClientProfile: async () => {
        set({ profileLoading: true, error: null })

        try {
          const response = await fetch('/api/v1/clients/me', {
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to fetch client profile')
          }

          const data = await response.json()

          set({
            client: data.data,
            savedJobs: data.data.savedJobs || [],
            followedCompanies: data.data.followedCompanies || [],
            applicationStats: data.data.applicationStats || initialState.applicationStats,
            profileLoading: false
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch profile',
            profileLoading: false
          })
        }
      },

      updateClientProfile: async (data: UpdateClientRequest) => {
        set({ updateLoading: true, error: null })
        
        try {
          const response = await fetch('/api/v1/clients/me', {
            method: 'PUT',
            headers: getAuthHeaders(),
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            throw new Error('Failed to update client profile')
          }

          const result = await response.json()
          
          set({ 
            client: result.data,
            updateLoading: false 
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to update profile',
            updateLoading: false
          })
        }
      },

      // Profile completeness
      calculateProfileCompleteness: async () => {
        try {
          const response = await fetch('/api/v1/clients/me/completeness', {
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to fetch profile completeness')
          }

          const result = await response.json()
          const completenessData = result.data

          set({ profileCompleteness: completenessData })
          return completenessData
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to calculate completeness' })
          throw error
        }
      },

      // Privacy settings
      updateProfileVisibility: async (settings) => {
        try {
          const response = await fetch('/api/v1/clients/me/visibility', {
            method: 'PUT',
            headers: getAuthHeaders(),
            body: JSON.stringify(settings)
          })

          if (!response.ok) {
            throw new Error('Failed to update privacy settings')
          }

          const result = await response.json()
          set({ privacySettings: result.data.privacy })
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to update privacy settings' })
          throw error
        }
      },

      // Profile export
      exportProfile: async (format: 'json' | 'pdf') => {
        try {
          const response = await fetch(`/api/v1/clients/me/export?format=${format}`, {
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to export profile')
          }

          const result = await response.json()
          return result.data
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to export profile' })
          throw error
        }
      },

      // Document management
      fetchDocuments: async () => {
        set({ documentsLoading: true })

        try {
          const response = await fetch('/api/v1/clients/documents', {
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to fetch documents')
          }

          const result = await response.json()
          set({
            documents: result.data.documents,
            documentsLoading: false
          })
          return result.data.documents
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch documents',
            documentsLoading: false
          })
          throw error
        }
      },

      uploadDocument: async (file: File, type: string, description?: string, isDefault?: boolean) => {
        try {
          const formData = new FormData()
          formData.append('file', file)
          formData.append('type', type)
          if (description) formData.append('description', description)
          if (isDefault !== undefined) formData.append('isDefault', isDefault.toString())

          const token = useAuthStore.getState().token
          const response = await fetch('/api/v1/clients/documents/upload', {
            method: 'POST',
            headers: {
              ...(token && { 'Authorization': `Bearer ${token}` })
            },
            body: formData
          })

          if (!response.ok) {
            throw new Error('Failed to upload document')
          }

          // Refresh documents after upload
          await get().fetchDocuments()
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to upload document' })
          throw error
        }
      },

      updateDocument: async (documentId: string, updates: { description?: string; isDefault?: boolean }) => {
        try {
          const response = await fetch(`/api/v1/clients/documents/${documentId}`, {
            method: 'PUT',
            headers: getAuthHeaders(),
            body: JSON.stringify(updates)
          })

          if (!response.ok) {
            throw new Error('Failed to update document')
          }

          // Refresh documents after update
          await get().fetchDocuments()
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to update document' })
          throw error
        }
      },

      deleteDocument: async (documentId: string) => {
        try {
          const response = await fetch(`/api/v1/clients/documents/${documentId}`, {
            method: 'DELETE',
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to delete document')
          }

          // Refresh documents after deletion
          await get().fetchDocuments()
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to delete document' })
          throw error
        }
      },

      // Social features - Company following
      followCompany: async (companyId: string) => {
        try {
          const response = await fetch(`/api/v1/clients/companies/${companyId}/follow`, {
            method: 'POST',
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to follow company')
          }

          // Refresh followed companies
          await get().fetchFollowedCompanies()
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to follow company' })
          throw error
        }
      },

      unfollowCompany: async (companyId: string) => {
        try {
          const response = await fetch(`/api/v1/clients/companies/${companyId}/follow`, {
            method: 'DELETE',
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to unfollow company')
          }

          // Update local state
          set(state => ({
            followedCompanies: state.followedCompanies.filter(company => company._id !== companyId)
          }))
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to unfollow company' })
          throw error
        }
      },

      fetchFollowedCompanies: async () => {
        set({ socialLoading: true })

        try {
          const response = await fetch('/api/v1/clients/companies/followed', {
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to fetch followed companies')
          }

          const result = await response.json()
          set({
            followedCompanies: result.data.followedCompanies,
            socialLoading: false
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch followed companies',
            socialLoading: false
          })
          throw error
        }
      },

      checkCompanyFollowStatus: async (companyId: string) => {
        try {
          const response = await fetch(`/api/v1/clients/companies/${companyId}/follow`, {
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            return false
          }

          const result = await response.json()
          return result.data.isFollowing
        } catch (error) {
          return false
        }
      },

      // Social features - Recruiter following
      followRecruiter: async (recruiterId: string) => {
        try {
          const response = await fetch(`/api/v1/clients/recruiters/${recruiterId}/follow`, {
            method: 'POST',
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to follow recruiter')
          }

          // Refresh followed recruiters
          await get().fetchFollowedRecruiters()
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to follow recruiter' })
          throw error
        }
      },

      unfollowRecruiter: async (recruiterId: string) => {
        try {
          const response = await fetch(`/api/v1/clients/recruiters/${recruiterId}/follow`, {
            method: 'DELETE',
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to unfollow recruiter')
          }

          // Update local state
          set(state => ({
            followedRecruiters: state.followedRecruiters.filter(recruiter => recruiter.id !== recruiterId)
          }))
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to unfollow recruiter' })
          throw error
        }
      },

      fetchFollowedRecruiters: async () => {
        set({ socialLoading: true })

        try {
          const response = await fetch('/api/v1/clients/recruiters/followed', {
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to fetch followed recruiters')
          }

          const result = await response.json()
          set({
            followedRecruiters: result.data.followedRecruiters,
            socialLoading: false
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch followed recruiters',
            socialLoading: false
          })
          throw error
        }
      },

      checkRecruiterFollowStatus: async (recruiterId: string) => {
        try {
          const response = await fetch(`/api/v1/clients/recruiters/${recruiterId}/follow`, {
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            return false
          }

          const result = await response.json()
          return result.data.isFollowing
        } catch (error) {
          return false
        }
      },

      // Messaging
      fetchConversations: async (type = 'all') => {
        set({ socialLoading: true })

        try {
          const response = await fetch(`/api/v1/clients/messages/conversations?type=${type}`, {
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to fetch conversations')
          }

          const result = await response.json()
          set({
            conversations: result.data.conversations,
            socialLoading: false
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch conversations',
            socialLoading: false
          })
          throw error
        }
      },

      sendMessage: async (recipientId: string, subject: string, content: string, replyTo?: string) => {
        try {
          const response = await fetch('/api/v1/clients/messages', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify({
              recipientId,
              subject,
              content,
              replyTo
            })
          })

          if (!response.ok) {
            throw new Error('Failed to send message')
          }

          // Refresh conversations
          await get().fetchConversations()
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to send message' })
          throw error
        }
      },

      markMessageAsRead: async (messageId: string) => {
        try {
          const response = await fetch(`/api/v1/clients/messages/${messageId}`, {
            method: 'PUT',
            headers: getAuthHeaders(),
            body: JSON.stringify({ action: 'mark_read' })
          })

          if (!response.ok) {
            throw new Error('Failed to mark message as read')
          }
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to mark message as read' })
          throw error
        }
      },

      fetchConversation: async (threadId: string) => {
        try {
          const response = await fetch(`/api/v1/clients/messages/${threadId}`, {
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to fetch conversation')
          }

          const result = await response.json()
          return result.data
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to fetch conversation' })
          throw error
        }
      },

      // Job management
      saveJob: async (jobId: string) => {
        const { client, savedJobs } = get()
        if (!client) return

        try {
          const response = await fetch(`/api/v1/clients/${client._id}/save-job`, {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify({ jobId })
          })

          if (!response.ok) {
            throw new Error('Failed to save job')
          }

          set({ 
            savedJobs: [...savedJobs, jobId]
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to save job'
          })
        }
      },

      unsaveJob: async (jobId: string) => {
        const { client, savedJobs } = get()
        if (!client) return

        try {
          const response = await fetch(`/api/v1/clients/${client._id}/unsave-job`, {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify({ jobId })
          })

          if (!response.ok) {
            throw new Error('Failed to unsave job')
          }

          set({ 
            savedJobs: savedJobs.filter(id => id !== jobId)
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to unsave job'
          })
        }
      },

      // Company management
      followCompany: async (companyId: string) => {
        const { client, followedCompanies } = get()
        if (!client) return

        try {
          const response = await fetch(`/api/v1/clients/${client._id}/follow-company`, {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify({ companyId })
          })

          if (!response.ok) {
            throw new Error('Failed to follow company')
          }

          set({ 
            followedCompanies: [...followedCompanies, companyId]
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to follow company'
          })
        }
      },

      unfollowCompany: async (companyId: string) => {
        const { client, followedCompanies } = get()
        if (!client) return

        try {
          const response = await fetch(`/api/v1/clients/${client._id}/unfollow-company`, {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify({ companyId })
          })

          if (!response.ok) {
            throw new Error('Failed to unfollow company')
          }

          set({ 
            followedCompanies: followedCompanies.filter(id => id !== companyId)
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to unfollow company'
          })
        }
      },

      // Search
      searchClients: async (filters: ClientSearchFilters, page = 1, limit = 20) => {
        set({ searchLoading: true, error: null })
        
        try {
          const searchParams = new URLSearchParams()
          
          if (filters.skills?.length) searchParams.set('skills', filters.skills.join(','))
          if (filters.experience?.length) searchParams.set('experience', filters.experience.join(','))
          if (filters.industries?.length) searchParams.set('industries', filters.industries.join(','))
          if (filters.locations?.length) searchParams.set('locations', filters.locations.join(','))
          if (filters.availability?.length) searchParams.set('availability', filters.availability.join(','))
          if (filters.salaryMin) searchParams.set('salaryMin', filters.salaryMin.toString())
          if (filters.salaryMax) searchParams.set('salaryMax', filters.salaryMax.toString())
          if (filters.profileVisibility?.length) searchParams.set('profileVisibility', filters.profileVisibility.join(','))
          
          searchParams.set('page', page.toString())
          searchParams.set('limit', limit.toString())

          const response = await fetch(`/api/v1/clients?${searchParams}`)

          if (!response.ok) {
            throw new Error('Failed to search clients')
          }

          const data = await response.json()
          
          set({ 
            searchResults: data.data,
            searchFilters: filters,
            searchLoading: false 
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to search clients',
            searchLoading: false 
          })
        }
      },

      updateSearchFilters: (filters: Partial<ClientSearchFilters>) => {
        set(state => ({
          searchFilters: { ...state.searchFilters, ...filters }
        }))
      },

      clearSearchResults: () => {
        set({ 
          searchResults: null,
          searchFilters: {}
        })
      },

      // Activity tracking
      updateProfileViews: async () => {
        const { client } = get()
        if (!client) return

        try {
          await fetch(`/api/v1/clients/${client._id}/activity`, {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify({ activityType: 'view' })
          })
        } catch (error) {
          // Silently fail for activity tracking
          console.error('Failed to update profile views:', error)
        }
      },

      // Utility
      setLoading: (loading: boolean) => set({ loading }),
      
      setError: (error: string | null) => set({ error }),
      
      clearError: () => set({ error: null }),
      
      reset: () => set(initialState)
    }),
    {
      name: 'client-store',
      partialize: (state) => ({
        client: state.client,
        savedJobs: state.savedJobs,
        followedCompanies: state.followedCompanies,
        searchFilters: state.searchFilters
      })
    }
  )
)

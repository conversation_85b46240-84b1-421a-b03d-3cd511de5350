// stores/company-showcase.store.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { 
  companiesShowcaseService, 
  type CompanyShowcase,
  type CompanyShowcaseQuery 
} from '@/lib/services/companies-showcase.service'

interface CompanyShowcaseState {
  // Data
  companies: CompanyShowcase[]
  featuredCompanies: CompanyShowcase[]
  
  // UI State
  isLoading: boolean
  isLoadingFeatured: boolean
  
  // Error States
  error: string | null
  featuredError: string | null
  
  // Cache
  lastFetchTime: number
  cacheExpiry: number
}

interface CompanyShowcaseActions {
  // Data fetching
  fetchShowcaseCompanies: (query?: CompanyShowcaseQuery) => Promise<void>
  fetchFeaturedCompanies: (limit?: number) => Promise<void>
  
  // Utility actions
  clearError: () => void
  clearFeaturedError: () => void
  refreshData: () => Promise<void>
  
  // Cache management
  clearCache: () => void
}

export const useCompanyShowcaseStore = create<CompanyShowcaseState & CompanyShowcaseActions>()(
  persist(
    (set, get) => ({
      // Initial state
      companies: [],
      featuredCompanies: [],
      
      isLoading: false,
      isLoadingFeatured: false,
      
      error: null,
      featuredError: null,
      
      lastFetchTime: 0,
      cacheExpiry: 10 * 60 * 1000, // 10 minutes

      // Actions
      fetchShowcaseCompanies: async (query = {}) => {
        const state = get()
        
        // Check if we need to fetch (cache not expired)
        const now = Date.now()
        if (state.companies.length > 0 && now - state.lastFetchTime < state.cacheExpiry) {
          return
        }

        set({ isLoading: true, error: null })
        
        try {
          const result = await companiesShowcaseService.getShowcaseCompanies(query)
          
          set({
            companies: result.companies,
            isLoading: false,
            lastFetchTime: now,
            error: null
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch companies',
            isLoading: false
          })
        }
      },

      fetchFeaturedCompanies: async (limit = 4) => {
        set({ isLoadingFeatured: true, featuredError: null })
        
        try {
          const companies = await companiesShowcaseService.getFeaturedCompanies(limit)
          
          set({
            featuredCompanies: companies,
            isLoadingFeatured: false,
            featuredError: null
          })
        } catch (error) {
          set({
            featuredError: error instanceof Error ? error.message : 'Failed to fetch featured companies',
            isLoadingFeatured: false
          })
        }
      },

      clearError: () => set({ error: null }),
      
      clearFeaturedError: () => set({ featuredError: null }),

      refreshData: async () => {
        const state = get()
        
        // Clear cache and fetch fresh data
        companiesShowcaseService.clearCache()
        set({ lastFetchTime: 0 })
        
        // Fetch both regular and featured companies
        await Promise.all([
          state.fetchShowcaseCompanies(),
          state.fetchFeaturedCompanies()
        ])
      },

      clearCache: () => {
        companiesShowcaseService.clearCache()
        set({ 
          companies: [],
          featuredCompanies: [],
          lastFetchTime: 0,
          error: null,
          featuredError: null
        })
      }
    }),
    {
      name: 'company-showcase-storage',
      partialize: (state) => ({
        companies: state.companies,
        featuredCompanies: state.featuredCompanies,
        lastFetchTime: state.lastFetchTime
      })
    }
  )
)

// Selector hooks for better performance
export const useCompanyShowcaseData = () => {
  const store = useCompanyShowcaseStore()
  return {
    companies: store.companies,
    featuredCompanies: store.featuredCompanies,
    isLoading: store.isLoading,
    isLoadingFeatured: store.isLoadingFeatured,
    error: store.error,
    featuredError: store.featuredError
  }
}

export const useCompanyShowcaseActions = () => {
  const store = useCompanyShowcaseStore()
  return {
    fetchShowcaseCompanies: store.fetchShowcaseCompanies,
    fetchFeaturedCompanies: store.fetchFeaturedCompanies,
    clearError: store.clearError,
    clearFeaturedError: store.clearFeaturedError,
    refreshData: store.refreshData,
    clearCache: store.clearCache
  }
}

import { create } from 'zustand'
import { persist } from 'zustand/middleware'

// Types
export interface QuizCategory {
  id: string
  name: string
  slug: string
  description: string
  icon: string
  color: string
  skills: string[]
  difficulty: string
  estimatedTime: number
  totalQuestions: number
  passingScore: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  // Stats
  attempts?: number
  averageScore?: number
  availableQuestions?: number
}

export interface QuizQuestion {
  id: string
  categoryId: string
  categoryName?: string
  categoryIcon?: string
  question: string
  options: string[]
  correctAnswer: number
  explanation?: string
  difficulty: string
  points: number
  tags: string[]
  isActive: boolean
  createdAt: string
  updatedAt: string
  // Stats
  attempts?: number
  correctRate?: number
}

export interface AwardTemplate {
  id: string
  name: string
  slug: string
  description: string
  categoryId: string
  categoryName?: string
  categoryIcon?: string
  badgeIcon: string
  badgeColor: string
  minimumScore: number
  rarity: string
  benefits: string[]
  certificateTemplate?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  // Stats
  timesEarned?: number
  averageScoreOfEarners?: number
}

export interface QuizAttempt {
  id: string
  sessionId: string
  userId?: string
  userEmail?: string
  categoryId: string
  categoryName?: string
  categoryIcon?: string
  score: number
  totalQuestions: number
  correctAnswers: number
  timeSpent: number
  completedAt: string
  earnedAwards: string[]
  isAnonymous: boolean
  ipAddress?: string
  userAgent?: string
}

export interface DashboardStats {
  totalCategories: number
  totalQuestions: number
  totalAwards: number
  totalAttempts: number
  averageScore: number
  passRate: number
  averageTime: number
  activeUsers: number
}

// Quiz Admin Store
interface QuizAdminState {
  // Categories
  categories: QuizCategory[]
  categoriesLoading: boolean
  categoriesError: string | null
  
  // Questions
  questions: QuizQuestion[]
  questionsLoading: boolean
  questionsError: string | null
  
  // Awards
  awards: AwardTemplate[]
  awardsLoading: boolean
  awardsError: string | null
  
  // Attempts
  attempts: QuizAttempt[]
  attemptsLoading: boolean
  attemptsError: string | null
  
  // Dashboard Stats
  dashboardStats: DashboardStats | null
  dashboardLoading: boolean
  dashboardError: string | null
  
  // Actions
  fetchCategories: () => Promise<void>
  createCategory: (category: Omit<QuizCategory, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  updateCategory: (id: string, updates: Partial<QuizCategory>) => Promise<void>
  deleteCategory: (id: string) => Promise<void>
  
  fetchQuestions: (filters?: { categoryId?: string; difficulty?: string }) => Promise<void>
  createQuestion: (question: Omit<QuizQuestion, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  updateQuestion: (id: string, updates: Partial<QuizQuestion>) => Promise<void>
  deleteQuestion: (id: string) => Promise<void>
  
  fetchAwards: (filters?: { categoryId?: string; rarity?: string }) => Promise<void>
  createAward: (award: Omit<AwardTemplate, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  updateAward: (id: string, updates: Partial<AwardTemplate>) => Promise<void>
  deleteAward: (id: string) => Promise<void>
  
  fetchAttempts: (filters?: { categoryId?: string; timeRange?: string }) => Promise<void>
  deleteAttempt: (id: string) => Promise<void>
  
  fetchDashboardStats: () => Promise<void>
  
  // Utility actions
  clearErrors: () => void
  reset: () => void
}

export const useQuizAdminStore = create<QuizAdminState>()(
  persist(
    (set, get) => ({
      // Initial state
      categories: [],
      categoriesLoading: false,
      categoriesError: null,
      
      questions: [],
      questionsLoading: false,
      questionsError: null,
      
      awards: [],
      awardsLoading: false,
      awardsError: null,
      
      attempts: [],
      attemptsLoading: false,
      attemptsError: null,
      
      dashboardStats: null,
      dashboardLoading: false,
      dashboardError: null,
      
      // Categories Actions
      fetchCategories: async () => {
        set({ categoriesLoading: true, categoriesError: null })
        try {
          const response = await fetch('/api/v1/quiz-admin/categories?includeStats=true')
          const result = await response.json()
          
          if (result.success) {
            set({ 
              categories: result.data.categories,
              categoriesLoading: false 
            })
          } else {
            set({ 
              categoriesError: result.error || 'Failed to fetch categories',
              categoriesLoading: false 
            })
          }
        } catch (error) {
          set({ 
            categoriesError: 'Network error while fetching categories',
            categoriesLoading: false 
          })
        }
      },
      
      createCategory: async (categoryData) => {
        set({ categoriesLoading: true, categoriesError: null })
        try {
          const response = await fetch('/api/v1/quiz-admin/categories', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(categoryData)
          })
          const result = await response.json()
          
          if (result.success) {
            const { categories } = get()
            set({ 
              categories: [result.data.category, ...categories],
              categoriesLoading: false 
            })
          } else {
            set({ 
              categoriesError: result.error || 'Failed to create category',
              categoriesLoading: false 
            })
          }
        } catch (error) {
          set({ 
            categoriesError: 'Network error while creating category',
            categoriesLoading: false 
          })
        }
      },
      
      updateCategory: async (id, updates) => {
        set({ categoriesLoading: true, categoriesError: null })
        try {
          const response = await fetch('/api/v1/quiz-admin/categories', {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ id, ...updates })
          })
          const result = await response.json()
          
          if (result.success) {
            const { categories } = get()
            set({ 
              categories: categories.map(cat => 
                cat.id === id ? { ...cat, ...updates } : cat
              ),
              categoriesLoading: false 
            })
          } else {
            set({ 
              categoriesError: result.error || 'Failed to update category',
              categoriesLoading: false 
            })
          }
        } catch (error) {
          set({ 
            categoriesError: 'Network error while updating category',
            categoriesLoading: false 
          })
        }
      },
      
      deleteCategory: async (id) => {
        set({ categoriesLoading: true, categoriesError: null })
        try {
          const response = await fetch(`/api/v1/quiz-admin/categories?id=${id}`, {
            method: 'DELETE'
          })
          const result = await response.json()
          
          if (result.success) {
            const { categories } = get()
            set({ 
              categories: categories.filter(cat => cat.id !== id),
              categoriesLoading: false 
            })
          } else {
            set({ 
              categoriesError: result.error || 'Failed to delete category',
              categoriesLoading: false 
            })
          }
        } catch (error) {
          set({ 
            categoriesError: 'Network error while deleting category',
            categoriesLoading: false 
          })
        }
      },

      bulkUploadCategories: async (categories, mode = 'create') => {
        set({ categoriesLoading: true, categoriesError: null })
        try {
          const response = await fetch('/api/v1/quiz-admin/categories/bulk', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ categories, mode })
          })
          const result = await response.json()

          if (result.success) {
            // Refresh categories list after bulk upload
            await get().fetchCategories()
            return result.data.results
          } else {
            set({
              categoriesError: result.error || 'Failed to upload categories',
              categoriesLoading: false
            })
            throw new Error(result.error || 'Failed to upload categories')
          }
        } catch (error) {
          set({
            categoriesError: 'Network error while uploading categories',
            categoriesLoading: false
          })
          throw error
        }
      },

      exportCategories: async (format = 'json') => {
        try {
          const response = await fetch(`/api/v1/quiz-admin/categories/bulk?format=${format}`)

          if (format === 'csv') {
            const blob = await response.blob()
            const url = window.URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = 'quiz-categories.csv'
            document.body.appendChild(a)
            a.click()
            window.URL.revokeObjectURL(url)
            document.body.removeChild(a)
          } else {
            const result = await response.json()
            if (result.success) {
              const blob = new Blob([JSON.stringify(result.data.categories, null, 2)], {
                type: 'application/json'
              })
              const url = window.URL.createObjectURL(blob)
              const a = document.createElement('a')
              a.href = url
              a.download = 'quiz-categories.json'
              document.body.appendChild(a)
              a.click()
              window.URL.revokeObjectURL(url)
              document.body.removeChild(a)
            }
          }
        } catch (error) {
          console.error('Error exporting categories:', error)
          throw error
        }
      },

      // Questions Actions
      fetchQuestions: async (filters = {}) => {
        set({ questionsLoading: true, questionsError: null })
        try {
          const params = new URLSearchParams()
          if (filters.categoryId) params.append('categoryId', filters.categoryId)
          if (filters.difficulty) params.append('difficulty', filters.difficulty)
          params.append('includeStats', 'true') // Always include stats for better UI

          const response = await fetch(`/api/v1/quiz-admin/questions?${params}`)
          const result = await response.json()
          
          if (result.success) {
            set({ 
              questions: result.data.questions,
              questionsLoading: false 
            })
          } else {
            set({ 
              questionsError: result.error || 'Failed to fetch questions',
              questionsLoading: false 
            })
          }
        } catch (error) {
          set({ 
            questionsError: 'Network error while fetching questions',
            questionsLoading: false 
          })
        }
      },
      
      createQuestion: async (questionData) => {
        set({ questionsLoading: true, questionsError: null })
        try {
          const response = await fetch('/api/v1/quiz-admin/questions', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(questionData)
          })
          const result = await response.json()
          
          if (result.success) {
            const { questions } = get()
            set({ 
              questions: [result.data.question, ...questions],
              questionsLoading: false 
            })
          } else {
            set({ 
              questionsError: result.error || 'Failed to create question',
              questionsLoading: false 
            })
          }
        } catch (error) {
          set({ 
            questionsError: 'Network error while creating question',
            questionsLoading: false 
          })
        }
      },
      
      updateQuestion: async (id, updates) => {
        set({ questionsLoading: true, questionsError: null })
        try {
          const response = await fetch('/api/v1/quiz-admin/questions', {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ id, ...updates })
          })
          const result = await response.json()
          
          if (result.success) {
            const { questions } = get()
            set({ 
              questions: questions.map(q => 
                q.id === id ? { ...q, ...updates } : q
              ),
              questionsLoading: false 
            })
          } else {
            set({ 
              questionsError: result.error || 'Failed to update question',
              questionsLoading: false 
            })
          }
        } catch (error) {
          set({ 
            questionsError: 'Network error while updating question',
            questionsLoading: false 
          })
        }
      },
      
      deleteQuestion: async (id) => {
        set({ questionsLoading: true, questionsError: null })
        try {
          const response = await fetch(`/api/v1/quiz-admin/questions?id=${id}`, {
            method: 'DELETE'
          })
          const result = await response.json()
          
          if (result.success) {
            const { questions } = get()
            set({ 
              questions: questions.filter(q => q.id !== id),
              questionsLoading: false 
            })
          } else {
            set({ 
              questionsError: result.error || 'Failed to delete question',
              questionsLoading: false 
            })
          }
        } catch (error) {
          set({ 
            questionsError: 'Network error while deleting question',
            questionsLoading: false 
          })
        }
      },

      bulkUploadQuestions: async (questions, mode = 'create', categoryId) => {
        set({ questionsLoading: true, questionsError: null })
        try {
          const response = await fetch('/api/v1/quiz-admin/questions/bulk', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ questions, mode, categoryId })
          })
          const result = await response.json()

          if (result.success) {
            // Refresh questions list after bulk upload
            await get().fetchQuestions()
            return result.data.results
          } else {
            set({
              questionsError: result.error || 'Failed to upload questions',
              questionsLoading: false
            })
            throw new Error(result.error || 'Failed to upload questions')
          }
        } catch (error) {
          set({
            questionsError: 'Network error while uploading questions',
            questionsLoading: false
          })
          throw error
        }
      },

      exportQuestions: async (format = 'json', categoryId) => {
        try {
          const params = new URLSearchParams()
          params.append('format', format)
          if (categoryId) params.append('categoryId', categoryId)

          const response = await fetch(`/api/v1/quiz-admin/questions/bulk?${params}`)

          if (format === 'csv') {
            const blob = await response.blob()
            const url = window.URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = 'quiz-questions.csv'
            document.body.appendChild(a)
            a.click()
            window.URL.revokeObjectURL(url)
            document.body.removeChild(a)
          } else {
            const result = await response.json()
            if (result.success) {
              const blob = new Blob([JSON.stringify(result.data.questions, null, 2)], {
                type: 'application/json'
              })
              const url = window.URL.createObjectURL(blob)
              const a = document.createElement('a')
              a.href = url
              a.download = 'quiz-questions.json'
              document.body.appendChild(a)
              a.click()
              window.URL.revokeObjectURL(url)
              document.body.removeChild(a)
            }
          }
        } catch (error) {
          console.error('Error exporting questions:', error)
          throw error
        }
      },

      // Awards Actions (similar pattern)
      fetchAwards: async (filters = {}) => {
        set({ awardsLoading: true, awardsError: null })
        try {
          const params = new URLSearchParams()
          if (filters.categoryId) params.append('categoryId', filters.categoryId)
          if (filters.rarity) params.append('rarity', filters.rarity)
          
          const response = await fetch(`/api/v1/quiz-admin/awards?${params}`)
          const result = await response.json()
          
          if (result.success) {
            set({ 
              awards: result.data.awards,
              awardsLoading: false 
            })
          } else {
            set({ 
              awardsError: result.error || 'Failed to fetch awards',
              awardsLoading: false 
            })
          }
        } catch (error) {
          set({ 
            awardsError: 'Network error while fetching awards',
            awardsLoading: false 
          })
        }
      },
      
      createAward: async (awardData) => {
        set({ awardsLoading: true, awardsError: null })
        try {
          const response = await fetch('/api/v1/quiz-admin/awards', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(awardData)
          })
          const result = await response.json()
          
          if (result.success) {
            const { awards } = get()
            set({ 
              awards: [result.data.award, ...awards],
              awardsLoading: false 
            })
          } else {
            set({ 
              awardsError: result.error || 'Failed to create award',
              awardsLoading: false 
            })
          }
        } catch (error) {
          set({ 
            awardsError: 'Network error while creating award',
            awardsLoading: false 
          })
        }
      },
      
      updateAward: async (id, updates) => {
        set({ awardsLoading: true, awardsError: null })
        try {
          const response = await fetch('/api/v1/quiz-admin/awards', {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ id, ...updates })
          })
          const result = await response.json()
          
          if (result.success) {
            const { awards } = get()
            set({ 
              awards: awards.map(a => 
                a.id === id ? { ...a, ...updates } : a
              ),
              awardsLoading: false 
            })
          } else {
            set({ 
              awardsError: result.error || 'Failed to update award',
              awardsLoading: false 
            })
          }
        } catch (error) {
          set({ 
            awardsError: 'Network error while updating award',
            awardsLoading: false 
          })
        }
      },
      
      deleteAward: async (id) => {
        set({ awardsLoading: true, awardsError: null })
        try {
          const response = await fetch(`/api/v1/quiz-admin/awards?id=${id}`, {
            method: 'DELETE'
          })
          const result = await response.json()
          
          if (result.success) {
            const { awards } = get()
            set({ 
              awards: awards.filter(a => a.id !== id),
              awardsLoading: false 
            })
          } else {
            set({ 
              awardsError: result.error || 'Failed to delete award',
              awardsLoading: false 
            })
          }
        } catch (error) {
          set({ 
            awardsError: 'Network error while deleting award',
            awardsLoading: false 
          })
        }
      },
      
      // Attempts Actions
      fetchAttempts: async (filters = {}) => {
        set({ attemptsLoading: true, attemptsError: null })
        try {
          const params = new URLSearchParams()
          if (filters.categoryId) params.append('categoryId', filters.categoryId)
          if (filters.timeRange) params.append('timeRange', filters.timeRange)
          
          const response = await fetch(`/api/v1/quiz-admin/attempts?${params}`)
          const result = await response.json()
          
          if (result.success) {
            set({ 
              attempts: result.data.attempts,
              attemptsLoading: false 
            })
          } else {
            set({ 
              attemptsError: result.error || 'Failed to fetch attempts',
              attemptsLoading: false 
            })
          }
        } catch (error) {
          set({ 
            attemptsError: 'Network error while fetching attempts',
            attemptsLoading: false 
          })
        }
      },
      
      deleteAttempt: async (id) => {
        set({ attemptsLoading: true, attemptsError: null })
        try {
          const response = await fetch(`/api/v1/quiz-admin/attempts?id=${id}`, {
            method: 'DELETE'
          })
          const result = await response.json()
          
          if (result.success) {
            const { attempts } = get()
            set({ 
              attempts: attempts.filter(a => a.id !== id),
              attemptsLoading: false 
            })
          } else {
            set({ 
              attemptsError: result.error || 'Failed to delete attempt',
              attemptsLoading: false 
            })
          }
        } catch (error) {
          set({ 
            attemptsError: 'Network error while deleting attempt',
            attemptsLoading: false 
          })
        }
      },
      
      // Dashboard Stats
      fetchDashboardStats: async () => {
        set({ dashboardLoading: true, dashboardError: null })
        try {
          const response = await fetch('/api/v1/quiz-admin/analytics')
          const result = await response.json()
          
          if (result.success) {
            set({ 
              dashboardStats: {
                totalCategories: result.data.totalCategories,
                totalQuestions: result.data.totalQuestions,
                totalAwards: result.data.totalAwards,
                totalAttempts: result.data.totalAttempts,
                averageScore: result.data.averageScore,
                passRate: result.data.passRate,
                averageTime: result.data.averageTime,
                activeUsers: result.data.totalAttempts // Mock active users
              },
              dashboardLoading: false 
            })
          } else {
            set({ 
              dashboardError: result.error || 'Failed to fetch dashboard stats',
              dashboardLoading: false 
            })
          }
        } catch (error) {
          set({ 
            dashboardError: 'Network error while fetching dashboard stats',
            dashboardLoading: false 
          })
        }
      },
      
      // Utility actions
      clearErrors: () => {
        set({
          categoriesError: null,
          questionsError: null,
          awardsError: null,
          attemptsError: null,
          dashboardError: null
        })
      },
      
      reset: () => {
        set({
          categories: [],
          questions: [],
          awards: [],
          attempts: [],
          dashboardStats: null,
          categoriesLoading: false,
          questionsLoading: false,
          awardsLoading: false,
          attemptsLoading: false,
          dashboardLoading: false,
          categoriesError: null,
          questionsError: null,
          awardsError: null,
          attemptsError: null,
          dashboardError: null
        })
      }
    }),
    {
      name: 'quiz-admin-store',
      partialize: (state) => ({
        // Only persist data, not loading states or errors
        categories: state.categories,
        questions: state.questions,
        awards: state.awards,
        attempts: state.attempts,
        dashboardStats: state.dashboardStats
      })
    }
  )
)

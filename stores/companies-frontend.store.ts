import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { companiesFrontendService } from '@/lib/services/companies-frontend.service'
import {
  CompanyFrontend,
  CompanySearchQuery,
  CompanySearchResult,
  CompanyFilters,
  CompanySize,
  FundingStage
} from '@/types/companies-frontend.types'

// Re-export types for backward compatibility
export type { CompanyFrontend, CompanySearchQuery, CompanySearchResult, CompanyFilters, CompanySize, FundingStage }

// Additional store-specific types
export interface CompanySearchFilters {
  industries: string[]
  sizes: CompanySize[]
  locations: string[]
  foundedRange: [number, number]
  employeeRange: [number, number]
  fundingStages: FundingStage[]
  benefits: string[]
  technologies: string[]
  isVerified?: boolean
  isFeatured?: boolean
  hasActiveJobs?: boolean
  minRating?: number
}

interface CompaniesState {
  // Data
  companies: CompanyFrontend[]
  currentCompany: CompanyFrontend | null
  searchResult: CompanySearchResult | null
  followedCompanies: Set<string>
  savedSearches: Array<CompanySearchQuery & { name: string }>
  
  // Search & Filters
  searchQuery: CompanySearchQuery
  activeFilters: Partial<CompanySearchFilters>
  quickFilters: {
    trending: boolean
    hiring: boolean
    verified: boolean
    remote: boolean
  }
  
  // UI State
  viewMode: 'grid' | 'list'
  showFilters: boolean
  selectedCompanyId: string | null
  
  // Loading States
  isLoading: boolean
  isSearching: boolean
  isLoadingMore: boolean
  isFollowing: boolean
  
  // Error States
  error: string | null
  searchError: string | null
  
  // Cache
  lastSearchTime: number
  cacheExpiry: number
}

interface CompaniesActions {
  // Search & Filter Actions
  searchCompanies: (query: Partial<CompanySearchQuery>) => Promise<void>
  updateFilters: (filters: Partial<CompanySearchFilters>) => void
  clearFilters: () => void
  applyQuickFilter: (filter: keyof CompaniesState['quickFilters']) => void
  
  // Company Actions
  getCompanyById: (id: string) => Promise<CompanyFrontend | null>
  followCompany: (companyId: string) => Promise<void>
  unfollowCompany: (companyId: string) => Promise<void>
  
  // UI Actions
  setViewMode: (mode: 'grid' | 'list') => void
  toggleFilters: () => void
  selectCompany: (companyId: string | null) => void
  
  // Pagination
  loadMore: () => Promise<void>
  goToPage: (page: number) => Promise<void>
  
  // Cache Management
  clearCache: () => void
  refreshData: () => Promise<void>
  
  // Saved Searches
  saveSearch: (query: CompanySearchQuery, name: string) => void
  loadSavedSearch: (index: number) => void
  deleteSavedSearch: (index: number) => void
  
  // Error Management
  clearError: () => void
  clearSearchError: () => void
}

const initialSearchQuery: CompanySearchQuery = {
  search: '',
  filters: {},
  sortBy: 'relevance',
  sortOrder: 'desc',
  page: 1,
  limit: 20
}

export const useCompaniesFrontendStore = create<CompaniesState & CompaniesActions>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial State
        companies: [],
        currentCompany: null,
        searchResult: null,
        followedCompanies: new Set(),
        savedSearches: [],
        
        searchQuery: initialSearchQuery,
        activeFilters: {},
        quickFilters: {
          trending: false,
          hiring: false,
          verified: false,
          remote: false
        },
        
        viewMode: 'list',
        showFilters: true,
        selectedCompanyId: null,
        
        isLoading: false,
        isSearching: false,
        isLoadingMore: false,
        isFollowing: false,
        
        error: null,
        searchError: null,
        
        lastSearchTime: 0,
        cacheExpiry: 5 * 60 * 1000,

        // Actions
        searchCompanies: async (queryUpdate) => {
          const state = get()
          const newQuery = { ...state.searchQuery, ...queryUpdate }
          
          set({ isSearching: true, searchError: null, searchQuery: newQuery })
          
          try {
            const result = await companiesFrontendService.searchCompanies(newQuery)

            set({
              searchResult: result,
              companies: result.companies,
              isSearching: false,
              lastSearchTime: Date.now()
            })
          } catch (error) {
            set({
              searchError: error instanceof Error ? error.message : 'Search failed',
              isSearching: false
            })
          }
        },

        updateFilters: (filters) => {
          const state = get()
          const newFilters = { ...state.activeFilters, ...filters }
          
          set({ activeFilters: newFilters })
          
          // Auto-search with new filters
          state.searchCompanies({
            filters: newFilters,
            page: 1 // Reset to first page
          })
        },

        clearFilters: () => {
          set({ 
            activeFilters: {},
            quickFilters: {
              trending: false,
              hiring: false,
              verified: false,
              remote: false
            }
          })
          
          const state = get()
          state.searchCompanies({
            filters: {},
            page: 1
          })
        },

        applyQuickFilter: (filter) => {
          const state = get()
          const newQuickFilters = {
            ...state.quickFilters,
            [filter]: !state.quickFilters[filter]
          }
          
          set({ quickFilters: newQuickFilters })
          
          // Convert quick filters to search filters
          const searchFilters: Partial<CompanySearchFilters> = {}
          
          if (newQuickFilters.verified) searchFilters.isVerified = true
          if (newQuickFilters.hiring) searchFilters.hasActiveJobs = true
          if (newQuickFilters.trending) {
            // Sort by follower count or recent activity
            state.searchCompanies({
              sortBy: 'rating',
              sortOrder: 'desc'
            })
            return
          }
          
          state.updateFilters(searchFilters)
        },

        getCompanyById: async (id) => {
          set({ isLoading: true, error: null })
          
          try {
            const company = await companiesFrontendService.getCompanyById(id)
            set({ currentCompany: company, isLoading: false })
            return company
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to load company',
              isLoading: false
            })
            return null
          }
        },

        followCompany: async (companyId) => {
          set({ isFollowing: true, error: null })
          
          try {
            await companiesFrontendService.followCompany(companyId)
            
            const state = get()
            const newFollowed = new Set(state.followedCompanies)
            newFollowed.add(companyId)
            
            set({ 
              followedCompanies: newFollowed,
              isFollowing: false
            })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to follow company',
              isFollowing: false
            })
          }
        },

        unfollowCompany: async (companyId) => {
          set({ isFollowing: true, error: null })
          
          try {
            await companiesFrontendService.unfollowCompany(companyId)
            
            const state = get()
            const newFollowed = new Set(state.followedCompanies)
            newFollowed.delete(companyId)
            
            set({ 
              followedCompanies: newFollowed,
              isFollowing: false
            })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to unfollow company',
              isFollowing: false
            })
          }
        },

        setViewMode: (mode) => {
          set({ viewMode: mode })
        },

        toggleFilters: () => {
          const state = get()
          set({ showFilters: !state.showFilters })
        },

        selectCompany: (companyId) => {
          set({ selectedCompanyId: companyId })
        },

        loadMore: async () => {
          const state = get()
          if (state.isLoadingMore || !state.searchResult) return
          
          const nextPage = state.searchQuery.page + 1
          if (nextPage > state.searchResult.pagination.totalPages) return
          
          set({ isLoadingMore: true })
          
          try {
            const result = await companiesFrontendService.searchCompanies({
              ...state.searchQuery,
              page: nextPage
            })
            
            set({
              searchResult: {
                ...result,
                companies: [...state.companies, ...result.companies]
              },
              companies: [...state.companies, ...result.companies],
              searchQuery: { ...state.searchQuery, page: nextPage },
              isLoadingMore: false
            })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to load more',
              isLoadingMore: false
            })
          }
        },

        goToPage: async (page) => {
          const state = get()
          await state.searchCompanies({ page })
        },

        clearCache: () => {
          companiesFrontendService.clearCache()
          set({ lastSearchTime: 0 })
        },

        refreshData: async () => {
          const state = get()
          state.clearCache()
          await state.searchCompanies({ page: 1 })
        },

        saveSearch: (query, name) => {
          const state = get()
          const savedSearch = { ...query, name }
          const newSavedSearches = [...state.savedSearches, savedSearch]
          
          set({ savedSearches: newSavedSearches })
        },

        loadSavedSearch: (index) => {
          const state = get()
          const savedSearch = state.savedSearches[index]
          if (savedSearch) {
            state.searchCompanies(savedSearch)
          }
        },

        deleteSavedSearch: (index) => {
          const state = get()
          const newSavedSearches = state.savedSearches.filter((_, i) => i !== index)
          set({ savedSearches: newSavedSearches })
        },

        clearError: () => set({ error: null }),
        clearSearchError: () => set({ searchError: null })
      }),
      {
        name: 'companies-frontend-store',
        partialize: (state) => ({
          followedCompanies: Array.from(state.followedCompanies),
          savedSearches: state.savedSearches,
          viewMode: state.viewMode,
          showFilters: state.showFilters
        }),
        onRehydrateStorage: () => (state) => {
          if (state?.followedCompanies) {
            state.followedCompanies = new Set(state.followedCompanies as string[])
          }
        }
      }
    ),
    { name: 'companies-frontend' }
  )
)

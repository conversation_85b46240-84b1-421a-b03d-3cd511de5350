// stores/auth.store.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { errorService, type AppError } from '@/lib/error-service'

// Types - Updated to match auth service response
export interface User {
  id: string  // Changed from _id to id to match auth service response
  email: string
  role: 'admin' | 'company_admin' | 'recruiter' | 'job_seeker'
  profile: {
    firstName: string
    lastName: string
    fullName: string  // Added to match auth service response
    avatar?: string
    phone?: string
    location?: {
      city?: string
      state?: string
      country?: string
    }
  }
  isEmailVerified: boolean  // Added to match auth service response
  companyId?: string  // Added to match auth service response
  preferences?: {
    emailNotifications: boolean
    jobAlerts: boolean
    marketingEmails: boolean
    theme: 'light' | 'dark' | 'system'
  }
  subscription?: {
    plan: 'free' | 'premium' | 'enterprise'
    status: 'active' | 'inactive' | 'cancelled'
  }
  isActive?: boolean
  lastLogin?: Date
  createdAt?: Date
  updatedAt?: Date
}

export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  role?: 'job_seeker' | 'company_admin'
  phone?: string
  location?: {
    city?: string
    state?: string
    country?: string
  }
  company?: {
    name?: string
    website?: string
    industry?: string
    size?: string
    description?: string
    location?: {
      city?: string
      state?: string
      country?: string
    }
  }
}

export interface UpdateProfileData {
  firstName?: string
  lastName?: string
  phone?: string
  location?: {
    city?: string
    state?: string
    country?: string
  }
  preferences?: Partial<User['preferences']>
}

interface AuthState {
  user: User | null
  token: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: AppError | null

  // Loading states for specific actions
  loginLoading: boolean
  registerLoading: boolean
  profileUpdateLoading: boolean
  logoutLoading: boolean
}

interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<void>
  register: (userData: RegisterData) => Promise<void>
  logout: () => Promise<void>
  refreshAuth: () => Promise<void>
  updateProfile: (profileData: UpdateProfileData) => Promise<void>
  clearError: () => void
  setLoading: (action: keyof Pick<AuthState, 'loginLoading' | 'registerLoading' | 'profileUpdateLoading' | 'logoutLoading'>, loading: boolean) => void
  checkAuthStatus: () => Promise<void>
  getRoleBasedRedirectPath: (role: string) => string
  hydrateFromStorage: () => void
}

// API Service functions (these would be implemented in a separate service file)
const AuthAPI = {
  async login(credentials: LoginCredentials) {
    try {
      console.log('🔍 AuthAPI.login called with:', {
        email: credentials.email,
        hasPassword: !!credentials.password,
        rememberMe: credentials.rememberMe
      })

      const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(credentials)
      })

      console.log('🔍 Login API response status:', response.status)
      console.log('🔍 Login API response ok:', response.ok)

      if (!response.ok) {
        let errorData
        try {
          errorData = await response.json()
        } catch (parseError) {
          console.error('🔍 Failed to parse error response:', parseError)
          throw new Error(`Login failed with status ${response.status}`)
        }

        console.log('🔍 Login API error data:', errorData)
        const errorObj = new Error(errorData.error || 'Login failed')
        Object.assign(errorObj, { status: response.status })
        throw errorObj
      }

      const result = await response.json()
      console.log('🔍 Login API success result:', result)
      return result
    } catch (error) {
      console.error('🔍 AuthAPI.login error:', error)
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Network error: Unable to connect to the server. Please check your internet connection.')
      }
      throw error
    }
  },

  async register(userData: RegisterData) {
    const response = await fetch('/api/v1/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(userData)
    })

    if (!response.ok) {
      const error = await response.json()
      const errorObj = new Error(error.error || 'Registration failed')
      Object.assign(errorObj, { status: response.status })
      throw errorObj
    }

    return response.json()
  },

  async refreshToken(refreshToken: string) {
    const response = await fetch('/api/v1/auth/refresh', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refreshToken })
    })

    if (!response.ok) {
      throw new Error('Token refresh failed')
    }

    return response.json()
  },

  async updateProfile(profileData: UpdateProfileData, token: string) {
    const response = await fetch('/api/v1/auth/profile', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(profileData)
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Profile update failed')
    }

    return response.json()
  },

  async getCurrentUser(token: string) {
    const response = await fetch('/api/v1/auth/me', {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      const error = await response.json()
      const errorObj = new Error(error.error || 'Failed to get current user')
      Object.assign(errorObj, { status: response.status })
      throw errorObj
    }

    const data = await response.json()
    console.log('🔍 getCurrentUser API response:', data)

    // Handle the API response structure: { success: true, data: { user: userData } }
    if (data.success && data.data && data.data.user) {
      return data.data.user
    }

    // Fallback for other response structures
    return data.user || data.data || data
  }
}

export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      loginLoading: false,
      registerLoading: false,
      profileUpdateLoading: false,
      logoutLoading: false,

      // Actions
      login: async (credentials) => {
        set({ loginLoading: true, error: null })
        try {
          const response = await AuthAPI.login(credentials)

          console.log('🔍 Auth API response:', response)
          console.log('🔍 Response data:', response.data)
          console.log('🔍 Setting user in store:', response.data?.user)

          // Extract data from the API response wrapper
          // The API returns { success: true, data: authData, meta: {...} }
          const authData = response.data

          const newState = {
            user: authData.user,
            token: authData.tokens?.accessToken || authData.token,
            refreshToken: authData.tokens?.refreshToken || authData.refreshToken,
            isAuthenticated: true,
            loginLoading: false
          }

          console.log('🔍 New auth state to set:', newState)

          set(newState)

          // Verify the user was set correctly
          const currentState = get()
          console.log('🔍 Current auth state after login:', {
            user: currentState.user,
            token: currentState.token ? 'Present' : 'Missing',
            isAuthenticated: currentState.isAuthenticated
          })

          // Company should be created during registration, not login
          // If company admin has no company, they should contact support or re-register
          if (authData.user?.role === 'company_admin' && !authData.user?.companyId) {
            console.warn('Company admin without company detected. Company should have been created during registration.')
          }

          return authData
        } catch (error) {
          const appError = errorService.logError(error as Error, 'auth_login')
          set({
            error: appError,
            loginLoading: false
          })
          throw appError
        }
      },

      register: async (userData) => {
        set({ registerLoading: true, error: null })
        try {
          const response = await AuthAPI.register(userData)
          console.log('🔍 Register API response:', response)

          // Extract data from the API response wrapper
          const authData = response.data

          set({
            user: authData.user,
            token: authData.tokens?.accessToken || authData.token,
            refreshToken: authData.tokens?.refreshToken || authData.refreshToken,
            isAuthenticated: true,
            registerLoading: false
          })
          return authData
        } catch (error) {
          const appError = errorService.logError(error as Error, 'auth_register')
          set({
            error: appError,
            registerLoading: false
          })
          throw appError
        }
      },

      logout: async () => {
        set({ logoutLoading: true })
        try {
          // Clear all auth state
          set({
            user: null,
            token: null,
            refreshToken: null,
            isAuthenticated: false,
            logoutLoading: false,
            error: null
          })

          // Clear persisted storage
          localStorage.removeItem('auth-storage')
        } catch (error) {
          set({ logoutLoading: false })
          console.error('Logout error:', error)
        }
      },

      refreshAuth: async () => {
        const { refreshToken } = get()
        if (!refreshToken) {
          throw new Error('No refresh token available')
        }

        try {
          const response = await AuthAPI.refreshToken(refreshToken)
          set({
            token: response.token,
            refreshToken: response.refreshToken || refreshToken
          })
        } catch (error) {
          // If refresh fails, logout user
          get().logout()
          throw error
        }
      },

      updateProfile: async (profileData) => {
        const { token } = get()
        if (!token) {
          throw new Error('No authentication token')
        }

        set({ profileUpdateLoading: true, error: null })
        try {
          const updatedUser = await AuthAPI.updateProfile(profileData, token)
          set({
            user: updatedUser,
            profileUpdateLoading: false
          })
        } catch (error) {
          const appError = errorService.logError(error as Error, 'auth_update_profile')
          set({
            error: appError,
            profileUpdateLoading: false
          })
          throw appError
        }
      },

      checkAuthStatus: async () => {
        const { token, refreshToken } = get()

        console.log('🔍 Checking auth status - token present:', !!token)
        console.log('🔍 Checking auth status - refresh token present:', !!refreshToken)

        if (!token) {
          console.log('🔍 No token found, clearing auth state')
          set({
            user: null,
            isAuthenticated: false,
            token: null,
            refreshToken: null
          })
          return
        }

        try {
          console.log('🔍 Attempting to get current user with token')
          const user = await AuthAPI.getCurrentUser(token)
          console.log('🔍 Successfully retrieved user:', {
            email: user?.email,
            role: user?.role,
            id: user?.id,
            fullUser: user
          })
          set({
            user,
            isAuthenticated: true,
            error: null
          })
        } catch (error: any) {
          console.log('🔍 Token validation failed:', error.message)

          // If we have a refresh token, try to refresh
          if (refreshToken) {
            try {
              console.log('🔍 Attempting to refresh token')
              await get().refreshAuth()
              console.log('🔍 Token refresh successful, retrying user fetch')

              // Retry getting current user with new token
              const newToken = get().token
              if (newToken) {
                const user = await AuthAPI.getCurrentUser(newToken)
                console.log('🔍 Successfully retrieved user after refresh:', user?.email)
                set({
                  user,
                  isAuthenticated: true,
                  error: null
                })
              }
            } catch (refreshError) {
              console.log('🔍 Token refresh failed, logging out user')
              // Token refresh failed, logout user
              get().logout()
            }
          } else {
            console.log('🔍 No refresh token available, logging out user')
            // No refresh token, logout user
            get().logout()
          }
        }
      },

      getRoleBasedRedirectPath: (role: string) => {
        console.log('Getting redirect path for role:', role)
        switch (role) {
          case 'company_admin':
            console.log('Redirecting company_admin to /company-dashboard')
            return '/company-dashboard'
          case 'recruiter':
            console.log('Redirecting recruiter to /company-dashboard')
            return '/company-dashboard'
          case 'job_seeker':
            console.log('Redirecting job_seeker to /client-dashboard')
            return '/client-dashboard'
          case 'admin':
            console.log('Redirecting admin to /admin')
            return '/admin'
          default:
            console.log('Unknown role, defaulting to /client-dashboard')
            return '/client-dashboard'
        }
      },

      clearError: () => set({ error: null }),

      setLoading: (action, loading) => {
        set({ [action]: loading })
      },

      hydrateFromStorage: () => {
        if (typeof window !== 'undefined') {
          try {
            const stored = localStorage.getItem('auth-storage')
            if (stored) {
              const parsedState = JSON.parse(stored)
              console.log('🔍 Manual hydration from storage:', parsedState)
              set({
                user: parsedState.state?.user || null,
                token: parsedState.state?.token || null,
                refreshToken: parsedState.state?.refreshToken || null,
                isAuthenticated: parsedState.state?.isAuthenticated || false
              })
            }
          } catch (error) {
            console.error('🔍 Failed to hydrate from storage:', error)
          }
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated
      }),
      // Ensure proper hydration
      onRehydrateStorage: () => (state) => {
        console.log('🔍 Auth store: Rehydrating from storage')
        if (state) {
          console.log('🔍 Auth store: Rehydrated state:', {
            hasUser: !!state.user,
            hasToken: !!state.token,
            hasRefreshToken: !!state.refreshToken,
            isAuthenticated: state.isAuthenticated
          })
        }
      },
      // Skip hydration on server side
      skipHydration: typeof window === 'undefined',
    }
  )
)

const mongoose = require('mongoose')

// MongoDB connection - using the actual URI from .env.local
const MONGODB_URI = 'mongodb+srv://winstonmhango23:<EMAIL>/?retryWrites=true&w=majority&appName=jobs'

async function debugDatabaseCompanies() {
  try {
    console.log('🔍 Debugging Database Companies')
    console.log('================================')
    
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI)
    console.log('✅ Connected to MongoDB')
    
    // Get all collections
    const collections = await mongoose.connection.db.listCollections().toArray()
    console.log('\n📋 Available Collections:')
    collections.forEach(col => {
      console.log(`  - ${col.name}`)
    })
    
    // Check companies collection
    const Company = mongoose.connection.db.collection('companies')
    const companyCount = await Company.countDocuments()
    console.log(`\n🏢 Companies Collection: ${companyCount} documents`)
    
    if (companyCount > 0) {
      console.log('\n📊 Company Documents:')
      const companies = await Company.find({}).toArray()
      companies.forEach((company, index) => {
        console.log(`\n${index + 1}. Company: ${company.name}`)
        console.log(`   ID: ${company._id}`)
        console.log(`   Slug: ${company.slug}`)
        console.log(`   Industry: ${company.industry}`)
        console.log(`   Size: ${company.size}`)
        console.log(`   Admins: ${company.admins?.length || 0}`)
        console.log(`   Team Members: ${company.teamMembers?.length || 0}`)
        console.log(`   Active: ${company.isActive}`)
        console.log(`   Verified: ${company.isVerified}`)
        if (company.locations && company.locations.length > 0) {
          console.log(`   Location: ${company.locations[0].city}, ${company.locations[0].country}`)
        }
      })
    } else {
      console.log('   No companies found in database')
    }
    
    // Check users collection
    const User = mongoose.connection.db.collection('users')
    const userCount = await User.countDocuments()
    console.log(`\n👥 Users Collection: ${userCount} documents`)
    
    if (userCount > 0) {
      console.log('\n📊 User Documents (with company info):')
      const users = await User.find({}).toArray()
      users.forEach((user, index) => {
        console.log(`\n${index + 1}. User: ${user.email}`)
        console.log(`   ID: ${user._id}`)
        console.log(`   Role: ${user.role}`)
        console.log(`   Company ID: ${user.companyId || 'None'}`)
        console.log(`   Active: ${user.isActive}`)
        console.log(`   Email Verified: ${user.isEmailVerified}`)
        if (user.profile) {
          console.log(`   Name: ${user.profile.firstName} ${user.profile.lastName}`)
        }
      })
      
      // Check for company admins without companies
      const companyAdmins = await User.find({ role: 'company_admin' }).toArray()
      console.log(`\n🏢 Company Admins: ${companyAdmins.length}`)
      companyAdmins.forEach((admin, index) => {
        console.log(`\n${index + 1}. Admin: ${admin.email}`)
        console.log(`   Has Company ID: ${admin.companyId ? 'YES' : 'NO'}`)
        if (admin.companyId) {
          console.log(`   Company ID: ${admin.companyId}`)
        }
      })
    }
    
    // Check for orphaned relationships
    console.log('\n🔗 Checking Relationships:')
    const usersWithCompanies = await User.find({ companyId: { $exists: true, $ne: null } }).toArray()
    console.log(`   Users with company IDs: ${usersWithCompanies.length}`)
    
    for (const user of usersWithCompanies) {
      const companyExists = await Company.findOne({ _id: user.companyId })
      console.log(`   User ${user.email} -> Company ${user.companyId}: ${companyExists ? 'EXISTS' : 'MISSING'}`)
    }
    
  } catch (error) {
    console.error('❌ Error:', error)
  } finally {
    await mongoose.disconnect()
    console.log('\n✅ Disconnected from MongoDB')
  }
}

// Run the debug function
debugDatabaseCompanies()

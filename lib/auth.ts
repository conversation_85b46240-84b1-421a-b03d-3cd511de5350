import jwt from 'jsonwebtoken'
import { NextRequest } from 'next/server'

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key'
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d'

export interface JWTPayload {
  userId: string
  email: string
  role: string
  iat?: number
  exp?: number
}

/**
 * Generate JWT token
 */
export function generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN
  })
}

/**
 * Verify JWT token
 */
export function verifyToken(token: string): JWTPayload | null {
  try {
    return jwt.verify(token, JWT_SECRET) as JWTPayload
  } catch (error) {
    return null
  }
}

/**
 * Extract token from Authorization header
 */
export function extractTokenFromHeader(authHeader: string | null): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null
  }
  return authHeader.substring(7)
}

/**
 * Get user from request
 */
export function getUserFromRequest(request: NextRequest): JWTPayload | null {
  const authHeader = request.headers.get('authorization')
  const token = extractTokenFromHeader(authHeader)
  
  if (!token) {
    return null
  }
  
  return verifyToken(token)
}

/**
 * Check if user has required role
 */
export function hasRole(user: JWTPayload | null, requiredRole: string | string[]): boolean {
  if (!user) return false
  
  if (Array.isArray(requiredRole)) {
    return requiredRole.includes(user.role)
  }
  
  return user.role === requiredRole
}

/**
 * Check if user is admin
 */
export function isAdmin(user: JWTPayload | null): boolean {
  return hasRole(user, 'admin')
}

/**
 * Check if user is company
 */
export function isCompany(user: JWTPayload | null): boolean {
  return hasRole(user, 'company')
}

/**
 * Check if user is job seeker
 */
export function isJobSeeker(user: JWTPayload | null): boolean {
  return hasRole(user, 'jobseeker')
}

/**
 * Generate password reset token
 */
export function generatePasswordResetToken(): string {
  return jwt.sign(
    { type: 'password_reset', timestamp: Date.now() },
    JWT_SECRET,
    { expiresIn: '1h' }
  )
}

/**
 * Verify password reset token
 */
export function verifyPasswordResetToken(token: string): boolean {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any
    return decoded.type === 'password_reset'
  } catch (error) {
    return false
  }
}

/**
 * Generate email verification token
 */
export function generateEmailVerificationToken(email: string): string {
  return jwt.sign(
    { email, type: 'email_verification' },
    JWT_SECRET,
    { expiresIn: '24h' }
  )
}

/**
 * Verify email verification token
 */
export function verifyEmailVerificationToken(token: string): { email: string } | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any
    if (decoded.type === 'email_verification') {
      return { email: decoded.email }
    }
    return null
  } catch (error) {
    return null
  }
}

// Note: requireAuth middleware moved to lib/middleware/auth.middleware.ts
// This file now contains only utility functions

/**
 * Rate limiting helper
 */
export class RateLimiter {
  private attempts: Map<string, { count: number; resetTime: number }> = new Map()
  
  constructor(
    private maxAttempts: number = 5,
    private windowMs: number = 15 * 60 * 1000 // 15 minutes
  ) {}
  
  isAllowed(identifier: string): boolean {
    const now = Date.now()
    const attempt = this.attempts.get(identifier)
    
    if (!attempt || now > attempt.resetTime) {
      this.attempts.set(identifier, { count: 1, resetTime: now + this.windowMs })
      return true
    }
    
    if (attempt.count >= this.maxAttempts) {
      return false
    }
    
    attempt.count++
    return true
  }
  
  reset(identifier: string): void {
    this.attempts.delete(identifier)
  }
}

// Global rate limiter instances
export const loginRateLimiter = new RateLimiter(5, 15 * 60 * 1000) // 5 attempts per 15 minutes
export const apiRateLimiter = new RateLimiter(100, 60 * 1000) // 100 requests per minute

import mongoose, { Schema, Document } from 'mongoose'

export interface IJob extends Document {
  title: string
  description: string
  companyId: mongoose.Types.ObjectId
  location: {
    city?: string
    state?: string
    country?: string
    remote?: boolean
    address?: string
  }
  salary?: {
    min?: number
    max?: number
    currency?: string
    period?: 'hourly' | 'monthly' | 'yearly'
  }
  requirements: string[]
  benefits?: string[]
  type: 'full-time' | 'part-time' | 'contract' | 'internship'
  level: 'entry' | 'mid' | 'senior' | 'executive'
  category: string
  tags: string[]
  status: 'draft' | 'active' | 'paused' | 'closed' | 'expired'
  isActive: boolean
  isRemote: boolean
  applicationDeadline?: Date
  createdBy: mongoose.Types.ObjectId
  applications: mongoose.Types.ObjectId[] // Array of application IDs
  applicationsCount: number
  viewsCount: number
  publishedAt?: Date
  closedAt?: Date
  closedBy?: mongoose.Types.ObjectId
  closedReason?: string
  expiredAt?: Date
  statusHistory?: Array<{
    fromStatus: string
    toStatus: string
    changedBy: mongoose.Types.ObjectId
    reason?: string
    timestamp: Date
  }>
  createdAt: Date
  updatedAt: Date
}

const jobSchema = new Schema<IJob>({
  title: {
    type: String,
    required: [true, 'Job title is required'],
    trim: true,
    maxlength: [200, 'Job title cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Job description is required'],
    trim: true,
    maxlength: [10000, 'Job description cannot exceed 10000 characters']
  },
  companyId: {
    type: Schema.Types.ObjectId,
    ref: 'Company',
    required: [true, 'Company ID is required']
  },
  location: {
    city: {
      type: String,
      trim: true,
      maxlength: [100, 'City name cannot exceed 100 characters']
    },
    state: {
      type: String,
      trim: true,
      maxlength: [100, 'State name cannot exceed 100 characters']
    },
    country: {
      type: String,
      trim: true,
      maxlength: [100, 'Country name cannot exceed 100 characters']
    },
    remote: {
      type: Boolean,
      default: false
    },
    address: {
      type: String,
      trim: true,
      maxlength: [500, 'Address cannot exceed 500 characters']
    }
  },
  salary: {
    min: {
      type: Number,
      min: [0, 'Minimum salary cannot be negative']
    },
    max: {
      type: Number,
      min: [0, 'Maximum salary cannot be negative'],
      validate: {
        validator: function(this: IJob, value: number) {
          return !this.salary?.min || value >= this.salary.min
        },
        message: 'Maximum salary must be greater than or equal to minimum salary'
      }
    },
    currency: {
      type: String,
      enum: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'INR'],
      default: 'USD'
    },
    period: {
      type: String,
      enum: ['hourly', 'monthly', 'yearly'],
      default: 'yearly'
    }
  },
  requirements: [{
    type: String,
    trim: true,
    maxlength: [500, 'Each requirement cannot exceed 500 characters']
  }],
  benefits: [{
    type: String,
    trim: true,
    maxlength: [200, 'Each benefit cannot exceed 200 characters']
  }],
  type: {
    type: String,
    enum: ['full-time', 'part-time', 'contract', 'internship'],
    required: [true, 'Job type is required']
  },
  level: {
    type: String,
    enum: ['entry', 'mid', 'senior', 'executive'],
    required: [true, 'Job level is required']
  },
  category: {
    type: String,
    required: [true, 'Job category is required'],
    trim: true,
    maxlength: [100, 'Category cannot exceed 100 characters']
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true,
    maxlength: [50, 'Each tag cannot exceed 50 characters']
  }],
  status: {
    type: String,
    enum: ['draft', 'active', 'paused', 'closed', 'expired'],
    default: 'draft'
  },
  isActive: {
    type: Boolean,
    default: false
  },
  isRemote: {
    type: Boolean,
    default: false
  },
  applicationDeadline: {
    type: Date,
    validate: {
      validator: function(value: Date) {
        return !value || value > new Date()
      },
      message: 'Application deadline must be in the future'
    }
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Creator ID is required']
  },
  applications: [{
    type: Schema.Types.ObjectId,
    ref: 'Application'
  }],
  applicationsCount: {
    type: Number,
    default: 0,
    min: [0, 'Applications count cannot be negative']
  },
  viewsCount: {
    type: Number,
    default: 0,
    min: [0, 'Views count cannot be negative']
  },
  publishedAt: {
    type: Date
  },
  closedAt: {
    type: Date
  },
  closedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  closedReason: {
    type: String,
    trim: true,
    maxlength: [500, 'Closed reason cannot exceed 500 characters']
  },
  expiredAt: {
    type: Date
  },
  statusHistory: [{
    fromStatus: {
      type: String,
      enum: ['draft', 'active', 'paused', 'closed', 'expired']
    },
    toStatus: {
      type: String,
      enum: ['draft', 'active', 'paused', 'closed', 'expired'],
      required: true
    },
    changedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    reason: {
      type: String,
      trim: true,
      maxlength: [500, 'Status change reason cannot exceed 500 characters']
    },
    timestamp: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  }
})

// Indexes for better query performance
jobSchema.index({ companyId: 1, status: 1 })
jobSchema.index({ companyId: 1, isActive: 1 })
jobSchema.index({ status: 1, isActive: 1 })
jobSchema.index({ category: 1, status: 1 })
jobSchema.index({ type: 1, level: 1, status: 1 })
jobSchema.index({ 'location.city': 1, 'location.state': 1, 'location.country': 1 })
jobSchema.index({ isRemote: 1, status: 1 })
jobSchema.index({ tags: 1, status: 1 })
jobSchema.index({ createdAt: -1 })
jobSchema.index({ applicationDeadline: 1, status: 1 })

// Text search index
jobSchema.index({
  title: 'text',
  description: 'text',
  category: 'text',
  tags: 'text'
})

// Virtual for checking if application deadline has passed
jobSchema.virtual('isExpired').get(function(this: IJob) {
  return this.applicationDeadline ? this.applicationDeadline < new Date() : false
})

// Pre-save middleware to set isRemote based on location and sync isActive with status
jobSchema.pre('save', function(this: IJob) {
  if (this.location?.remote) {
    this.isRemote = true
  }

  // Sync isActive with status
  this.isActive = this.status === 'active'

  // Set publishedAt when status changes to active for the first time
  if (this.status === 'active' && !this.publishedAt) {
    this.publishedAt = new Date()
  }
})

// Static methods
jobSchema.statics.findActiveJobs = function() {
  return this.find({ status: 'active', $or: [{ applicationDeadline: { $gte: new Date() } }, { applicationDeadline: null }] })
}

jobSchema.statics.findByCompany = function(companyId: string) {
  return this.find({ companyId, status: { $in: ['active', 'paused'] } })
}

jobSchema.statics.searchJobs = function(searchTerm: string) {
  return this.find({
    $text: { $search: searchTerm },
    status: 'active'
  }).sort({ score: { $meta: 'textScore' } })
}

// Instance methods
jobSchema.methods.incrementViews = function() {
  this.viewsCount += 1
  return this.save()
}

jobSchema.methods.addApplication = function(applicationId: mongoose.Types.ObjectId) {
  // Add application to the array if not already present
  if (!this.applications.includes(applicationId)) {
    this.applications.push(applicationId)
    this.applicationsCount = this.applications.length
  }
  return this.save()
}

jobSchema.methods.removeApplication = function(applicationId: mongoose.Types.ObjectId) {
  // Remove application from the array
  this.applications = this.applications.filter(id => !id.equals(applicationId))
  this.applicationsCount = this.applications.length
  return this.save()
}

jobSchema.methods.incrementApplications = function() {
  this.applicationsCount += 1
  return this.save()
}

jobSchema.methods.decrementApplications = function() {
  if (this.applicationsCount > 0) {
    this.applicationsCount -= 1
    return this.save()
  }
  return Promise.resolve(this)
}

export const Job = mongoose.models.Job || mongoose.model<IJob>('Job', jobSchema)

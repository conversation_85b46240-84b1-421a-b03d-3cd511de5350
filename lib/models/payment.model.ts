import mongoose, { Document, Schema } from 'mongoose'

export interface IPayment extends Document {
  company: mongoose.Types.ObjectId
  subscription?: mongoose.Types.ObjectId
  invoice?: mongoose.Types.ObjectId
  amount: number
  currency: string
  status: 'pending' | 'succeeded' | 'failed' | 'canceled' | 'refunded' | 'partially_refunded'
  paymentMethod: 'card' | 'bank_transfer' | 'paypal' | 'stripe' | 'manual'
  paymentIntentId?: string
  stripePaymentId?: string
  stripeChargeId?: string
  description: string
  metadata?: Record<string, any>
  failureReason?: string
  refundAmount?: number
  refundReason?: string
  refundedAt?: Date
  paidAt?: Date
  dueDate?: Date
  createdBy?: mongoose.Types.ObjectId
  createdAt: Date
  updatedAt: Date
}

const PaymentSchema = new Schema<IPayment>({
  company: {
    type: Schema.Types.ObjectId,
    ref: 'Company',
    required: true
  },
  subscription: {
    type: Schema.Types.ObjectId,
    ref: 'Subscription'
  },
  invoice: {
    type: Schema.Types.ObjectId,
    ref: 'Invoice'
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    default: 'USD',
    enum: ['USD', 'EUR', 'GBP', 'CAD']
  },
  status: {
    type: String,
    enum: ['pending', 'succeeded', 'failed', 'canceled', 'refunded', 'partially_refunded'],
    default: 'pending'
  },
  paymentMethod: {
    type: String,
    enum: ['card', 'bank_transfer', 'paypal', 'stripe', 'manual'],
    default: 'stripe'
  },
  paymentIntentId: String,
  stripePaymentId: String,
  stripeChargeId: String,
  description: {
    type: String,
    required: true
  },
  metadata: {
    type: Schema.Types.Mixed,
    default: {}
  },
  failureReason: String,
  refundAmount: {
    type: Number,
    min: 0
  },
  refundReason: String,
  refundedAt: Date,
  paidAt: Date,
  dueDate: Date,
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
})

// Indexes
PaymentSchema.index({ company: 1 })
PaymentSchema.index({ subscription: 1 })
PaymentSchema.index({ status: 1 })
PaymentSchema.index({ paidAt: -1 })
PaymentSchema.index({ stripePaymentId: 1 })
PaymentSchema.index({ stripeChargeId: 1 })
PaymentSchema.index({ createdAt: -1 })

// Virtual for net amount (amount - refund)
PaymentSchema.virtual('netAmount').get(function() {
  return this.amount - (this.refundAmount || 0)
})

// Virtual for is refunded
PaymentSchema.virtual('isRefunded').get(function() {
  return this.status === 'refunded' || this.status === 'partially_refunded'
})

// Ensure virtual fields are serialized
PaymentSchema.set('toJSON', { virtuals: true })
PaymentSchema.set('toObject', { virtuals: true })

export const Payment = mongoose.models.Payment || mongoose.model<IPayment>('Payment', PaymentSchema)

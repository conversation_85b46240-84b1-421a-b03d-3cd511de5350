import mongoose, { Document, Schema } from 'mongoose'

export interface IDispute extends Document {
  applicationId: mongoose.Types.ObjectId
  disputeType: 'application_status' | 'discrimination' | 'unfair_process' | 'communication' | 'other'
  status: 'open' | 'investigating' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  reportedBy: 'applicant' | 'company'
  reportedById: mongoose.Types.ObjectId
  description: string
  evidence?: string[]
  assignedTo?: mongoose.Types.ObjectId
  resolution?: string
  resolutionDate?: Date
  resolvedBy?: mongoose.Types.ObjectId
  messages: Array<{
    _id?: mongoose.Types.ObjectId
    sender: {
      _id: mongoose.Types.ObjectId
      firstName: string
      lastName: string
      role: string
    }
    message: string
    timestamp: Date
    isInternal: boolean
  }>
  createdAt: Date
  updatedAt: Date
}

const DisputeSchema = new Schema<IDispute>({
  applicationId: {
    type: Schema.Types.ObjectId,
    ref: 'Application',
    required: true
  },
  disputeType: {
    type: String,
    enum: ['application_status', 'discrimination', 'unfair_process', 'communication', 'other'],
    required: true
  },
  status: {
    type: String,
    enum: ['open', 'investigating', 'resolved', 'closed'],
    default: 'open'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  reportedBy: {
    type: String,
    enum: ['applicant', 'company'],
    required: true
  },
  reportedById: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  description: {
    type: String,
    required: true,
    maxlength: 2000
  },
  evidence: [{
    type: String,
    maxlength: 500
  }],
  assignedTo: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  resolution: {
    type: String,
    maxlength: 2000
  },
  resolutionDate: Date,
  resolvedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  messages: [{
    sender: {
      _id: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      firstName: {
        type: String,
        required: true
      },
      lastName: {
        type: String,
        required: true
      },
      role: {
        type: String,
        required: true
      }
    },
    message: {
      type: String,
      required: true,
      maxlength: 1000
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    isInternal: {
      type: Boolean,
      default: false
    }
  }]
}, {
  timestamps: true
})

// Indexes for better query performance
DisputeSchema.index({ applicationId: 1 })
DisputeSchema.index({ status: 1, priority: 1 })
DisputeSchema.index({ reportedBy: 1, status: 1 })
DisputeSchema.index({ assignedTo: 1, status: 1 })
DisputeSchema.index({ createdAt: -1 })
DisputeSchema.index({ disputeType: 1, status: 1 })

export const Dispute = mongoose.models.Dispute || mongoose.model<IDispute>('Dispute', DisputeSchema)

import mongoose, { Schema, Document } from 'mongoose'

// Award Template Interface
export interface IAwardTemplate extends Document {
  name: string
  slug: string
  description: string
  categoryId: mongoose.Types.ObjectId
  badgeIcon: string
  badgeColor: string
  minimumScore: number // percentage required to earn this award
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary'
  benefits: string[]
  certificateTemplate: string // HTML template for certificate
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// User Award Interface
export interface IUserAward extends Document {
  userId: mongoose.Types.ObjectId
  awardTemplateId: mongoose.Types.ObjectId
  quizAttemptId: mongoose.Types.ObjectId
  earnedAt: Date
  score: number
  certificateUrl?: string
  isDownloaded: boolean
  downloadedAt?: Date
  isPublic: boolean // whether to show on public profile
  createdAt: Date
}

// Award Template Schema
const awardTemplateSchema = new Schema<IAwardTemplate>({
  name: {
    type: String,
    required: true,
    trim: true
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true
  },
  description: {
    type: String,
    required: true
  },
  categoryId: {
    type: Schema.Types.ObjectId,
    ref: 'QuizCategory',
    required: true
  },
  badgeIcon: {
    type: String,
    required: true
  },
  badgeColor: {
    type: String,
    required: true
  },
  minimumScore: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  rarity: {
    type: String,
    enum: ['common', 'uncommon', 'rare', 'epic', 'legendary'],
    default: 'common'
  },
  benefits: [{
    type: String,
    trim: true
  }],
  certificateTemplate: {
    type: String,
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  }
})

// User Award Schema
const userAwardSchema = new Schema<IUserAward>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  awardTemplateId: {
    type: Schema.Types.ObjectId,
    ref: 'AwardTemplate',
    required: true
  },
  quizAttemptId: {
    type: Schema.Types.ObjectId,
    ref: 'QuizAttempt',
    required: true
  },
  earnedAt: {
    type: Date,
    required: true,
    default: Date.now
  },
  score: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  certificateUrl: {
    type: String
  },
  isDownloaded: {
    type: Boolean,
    default: false
  },
  downloadedAt: {
    type: Date
  },
  isPublic: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  }
})

// Indexes for better performance
awardTemplateSchema.index({ categoryId: 1, isActive: 1 })
awardTemplateSchema.index({ slug: 1 })
awardTemplateSchema.index({ minimumScore: 1 })

userAwardSchema.index({ userId: 1, earnedAt: -1 })
userAwardSchema.index({ awardTemplateId: 1 })
userAwardSchema.index({ quizAttemptId: 1 })
userAwardSchema.index({ userId: 1, isPublic: 1 })

// Compound index for preventing duplicate awards
userAwardSchema.index({ userId: 1, awardTemplateId: 1 }, { unique: true })

// Export models
export const AwardTemplate = mongoose.models.AwardTemplate || mongoose.model<IAwardTemplate>('AwardTemplate', awardTemplateSchema)
export const UserAward = mongoose.models.UserAward || mongoose.model<IUserAward>('UserAward', userAwardSchema)

import mongoose, { Document, Schema } from 'mongoose'

export interface IBillingPlan extends Document {
  name: string
  slug: string
  description: string
  price: {
    monthly: number
    yearly: number
    currency: string
  }
  features: Array<{
    name: string
    description?: string
    included: boolean
    limit?: number
  }>
  limits: {
    jobPostings: number
    featuredJobs: number
    applicationsPerJob: number
    teamMembers: number
    customBranding: boolean
    prioritySupport: boolean
    analytics: boolean
    apiAccess: boolean
  }
  isActive: boolean
  isPopular: boolean
  sortOrder: number
  trialDays: number
  createdBy: mongoose.Types.ObjectId
  createdAt: Date
  updatedAt: Date
}

const BillingPlanSchema = new Schema<IBillingPlan>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    maxlength: 500
  },
  price: {
    monthly: {
      type: Number,
      required: true,
      min: 0
    },
    yearly: {
      type: Number,
      required: true,
      min: 0
    },
    currency: {
      type: String,
      default: 'USD',
      enum: ['USD', 'EUR', 'GBP', 'CAD']
    }
  },
  features: [{
    name: {
      type: String,
      required: true
    },
    description: String,
    included: {
      type: Boolean,
      default: true
    },
    limit: Number
  }],
  limits: {
    jobPostings: {
      type: Number,
      default: -1 // -1 means unlimited
    },
    featuredJobs: {
      type: Number,
      default: 0
    },
    applicationsPerJob: {
      type: Number,
      default: -1
    },
    teamMembers: {
      type: Number,
      default: 1
    },
    customBranding: {
      type: Boolean,
      default: false
    },
    prioritySupport: {
      type: Boolean,
      default: false
    },
    analytics: {
      type: Boolean,
      default: false
    },
    apiAccess: {
      type: Boolean,
      default: false
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isPopular: {
    type: Boolean,
    default: false
  },
  sortOrder: {
    type: Number,
    default: 0
  },
  trialDays: {
    type: Number,
    default: 14
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
})

// Indexes
BillingPlanSchema.index({ slug: 1 })
BillingPlanSchema.index({ isActive: 1, sortOrder: 1 })
BillingPlanSchema.index({ 'price.monthly': 1 })

export const BillingPlan = mongoose.models.BillingPlan || mongoose.model<IBillingPlan>('BillingPlan', BillingPlanSchema)

import mongoose, { Document, Schema } from 'mongoose'

export interface ISubscription extends Document {
  company: mongoose.Types.ObjectId
  plan: mongoose.Types.ObjectId
  status: 'active' | 'canceled' | 'past_due' | 'trialing' | 'paused'
  billingCycle: 'monthly' | 'yearly'
  currentPeriodStart: Date
  currentPeriodEnd: Date
  trialStart?: Date
  trialEnd?: Date
  canceledAt?: Date
  cancelAtPeriodEnd: boolean
  cancelReason?: string
  stripeSubscriptionId?: string
  stripeCustomerId?: string
  pricePerPeriod: number
  currency: string
  usage: {
    jobPostings: number
    featuredJobs: number
    teamMembers: number
    apiCalls: number
  }
  metadata?: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

const SubscriptionSchema = new Schema<ISubscription>({
  company: {
    type: Schema.Types.ObjectId,
    ref: 'Company',
    required: true
  },
  plan: {
    type: Schema.Types.ObjectId,
    ref: 'BillingPlan',
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'canceled', 'past_due', 'trialing', 'paused'],
    default: 'trialing'
  },
  billingCycle: {
    type: String,
    enum: ['monthly', 'yearly'],
    required: true
  },
  currentPeriodStart: {
    type: Date,
    required: true
  },
  currentPeriodEnd: {
    type: Date,
    required: true
  },
  trialStart: Date,
  trialEnd: Date,
  canceledAt: Date,
  cancelAtPeriodEnd: {
    type: Boolean,
    default: false
  },
  cancelReason: String,
  stripeSubscriptionId: String,
  stripeCustomerId: String,
  pricePerPeriod: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    default: 'USD',
    enum: ['USD', 'EUR', 'GBP', 'CAD']
  },
  usage: {
    jobPostings: {
      type: Number,
      default: 0
    },
    featuredJobs: {
      type: Number,
      default: 0
    },
    teamMembers: {
      type: Number,
      default: 1
    },
    apiCalls: {
      type: Number,
      default: 0
    }
  },
  metadata: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
})

// Indexes
SubscriptionSchema.index({ company: 1 })
SubscriptionSchema.index({ status: 1 })
SubscriptionSchema.index({ currentPeriodEnd: 1 })
SubscriptionSchema.index({ stripeSubscriptionId: 1 })
SubscriptionSchema.index({ stripeCustomerId: 1 })

// Virtual for days remaining in current period
SubscriptionSchema.virtual('daysRemaining').get(function() {
  const now = new Date()
  const end = new Date(this.currentPeriodEnd)
  const diffTime = end.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return Math.max(0, diffDays)
})

// Virtual for trial days remaining
SubscriptionSchema.virtual('trialDaysRemaining').get(function() {
  if (!this.trialEnd) return 0
  const now = new Date()
  const end = new Date(this.trialEnd)
  const diffTime = end.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return Math.max(0, diffDays)
})

// Ensure virtual fields are serialized
SubscriptionSchema.set('toJSON', { virtuals: true })
SubscriptionSchema.set('toObject', { virtuals: true })

export const Subscription = mongoose.models.Subscription || mongoose.model<ISubscription>('Subscription', SubscriptionSchema)

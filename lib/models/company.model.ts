// lib/models/company.model.ts
import mongoose, { Document, Schema } from 'mongoose'

export interface ICompany extends Document {
  _id: mongoose.Types.ObjectId
  name: string
  slug: string
  description: string
  tagline?: string
  logo?: string
  coverImage?: string
  website?: string
  industry: string[]
  size: 'startup' | 'small' | 'medium' | 'large' | 'enterprise'
  founded?: number
  employeeCount?: {
    min: number
    max: number
  }
  locations: Array<{
    city: string
    state?: string
    country: string
    isHeadquarters: boolean
    address?: string
    postalCode?: string
    coordinates?: [number, number]
  }>
  contact: {
    email: string
    phone?: string
    address?: string
    supportEmail?: string
    hrEmail?: string
  }
  culture: {
    values?: string[]
    benefits?: string[]
    workEnvironment?: string
    diversity?: string
    mission?: string
    vision?: string
    perks?: string[]
  }
  socialLinks: {
    linkedin?: string
    twitter?: string
    facebook?: string
    instagram?: string
    github?: string
    youtube?: string
    glassdoor?: string
  }
  subscription: {
    plan: 'starter' | 'professional' | 'enterprise'
    status: 'active' | 'inactive' | 'cancelled' | 'trial'
    stripeCustomerId?: string
    stripeSubscriptionId?: string
    currentPeriodEnd?: Date
    jobPostingLimit: number
    jobPostingsUsed: number
    featuredJobsLimit: number
    featuredJobsUsed: number
  }
  admins: mongoose.Types.ObjectId[]
  recruiters: mongoose.Types.ObjectId[]
  teamMembers: Array<{
    user: mongoose.Types.ObjectId
    role: string
    department?: string
    joinedAt: Date
    isActive: boolean
  }>
  stats: {
    totalJobs: number
    activeJobs: number
    totalApplications: number
    totalHires: number
    averageTimeToHire?: number
    responseRate?: number
    profileViews: number
    followerCount: number
  }
  verification: {
    isVerified: boolean
    verifiedAt?: Date
    verifiedBy?: mongoose.Types.ObjectId
    documents?: string[]
    businessRegistration?: string
    taxId?: string
  }
  settings: {
    // Profile & Privacy Settings
    allowPublicProfile: boolean
    showSalaryRanges: boolean
    allowDirectContact: boolean
    showCompanyStats: boolean

    // Application Management Settings
    autoRejectAfterDays?: number
    requireCoverLetter: boolean
    allowRemoteApplications: boolean
    autoResponseEnabled: boolean
    applicationDeadlineReminder: boolean

    // Notification Settings
    emailNotifications: {
      newApplications: boolean
      applicationUpdates: boolean
      interviewReminders: boolean
      jobExpiring: boolean
      weeklyReports: boolean
      marketingEmails: boolean
    }
    pushNotifications: {
      newApplications: boolean
      urgentUpdates: boolean
      dailyDigest: boolean
      teamMentions: boolean
    }

    // Branding & Customization
    branding: {
      primaryColor?: string
      secondaryColor?: string
      customLogo?: string
      customBanner?: string
      customTheme?: 'light' | 'dark' | 'auto'
    }

    // Team & Collaboration Settings
    teamSettings: {
      allowTeamCollaboration: boolean
      requireApprovalForJobPosting: boolean
      allowRecruiterAccess: boolean
      teamNotifications: boolean
    }

    // Integration Settings
    integrations: {
      atsIntegration?: {
        enabled: boolean
        provider?: string
        apiKey?: string
      }
      calendarIntegration?: {
        enabled: boolean
        provider?: 'google' | 'outlook' | 'calendly'
        settings?: Record<string, unknown>
      }
      slackIntegration?: {
        enabled: boolean
        webhookUrl?: string
        channels?: string[]
      }
    }

    // Advanced Settings
    advanced: {
      dataRetentionPeriod: number // in days
      allowAnalytics: boolean
      allowCookies: boolean
      timezone: string
      language: string
      currency: string
    }
  }
  awards?: string[]
  certifications?: string[]
  specialties?: string[]
  technologies?: string[]
  funding?: {
    stage: 'pre_seed' | 'seed' | 'series_a' | 'series_b' | 'series_c' | 'ipo' | 'acquired'
    amount?: number
    currency?: string
    date?: Date
    investors?: string[]
  }
  isActive: boolean
  isFeatured: boolean
  createdBy: mongoose.Types.ObjectId
  createdAt: Date
  updatedAt: Date
}

const LocationSchema = new Schema({
  city: {
    type: String,
    required: [true, 'City is required'],
    trim: true
  },
  state: { type: String, trim: true },
  country: {
    type: String,
    required: [true, 'Country is required'],
    trim: true
  },
  isHeadquarters: { type: Boolean, default: false },
  address: { type: String, trim: true },
  postalCode: { type: String, trim: true },
  coordinates: {
    type: [Number]
  }
}, { _id: false })

const ContactSchema = new Schema({
  email: {
    type: String,
    required: [true, 'Contact email is required'],
    lowercase: true,
    trim: true
  },
  phone: { type: String, trim: true },
  address: { type: String, trim: true },
  supportEmail: { type: String, lowercase: true, trim: true },
  hrEmail: { type: String, lowercase: true, trim: true }
}, { _id: false })

const EmployeeCountSchema = new Schema({
  min: { type: Number, min: 1 },
  max: { type: Number, min: 1 }
}, { _id: false })

const TeamMemberSchema = new Schema({
  user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  role: { type: String, required: true, trim: true },
  department: { type: String, trim: true },
  joinedAt: { type: Date, default: Date.now },
  isActive: { type: Boolean, default: true }
}, { _id: false })

const FundingSchema = new Schema({
  stage: {
    type: String,
    enum: ['pre_seed', 'seed', 'series_a', 'series_b', 'series_c', 'ipo', 'acquired']
  },
  amount: { type: Number, min: 0 },
  currency: { type: String, default: 'USD' },
  date: { type: Date },
  investors: [{ type: String, trim: true }]
}, { _id: false })

// Enhanced Settings Schema
const EmailNotificationsSchema = new Schema({
  newApplications: { type: Boolean, default: true },
  applicationUpdates: { type: Boolean, default: true },
  interviewReminders: { type: Boolean, default: true },
  jobExpiring: { type: Boolean, default: true },
  weeklyReports: { type: Boolean, default: false },
  marketingEmails: { type: Boolean, default: false }
}, { _id: false })

const PushNotificationsSchema = new Schema({
  newApplications: { type: Boolean, default: true },
  urgentUpdates: { type: Boolean, default: true },
  dailyDigest: { type: Boolean, default: false },
  teamMentions: { type: Boolean, default: true }
}, { _id: false })

const BrandingSchema = new Schema({
  primaryColor: { type: String, match: /^#[0-9A-F]{6}$/i },
  secondaryColor: { type: String, match: /^#[0-9A-F]{6}$/i },
  customLogo: { type: String },
  customBanner: { type: String },
  customTheme: {
    type: String,
    enum: ['light', 'dark', 'auto'],
    default: 'light'
  }
}, { _id: false })

const TeamSettingsSchema = new Schema({
  allowTeamCollaboration: { type: Boolean, default: true },
  requireApprovalForJobPosting: { type: Boolean, default: false },
  allowRecruiterAccess: { type: Boolean, default: true },
  teamNotifications: { type: Boolean, default: true }
}, { _id: false })

const IntegrationsSchema = new Schema({
  atsIntegration: {
    enabled: { type: Boolean, default: false },
    provider: { type: String },
    apiKey: { type: String }
  },
  calendarIntegration: {
    enabled: { type: Boolean, default: false },
    provider: {
      type: String,
      enum: ['google', 'outlook', 'calendly']
    },
    settings: { type: Schema.Types.Mixed }
  },
  slackIntegration: {
    enabled: { type: Boolean, default: false },
    webhookUrl: { type: String },
    channels: [{ type: String }]
  }
}, { _id: false })

const AdvancedSettingsSchema = new Schema({
  dataRetentionPeriod: {
    type: Number,
    default: 730, // 2 years
    min: 30,
    max: 2555 // 7 years
  },
  allowAnalytics: { type: Boolean, default: true },
  allowCookies: { type: Boolean, default: true },
  timezone: {
    type: String,
    default: 'UTC',
    validate: {
      validator: function(v: string) {
        // Basic timezone validation
        return /^[A-Za-z_]+\/[A-Za-z_]+$/.test(v) || v === 'UTC'
      },
      message: 'Invalid timezone format'
    }
  },
  language: {
    type: String,
    default: 'en',
    enum: ['en', 'es', 'fr', 'de', 'it', 'pt', 'zh', 'ja', 'ko']
  },
  currency: {
    type: String,
    default: 'USD',
    enum: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CNY', 'INR']
  }
}, { _id: false })

const SettingsSchema = new Schema({
  // Profile & Privacy Settings
  allowPublicProfile: { type: Boolean, default: true },
  showSalaryRanges: { type: Boolean, default: true },
  allowDirectContact: { type: Boolean, default: true },
  showCompanyStats: { type: Boolean, default: true },

  // Application Management Settings
  autoRejectAfterDays: {
    type: Number,
    min: 1,
    max: 365,
    validate: {
      validator: function(v: number) {
        return v === undefined || (v >= 1 && v <= 365)
      },
      message: 'Auto reject days must be between 1 and 365'
    }
  },
  requireCoverLetter: { type: Boolean, default: false },
  allowRemoteApplications: { type: Boolean, default: true },
  autoResponseEnabled: { type: Boolean, default: true },
  applicationDeadlineReminder: { type: Boolean, default: true },

  // Notification Settings
  emailNotifications: {
    type: EmailNotificationsSchema,
    default: () => ({})
  },
  pushNotifications: {
    type: PushNotificationsSchema,
    default: () => ({})
  },

  // Branding & Customization
  branding: {
    type: BrandingSchema,
    default: () => ({})
  },

  // Team & Collaboration Settings
  teamSettings: {
    type: TeamSettingsSchema,
    default: () => ({})
  },

  // Integration Settings
  integrations: {
    type: IntegrationsSchema,
    default: () => ({})
  },

  // Advanced Settings
  advanced: {
    type: AdvancedSettingsSchema,
    default: () => ({})
  }
}, { _id: false })

const CultureSchema = new Schema({
  values: [{ type: String, trim: true }],
  benefits: [{ type: String, trim: true }],
  workEnvironment: { type: String, trim: true },
  diversity: { type: String, trim: true },
  mission: { type: String, trim: true },
  vision: { type: String, trim: true },
  perks: [{ type: String, trim: true }]
}, { _id: false })

const SocialLinksSchema = new Schema({
  linkedin: { 
    type: String,
    validate: {
      validator: function(v: string) {
        return !v || /^https?:\/\/(www\.)?linkedin\.com\//.test(v)
      },
      message: 'Invalid LinkedIn URL'
    }
  },
  twitter: { 
    type: String,
    validate: {
      validator: function(v: string) {
        return !v || /^https?:\/\/(www\.)?twitter\.com\//.test(v)
      },
      message: 'Invalid Twitter URL'
    }
  },
  facebook: { 
    type: String,
    validate: {
      validator: function(v: string) {
        return !v || /^https?:\/\/(www\.)?facebook\.com\//.test(v)
      },
      message: 'Invalid Facebook URL'
    }
  },
  instagram: { 
    type: String,
    validate: {
      validator: function(v: string) {
        return !v || /^https?:\/\/(www\.)?instagram\.com\//.test(v)
      },
      message: 'Invalid Instagram URL'
    }
  },
  github: {
    type: String,
    validate: {
      validator: function(v: string) {
        return !v || /^https?:\/\/(www\.)?github\.com\//.test(v)
      },
      message: 'Invalid GitHub URL'
    }
  },
  youtube: {
    type: String,
    validate: {
      validator: function(v: string) {
        return !v || /^https?:\/\/(www\.)?youtube\.com\//.test(v)
      },
      message: 'Invalid YouTube URL'
    }
  },
  glassdoor: {
    type: String,
    validate: {
      validator: function(v: string) {
        return !v || /^https?:\/\/(www\.)?glassdoor\.com\//.test(v)
      },
      message: 'Invalid Glassdoor URL'
    }
  }
}, { _id: false })

const SubscriptionSchema = new Schema({
  plan: { 
    type: String, 
    enum: ['starter', 'professional', 'enterprise'], 
    default: 'starter' 
  },
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'cancelled', 'trial'], 
    default: 'trial' 
  },
  stripeCustomerId: String,
  stripeSubscriptionId: String,
  currentPeriodEnd: Date,
  jobPostingLimit: { 
    type: Number, 
    default: 5 // Starter plan default
  },
  jobPostingsUsed: {
    type: Number,
    default: 0
  },
  featuredJobsLimit: {
    type: Number,
    default: 1
  },
  featuredJobsUsed: {
    type: Number,
    default: 0
  }
}, { _id: false })

const StatsSchema = new Schema({
  totalJobs: { type: Number, default: 0 },
  activeJobs: { type: Number, default: 0 },
  totalApplications: { type: Number, default: 0 },
  totalHires: { type: Number, default: 0 },
  averageTimeToHire: Number,
  responseRate: Number,
  profileViews: { type: Number, default: 0 },
  followerCount: { type: Number, default: 0 }
}, { _id: false })

const VerificationSchema = new Schema({
  isVerified: { type: Boolean, default: false },
  verifiedAt: Date,
  verifiedBy: { type: Schema.Types.ObjectId, ref: 'User' },
  documents: [String],
  businessRegistration: String,
  taxId: String
}, { _id: false })

const CompanySchema = new Schema<ICompany>({
  name: {
    type: String,
    required: [true, 'Company name is required'],
    trim: true,
    maxlength: [100, 'Company name cannot exceed 100 characters']
  },
  slug: {
    type: String,
    required: true,
    lowercase: true,
    trim: true,
    validate: {
      validator: function(v: string) {
        return /^[a-z0-9-]+$/.test(v)
      },
      message: 'Slug can only contain lowercase letters, numbers, and hyphens'
    }
  },
  description: {
    type: String,
    required: [true, 'Company description is required'],
    trim: true,
    maxlength: [2000, 'Description cannot exceed 2000 characters']
  },
  logo: String,
  website: {
    type: String,
    validate: {
      validator: function(v: string) {
        return !v || /^https?:\/\/.+\..+/.test(v)
      },
      message: 'Invalid website URL'
    }
  },
  industry: {
    type: [String],
    required: [true, 'At least one industry is required'],
    validate: {
      validator: function(v: string[]) {
        return v && v.length > 0
      },
      message: 'At least one industry must be specified'
    }
  },
  size: {
    type: String,
    enum: ['startup', 'small', 'medium', 'large', 'enterprise'],
    required: [true, 'Company size is required']
  },
  founded: {
    type: Number,
    min: [1800, 'Founded year cannot be before 1800'],
    max: [new Date().getFullYear(), 'Founded year cannot be in the future']
  },
  tagline: {
    type: String,
    trim: true,
    maxlength: [200, 'Tagline cannot exceed 200 characters']
  },
  coverImage: String,
  employeeCount: EmployeeCountSchema,
  contact: {
    type: ContactSchema,
    required: [true, 'Contact information is required']
  },
  locations: {
    type: [LocationSchema],
    required: [true, 'At least one location is required'],
    validate: {
      validator: function(v: Array<Record<string, unknown>>) {
        return v && v.length > 0
      },
      message: 'At least one location must be specified'
    }
  },
  culture: {
    type: CultureSchema,
    default: () => ({})
  },
  socialLinks: {
    type: SocialLinksSchema,
    default: () => ({})
  },
  subscription: {
    type: SubscriptionSchema,
    default: () => ({})
  },
  admins: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],
  recruiters: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],
  teamMembers: [TeamMemberSchema],
  stats: {
    type: StatsSchema,
    default: () => ({})
  },
  verification: {
    type: VerificationSchema,
    default: () => ({})
  },
  settings: {
    type: SettingsSchema,
    default: () => ({})
  },
  awards: [{ type: String, trim: true }],
  certifications: [{ type: String, trim: true }],
  specialties: [{ type: String, trim: true }],
  technologies: [{ type: String, trim: true }],
  funding: FundingSchema,
  isActive: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Created by user is required']
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Indexes
CompanySchema.index({ slug: 1 }, { unique: true })
CompanySchema.index({ name: 1 })
CompanySchema.index({ industry: 1 })
CompanySchema.index({ size: 1 })
CompanySchema.index({ 'locations.coordinates': '2dsphere' })
CompanySchema.index({ 'verification.isVerified': 1, isActive: 1 })
CompanySchema.index({ createdAt: -1 })

// Pre-save middleware to generate slug
CompanySchema.pre('save', function(next) {
  if (this.isModified('name') && !this.slug) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }
  next()
})

// Virtual for team size
CompanySchema.virtual('teamSize').get(function() {
  const adminsLength = this.admins ? this.admins.length : 0
  const recruitersLength = this.recruiters ? this.recruiters.length : 0
  return adminsLength + recruitersLength
})

export const Company = mongoose.models.Company || mongoose.model<ICompany>('Company', CompanySchema)

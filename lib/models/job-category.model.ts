import mongoose, { Document, Schema } from 'mongoose'

export interface IJobCategory extends Document {
  name: string
  slug: string
  description?: string
  icon?: string
  color?: string
  isActive: boolean
  parentCategory?: mongoose.Types.ObjectId
  createdBy: mongoose.Types.ObjectId
  createdAt: Date
  updatedAt: Date
}

const JobCategorySchema = new Schema<IJobCategory>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
    unique: true
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  description: {
    type: String,
    maxlength: 500
  },
  icon: {
    type: String,
    maxlength: 10 // For emoji or short icon codes
  },
  color: {
    type: String,
    default: '#3B82F6',
    match: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/ // Hex color validation
  },
  isActive: {
    type: Boolean,
    default: true
  },
  parentCategory: {
    type: Schema.Types.ObjectId,
    ref: 'JobCategory',
    default: null
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
})

// Indexes
JobCategorySchema.index({ slug: 1 })
JobCategorySchema.index({ isActive: 1 })
JobCategorySchema.index({ parentCategory: 1 })
JobCategorySchema.index({ name: 'text', description: 'text' })

// Virtual for subcategories
JobCategorySchema.virtual('subcategories', {
  ref: 'JobCategory',
  localField: '_id',
  foreignField: 'parentCategory'
})

// Ensure virtual fields are serialized
JobCategorySchema.set('toJSON', { virtuals: true })
JobCategorySchema.set('toObject', { virtuals: true })

export const JobCategory = mongoose.models.JobCategory || mongoose.model<IJobCategory>('JobCategory', JobCategorySchema)

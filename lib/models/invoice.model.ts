import mongoose, { Document, Schema } from 'mongoose'

export interface IInvoice extends Document {
  invoiceNumber: string
  company: mongoose.Types.ObjectId
  subscription?: mongoose.Types.ObjectId
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'canceled' | 'refunded'
  items: Array<{
    description: string
    quantity: number
    unitPrice: number
    amount: number
    metadata?: Record<string, any>
  }>
  subtotal: number
  taxRate: number
  taxAmount: number
  discountAmount: number
  totalAmount: number
  currency: string
  issueDate: Date
  dueDate: Date
  paidAt?: Date
  sentAt?: Date
  notes?: string
  paymentTerms: string
  stripeInvoiceId?: string
  metadata?: Record<string, any>
  createdBy: mongoose.Types.ObjectId
  createdAt: Date
  updatedAt: Date
}

const InvoiceSchema = new Schema<IInvoice>({
  invoiceNumber: {
    type: String,
    required: true,
    unique: true
  },
  company: {
    type: Schema.Types.ObjectId,
    ref: 'Company',
    required: true
  },
  subscription: {
    type: Schema.Types.ObjectId,
    ref: 'Subscription'
  },
  status: {
    type: String,
    enum: ['draft', 'sent', 'paid', 'overdue', 'canceled', 'refunded'],
    default: 'draft'
  },
  items: [{
    description: {
      type: String,
      required: true
    },
    quantity: {
      type: Number,
      required: true,
      min: 0
    },
    unitPrice: {
      type: Number,
      required: true,
      min: 0
    },
    amount: {
      type: Number,
      required: true,
      min: 0
    },
    metadata: {
      type: Schema.Types.Mixed,
      default: {}
    }
  }],
  subtotal: {
    type: Number,
    required: true,
    min: 0
  },
  taxRate: {
    type: Number,
    default: 0,
    min: 0,
    max: 1
  },
  taxAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  discountAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  totalAmount: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    default: 'USD',
    enum: ['USD', 'EUR', 'GBP', 'CAD']
  },
  issueDate: {
    type: Date,
    required: true
  },
  dueDate: {
    type: Date,
    required: true
  },
  paidAt: Date,
  sentAt: Date,
  notes: String,
  paymentTerms: {
    type: String,
    default: 'Net 30'
  },
  stripeInvoiceId: String,
  metadata: {
    type: Schema.Types.Mixed,
    default: {}
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
})

// Indexes
InvoiceSchema.index({ invoiceNumber: 1 })
InvoiceSchema.index({ company: 1 })
InvoiceSchema.index({ subscription: 1 })
InvoiceSchema.index({ status: 1 })
InvoiceSchema.index({ dueDate: 1 })
InvoiceSchema.index({ issueDate: -1 })
InvoiceSchema.index({ stripeInvoiceId: 1 })

// Virtual for days overdue
InvoiceSchema.virtual('daysOverdue').get(function() {
  if (this.status === 'paid' || !this.dueDate) return 0
  const now = new Date()
  const due = new Date(this.dueDate)
  const diffTime = now.getTime() - due.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return Math.max(0, diffDays)
})

// Virtual for is overdue
InvoiceSchema.virtual('isOverdue').get(function() {
  return this.daysOverdue > 0 && this.status !== 'paid'
})

// Pre-save middleware to update status based on due date
InvoiceSchema.pre('save', function(next) {
  if (this.isOverdue && this.status === 'sent') {
    this.status = 'overdue'
  }
  next()
})

// Ensure virtual fields are serialized
InvoiceSchema.set('toJSON', { virtuals: true })
InvoiceSchema.set('toObject', { virtuals: true })

export const Invoice = mongoose.models.Invoice || mongoose.model<IInvoice>('Invoice', InvoiceSchema)

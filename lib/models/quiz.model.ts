import mongoose, { Schema, Document } from 'mongoose'

// Quiz Category Interface
export interface IQuizCategory extends Document {
  name: string
  slug: string
  description: string
  icon: string
  color: string
  skills: string[]
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  estimatedTime: number // in minutes
  totalQuestions: number
  passingScore: number // percentage
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// Quiz Question Interface
export interface IQuizQuestion extends Document {
  categoryId: mongoose.Types.ObjectId
  question: string
  options: string[]
  correctAnswer: number // index of correct option
  explanation?: string
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  points: number
  tags: string[]
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// Quiz Attempt Interface
export interface IQuizAttempt extends Document {
  userId?: mongoose.Types.ObjectId // null for anonymous users
  sessionId: string // for anonymous tracking
  categoryId: mongoose.Types.ObjectId
  questions: Array<{
    questionId: mongoose.Types.ObjectId
    selectedAnswer: number
    isCorrect: boolean
    timeSpent: number // in seconds
    points: number
  }>
  score: number // percentage
  totalPoints: number
  maxPoints: number
  timeSpent: number // total time in seconds
  completedAt: Date
  ipAddress?: string
  userAgent?: string
  isAnonymous: boolean
  createdAt: Date
}

// Quiz Category Schema
const quizCategorySchema = new Schema<IQuizCategory>({
  name: {
    type: String,
    required: true,
    trim: true
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true
  },
  description: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    required: true
  },
  color: {
    type: String,
    required: true
  },
  skills: [{
    type: String,
    trim: true
  }],
  difficulty: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced', 'expert'],
    default: 'intermediate'
  },
  estimatedTime: {
    type: Number,
    required: true,
    min: 1
  },
  totalQuestions: {
    type: Number,
    required: true,
    min: 1
  },
  passingScore: {
    type: Number,
    required: true,
    min: 0,
    max: 100,
    default: 70
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  }
})

// Quiz Question Schema
const quizQuestionSchema = new Schema<IQuizQuestion>({
  categoryId: {
    type: Schema.Types.ObjectId,
    ref: 'QuizCategory',
    required: true
  },
  question: {
    type: String,
    required: true,
    trim: true
  },
  options: [{
    type: String,
    required: true,
    trim: true
  }],
  correctAnswer: {
    type: Number,
    required: true,
    min: 0
  },
  explanation: {
    type: String,
    trim: true
  },
  difficulty: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced', 'expert'],
    required: true
  },
  points: {
    type: Number,
    required: true,
    min: 1,
    default: 10
  },
  tags: [{
    type: String,
    trim: true
  }],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  }
})

// Quiz Attempt Schema
const quizAttemptSchema = new Schema<IQuizAttempt>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  sessionId: {
    type: String,
    required: true
  },
  categoryId: {
    type: Schema.Types.ObjectId,
    ref: 'QuizCategory',
    required: true
  },
  questions: [{
    questionId: {
      type: Schema.Types.ObjectId,
      ref: 'QuizQuestion',
      required: true
    },
    selectedAnswer: {
      type: Number,
      required: true
    },
    isCorrect: {
      type: Boolean,
      required: true
    },
    timeSpent: {
      type: Number,
      required: true,
      min: 0
    },
    points: {
      type: Number,
      required: true,
      min: 0
    }
  }],
  score: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  totalPoints: {
    type: Number,
    required: true,
    min: 0
  },
  maxPoints: {
    type: Number,
    required: true,
    min: 0
  },
  timeSpent: {
    type: Number,
    required: true,
    min: 0
  },
  completedAt: {
    type: Date,
    required: true,
    default: Date.now
  },
  ipAddress: String,
  userAgent: String,
  isAnonymous: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  }
})

// Indexes for better performance
quizCategorySchema.index({ isActive: 1 })

quizQuestionSchema.index({ categoryId: 1, isActive: 1 })
quizQuestionSchema.index({ difficulty: 1 })

quizAttemptSchema.index({ userId: 1, categoryId: 1 })
quizAttemptSchema.index({ completedAt: -1 })
quizAttemptSchema.index({ score: -1 })

// Export models
export const QuizCategory = mongoose.models.QuizCategory || mongoose.model<IQuizCategory>('QuizCategory', quizCategorySchema)
export const QuizQuestion = mongoose.models.QuizQuestion || mongoose.model<IQuizQuestion>('QuizQuestion', quizQuestionSchema)
export const QuizAttempt = mongoose.models.QuizAttempt || mongoose.model<IQuizAttempt>('QuizAttempt', quizAttemptSchema)

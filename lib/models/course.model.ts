import mongoose, { Schema, Document, Types } from 'mongoose'

// Course Category Interface
export interface ICourseCategory extends Document {
  _id: Types.ObjectId
  name: string
  description: string
  icon: string
  color: string
  slug: string
  isActive: boolean
  sortOrder: number
  createdAt: Date
  updatedAt: Date
}

// Course Interface
export interface ICourse extends Document {
  _id: Types.ObjectId
  title: string
  description: string
  shortDescription: string
  slug: string
  thumbnail: string
  previewVideo?: string
  categoryId: Types.ObjectId
  instructorId: Types.ObjectId
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  duration: number // in minutes
  price: number
  originalPrice?: number
  currency: string
  language: string
  tags: string[]
  skills: string[]
  requirements: string[]
  whatYouWillLearn: string[]
  targetAudience: string[]
  isPublished: boolean
  isFeatured: boolean
  isPremium: boolean
  rating: number
  totalRatings: number
  totalStudents: number
  totalLessons: number
  lastUpdated: Date
  createdAt: Date
  updatedAt: Date
}

// Course Section Interface
export interface ICourseSection extends Document {
  _id: Types.ObjectId
  courseId: Types.ObjectId
  title: string
  description?: string
  sortOrder: number
  isPublished: boolean
  createdAt: Date
  updatedAt: Date
}

// Course Lesson Interface
export interface ICourseLesson extends Document {
  _id: Types.ObjectId
  courseId: Types.ObjectId
  sectionId: Types.ObjectId
  title: string
  description?: string
  content: string
  videoUrl?: string
  videoDuration?: number
  attachments: Array<{
    name: string
    url: string
    type: string
    size: number
  }>
  type: 'video' | 'text' | 'quiz' | 'assignment' | 'download'
  sortOrder: number
  isPreview: boolean
  isPublished: boolean
  createdAt: Date
  updatedAt: Date
}

// Course Enrollment Interface
export interface ICourseEnrollment extends Document {
  _id: Types.ObjectId
  userId: Types.ObjectId
  courseId: Types.ObjectId
  enrolledAt: Date
  completedAt?: Date
  progress: number // 0-100
  currentLessonId?: Types.ObjectId
  lastAccessedAt: Date
  certificateIssued: boolean
  certificateId?: string
  paymentId?: string
  paymentStatus: 'pending' | 'completed' | 'failed' | 'refunded'
  paymentAmount: number
  createdAt: Date
  updatedAt: Date
}

// Course Progress Interface
export interface ICourseProgress extends Document {
  _id: Types.ObjectId
  userId: Types.ObjectId
  courseId: Types.ObjectId
  lessonId: Types.ObjectId
  isCompleted: boolean
  completedAt?: Date
  timeSpent: number // in seconds
  lastPosition?: number // for video lessons
  notes?: string
  createdAt: Date
  updatedAt: Date
}

// Course Review Interface
export interface ICourseReview extends Document {
  _id: Types.ObjectId
  userId: Types.ObjectId
  courseId: Types.ObjectId
  rating: number // 1-5
  title: string
  comment: string
  isPublished: boolean
  helpfulVotes: number
  createdAt: Date
  updatedAt: Date
}

// Course Certificate Interface
export interface ICourseCertificate extends Document {
  _id: Types.ObjectId
  userId: Types.ObjectId
  courseId: Types.ObjectId
  enrollmentId: Types.ObjectId
  certificateId: string
  issuedAt: Date
  verificationUrl: string
  credentialData: {
    studentName: string
    courseName: string
    completionDate: Date
    grade?: string
    instructorName: string
    certificateNumber: string
  }
  createdAt: Date
  updatedAt: Date
}

// Schemas
const CourseCategorySchema = new Schema<ICourseCategory>({
  name: { type: String, required: true, trim: true },
  description: { type: String, required: true },
  icon: { type: String, required: true },
  color: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  isActive: { type: Boolean, default: true },
  sortOrder: { type: Number, default: 0 }
}, {
  timestamps: true
})

const CourseSchema = new Schema<ICourse>({
  title: { type: String, required: true, trim: true },
  description: { type: String, required: true },
  shortDescription: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  thumbnail: { type: String, required: true },
  previewVideo: { type: String },
  categoryId: { type: Schema.Types.ObjectId, ref: 'CourseCategory', required: true },
  instructorId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  level: { type: String, enum: ['beginner', 'intermediate', 'advanced', 'expert'], required: true },
  duration: { type: Number, required: true },
  price: { type: Number, required: true, min: 0 },
  originalPrice: { type: Number, min: 0 },
  currency: { type: String, default: 'USD' },
  language: { type: String, default: 'English' },
  tags: [{ type: String }],
  skills: [{ type: String }],
  requirements: [{ type: String }],
  whatYouWillLearn: [{ type: String }],
  targetAudience: [{ type: String }],
  isPublished: { type: Boolean, default: false },
  isFeatured: { type: Boolean, default: false },
  isPremium: { type: Boolean, default: false },
  rating: { type: Number, default: 0, min: 0, max: 5 },
  totalRatings: { type: Number, default: 0 },
  totalStudents: { type: Number, default: 0 },
  totalLessons: { type: Number, default: 0 },
  lastUpdated: { type: Date, default: Date.now }
}, {
  timestamps: true
})

const CourseSectionSchema = new Schema<ICourseSection>({
  courseId: { type: Schema.Types.ObjectId, ref: 'Course', required: true },
  title: { type: String, required: true, trim: true },
  description: { type: String },
  sortOrder: { type: Number, required: true },
  isPublished: { type: Boolean, default: true }
}, {
  timestamps: true
})

const CourseLessonSchema = new Schema<ICourseLesson>({
  courseId: { type: Schema.Types.ObjectId, ref: 'Course', required: true },
  sectionId: { type: Schema.Types.ObjectId, ref: 'CourseSection', required: true },
  title: { type: String, required: true, trim: true },
  description: { type: String },
  content: { type: String, required: true },
  videoUrl: { type: String },
  videoDuration: { type: Number },
  attachments: [{
    name: { type: String, required: true },
    url: { type: String, required: true },
    type: { type: String, required: true },
    size: { type: Number, required: true }
  }],
  type: { type: String, enum: ['video', 'text', 'quiz', 'assignment', 'download'], required: true },
  sortOrder: { type: Number, required: true },
  isPreview: { type: Boolean, default: false },
  isPublished: { type: Boolean, default: true }
}, {
  timestamps: true
})

const CourseEnrollmentSchema = new Schema<ICourseEnrollment>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  courseId: { type: Schema.Types.ObjectId, ref: 'Course', required: true },
  enrolledAt: { type: Date, default: Date.now },
  completedAt: { type: Date },
  progress: { type: Number, default: 0, min: 0, max: 100 },
  currentLessonId: { type: Schema.Types.ObjectId, ref: 'CourseLesson' },
  lastAccessedAt: { type: Date, default: Date.now },
  certificateIssued: { type: Boolean, default: false },
  certificateId: { type: String },
  paymentId: { type: String },
  paymentStatus: { type: String, enum: ['pending', 'completed', 'failed', 'refunded'], default: 'pending' },
  paymentAmount: { type: Number, required: true }
}, {
  timestamps: true
})

const CourseProgressSchema = new Schema<ICourseProgress>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  courseId: { type: Schema.Types.ObjectId, ref: 'Course', required: true },
  lessonId: { type: Schema.Types.ObjectId, ref: 'CourseLesson', required: true },
  isCompleted: { type: Boolean, default: false },
  completedAt: { type: Date },
  timeSpent: { type: Number, default: 0 },
  lastPosition: { type: Number },
  notes: { type: String }
}, {
  timestamps: true
})

const CourseReviewSchema = new Schema<ICourseReview>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  courseId: { type: Schema.Types.ObjectId, ref: 'Course', required: true },
  rating: { type: Number, required: true, min: 1, max: 5 },
  title: { type: String, required: true, trim: true },
  comment: { type: String, required: true },
  isPublished: { type: Boolean, default: true },
  helpfulVotes: { type: Number, default: 0 }
}, {
  timestamps: true
})

const CourseCertificateSchema = new Schema<ICourseCertificate>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  courseId: { type: Schema.Types.ObjectId, ref: 'Course', required: true },
  enrollmentId: { type: Schema.Types.ObjectId, ref: 'CourseEnrollment', required: true },
  certificateId: { type: String, required: true, unique: true },
  issuedAt: { type: Date, default: Date.now },
  verificationUrl: { type: String, required: true },
  credentialData: {
    studentName: { type: String, required: true },
    courseName: { type: String, required: true },
    completionDate: { type: Date, required: true },
    grade: { type: String },
    instructorName: { type: String, required: true },
    certificateNumber: { type: String, required: true }
  }
}, {
  timestamps: true
})

// Indexes (only non-unique indexes, unique ones are handled by schema definition)
CourseCategorySchema.index({ isActive: 1, sortOrder: 1 })

CourseSchema.index({ categoryId: 1, isPublished: 1 })
CourseSchema.index({ isPublished: 1, isFeatured: 1 })
CourseSchema.index({ instructorId: 1 })
CourseSchema.index({ tags: 1 })
CourseSchema.index({ level: 1 })
CourseSchema.index({ price: 1 })
CourseSchema.index({ rating: -1 })
CourseSchema.index({ totalStudents: -1 })
CourseSchema.index({ createdAt: -1 })

CourseSectionSchema.index({ courseId: 1, sortOrder: 1 })
CourseLessonSchema.index({ courseId: 1, sectionId: 1, sortOrder: 1 })

// Export models
export const CourseCategory = mongoose.models.CourseCategory || mongoose.model<ICourseCategory>('CourseCategory', CourseCategorySchema)
export const Course = mongoose.models.Course || mongoose.model<ICourse>('Course', CourseSchema)
export const CourseSection = mongoose.models.CourseSection || mongoose.model<ICourseSection>('CourseSection', CourseSectionSchema)
export const CourseLesson = mongoose.models.CourseLesson || mongoose.model<ICourseLesson>('CourseLesson', CourseLessonSchema)
export const CourseEnrollment = mongoose.models.CourseEnrollment || mongoose.model<ICourseEnrollment>('CourseEnrollment', CourseEnrollmentSchema)
export const CourseProgress = mongoose.models.CourseProgress || mongoose.model<ICourseProgress>('CourseProgress', CourseProgressSchema)
export const CourseReview = mongoose.models.CourseReview || mongoose.model<ICourseReview>('CourseReview', CourseReviewSchema)
export const CourseCertificate = mongoose.models.CourseCertificate || mongoose.model<ICourseCertificate>('CourseCertificate', CourseCertificateSchema)

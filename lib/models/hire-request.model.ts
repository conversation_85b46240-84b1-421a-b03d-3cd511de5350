import mongoose, { Document, Schema } from 'mongoose'
import { HireRequest, HireStatus, HireType, ContractType } from '@/types/hire.types'

export interface IHireRequest extends Document, Omit<HireRequest, '_id' | 'id'> {
  _id: mongoose.Types.ObjectId
}

// Timeline Entry Schema
const HireTimelineSchema = new Schema({
  status: {
    type: String,
    enum: ['pending', 'reviewing', 'negotiating', 'contract_sent', 'contract_signed', 'hired', 'rejected', 'cancelled', 'expired'],
    required: true
  },
  date: {
    type: Date,
    required: true,
    default: Date.now
  },
  notes: {
    type: String,
    maxlength: 1000
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedByRole: {
    type: String,
    enum: ['company', 'talent', 'system'],
    required: true
  },
  metadata: {
    type: Map,
    of: Schema.Types.Mixed,
    default: new Map()
  }
}, { _id: false })

// Milestone Schema
const MilestoneSchema = new Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    required: true,
    maxlength: 1000
  },
  dueDate: {
    type: Date,
    required: true
  },
  deliverables: [{
    type: String,
    trim: true,
    maxlength: 500
  }]
}, { _id: false })

// Project Details Schema
const ProjectDetailsSchema = new Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    required: true,
    maxlength: 5000
  },
  requirements: [{
    type: String,
    trim: true,
    maxlength: 500
  }],
  deliverables: [{
    type: String,
    trim: true,
    maxlength: 500
  }],
  timeline: {
    startDate: {
      type: Date,
      required: true
    },
    endDate: Date,
    milestones: [MilestoneSchema]
  },
  budget: {
    amount: {
      type: Number,
      min: 0
    },
    currency: {
      type: String,
      default: 'USD',
      uppercase: true,
      minlength: 3,
      maxlength: 3
    },
    paymentTerms: {
      type: String,
      maxlength: 1000
    },
    paymentSchedule: {
      type: String,
      enum: ['hourly', 'weekly', 'monthly', 'milestone', 'completion']
    }
  }
}, { _id: false })

// Bonus Schema
const BonusSchema = new Schema({
  type: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  criteria: {
    type: String,
    required: true,
    maxlength: 500
  }
}, { _id: false })

// Contract Terms Schema
const ContractTermsSchema = new Schema({
  type: {
    type: String,
    enum: ['employment', 'service', 'nda', 'freelance'],
    required: true
  },
  duration: {
    startDate: {
      type: Date,
      required: true
    },
    endDate: Date,
    isIndefinite: {
      type: Boolean,
      default: false
    }
  },
  compensation: {
    amount: {
      type: Number,
      required: true,
      min: 0
    },
    currency: {
      type: String,
      required: true,
      default: 'USD',
      uppercase: true,
      minlength: 3,
      maxlength: 3
    },
    frequency: {
      type: String,
      enum: ['hourly', 'daily', 'weekly', 'monthly', 'yearly', 'project'],
      required: true
    },
    benefits: [{
      type: String,
      trim: true,
      maxlength: 200
    }],
    bonuses: [BonusSchema]
  },
  workArrangement: {
    type: {
      type: String,
      enum: ['remote', 'onsite', 'hybrid'],
      required: true
    },
    location: {
      type: String,
      trim: true,
      maxlength: 200
    },
    hoursPerWeek: {
      type: Number,
      min: 1,
      max: 168
    },
    flexibleHours: {
      type: Boolean,
      default: false
    },
    timezone: {
      type: String,
      trim: true,
      maxlength: 50
    }
  },
  terms: {
    probationPeriod: {
      type: Number,
      min: 0,
      max: 365
    },
    noticePeriod: {
      type: Number,
      min: 0,
      max: 365
    },
    nonCompete: {
      type: Boolean,
      default: false
    },
    nonDisclosure: {
      type: Boolean,
      default: false
    },
    intellectualProperty: {
      type: String,
      maxlength: 2000
    },
    terminationClause: {
      type: String,
      maxlength: 2000
    },
    additionalTerms: [{
      type: String,
      maxlength: 500
    }]
  }
}, { _id: false })

// Contract Document Schema
const ContractDocumentSchema = new Schema({
  type: {
    type: String,
    enum: ['employment', 'service', 'nda', 'freelance'],
    required: true
  },
  filename: {
    type: String,
    required: true,
    trim: true,
    maxlength: 255
  },
  url: {
    type: String,
    required: true,
    trim: true
  },
  uploadDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  signedBy: [{
    party: {
      type: String,
      enum: ['company', 'talent'],
      required: true
    },
    signedAt: {
      type: Date,
      required: true
    },
    signature: String
  }]
}, { _id: false })

// Main Hire Request Schema
const HireRequestSchema = new Schema<IHireRequest>({
  companyId: {
    type: Schema.Types.ObjectId,
    ref: 'Company',
    required: true,
    index: true
  },
  talentId: {
    type: Schema.Types.ObjectId,
    ref: 'Client',
    required: true,
    index: true
  },
  hireType: {
    type: String,
    enum: ['full_time', 'part_time', 'contract', 'freelance', 'internship', 'temporary'],
    required: true,
    index: true
  },
  status: {
    type: String,
    enum: ['pending', 'reviewing', 'negotiating', 'contract_sent', 'contract_signed', 'hired', 'rejected', 'cancelled', 'expired'],
    default: 'pending',
    required: true,
    index: true
  },
  projectDetails: {
    type: ProjectDetailsSchema,
    required: true
  },
  contractTerms: {
    type: ContractTermsSchema,
    required: true
  },
  message: {
    type: String,
    maxlength: 2000
  },
  companyNotes: {
    type: String,
    maxlength: 2000
  },
  talentResponse: {
    type: String,
    maxlength: 2000
  },
  timeline: {
    type: [HireTimelineSchema],
    required: true,
    default: []
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium',
    index: true
  },
  source: {
    type: String,
    enum: ['talent_search', 'application', 'referral', 'direct'],
    default: 'talent_search'
  },
  referenceId: {
    type: String,
    trim: true
  },
  contractDocuments: [ContractDocumentSchema],
  expiresAt: {
    type: Date,
    index: true
  },
  responseDeadline: {
    type: Date,
    index: true
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastUpdatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  },
  toObject: { virtuals: true }
})

// Indexes for better query performance
HireRequestSchema.index({ companyId: 1, status: 1 })
HireRequestSchema.index({ talentId: 1, status: 1 })
HireRequestSchema.index({ status: 1, createdAt: -1 })
HireRequestSchema.index({ expiresAt: 1 }, { sparse: true })
HireRequestSchema.index({ responseDeadline: 1 }, { sparse: true })
HireRequestSchema.index({ priority: 1, createdAt: -1 })
HireRequestSchema.index({ hireType: 1, status: 1 })
HireRequestSchema.index({ createdAt: -1 })
HireRequestSchema.index({ updatedAt: -1 })

// Compound index for company hire management
HireRequestSchema.index({ companyId: 1, status: 1, priority: 1, createdAt: -1 })

// Text search index
HireRequestSchema.index({
  'projectDetails.title': 'text',
  'projectDetails.description': 'text',
  message: 'text',
  companyNotes: 'text'
})

// Virtual for days since creation
HireRequestSchema.virtual('daysSinceCreated').get(function() {
  return Math.floor((Date.now() - this.createdAt.getTime()) / (1000 * 60 * 60 * 24))
})

// Virtual for days until expiration
HireRequestSchema.virtual('daysUntilExpiration').get(function() {
  if (!this.expiresAt) return null
  return Math.floor((this.expiresAt.getTime() - Date.now()) / (1000 * 60 * 60 * 24))
})

// Virtual for current status duration
HireRequestSchema.virtual('currentStatusDuration').get(function() {
  const lastStatusChange = this.timeline[this.timeline.length - 1]
  if (!lastStatusChange) return 0
  return Math.floor((Date.now() - lastStatusChange.date.getTime()) / (1000 * 60 * 60 * 24))
})

// Pre-save middleware to update timeline
HireRequestSchema.pre('save', function(next) {
  if (this.isModified('status')) {
    this.timeline.push({
      status: this.status,
      date: new Date(),
      updatedBy: this.lastUpdatedBy,
      updatedByRole: 'system',
      notes: `Status changed to ${this.status}`
    })
  }
  next()
})

// Static method to find expiring requests
HireRequestSchema.statics.findExpiringSoon = function(days = 7) {
  const expirationDate = new Date()
  expirationDate.setDate(expirationDate.getDate() + days)
  
  return this.find({
    expiresAt: { $lte: expirationDate, $gte: new Date() },
    status: { $in: ['pending', 'reviewing', 'negotiating'] },
    isActive: true
  })
}

// Static method to get hire stats for a company
HireRequestSchema.statics.getCompanyStats = function(companyId: string) {
  return this.aggregate([
    { $match: { companyId: new mongoose.Types.ObjectId(companyId), isActive: true } },
    {
      $group: {
        _id: null,
        total: { $sum: 1 },
        byStatus: {
          $push: {
            status: '$status',
            count: 1
          }
        },
        byType: {
          $push: {
            type: '$hireType',
            count: 1
          }
        },
        avgTimeToHire: {
          $avg: {
            $cond: [
              { $eq: ['$status', 'hired'] },
              { $subtract: ['$updatedAt', '$createdAt'] },
              null
            ]
          }
        }
      }
    }
  ])
}

export const HireRequest = mongoose.models.HireRequest || mongoose.model<IHireRequest>('HireRequest', HireRequestSchema)

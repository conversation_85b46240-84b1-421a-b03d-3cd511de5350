// Comprehensive validation utilities for quiz data
export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  data?: any
}

export interface CategoryData {
  name: string
  description: string
  icon?: string
  color?: string
  skills?: string | string[]
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  estimatedTime?: number
  totalQuestions?: number
  passingScore?: number
  isActive?: boolean
}

export interface QuestionData {
  categoryName?: string
  categoryId?: string
  question: string
  option1: string
  option2: string
  option3?: string
  option4?: string
  option5?: string
  option6?: string
  correctAnswer: number
  explanation?: string
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  points?: number
  tags?: string | string[]
  isActive?: boolean
}

export class QuizValidator {
  private static readonly VALID_DIFFICULTIES = ['beginner', 'intermediate', 'advanced', 'expert']
  private static readonly VALID_COLORS = [
    'bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-red-500',
    'bg-yellow-500', 'bg-indigo-500', 'bg-pink-500', 'bg-teal-500',
    'bg-orange-500', 'bg-cyan-500', 'bg-gray-500', 'bg-slate-500'
  ]
  private static readonly EMOJI_REGEX = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u

  static validateCategory(data: any, index?: number): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []
    const prefix = index !== undefined ? `Row ${index + 1}: ` : ''

    // Required fields validation
    if (!data.name || typeof data.name !== 'string' || data.name.trim().length === 0) {
      errors.push(`${prefix}Category name is required and must be a non-empty string`)
    } else if (data.name.trim().length < 3) {
      errors.push(`${prefix}Category name must be at least 3 characters long`)
    } else if (data.name.trim().length > 100) {
      errors.push(`${prefix}Category name must be less than 100 characters`)
    }

    if (!data.description || typeof data.description !== 'string' || data.description.trim().length === 0) {
      errors.push(`${prefix}Category description is required and must be a non-empty string`)
    } else if (data.description.trim().length < 10) {
      warnings.push(`${prefix}Category description is quite short (less than 10 characters)`)
    } else if (data.description.trim().length > 500) {
      errors.push(`${prefix}Category description must be less than 500 characters`)
    }

    // Difficulty validation
    if (!data.difficulty || !this.VALID_DIFFICULTIES.includes(data.difficulty)) {
      errors.push(`${prefix}Valid difficulty level is required (${this.VALID_DIFFICULTIES.join(', ')})`)
    }

    // Optional fields validation
    if (data.icon && !this.EMOJI_REGEX.test(data.icon)) {
      warnings.push(`${prefix}Icon should be an emoji character`)
    }

    if (data.color && !this.VALID_COLORS.includes(data.color)) {
      warnings.push(`${prefix}Color should be a valid Tailwind CSS class (${this.VALID_COLORS.slice(0, 5).join(', ')}, ...)`)
    }

    if (data.estimatedTime !== undefined) {
      const time = Number(data.estimatedTime)
      if (isNaN(time) || time < 1 || time > 120) {
        errors.push(`${prefix}Estimated time must be a number between 1 and 120 minutes`)
      }
    }

    if (data.totalQuestions !== undefined) {
      const questions = Number(data.totalQuestions)
      if (isNaN(questions) || questions < 1 || questions > 100) {
        errors.push(`${prefix}Total questions must be a number between 1 and 100`)
      }
    }

    if (data.passingScore !== undefined) {
      const score = Number(data.passingScore)
      if (isNaN(score) || score < 50 || score > 100) {
        errors.push(`${prefix}Passing score must be a number between 50 and 100`)
      }
    }

    // Skills validation
    let skills: string[] = []
    if (data.skills) {
      if (typeof data.skills === 'string') {
        skills = data.skills.split(',').map(s => s.trim()).filter(Boolean)
      } else if (Array.isArray(data.skills)) {
        skills = data.skills.filter(s => typeof s === 'string' && s.trim()).map(s => s.trim())
      } else {
        warnings.push(`${prefix}Skills should be a string or array of strings`)
      }
    }

    if (skills.length === 0) {
      warnings.push(`${prefix}Consider adding skills to help categorize this quiz`)
    }

    // Generate processed data
    const processedData: CategoryData = {
      name: data.name?.trim() || '',
      description: data.description?.trim() || '',
      icon: data.icon || '📚',
      color: data.color || 'bg-blue-500',
      skills,
      difficulty: data.difficulty,
      estimatedTime: data.estimatedTime ? Number(data.estimatedTime) : 15,
      totalQuestions: data.totalQuestions ? Number(data.totalQuestions) : 10,
      passingScore: data.passingScore ? Number(data.passingScore) : 70,
      isActive: data.isActive !== false
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      data: processedData
    }
  }

  static validateQuestion(data: any, index?: number, availableCategories?: Map<string, string>): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []
    const prefix = index !== undefined ? `Row ${index + 1}: ` : ''

    // Question text validation
    if (!data.question || typeof data.question !== 'string' || data.question.trim().length === 0) {
      errors.push(`${prefix}Question text is required and must be a non-empty string`)
    } else if (data.question.trim().length < 10) {
      warnings.push(`${prefix}Question text is quite short (less than 10 characters)`)
    } else if (data.question.trim().length > 1000) {
      errors.push(`${prefix}Question text must be less than 1000 characters`)
    }

    // Category validation
    let categoryId = data.categoryId
    if (data.categoryName && availableCategories) {
      categoryId = availableCategories.get(data.categoryName)
      if (!categoryId) {
        errors.push(`${prefix}Category '${data.categoryName}' not found`)
      }
    } else if (!categoryId) {
      errors.push(`${prefix}Category name or ID is required`)
    }

    // Options validation
    const options: string[] = []
    for (let i = 1; i <= 6; i++) {
      const option = data[`option${i}`]
      if (option && typeof option === 'string' && option.trim().length > 0) {
        if (option.trim().length > 200) {
          errors.push(`${prefix}Option ${i} must be less than 200 characters`)
        } else {
          options.push(option.trim())
        }
      }
    }

    if (options.length < 2) {
      errors.push(`${prefix}At least 2 answer options are required`)
    }

    // Check for duplicate options
    const uniqueOptions = new Set(options.map(opt => opt.toLowerCase()))
    if (uniqueOptions.size !== options.length) {
      warnings.push(`${prefix}Some answer options appear to be duplicates`)
    }

    // Correct answer validation
    const correctAnswer = Number(data.correctAnswer) - 1 // Convert to 0-based index
    if (isNaN(correctAnswer) || correctAnswer < 0 || correctAnswer >= options.length) {
      errors.push(`${prefix}Correct answer must be a valid option number (1-${options.length})`)
    }

    // Difficulty validation
    if (!data.difficulty || !this.VALID_DIFFICULTIES.includes(data.difficulty)) {
      errors.push(`${prefix}Valid difficulty level is required (${this.VALID_DIFFICULTIES.join(', ')})`)
    }

    // Points validation
    if (data.points !== undefined) {
      const points = Number(data.points)
      if (isNaN(points) || points < 1 || points > 20) {
        errors.push(`${prefix}Points must be a number between 1 and 20`)
      }
    }

    // Explanation validation
    if (data.explanation && data.explanation.length > 500) {
      errors.push(`${prefix}Explanation must be less than 500 characters`)
    }

    // Tags validation
    let tags: string[] = []
    if (data.tags) {
      if (typeof data.tags === 'string') {
        tags = data.tags.split(',').map(t => t.trim()).filter(Boolean)
      } else if (Array.isArray(data.tags)) {
        tags = data.tags.filter(t => typeof t === 'string' && t.trim()).map(t => t.trim())
      } else {
        warnings.push(`${prefix}Tags should be a string or array of strings`)
      }
    }

    if (tags.length === 0) {
      warnings.push(`${prefix}Consider adding tags to help categorize this question`)
    }

    // Generate processed data
    const processedData: QuestionData & { categoryId: string } = {
      categoryId: categoryId || '',
      question: data.question?.trim() || '',
      option1: options[0] || '',
      option2: options[1] || '',
      option3: options[2] || '',
      option4: options[3] || '',
      option5: options[4] || '',
      option6: options[5] || '',
      correctAnswer: correctAnswer,
      explanation: data.explanation?.trim() || '',
      difficulty: data.difficulty,
      points: data.points ? Number(data.points) : this.getDefaultPoints(data.difficulty),
      tags,
      isActive: data.isActive !== false
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      data: processedData
    }
  }

  static validateBulkData(data: any[], type: 'categories' | 'questions', availableCategories?: Map<string, string>): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []
    const validatedData: any[] = []

    if (!Array.isArray(data)) {
      errors.push('Data must be an array')
      return { isValid: false, errors, warnings }
    }

    if (data.length === 0) {
      errors.push('Data array cannot be empty')
      return { isValid: false, errors, warnings }
    }

    if (data.length > 1000) {
      errors.push('Cannot process more than 1000 items at once')
      return { isValid: false, errors, warnings }
    }

    // Validate each item
    for (let i = 0; i < data.length; i++) {
      const item = data[i]
      let result: ValidationResult

      if (type === 'categories') {
        result = this.validateCategory(item, i)
      } else {
        result = this.validateQuestion(item, i, availableCategories)
      }

      if (result.isValid && result.data) {
        validatedData.push(result.data)
      }

      errors.push(...result.errors)
      warnings.push(...result.warnings)
    }

    // Check for duplicates
    if (type === 'categories') {
      const names = validatedData.map(item => item.name.toLowerCase())
      const duplicates = names.filter((name, index) => names.indexOf(name) !== index)
      if (duplicates.length > 0) {
        warnings.push(`Duplicate category names found: ${[...new Set(duplicates)].join(', ')}`)
      }
    } else {
      const questions = validatedData.map(item => item.question.toLowerCase())
      const duplicates = questions.filter((question, index) => questions.indexOf(question) !== index)
      if (duplicates.length > 0) {
        warnings.push(`Duplicate questions found (${duplicates.length} duplicates)`)
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      data: validatedData
    }
  }

  private static getDefaultPoints(difficulty: string): number {
    switch (difficulty) {
      case 'beginner': return 5
      case 'intermediate': return 10
      case 'advanced': return 15
      case 'expert': return 20
      default: return 10
    }
  }
}

// lib/services/companies-showcase.service.ts
'use client'

export interface CompanyShowcase {
  id: string
  name: string
  slug: string
  description: string
  logo: string | null
  website?: string
  industry: string
  size: string
  location: string
  openJobs: number
  featured: boolean
  verified: boolean
  founded?: number
}

export interface CompanyShowcaseQuery {
  limit?: number
  featured?: boolean
}

export interface CompanyShowcaseResult {
  companies: CompanyShowcase[]
  total: number
}

export interface CompanyShowcaseResponse {
  success: boolean
  data: CompanyShowcaseResult
}

class CompaniesShowcaseService {
  private baseUrl = '/api/v1'
  private cache: { data: CompanyShowcaseResult; timestamp: number } | null = null
  private cacheExpiry = 10 * 60 * 1000 // 10 minutes

  /**
   * Get companies for showcase with caching
   */
  async getShowcaseCompanies(query: CompanyShowcaseQuery = {}): Promise<CompanyShowcaseResult> {
    try {
      const cacheKey = JSON.stringify(query)
      
      // Check cache first
      if (this.cache && Date.now() - this.cache.timestamp < this.cacheExpiry) {
        return this.cache.data
      }

      const params = new URLSearchParams()
      
      if (query.limit) params.append('limit', query.limit.toString())
      if (query.featured !== undefined) params.append('featured', query.featured.toString())

      const response = await fetch(`${this.baseUrl}/companies/showcase?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch showcase companies: ${response.statusText}`)
      }

      const result: CompanyShowcaseResponse = await response.json()
      
      if (!result.success) {
        throw new Error(result.data as any || 'Failed to fetch showcase companies')
      }

      // Update cache
      this.cache = {
        data: result.data,
        timestamp: Date.now()
      }

      return result.data

    } catch (error) {
      console.error('Error fetching showcase companies:', error)
      
      // Return fallback companies if API fails
      return this.getFallbackCompanies()
    }
  }

  /**
   * Get featured companies only
   */
  async getFeaturedCompanies(limit: number = 4): Promise<CompanyShowcase[]> {
    const result = await this.getShowcaseCompanies({ featured: true, limit })
    return result.companies
  }

  /**
   * Get all showcase companies
   */
  async getAllShowcaseCompanies(limit: number = 6): Promise<CompanyShowcase[]> {
    const result = await this.getShowcaseCompanies({ limit })
    return result.companies
  }

  /**
   * Get fallback companies when API is unavailable
   */
  private getFallbackCompanies(): CompanyShowcaseResult {
    return {
      companies: [
        {
          id: '1',
          name: 'TechCorp Inc.',
          slug: 'techcorp-inc',
          description: 'Leading technology company focused on innovative solutions.',
          logo: '/images/companies/techcorp-logo.jpg',
          website: 'https://techcorp.com',
          industry: 'Technology',
          size: '1000+ employees',
          location: 'San Francisco, CA',
          openJobs: 15,
          featured: true,
          verified: true,
          founded: 2010
        },
        {
          id: '2',
          name: 'InnovateLab',
          slug: 'innovatelab',
          description: 'Fast-growing startup revolutionizing the industry.',
          logo: '/images/companies/innovatelab-logo.jpg',
          website: 'https://innovatelab.com',
          industry: 'Startup',
          size: '50-200 employees',
          location: 'Austin, TX',
          openJobs: 8,
          featured: false,
          verified: true,
          founded: 2018
        },
        {
          id: '3',
          name: 'DesignStudio',
          slug: 'designstudio',
          description: 'Creative agency with award-winning design solutions.',
          logo: '/images/companies/globaltech-logo.jpg',
          website: 'https://designstudio.com',
          industry: 'Design',
          size: '100-500 employees',
          location: 'New York, NY',
          openJobs: 12,
          featured: true,
          verified: true,
          founded: 2015
        },
        {
          id: '4',
          name: 'CloudTech',
          slug: 'cloudtech',
          description: 'Cloud infrastructure and services provider.',
          logo: '/images/hero/office-meeting.jpg',
          website: 'https://cloudtech.com',
          industry: 'Cloud Services',
          size: '500-1000 employees',
          location: 'Seattle, WA',
          openJobs: 20,
          featured: false,
          verified: true,
          founded: 2012
        }
      ],
      total: 4
    }
  }

  /**
   * Clear the cache (useful for testing or manual refresh)
   */
  clearCache(): void {
    this.cache = null
  }

  /**
   * Format company size for display
   */
  formatCompanySize(size: string): string {
    const sizeMap: Record<string, string> = {
      'startup': '1-10 employees',
      'small': '11-50 employees', 
      'medium': '51-200 employees',
      'large': '201-1000 employees',
      'enterprise': '1000+ employees'
    }
    
    return sizeMap[size] || size || 'Size not specified'
  }

  /**
   * Get company logo URL with fallback
   */
  getCompanyLogoUrl(company: CompanyShowcase): string {
    return company.logo || '/placeholder.svg'
  }
}

// Create singleton instance
export const companiesShowcaseService = new CompaniesShowcaseService()

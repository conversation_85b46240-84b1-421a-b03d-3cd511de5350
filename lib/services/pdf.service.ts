import PDFDocument from 'pdfkit'
import { connectDB } from '@/lib/db'
import { Invoice } from '@/lib/models/invoice.model'
import { Company } from '@/lib/models/company.model'

export class PDFService {
  static async generateInvoicePDF(invoiceId: string): Promise<Buffer> {
    await connectDB()
    
    const invoice = await Invoice.findById(invoiceId)
      .populate('company')
      .populate('subscription.plan')
    
    if (!invoice) {
      throw new Error('Invoice not found')
    }

    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument({ margin: 50 })
        const buffers: Buffer[] = []

        doc.on('data', buffers.push.bind(buffers))
        doc.on('end', () => {
          const pdfData = Buffer.concat(buffers)
          resolve(pdfData)
        })

        // Company header
        this.addHeader(doc, invoice)
        
        // Invoice details
        this.addInvoiceDetails(doc, invoice)
        
        // Bill to section
        this.addBillToSection(doc, invoice)
        
        // Items table
        this.addItemsTable(doc, invoice)
        
        // Totals
        this.addTotals(doc, invoice)
        
        // Footer
        this.addFooter(doc, invoice)

        doc.end()
      } catch (error) {
        reject(error)
      }
    })
  }

  private static addHeader(doc: PDFKit.PDFDocument, invoice: any) {
    // Company logo placeholder (you can add actual logo loading here)
    doc.fontSize(24)
       .fillColor('#2563eb')
       .text('JobMarketplace', 50, 50)
    
    doc.fontSize(10)
       .fillColor('#666666')
       .text('Professional Job Marketplace Platform', 50, 80)
       .text('123 Business Street', 50, 95)
       .text('City, State 12345', 50, 110)
       .text('<EMAIL>', 50, 125)
       .text('(555) 123-4567', 50, 140)

    // Invoice title
    doc.fontSize(28)
       .fillColor('#000000')
       .text('INVOICE', 400, 50, { align: 'right' })
    
    doc.moveTo(50, 170)
       .lineTo(550, 170)
       .stroke('#cccccc')
  }

  private static addInvoiceDetails(doc: PDFKit.PDFDocument, invoice: any) {
    const startY = 190

    doc.fontSize(12)
       .fillColor('#000000')
       .text('Invoice Number:', 400, startY)
       .text(`#${invoice.invoiceNumber}`, 500, startY)
       
       .text('Issue Date:', 400, startY + 20)
       .text(this.formatDate(invoice.issueDate), 500, startY + 20)
       
       .text('Due Date:', 400, startY + 40)
       .text(this.formatDate(invoice.dueDate), 500, startY + 40)
       
       .text('Status:', 400, startY + 60)

    // Status with color
    const statusColor = this.getStatusColor(invoice.status)
    doc.fillColor(statusColor)
       .text(invoice.status.toUpperCase(), 500, startY + 60)
  }

  private static addBillToSection(doc: PDFKit.PDFDocument, invoice: any) {
    const startY = 190

    doc.fontSize(14)
       .fillColor('#000000')
       .text('Bill To:', 50, startY)

    doc.fontSize(12)
       .text(invoice.company.name, 50, startY + 25)
       .text(invoice.company.email, 50, startY + 45)

    if (invoice.company.address) {
      doc.text(invoice.company.address.street || '', 50, startY + 65)
      doc.text(`${invoice.company.address.city || ''}, ${invoice.company.address.state || ''} ${invoice.company.address.zipCode || ''}`, 50, startY + 85)
      doc.text(invoice.company.address.country || '', 50, startY + 105)
    }
  }

  private static addItemsTable(doc: PDFKit.PDFDocument, invoice: any) {
    const startY = 320
    const tableTop = startY
    const itemCodeX = 50
    const descriptionX = 150
    const quantityX = 350
    const priceX = 400
    const amountX = 480

    // Table header
    doc.fontSize(12)
       .fillColor('#000000')
    
    this.generateTableRow(
      doc,
      tableTop,
      'Item',
      'Description',
      'Qty',
      'Price',
      'Amount'
    )

    // Header line
    doc.moveTo(50, tableTop + 20)
       .lineTo(550, tableTop + 20)
       .stroke('#cccccc')

    // Table rows
    let position = tableTop + 30
    invoice.items.forEach((item: any, index: number) => {
      this.generateTableRow(
        doc,
        position,
        `${index + 1}`,
        item.description,
        item.quantity.toString(),
        this.formatCurrency(item.unitPrice, invoice.currency),
        this.formatCurrency(item.amount, invoice.currency)
      )
      position += 25
    })

    // Bottom line
    doc.moveTo(50, position)
       .lineTo(550, position)
       .stroke('#cccccc')

    return position + 20
  }

  private static addTotals(doc: PDFKit.PDFDocument, invoice: any) {
    const startY = 450
    
    doc.fontSize(12)
       .fillColor('#000000')

    // Subtotal
    doc.text('Subtotal:', 400, startY)
       .text(this.formatCurrency(invoice.subtotal, invoice.currency), 500, startY, { align: 'right' })

    // Tax
    if (invoice.taxAmount > 0) {
      doc.text(`Tax (${(invoice.taxRate * 100).toFixed(1)}%):`, 400, startY + 20)
         .text(this.formatCurrency(invoice.taxAmount, invoice.currency), 500, startY + 20, { align: 'right' })
    }

    // Discount
    if (invoice.discountAmount > 0) {
      doc.text('Discount:', 400, startY + 40)
         .text(`-${this.formatCurrency(invoice.discountAmount, invoice.currency)}`, 500, startY + 40, { align: 'right' })
    }

    // Total line
    const totalY = startY + (invoice.taxAmount > 0 ? 60 : 40) + (invoice.discountAmount > 0 ? 20 : 0)
    doc.moveTo(400, totalY - 5)
       .lineTo(550, totalY - 5)
       .stroke('#000000')

    // Total
    doc.fontSize(14)
       .fillColor('#000000')
       .text('Total:', 400, totalY)
       .text(this.formatCurrency(invoice.totalAmount, invoice.currency), 500, totalY, { align: 'right' })
  }

  private static addFooter(doc: PDFKit.PDFDocument, invoice: any) {
    const startY = 650

    if (invoice.notes) {
      doc.fontSize(10)
         .fillColor('#666666')
         .text('Notes:', 50, startY)
         .text(invoice.notes, 50, startY + 15, { width: 500 })
    }

    // Payment terms
    doc.fontSize(10)
       .fillColor('#666666')
       .text(`Payment Terms: ${invoice.paymentTerms}`, 50, startY + 60)

    // Thank you message
    doc.fontSize(12)
       .fillColor('#2563eb')
       .text('Thank you for your business!', 50, startY + 90)

    // Page number
    doc.fontSize(8)
       .fillColor('#999999')
       .text('Page 1 of 1', 500, 750, { align: 'right' })
  }

  private static generateTableRow(
    doc: PDFKit.PDFDocument,
    y: number,
    item: string,
    description: string,
    quantity: string,
    price: string,
    amount: string
  ) {
    doc.fontSize(10)
       .text(item, 50, y)
       .text(description, 150, y, { width: 180 })
       .text(quantity, 350, y, { align: 'center' })
       .text(price, 400, y, { align: 'right' })
       .text(amount, 480, y, { align: 'right' })
  }

  private static formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(new Date(date))
  }

  private static formatCurrency(amount: number, currency: string): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD'
    }).format(amount)
  }

  private static getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'paid':
        return '#10b981'
      case 'overdue':
        return '#ef4444'
      case 'sent':
        return '#3b82f6'
      case 'draft':
        return '#6b7280'
      default:
        return '#000000'
    }
  }

  // Generate receipt PDF for payments
  static async generateReceiptPDF(paymentId: string): Promise<Buffer> {
    await connectDB()
    
    const payment = await Payment.findById(paymentId)
      .populate('company')
      .populate('subscription.plan')
    
    if (!payment) {
      throw new Error('Payment not found')
    }

    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument({ margin: 50 })
        const buffers: Buffer[] = []

        doc.on('data', buffers.push.bind(buffers))
        doc.on('end', () => {
          const pdfData = Buffer.concat(buffers)
          resolve(pdfData)
        })

        // Header
        doc.fontSize(24)
           .fillColor('#2563eb')
           .text('PAYMENT RECEIPT', 50, 50)

        // Receipt details
        doc.fontSize(12)
           .fillColor('#000000')
           .text(`Receipt #: ${payment._id}`, 50, 100)
           .text(`Date: ${this.formatDate(payment.paidAt || payment.createdAt)}`, 50, 120)
           .text(`Payment Method: ${payment.paymentMethod.toUpperCase()}`, 50, 140)

        // Company details
        doc.text('Paid by:', 50, 180)
           .text(payment.company.name, 50, 200)
           .text(payment.company.email, 50, 220)

        // Payment details
        doc.text('Description:', 50, 260)
           .text(payment.description, 50, 280)
           .text('Amount:', 50, 320)
           .fontSize(18)
           .text(this.formatCurrency(payment.amount, payment.currency), 50, 340)

        // Status
        doc.fontSize(12)
           .fillColor(payment.status === 'succeeded' ? '#10b981' : '#ef4444')
           .text(`Status: ${payment.status.toUpperCase()}`, 50, 380)

        doc.end()
      } catch (error) {
        reject(error)
      }
    })
  }
}

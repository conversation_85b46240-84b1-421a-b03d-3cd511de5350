// Base Service
export { default as BaseService } from './base.service'
export type { PaginationOptions, PaginationResult, SearchOptions } from './base.service'

// Cache Service
export { cacheService, CacheService } from './cache.service'

// Auth Services
export { authService, AuthService } from './auth.service'
export type { LoginRequest, RegisterRequest, AuthResponse } from './auth.service'

// User Services
export { userService, UserService } from './user.service'
export type {
  UserProfile,
  UpdateUserRequest,
  ChangePasswordRequest
} from './user.service'

// Company Services
export { companyService, CompanyService } from './company.service'
export type {
  CompanyProfile,
  CreateCompanyRequest,
  UpdateCompanyRequest
} from './company.service'

// Company Settings Services
export { companySettingsService, CompanySettingsService } from './company-settings.service'

// Job Services
export { jobService, JobService } from './job.service'
export type {
  JobProfile,
  CreateJobRequest,
  UpdateJobRequest,
  JobSearchFilters
} from './job.service'

// Application Services
export { applicationService, ApplicationService } from './application.service'
export type {
  ApplicationProfile,
  CreateApplicationRequest,
  UpdateApplicationStatusRequest,
  AddNoteRequest
} from './application.service'

// Notification Services
export { notificationService, NotificationService } from './notification.service'
export type {
  NotificationProfile,
  CreateNotificationRequest,
  NotificationStats
} from './notification.service'

// Validation Services
export { validationService, ValidationService } from './validation.service'

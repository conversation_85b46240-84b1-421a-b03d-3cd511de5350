// lib/services/home-jobs-frontend.service.ts
'use client'

import { JobFrontend, jobsFrontendService } from './jobs-frontend.service'

export interface LocationBasedJobQuery {
  // Location parameters
  userLocation?: {
    city: string
    region: string
    country: string
    continent: string
    coordinates?: {
      latitude: number
      longitude: number
    }
  }
  locationLevel?: 'local' | 'regional' | 'national' | 'continental' | 'international'
  maxDistance?: number // in kilometers
  
  // Search parameters
  searchTerm?: string
  locationTerm?: string
  
  // Filters
  jobTypes?: string[]
  experienceLevels?: string[]
  salaryRange?: {
    min?: number
    max?: number
  }
  workModels?: string[]
  categories?: string[]
  skills?: string[]
  companies?: string[]
  
  // Pagination and sorting
  page?: number
  limit?: number
  sortBy?: 'relevance' | 'date' | 'salary' | 'distance'
  sortOrder?: 'asc' | 'desc'
  
  // Special flags
  featuredOnly?: boolean
  urgentOnly?: boolean
  remoteOnly?: boolean
  recentOnly?: boolean // last 7 days
}

export interface HomeJobSearchResult {
  jobs: JobFrontend[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  locationStats: {
    local: number
    regional: number
    national: number
    continental: number
    international: number
  }
  searchMeta: {
    searchTime: number
    totalResults: number
    query: string
    appliedFilters: string[]
  }
}

export interface JobInteraction {
  jobId: string
  type: 'like' | 'bookmark' | 'share' | 'view' | 'apply'
  timestamp: Date
  metadata?: Record<string, unknown>
}

class HomeJobsFrontendService {
  private baseUrl = '/api/v1'

  /**
   * Search jobs with location-based filtering for home page
   */
  async searchLocationBasedJobs(query: LocationBasedJobQuery): Promise<HomeJobSearchResult> {
    try {
      const params = new URLSearchParams()
      
      // Add all query parameters
      Object.entries(query).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            value.forEach(v => params.append(key, v.toString()))
          } else if (typeof value === 'object') {
            params.append(key, JSON.stringify(value))
          } else {
            params.append(key, value.toString())
          }
        }
      })

      const response = await fetch(`${this.baseUrl}/jobs/home/<USER>

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to search jobs')
      }

      const data = await response.json()

      // Transform backend jobs to frontend format
      const transformedJobs = data.jobs.map((job: any) => jobsFrontendService.transformJob(job))

      return {
        ...data,
        jobs: transformedJobs
      }
    } catch (error) {
      console.error('Error searching location-based jobs:', error)
      throw error
    }
  }

  /**
   * Get jobs by specific location level
   */
  async getJobsByLocationLevel(
    locationLevel: 'local' | 'regional' | 'national' | 'continental' | 'international',
    userLocation: LocationBasedJobQuery['userLocation'],
    options: {
      limit?: number
      page?: number
      filters?: Partial<LocationBasedJobQuery>
    } = {}
  ): Promise<HomeJobSearchResult> {
    const query: LocationBasedJobQuery = {
      userLocation,
      locationLevel,
      limit: options.limit || 6,
      page: options.page || 1,
      sortBy: 'relevance',
      ...options.filters
    }

    return this.searchLocationBasedJobs(query)
  }

  /**
   * Get location statistics for current user location
   */
  async getLocationStats(userLocation: LocationBasedJobQuery['userLocation']): Promise<{
    local: number
    regional: number
    national: number
    continental: number
    international: number
  }> {
    try {
      const params = new URLSearchParams()
      if (userLocation) {
        params.append('userLocation', JSON.stringify(userLocation))
      }

      const response = await fetch(`${this.baseUrl}/jobs/home/<USER>
      
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to get location stats')
      }

      return await response.json()
    } catch (error) {
      console.error('Error getting location stats:', error)
      throw error
    }
  }

  /**
   * Advanced search with sophisticated scoring algorithm
   */
  async advancedSearch(
    searchTerm: string,
    locationTerm: string,
    userLocation?: LocationBasedJobQuery['userLocation'],
    filters?: Partial<LocationBasedJobQuery>
  ): Promise<HomeJobSearchResult> {
    const query: LocationBasedJobQuery = {
      searchTerm: searchTerm.trim(),
      locationTerm: locationTerm.trim(),
      userLocation,
      sortBy: 'relevance',
      limit: 20,
      page: 1,
      ...filters
    }

    return this.searchLocationBasedJobs(query)
  }

  /**
   * Get featured jobs for home page
   */
  async getFeaturedJobs(
    userLocation?: LocationBasedJobQuery['userLocation'],
    limit: number = 6
  ): Promise<JobFrontend[]> {
    const result = await this.searchLocationBasedJobs({
      userLocation,
      featuredOnly: true,
      limit,
      sortBy: 'date',
      sortOrder: 'desc'
    })

    return result.jobs
  }

  /**
   * Get urgent jobs for home page
   */
  async getUrgentJobs(
    userLocation?: LocationBasedJobQuery['userLocation'],
    limit: number = 6
  ): Promise<JobFrontend[]> {
    const result = await this.searchLocationBasedJobs({
      userLocation,
      urgentOnly: true,
      limit,
      sortBy: 'date',
      sortOrder: 'desc'
    })

    return result.jobs
  }

  /**
   * Get recent jobs (last 7 days)
   */
  async getRecentJobs(
    userLocation?: LocationBasedJobQuery['userLocation'],
    limit: number = 12
  ): Promise<JobFrontend[]> {
    const result = await this.searchLocationBasedJobs({
      userLocation,
      recentOnly: true,
      limit,
      sortBy: 'date',
      sortOrder: 'desc'
    })

    return result.jobs
  }

  /**
   * Track job interaction (like, bookmark, share, view)
   */
  async trackJobInteraction(interaction: JobInteraction): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/jobs/interactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(interaction)
      })

      if (!response.ok) {
        console.warn('Failed to track job interaction:', interaction)
      }
    } catch (error) {
      console.warn('Error tracking job interaction:', error)
    }
  }

  /**
   * Get user's job interactions (likes, bookmarks)
   */
  async getUserJobInteractions(userId: string): Promise<{
    likedJobs: string[]
    bookmarkedJobs: string[]
    viewedJobs: string[]
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/jobs/interactions/${userId}`)
      
      if (!response.ok) {
        return { likedJobs: [], bookmarkedJobs: [], viewedJobs: [] }
      }

      return await response.json()
    } catch (error) {
      console.error('Error getting user job interactions:', error)
      return { likedJobs: [], bookmarkedJobs: [], viewedJobs: [] }
    }
  }

  /**
   * Get job recommendations based on user location and preferences
   */
  async getJobRecommendations(
    userLocation: LocationBasedJobQuery['userLocation'],
    userPreferences: {
      jobTypes?: string[]
      experienceLevels?: string[]
      skills?: string[]
      salaryRange?: { min?: number; max?: number }
      workModels?: string[]
    },
    limit: number = 10
  ): Promise<JobFrontend[]> {
    const result = await this.searchLocationBasedJobs({
      userLocation,
      jobTypes: userPreferences.jobTypes,
      experienceLevels: userPreferences.experienceLevels,
      skills: userPreferences.skills,
      salaryRange: userPreferences.salaryRange,
      workModels: userPreferences.workModels,
      limit,
      sortBy: 'relevance'
    })

    return result.jobs
  }
}

export const homeJobsFrontendService = new HomeJobsFrontendService()
export default homeJobsFrontendService

// lib/services/validation.service.ts
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import { LoginRequest, RegisterRequest } from './auth.service'

export class ValidationService {
  /**
   * Validate login request data
   */
  validateLoginRequest(data: unknown): LoginRequest {
    const errors: string[] = []

    // Type guard to ensure data is an object
    if (typeof data !== 'object' || data === null) {
      throw errorService.createError(ErrorCode.VALIDATION_ERROR, 'Invalid request data')
    }

    const requestData = data as Record<string, unknown>

    if (!requestData.email) errors.push('Email is required')
    if (!requestData.password) errors.push('Password is required')
    
    if (requestData.email && typeof requestData.email === 'string' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(requestData.email)) {
      errors.push('Invalid email format')
    }
    
    if (errors.length > 0) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        `Validation failed: ${errors.join(', ')}`,
        undefined,
        { validationErrors: errors }
      )
    }
    
    return {
      email: String(requestData.email).toLowerCase().trim(),
      password: String(requestData.password),
      rememberMe: Boolean(requestData.rememberMe)
    }
  }

  /**
   * Validate registration request data
   */
  validateRegisterRequest(data: unknown): RegisterRequest {
    const errors: string[] = []

    // Type guard to ensure data is an object
    if (typeof data !== 'object' || data === null) {
      throw errorService.createError(ErrorCode.VALIDATION_ERROR, 'Invalid request data')
    }

    const requestData = data as Record<string, unknown>

    // Required fields validation
    if (!requestData.email) errors.push('Email is required')
    if (!requestData.password) errors.push('Password is required')
    if (!requestData.firstName) errors.push('First name is required')
    if (!requestData.lastName) errors.push('Last name is required')

    // Email format validation
    if (requestData.email && typeof requestData.email === 'string' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(requestData.email)) {
      errors.push('Invalid email format')
    }

    // Password strength validation
    if (requestData.password && typeof requestData.password === 'string' && requestData.password.length < 8) {
      errors.push('Password must be at least 8 characters long')
    }

    // Role validation
    if (requestData.role && typeof requestData.role === 'string' && !['job_seeker', 'company_admin'].includes(requestData.role)) {
      errors.push('Invalid role. Must be job_seeker or company_admin')
    }

    // Phone validation (if provided)
    if (requestData.phone && typeof requestData.phone === 'string' && !/^\+?[\d\s\-\(\)]+$/.test(requestData.phone)) {
      errors.push('Invalid phone number format')
    }

    // Location validation - country is required if location is provided
    if (requestData.location && typeof requestData.location === 'object') {
      const location = requestData.location as Record<string, unknown>
      if (!location.country || typeof location.country !== 'string' || location.country.trim() === '') {
        errors.push('Country is required for location (needed for job applications and filtering)')
      }
    }

    if (errors.length > 0) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        `Validation failed: ${errors.join(', ')}`,
        undefined,
        { validationErrors: errors }
      )
    }

    // Process location data
    let processedLocation: any = undefined
    if (requestData.location && typeof requestData.location === 'object') {
      const location = requestData.location as Record<string, unknown>
      processedLocation = {
        city: location.city ? String(location.city).trim() : undefined,
        state: location.state ? String(location.state).trim() : undefined,
        country: location.country ? String(location.country).trim() : 'United States',
        address: location.address ? String(location.address).trim() : undefined,
        postalCode: location.postalCode ? String(location.postalCode).trim() : undefined
      }
      // Ensure country is always a string for the interface requirement
      if (!processedLocation.country) {
        processedLocation.country = 'United States'
      }
    }

    return {
      email: String(requestData.email).toLowerCase().trim(),
      password: String(requestData.password),
      firstName: String(requestData.firstName).trim(),
      lastName: String(requestData.lastName).trim(),
      role: (requestData.role as 'job_seeker' | 'company_admin') || 'job_seeker',
      phone: requestData.phone ? String(requestData.phone).trim() : undefined,
      location: processedLocation,
      company: requestData.company as any // Pass through company data
    }
  }

  /**
   * Enhanced validation for company admin registration
   */
  validateCompanyAdminRegisterRequest(data: unknown): RegisterRequest {
    const errors: string[] = []

    // Type guard to ensure data is an object
    if (typeof data !== 'object' || data === null) {
      throw errorService.createError(ErrorCode.VALIDATION_ERROR, 'Invalid request data')
    }

    const requestData = data as Record<string, any>

    // Required fields validation
    if (!requestData.email) errors.push('Email is required')
    if (!requestData.password) errors.push('Password is required')
    if (!requestData.firstName) errors.push('First name is required')
    if (!requestData.lastName) errors.push('Last name is required')

    // Role must be company_admin for this endpoint
    if (!requestData.role || requestData.role !== 'company_admin') {
      errors.push('Role must be company_admin for this registration endpoint')
    }

    // Company data is required for company admin
    if (!requestData.company) {
      errors.push('Company information is required for company admin registration')
    } else {
      // Validate company fields
      if (!requestData.company.name) errors.push('Company name is required')
      if (!requestData.company.industry) errors.push('Company industry is required')
      if (!requestData.company.size) errors.push('Company size is required')
      if (!requestData.company.description) errors.push('Company description is required')

      // Validate company name length
      if (requestData.company.name && String(requestData.company.name).trim().length < 2) {
        errors.push('Company name must be at least 2 characters long')
      }
      if (requestData.company.name && String(requestData.company.name).trim().length > 100) {
        errors.push('Company name must be less than 100 characters')
      }

      // Validate company description length
      if (requestData.company.description && String(requestData.company.description).trim().length < 10) {
        errors.push('Company description must be at least 10 characters long')
      }
      if (requestData.company.description && String(requestData.company.description).trim().length > 1000) {
        errors.push('Company description must be less than 1000 characters')
      }

      // Validate company size
      const validSizes = ['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+']
      if (requestData.company.size && !validSizes.includes(requestData.company.size)) {
        errors.push(`Invalid company size. Must be one of: ${validSizes.join(', ')}`)
      }

      // Validate website URL if provided
      if (requestData.company.website) {
        try {
          const website = String(requestData.company.website).trim()
          new URL(website.startsWith('http') ? website : `https://${website}`)
        } catch {
          errors.push('Invalid website URL format')
        }
      }

      // Validate company location if provided
      if (requestData.company.location && typeof requestData.company.location === 'object') {
        const location = requestData.company.location as Record<string, unknown>
        if (!location.city || typeof location.city !== 'string' || location.city.trim() === '') {
          errors.push('Company city is required')
        }
        if (!location.country || typeof location.country !== 'string' || location.country.trim() === '') {
          errors.push('Company country is required')
        }
      }
    }

    // Email format validation
    if (requestData.email && typeof requestData.email === 'string' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(requestData.email)) {
      errors.push('Invalid email format')
    }

    // Enhanced password validation
    if (requestData.password && typeof requestData.password === 'string') {
      if (requestData.password.length < 8) {
        errors.push('Password must be at least 8 characters long')
      }
      if (!/(?=.*[a-z])/.test(requestData.password)) {
        errors.push('Password must contain at least one lowercase letter')
      }
      if (!/(?=.*[A-Z])/.test(requestData.password)) {
        errors.push('Password must contain at least one uppercase letter')
      }
      if (!/(?=.*\d)/.test(requestData.password)) {
        errors.push('Password must contain at least one number')
      }
    }

    // Phone validation (if provided)
    if (requestData.phone && typeof requestData.phone === 'string' && !/^\+?[\d\s\-\(\)]+$/.test(requestData.phone)) {
      errors.push('Invalid phone number format')
    }

    // User location validation - country is required if location is provided
    if (requestData.location && typeof requestData.location === 'object') {
      const location = requestData.location as Record<string, unknown>
      if (!location.country || typeof location.country !== 'string' || location.country.trim() === '') {
        errors.push('Country is required for user location (needed for job applications and filtering)')
      }
    }

    if (errors.length > 0) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        `Validation failed: ${errors.join(', ')}`,
        undefined,
        { validationErrors: errors }
      )
    }

    return {
      email: String(requestData.email).toLowerCase().trim(),
      password: String(requestData.password),
      firstName: String(requestData.firstName).trim(),
      lastName: String(requestData.lastName).trim(),
      role: 'company_admin',
      phone: requestData.phone ? String(requestData.phone).trim() : undefined,
      location: requestData.location ? {
        city: requestData.location.city ? String(requestData.location.city).trim() : undefined,
        state: requestData.location.state ? String(requestData.location.state).trim() : undefined,
        country: requestData.location.country ? String(requestData.location.country).trim() : undefined
      } : undefined,
      company: {
        name: String(requestData.company.name).trim(),
        website: requestData.company.website ? String(requestData.company.website).trim() : undefined,
        industry: String(requestData.company.industry).trim(),
        size: String(requestData.company.size).trim(),
        description: String(requestData.company.description).trim(),
        location: requestData.company.location ? {
          city: requestData.company.location.city ? String(requestData.company.location.city).trim() : undefined,
          state: requestData.company.location.state ? String(requestData.company.location.state).trim() : undefined,
          country: requestData.company.location.country ? String(requestData.company.location.country).trim() : undefined,
          address: requestData.company.location.address ? String(requestData.company.location.address).trim() : undefined,
          postalCode: requestData.company.location.postalCode ? String(requestData.company.location.postalCode).trim() : undefined
        } : undefined
      }
    }
  }

  /**
   * Validate email format
   */
  validateEmail(email: string): boolean {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
  }

  /**
   * Validate password strength
   */
  validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long')
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter')
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter')
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number')
    }
    
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Validate phone number format
   */
  validatePhone(phone: string): boolean {
    return /^\+?[\d\s\-\(\)]+$/.test(phone)
  }

  /**
   * Sanitize string input
   */
  sanitizeString(input: string): string {
    return input.trim().replace(/[<>]/g, '')
  }

  /**
   * Validate required fields
   */
  validateRequiredFields(data: Record<string, unknown>, requiredFields: string[]): string[] {
    const errors: string[] = []
    
    requiredFields.forEach(field => {
      if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
        errors.push(`${field} is required`)
      }
    })
    
    return errors
  }
}

export const validationService = new ValidationService()

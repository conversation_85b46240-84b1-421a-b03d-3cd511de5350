import nodemailer from 'nodemailer'
import { connectDB } from '@/lib/db'

// Email configuration
const emailConfig = {
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS
  }
}

export class EmailService {
  private static transporter = nodemailer.createTransport(emailConfig)

  // Send invoice email with PDF attachment
  static async sendInvoiceEmail(
    to: string,
    subject: string,
    pdfBuffer: Buffer,
    filename: string,
    invoiceData?: any
  ) {
    try {
      const mailOptions = {
        from: `"JobMarketplace Billing" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
        to,
        subject,
        html: this.getInvoiceEmailTemplate(invoiceData),
        attachments: [
          {
            filename,
            content: pdfBuffer,
            contentType: 'application/pdf'
          }
        ]
      }

      const result = await this.transporter.sendMail(mailOptions)
      console.log('Invoice email sent:', result.messageId)
      return result
    } catch (error) {
      console.error('Error sending invoice email:', error)
      throw error
    }
  }

  // Send payment confirmation email
  static async sendPaymentConfirmation(
    to: string,
    companyName: string,
    paymentData: any
  ) {
    try {
      const mailOptions = {
        from: `"JobMarketplace Billing" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
        to,
        subject: 'Payment Confirmation - JobMarketplace',
        html: this.getPaymentConfirmationTemplate(companyName, paymentData)
      }

      const result = await this.transporter.sendMail(mailOptions)
      console.log('Payment confirmation email sent:', result.messageId)
      return result
    } catch (error) {
      console.error('Error sending payment confirmation email:', error)
      throw error
    }
  }

  // Send payment failed notification
  static async sendPaymentFailedNotification(
    to: string,
    companyName: string,
    paymentData: any
  ) {
    try {
      const mailOptions = {
        from: `"JobMarketplace Billing" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
        to,
        subject: 'Payment Failed - Action Required',
        html: this.getPaymentFailedTemplate(companyName, paymentData)
      }

      const result = await this.transporter.sendMail(mailOptions)
      console.log('Payment failed email sent:', result.messageId)
      return result
    } catch (error) {
      console.error('Error sending payment failed email:', error)
      throw error
    }
  }

  // Send subscription status update
  static async sendSubscriptionStatusUpdate(
    to: string,
    companyName: string,
    status: string,
    subscriptionData: any
  ) {
    try {
      const mailOptions = {
        from: `"JobMarketplace Billing" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
        to,
        subject: `Subscription ${status.charAt(0).toUpperCase() + status.slice(1)} - JobMarketplace`,
        html: this.getSubscriptionStatusTemplate(companyName, status, subscriptionData)
      }

      const result = await this.transporter.sendMail(mailOptions)
      console.log('Subscription status email sent:', result.messageId)
      return result
    } catch (error) {
      console.error('Error sending subscription status email:', error)
      throw error
    }
  }

  // Send subscription canceled notification
  static async sendSubscriptionCanceled(
    to: string,
    companyName: string,
    subscriptionData: any
  ) {
    try {
      const mailOptions = {
        from: `"JobMarketplace Billing" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
        to,
        subject: 'Subscription Canceled - JobMarketplace',
        html: this.getSubscriptionCanceledTemplate(companyName, subscriptionData)
      }

      const result = await this.transporter.sendMail(mailOptions)
      console.log('Subscription canceled email sent:', result.messageId)
      return result
    } catch (error) {
      console.error('Error sending subscription canceled email:', error)
      throw error
    }
  }

  // Send trial ending reminder
  static async sendTrialEndingReminder(
    to: string,
    companyName: string,
    daysRemaining: number,
    subscriptionData: any
  ) {
    try {
      const mailOptions = {
        from: `"JobMarketplace Billing" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
        to,
        subject: `Your trial ends in ${daysRemaining} days - JobMarketplace`,
        html: this.getTrialEndingTemplate(companyName, daysRemaining, subscriptionData)
      }

      const result = await this.transporter.sendMail(mailOptions)
      console.log('Trial ending email sent:', result.messageId)
      return result
    } catch (error) {
      console.error('Error sending trial ending email:', error)
      throw error
    }
  }

  // Email templates
  private static getInvoiceEmailTemplate(invoiceData?: any): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Invoice</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; color: #666; }
          .button { display: inline-block; padding: 12px 24px; background: #2563eb; color: white; text-decoration: none; border-radius: 4px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>JobMarketplace</h1>
            <p>Invoice Attached</p>
          </div>
          <div class="content">
            <h2>Hello!</h2>
            <p>Please find your invoice attached to this email.</p>
            ${invoiceData ? `
              <p><strong>Invoice Number:</strong> #${invoiceData.invoiceNumber}</p>
              <p><strong>Amount:</strong> ${this.formatCurrency(invoiceData.totalAmount, invoiceData.currency)}</p>
              <p><strong>Due Date:</strong> ${new Date(invoiceData.dueDate).toLocaleDateString()}</p>
            ` : ''}
            <p>If you have any questions about this invoice, please don't hesitate to contact our support team.</p>
          </div>
          <div class="footer">
            <p>Thank you for your business!</p>
            <p>JobMarketplace Team</p>
          </div>
        </div>
      </body>
      </html>
    `
  }

  private static getPaymentConfirmationTemplate(companyName: string, paymentData: any): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Payment Confirmation</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #10b981; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; color: #666; }
          .success { color: #10b981; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Payment Confirmed</h1>
            <p class="success">✓ Payment Successful</p>
          </div>
          <div class="content">
            <h2>Hello ${companyName}!</h2>
            <p>We've successfully processed your payment. Here are the details:</p>
            <ul>
              <li><strong>Amount:</strong> ${this.formatCurrency(paymentData.amount, paymentData.currency)}</li>
              <li><strong>Payment Method:</strong> ${paymentData.paymentMethod}</li>
              <li><strong>Date:</strong> ${new Date(paymentData.paidAt || paymentData.createdAt).toLocaleDateString()}</li>
              <li><strong>Transaction ID:</strong> ${paymentData._id}</li>
            </ul>
            <p>Your account has been updated and services will continue uninterrupted.</p>
          </div>
          <div class="footer">
            <p>Thank you for your business!</p>
            <p>JobMarketplace Team</p>
          </div>
        </div>
      </body>
      </html>
    `
  }

  private static getPaymentFailedTemplate(companyName: string, paymentData: any): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Payment Failed</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #ef4444; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; color: #666; }
          .button { display: inline-block; padding: 12px 24px; background: #2563eb; color: white; text-decoration: none; border-radius: 4px; }
          .error { color: #ef4444; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Payment Failed</h1>
            <p class="error">⚠ Action Required</p>
          </div>
          <div class="content">
            <h2>Hello ${companyName}!</h2>
            <p>We were unable to process your payment. Please update your payment method to continue using our services.</p>
            <ul>
              <li><strong>Amount:</strong> ${this.formatCurrency(paymentData.amount, paymentData.currency)}</li>
              <li><strong>Reason:</strong> ${paymentData.failureReason || 'Payment declined'}</li>
              <li><strong>Date:</strong> ${new Date(paymentData.createdAt).toLocaleDateString()}</li>
            </ul>
            <p>Please update your payment method in your account settings to avoid service interruption.</p>
            <p style="text-align: center;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/billing" class="button">Update Payment Method</a>
            </p>
          </div>
          <div class="footer">
            <p>Need help? Contact our support team.</p>
            <p>JobMarketplace Team</p>
          </div>
        </div>
      </body>
      </html>
    `
  }

  private static getSubscriptionStatusTemplate(companyName: string, status: string, subscriptionData: any): string {
    const statusColors: { [key: string]: string } = {
      active: '#10b981',
      canceled: '#ef4444',
      paused: '#f59e0b',
      trialing: '#3b82f6'
    }

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Subscription Update</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: ${statusColors[status] || '#2563eb'}; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Subscription ${status.charAt(0).toUpperCase() + status.slice(1)}</h1>
          </div>
          <div class="content">
            <h2>Hello ${companyName}!</h2>
            <p>Your subscription status has been updated to: <strong>${status}</strong></p>
            <ul>
              <li><strong>Plan:</strong> ${subscriptionData.plan?.name || 'N/A'}</li>
              <li><strong>Billing Cycle:</strong> ${subscriptionData.billingCycle}</li>
              <li><strong>Amount:</strong> ${this.formatCurrency(subscriptionData.pricePerPeriod, subscriptionData.currency)}</li>
            </ul>
          </div>
          <div class="footer">
            <p>JobMarketplace Team</p>
          </div>
        </div>
      </body>
      </html>
    `
  }

  private static getSubscriptionCanceledTemplate(companyName: string, subscriptionData: any): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Subscription Canceled</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #6b7280; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; color: #666; }
          .button { display: inline-block; padding: 12px 24px; background: #2563eb; color: white; text-decoration: none; border-radius: 4px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Subscription Canceled</h1>
          </div>
          <div class="content">
            <h2>Hello ${companyName}!</h2>
            <p>Your subscription has been canceled. We're sorry to see you go!</p>
            <p>Your account will remain active until ${new Date(subscriptionData.currentPeriodEnd).toLocaleDateString()}.</p>
            <p>If you change your mind, you can reactivate your subscription at any time.</p>
            <p style="text-align: center;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/billing" class="button">Reactivate Subscription</a>
            </p>
          </div>
          <div class="footer">
            <p>We'd love to have you back!</p>
            <p>JobMarketplace Team</p>
          </div>
        </div>
      </body>
      </html>
    `
  }

  private static getTrialEndingTemplate(companyName: string, daysRemaining: number, subscriptionData: any): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Trial Ending Soon</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #f59e0b; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; color: #666; }
          .button { display: inline-block; padding: 12px 24px; background: #2563eb; color: white; text-decoration: none; border-radius: 4px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Trial Ending Soon</h1>
            <p>${daysRemaining} days remaining</p>
          </div>
          <div class="content">
            <h2>Hello ${companyName}!</h2>
            <p>Your free trial will end in ${daysRemaining} days. To continue using JobMarketplace, please add a payment method.</p>
            <ul>
              <li><strong>Plan:</strong> ${subscriptionData.plan?.name || 'N/A'}</li>
              <li><strong>Price:</strong> ${this.formatCurrency(subscriptionData.pricePerPeriod, subscriptionData.currency)} / ${subscriptionData.billingCycle}</li>
              <li><strong>Trial Ends:</strong> ${new Date(subscriptionData.trialEnd).toLocaleDateString()}</li>
            </ul>
            <p style="text-align: center;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/billing" class="button">Add Payment Method</a>
            </p>
          </div>
          <div class="footer">
            <p>Questions? Contact our support team.</p>
            <p>JobMarketplace Team</p>
          </div>
        </div>
      </body>
      </html>
    `
  }

  private static formatCurrency(amount: number, currency: string): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD'
    }).format(amount)
  }
}

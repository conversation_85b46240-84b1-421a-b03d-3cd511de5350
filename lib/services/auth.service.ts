// lib/services/auth.service.ts
import mongoose from 'mongoose'
import { User } from '@/lib/models/user.model'
import { Company } from '@/lib/models/company.model'
import { Client } from '@/lib/models/client.model'
import { generateTokens } from '@/lib/middleware/auth.middleware'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import { normalizeWebsiteUrl } from '@/lib/utils'
import { registrationTracker } from '@/lib/services/registration-tracker.service'

// Interfaces for type safety
interface UserDocument {
  _id: string
  email: string
  role: string
  profile: {
    firstName: string
    lastName: string
    phone?: string
    avatar?: string
    location?: {
      city?: string
      state?: string
      country?: string
    }
  }
  companyId?: string
  isActive: boolean
  isEmailVerified: boolean
  save: () => Promise<void>
}

interface CompanyData {
  name: string
  description?: string
  website?: string
  industry: string | string[]
  size: string
  location?: {
    city?: string
    state?: string
    country?: string
    address?: string
    postalCode?: string
  }
}

interface LocationData {
  city?: string
  state?: string
  country?: string
  address?: string
  postalCode?: string
}

export interface LoginRequest {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterRequest {
  email: string
  password: string
  firstName: string
  lastName: string
  role?: 'job_seeker' | 'company_admin'
  phone?: string
  location?: {
    city?: string
    state?: string
    country: string // Required for job applications and filtering
    address?: string
    postalCode?: string
  }
  company?: {
    name: string
    description?: string
    website?: string
    industry: string
    size: string
    location?: {
      city: string
      state?: string
      country: string
      address?: string
      postalCode?: string
    }
  }
}

export interface AuthResponse {
  user: {
    id: string
    email: string
    role: string
    profile: {
      firstName: string
      lastName: string
      fullName: string
      avatar?: string
    }
    isEmailVerified: boolean
    companyId?: string
  }
  tokens: {
    accessToken: string
    refreshToken: string
  }
  company?: {
    _id: string
    name: string
    slug: string
    description: string
    website?: string
    industry: string[]
    size: string
    verification: {
      isVerified: boolean
      verifiedAt?: Date
      documents?: string[]
    }
  }
  client?: {
    _id: string
    headline: string
    summary: string
    experience: {
      years: number
      level: 'entry' | 'mid' | 'senior' | 'lead'
      positions?: Array<{
        title: string
        company: string
        duration: string
      }>
    }
    profileCompleteness: number
  }
  emailVerificationRequired?: boolean
}

export class AuthService {
  /**
   * Authenticate user with email and password
   */
  async login(loginData: LoginRequest): Promise<AuthResponse> {
    try {
      // Find user by email and include password for comparison
      const user = await User.findOne({
        email: loginData.email
      }).select('+password')
      
      if (!user) {
        throw errorService.createError(
          ErrorCode.INVALID_CREDENTIALS,
          'Invalid email or password',
          'email'
        )
      }
      
      // Check if account is active
      if (!user.isActive) {
        throw errorService.createError(
          ErrorCode.FORBIDDEN,
          'Account has been deactivated. Please contact support.',
          'account'
        )
      }
      
      // Verify password
      const isPasswordValid = await user.comparePassword(loginData.password)
      if (!isPasswordValid) {
        throw errorService.createError(
          ErrorCode.INVALID_CREDENTIALS,
          'Invalid email or password',
          'password'
        )
      }
      
      // Update last login
      user.lastLogin = new Date()
      await user.save()
      
      // Generate tokens
      const tokens = generateTokens(user)
      
      return await this.formatAuthResponse(user, tokens)
      
    } catch (error: unknown) {
      // Don't expose specific error details for security
      if (error instanceof Error && error.message.includes('Invalid email or password')) {
        throw error
      }
      
      // Log the actual error but return generic message
      console.error('Login error:', error)
      throw errorService.createError(
        ErrorCode.INVALID_CREDENTIALS,
        'Invalid email or password'
      )
    }
  }

  /**
   * Register a new user (legacy method - use registerWithTransaction for new registrations)
   */
  async register(registerData: RegisterRequest): Promise<AuthResponse> {
    // Delegate to the new atomic registration method
    return this.registerWithTransaction(registerData)
  }

  /**
   * Register a new user with atomic transaction handling
   */
  async registerWithTransaction(registerData: RegisterRequest): Promise<AuthResponse> {
    // Start registration tracking
    const trackingId = registrationTracker.startRegistration(
      registerData.email,
      registerData.role || 'job_seeker',
      registerData.company?.name
    )

    const session = await mongoose.startSession()

    try {
      // Start transaction
      await session.startTransaction()

      // Update tracking: validation step
      registrationTracker.updateStep(trackingId, 'validation', 'in_progress', 'Validating registration data')

      // Check if user already exists
      const existingUser = await User.findOne({ email: registerData.email }).session(session)
      if (existingUser) {
        registrationTracker.updateStep(trackingId, 'validation', 'failed', 'User with this email already exists')
        throw errorService.createError(
          ErrorCode.DUPLICATE_ENTRY,
          'An account with this email already exists',
          'email'
        )
      }

      registrationTracker.updateStep(trackingId, 'validation', 'completed', 'Registration data validated successfully')

      // For company admin registration, validate company data first
      if (registerData.role === 'company_admin') {
        registrationTracker.updateStep(trackingId, 'company_validation', 'in_progress', 'Validating company data')

        if (!registerData.company) {
          registrationTracker.updateStep(trackingId, 'company_validation', 'failed', 'Company information is required')
          throw errorService.createError(
            ErrorCode.VALIDATION_ERROR,
            'Company information is required for company admin registration',
            'company'
          )
        }

        // Validate required company fields
        const requiredFields = ['name', 'industry', 'size', 'description']
        for (const field of requiredFields) {
          if (!registerData.company[field as keyof typeof registerData.company]) {
            registrationTracker.updateStep(trackingId, 'company_validation', 'failed', `Company ${field} is required`)
            throw errorService.createError(
              ErrorCode.VALIDATION_ERROR,
              `Company ${field} is required`,
              `company.${field}`
            )
          }
        }

        // Check if company name already exists
        const existingCompany = await Company.findOne({
          name: { $regex: new RegExp(`^${registerData.company.name.trim()}$`, 'i') }
        }).session(session)

        if (existingCompany) {
          registrationTracker.updateStep(trackingId, 'company_validation', 'failed', `Company name "${registerData.company.name}" already exists`)
          throw errorService.createError(
            ErrorCode.DUPLICATE_ENTRY,
            `A company with the name "${registerData.company.name}" already exists`,
            'company.name'
          )
        }

        registrationTracker.updateStep(trackingId, 'company_validation', 'completed', 'Company data validated successfully')
      }

      // Create new user
      registrationTracker.updateStep(trackingId, 'user_creation', 'in_progress', 'Creating user account')

      // Ensure location has required country field
      const userLocation = {
        city: registerData.location?.city || '',
        state: registerData.location?.state || '',
        country: registerData.location?.country || 'United States', // Default to US if not provided
        address: registerData.location?.address || '',
        postalCode: registerData.location?.postalCode || ''
      }

      const userData = {
        email: registerData.email,
        password: registerData.password,
        role: registerData.role || 'job_seeker',
        profile: {
          firstName: registerData.firstName,
          lastName: registerData.lastName,
          phone: registerData.phone,
          location: userLocation
        },
        preferences: {
          emailNotifications: true,
          jobAlerts: true,
          marketingEmails: false,
          theme: 'system'
        },
        isActive: true,
        isEmailVerified: false
      }

      const [user] = await User.create([userData], { session })

      registrationTracker.updateStep(trackingId, 'user_creation', 'completed', `User account created: ${user._id}`)

      console.log('✅ User created successfully:', {
        userId: user._id,
        email: user.email,
        role: user.role
      })

      let company = null

      // Create company for company admin within the same transaction
      if (registerData.role === 'company_admin' && registerData.company) {
        registrationTracker.updateStep(trackingId, 'company_creation', 'in_progress', 'Creating company profile')

        console.log('🏢 Creating company within transaction:', {
          userId: user._id,
          companyName: registerData.company.name,
          companyIndustry: registerData.company.industry,
          companySize: registerData.company.size
        })

        company = await this.createCompanyForUserInTransaction(user, registerData.company, session)

        registrationTracker.updateStep(trackingId, 'company_creation', 'completed', `Company created: ${company._id}`)
        registrationTracker.updateStep(trackingId, 'relationship_setup', 'in_progress', 'Setting up user-company relationship')

        // Update user with company reference within transaction
        user.companyId = company._id
        await user.save({ session })

        registrationTracker.updateStep(trackingId, 'relationship_setup', 'completed', 'User-company relationship established')

        console.log('✅ Company-User relationship established:', {
          companyId: company._id,
          userId: user._id,
          userCompanyId: user.companyId
        })
      }

      // Create client profile for job seekers within transaction
      if (registerData.role === 'job_seeker') {
        registrationTracker.updateStep(trackingId, 'client_creation', 'in_progress', 'Creating client profile')
        try {
          await this.createClientForUserInTransaction(user, userLocation, session)
          registrationTracker.updateStep(trackingId, 'client_creation', 'completed', 'Client profile created')
        } catch (clientError) {
          console.error('Client creation error:', clientError)
          registrationTracker.updateStep(trackingId, 'client_creation', 'failed', 'Client creation failed', clientError instanceof Error ? clientError.message : 'Unknown error')
          // Continue with user registration even if client creation fails
        }
      }

      // Commit transaction
      await session.commitTransaction()

      console.log('✅ Registration transaction committed successfully')

      // Generate tokens (outside transaction)
      registrationTracker.updateStep(trackingId, 'token_generation', 'in_progress', 'Generating authentication tokens')
      const tokens = generateTokens(user)
      registrationTracker.updateStep(trackingId, 'token_generation', 'completed', 'Authentication tokens generated')

      // Complete registration
      registrationTracker.updateStep(trackingId, 'completion', 'completed', 'Registration completed successfully')

      // TODO: Send email verification email
      // await emailService.sendVerificationEmail(user)

      return await this.formatAuthResponse(user, tokens)

    } catch (error: unknown) {
      // Rollback transaction on any error
      await session.abortTransaction()
      console.error('❌ Registration transaction aborted:', error)

      // Update tracking with error
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      registrationTracker.updateStep(trackingId, 'completion', 'failed', 'Registration failed', errorMessage)

      // Handle specific database errors
      if (typeof error === 'object' && error !== null && 'code' in error && (error as Record<string, unknown>).code === 11000) {
        throw errorService.createError(
          ErrorCode.DUPLICATE_ENTRY,
          'An account with this email already exists',
          'email'
        )
      }

      throw error
    } finally {
      // End session
      await session.endSession()
    }
  }

  /**
   * Create company for company admin user (legacy method)
   */
  private async createCompanyForUser(user: UserDocument, companyData: CompanyData): Promise<Record<string, unknown>> {
    // Delegate to transaction-based method
    const session = await mongoose.startSession()
    try {
      await session.startTransaction()
      const company = await this.createCompanyForUserInTransaction(user, companyData, session)
      await session.commitTransaction()
      return company
    } catch (error) {
      await session.abortTransaction()
      throw error
    } finally {
      await session.endSession()
    }
  }

  /**
   * Create company for company admin user within a transaction
   */
  private async createCompanyForUserInTransaction(user: UserDocument, companyData: CompanyData, session: mongoose.ClientSession): Promise<Record<string, unknown>> {
    // Validate required company data
    if (!companyData.name || !companyData.industry || !companyData.size) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        'Missing required company data: name, industry, and size are required',
        'company'
      )
    }

    // Validate user is eligible to be company admin
    if (user.role !== 'company_admin') {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        'User must have company_admin role to create a company',
        'user.role'
      )
    }

    // Check if company name already exists (within transaction)
    const existingCompany = await Company.findOne({
      name: { $regex: new RegExp(`^${companyData.name.trim()}$`, 'i') }
    }).session(session)

    if (existingCompany) {
      throw errorService.createError(
        ErrorCode.DUPLICATE_ENTRY,
        `A company with the name "${companyData.name}" already exists`,
        'company.name'
      )
    }

    // Map company size from registration format to model format
    const sizeMapping: Record<string, string> = {
      '1-10': 'startup',
      '11-50': 'small',
      '51-200': 'medium',
      '201-500': 'large',
      '501-1000': 'large',
      '1000+': 'enterprise'
    }

    // Generate unique slug
    const baseSlug = companyData.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
    let slug = baseSlug
    let slugCounter = 1

    // Ensure slug uniqueness (within transaction)
    while (await Company.findOne({ slug }).session(session)) {
      slug = `${baseSlug}-${slugCounter}`
      slugCounter++
    }

    console.log('Creating company with data:', {
      name: companyData.name,
      slug,
      description: companyData.description,
      industry: companyData.industry,
      size: companyData.size,
      mappedSize: sizeMapping[companyData.size] || 'startup'
    })

    const newCompany = new Company({
      name: companyData.name.trim(),
      slug,
      description: companyData.description?.trim() || '', // Use exact description from registration
      tagline: `${companyData.name.trim()} - ${companyData.industry}`, // More professional tagline
      website: companyData.website ? normalizeWebsiteUrl(companyData.website.trim()) : undefined,
      industry: [companyData.industry],
      size: sizeMapping[companyData.size] || 'startup',
      contact: {
        email: user.email,
        phone: user.profile.phone || '',
        address: companyData.location?.address || '',
        supportEmail: user.email,
        hrEmail: user.email
      },
      locations: [{
        city: companyData.location?.city || user.profile.location?.city || '',
        state: companyData.location?.state || user.profile.location?.state || '',
        country: companyData.location?.country || user.profile.location?.country || '',
        isHeadquarters: true,
        address: companyData.location?.address || '',
        postalCode: companyData.location?.postalCode || ''
      }],
      culture: {
        values: [],
        benefits: [],
        workEnvironment: '',
        diversity: '',
        mission: '',
        vision: '',
        perks: []
      },
      socialLinks: {
        linkedin: '',
        twitter: '',
        facebook: '',
        instagram: '',
        github: '',
        youtube: '',
        glassdoor: ''
      },
      subscription: {
        plan: 'starter',
        status: 'trial',
        jobPostingLimit: 5,
        jobPostingsUsed: 0,
        featuredJobsLimit: 1,
        featuredJobsUsed: 0
      },
      admins: [user._id], // Add user as company admin
      recruiters: [], // Empty initially
      teamMembers: [{
        user: user._id,
        role: 'owner', // Company owner role
        department: 'Management',
        joinedAt: new Date(),
        isActive: true
      }],
      stats: {
        totalJobs: 0,
        activeJobs: 0,
        totalApplications: 0,
        totalHires: 0,
        profileViews: 0,
        followerCount: 0
      },
      verification: {
        isVerified: false,
        documents: [],
        status: 'pending'
      },
      settings: {
        // Profile & Privacy Settings
        allowPublicProfile: true,
        showSalaryRanges: true,
        allowDirectContact: true,
        showCompanyStats: false,

        // Application Management Settings
        autoRejectAfterDays: 30,
        requireCoverLetter: false,
        enableApplicationTracking: true,
        allowRemoteApplications: true,
        customApplicationQuestions: [],

        // Notification Settings
        emailNotifications: {
          newApplications: true,
          applicationUpdates: true,
          interviewReminders: true,
          jobExpiring: true,
          weeklyReports: false,
          marketingEmails: false
        },
        pushNotifications: {
          newApplications: true,
          applicationUpdates: false,
          interviewReminders: true,
          systemUpdates: true
        },

        // Branding Settings
        branding: {
          primaryColor: '#000000',
          secondaryColor: '#ffffff',
          logoUrl: '',
          coverImageUrl: '',
          customCss: ''
        },

        // Team Management Settings
        team: {
          allowMemberInvites: true,
          requireApprovalForInvites: true,
          defaultMemberRole: 'recruiter',
          maxTeamMembers: 10
        },

        // Integration Settings
        integrations: {
          atsEnabled: false,
          atsProvider: '',
          atsApiKey: '',
          slackWebhookUrl: '',
          googleAnalyticsId: '',
          linkedinCompanyId: ''
        },

        // Advanced Settings
        advanced: {
          customApplicationFields: [],
          autoResponseEnabled: true,
          autoResponseMessage: 'Thank you for your application. We will review it and get back to you soon.',
          applicationDeadlineReminder: true,
          bulkActionsEnabled: true
        }
      },
      isActive: true,
      isFeatured: false,
      createdBy: user._id
    })

    // Save the company within transaction
    await newCompany.save({ session })

    // Verify the company was created with proper relationships
    const savedCompany = await Company.findById(newCompany._id)
      .populate('admins', 'email profile.firstName profile.lastName')
      .populate('teamMembers.user', 'email profile.firstName profile.lastName')
      .session(session)

    if (!savedCompany) {
      throw errorService.createError(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Failed to save company to database',
        'company'
      )
    }

    // Verify admin relationship
    const isUserInAdmins = savedCompany.admins.some((admin: any) =>
      admin._id.toString() === user._id.toString()
    )

    if (!isUserInAdmins) {
      throw errorService.createError(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Failed to establish admin relationship',
        'company.admins'
      )
    }

    console.log('Company created successfully with proper relationships:', {
      id: savedCompany._id,
      name: savedCompany.name,
      description: savedCompany.description,
      slug: savedCompany.slug,
      admins: savedCompany.admins.map((admin: any) => ({
        id: admin._id,
        email: admin.email,
        name: `${admin.profile?.firstName} ${admin.profile?.lastName}`
      })),
      teamMembers: savedCompany.teamMembers?.map((member: any) => ({
        userId: member.user._id,
        email: member.user.email,
        role: member.role,
        department: member.department
      })),
      createdBy: savedCompany.createdBy
    })

    return savedCompany
  }

  /**
   * Create client profile for job seeker user (legacy method)
   */
  private async createClientForUser(user: UserDocument, location: LocationData): Promise<Record<string, unknown>> {
    const session = await mongoose.startSession()
    try {
      await session.startTransaction()
      const client = await this.createClientForUserInTransaction(user, location, session)
      await session.commitTransaction()
      return client
    } catch (error) {
      await session.abortTransaction()
      throw error
    } finally {
      await session.endSession()
    }
  }

  /**
   * Create client profile for job seeker user within a transaction
   */
  private async createClientForUserInTransaction(user: UserDocument, location: LocationData, session: mongoose.ClientSession): Promise<Record<string, unknown>> {
    const newClient = new Client({
      user: user._id,
      headline: `${user.profile.firstName} ${user.profile.lastName} - Professional`,
      summary: 'I am a motivated professional looking for new opportunities to grow my career.',
      experience: {
        level: 'entry',
        yearsOfExperience: 0,
        industries: []
      },
      jobPreferences: {
        desiredRoles: [],
        industries: [],
        locations: [{
          city: location?.city || '',
          state: location?.state || '',
          country: location?.country || 'United States', // Ensure country is always set
          remote: false,
          relocationWilling: false
        }],
        salaryExpectation: {
          min: 30000,
          max: 60000,
          currency: 'USD',
          period: 'yearly',
          negotiable: true
        },
        jobTypes: ['full-time'],
        workArrangement: ['onsite'],
        availability: 'immediately',
        benefits: [],
        companySize: []
      },
      privacy: {
        profileVisibility: 'public',
        showSalaryExpectation: true,
        showCurrentCompany: true,
        allowRecruiterContact: true,
        showProfileToCurrentEmployer: false
      },
      isActive: true,
      isPublic: true
    })

    await newClient.save({ session })
    return newClient
  }

  /**
   * Format user data for API response
   */
  private async formatAuthResponse(user: UserDocument, tokens: { accessToken: string; refreshToken: string }): Promise<AuthResponse> {
    // Get company data if user is company admin
    let companyData: {
      _id: string
      name: string
      slug: string
      description: string
      website?: string
      industry: string[]
      size: string
      verification: {
        isVerified: boolean
        verifiedAt?: Date
        documents?: string[]
      }
    } | undefined = undefined

    if (user.role === 'company_admin' && user.companyId) {
      try {
        const company = await Company.findById(user.companyId).lean() as any
        if (company) {
          companyData = {
            _id: company._id.toString(),
            name: company.name || '',
            slug: company.slug || '',
            description: company.description || '',
            website: company.website,
            industry: company.industry || [],
            size: company.size || 'startup',
            verification: company.verification || { isVerified: false }
          }
        }
      } catch (companyError) {
        console.error('Error fetching company data:', companyError)
        // Continue without company data
      }
    }

    // Get client data if user is job seeker
    let clientData: {
      _id: string
      headline: string
      summary: string
      experience: {
        years: number
        level: 'entry' | 'mid' | 'senior' | 'lead'
        positions?: Array<{
          title: string
          company: string
          duration: string
        }>
      }
      profileCompleteness: number
    } | undefined = undefined

    if (user.role === 'job_seeker') {
      try {
        const client = await Client.findOne({ user: user._id }).lean() as any
        if (client) {
          clientData = {
            _id: client._id.toString(),
            headline: client.headline || '',
            summary: client.summary || '',
            experience: client.experience || { years: 0, level: 'entry' },
            profileCompleteness: client.activity?.profileCompleteness || 0
          }
        }
      } catch (clientError) {
        console.error('Error fetching client data:', clientError)
        // Continue without client data
      }
    }

    return {
      user: {
        id: user._id.toString(),
        email: user.email,
        role: user.role,
        profile: {
          firstName: user.profile.firstName,
          lastName: user.profile.lastName,
          fullName: `${user.profile.firstName} ${user.profile.lastName}`,
          avatar: user.profile.avatar
        },
        isEmailVerified: user.isEmailVerified,
        companyId: user.companyId?.toString()
      },
      tokens,
      company: companyData,
      client: clientData,
      emailVerificationRequired: !user.isEmailVerified
    }
  }
}

export const authService = new AuthService()

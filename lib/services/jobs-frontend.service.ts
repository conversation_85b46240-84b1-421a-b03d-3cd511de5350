// lib/services/jobs-frontend.service.ts
import { Job } from '@/stores/jobs.store'

// Frontend job type that matches the expected structure in components
export interface JobFrontend {
  id: string
  title: string
  company: {
    id: string
    name: string
    logo: string
    industry?: string
    size?: string
    rating?: number
    verified: boolean
  }
  location: string
  locationDetails?: {
    city: string
    region?: string
    country: string
    continent?: string
    coordinates?: {
      latitude: number
      longitude: number
    }
  }
  type: string
  remote: boolean
  salary: {
    min: number
    max: number
    currency: string
    period: string
  }
  experience: string
  description: string
  requirements: string[]
  responsibilities?: string[]
  benefits: string[]
  skills: string[]
  department?: string
  posted: string
  deadline?: string
  applicants: number
  views: number
  featured: boolean
  urgent: boolean
  category: string
  workModel: string
  applicationStatus: string
  maxApplicants?: number
  closingReason?: string
}

export class JobsFrontendService {
  // Helper method to format dates
  private formatDate(dateInput: string | Date | undefined): string {
    if (!dateInput) return new Date().toLocaleDateString()

    const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) return 'Today'
    if (diffDays === 1) return 'Yesterday'
    if (diffDays < 7) return `${diffDays} days ago`
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`

    return date.toLocaleDateString()
  }

  // Transform a backend job to frontend format
  transformJob(backendJob: Job): JobFrontend {
    // Extract company data
    const company = backendJob.company || {
      _id: backendJob.companyId || '',
      name: 'Unknown Company',
      logo: '',
      verification: { isVerified: false }
    }

    // Map experience level
    const experienceMap: Record<string, string> = {
      'entry': 'Entry Level',
      'junior': 'Junior Level',
      'mid': 'Mid Level',
      'senior': 'Senior Level',
      'executive': 'Executive',
      'lead': 'Lead Level'
    }

    // Map job type
    const typeMap: Record<string, string> = {
      'full-time': 'Full-time',
      'part-time': 'Part-time',
      'contract': 'Contract',
      'internship': 'Internship',
      'freelance': 'Freelance'
    }

    // Format location string
    const formatLocation = () => {
      if (!backendJob.location) return 'Remote'
      
      const location = backendJob.location
      const parts = []
      
      if (location.city) parts.push(location.city)
      if (location.state) parts.push(location.state)
      if (location.country) parts.push(location.country)
      
      if (location.remote) {
        return parts.length > 0 ? `Remote / ${parts.join(', ')}` : 'Remote'
      }
      
      return parts.join(', ') || 'Location not specified'
    }

    // Determine application status
    const getApplicationStatus = (): 'open' | 'closing-soon' | 'closed' | 'filled' => {
      if (backendJob.status === 'closed') return 'closed'
      if (backendJob.status === 'filled') return 'filled'
      
      if (backendJob.applicationDeadline) {
        const deadline = new Date(backendJob.applicationDeadline)
        const now = new Date()
        const daysRemaining = Math.ceil((deadline.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
        
        if (daysRemaining <= 3) return 'closing-soon'
      }
      
      return 'open'
    }

    // Transform to frontend format
    return {
      id: backendJob._id,
      title: backendJob.title,
      company: {
        id: typeof company === 'object' ? company._id || '' : company || '',
        name: typeof company === 'object' ? company.name || 'Unknown Company' : 'Unknown Company',
        logo: typeof company === 'object' ? company.logo || '' : '',
        industry: typeof company === 'object' ? company.industry || undefined : undefined,
        size: typeof company === 'object' ? company.size || undefined : undefined,
        rating: typeof company === 'object' ? company.rating || 4.5 : 4.5,
        verified: typeof company === 'object' ? 
          (company.verification?.isVerified || false) : false
      },
      location: formatLocation(),
      locationDetails: {
        city: backendJob.location?.city || '',
        country: backendJob.location?.country || '',
        region: backendJob.location?.state || ''
      },
      type: typeMap[backendJob.type] || backendJob.type || 'Full-time',
      remote: backendJob.location?.remote || false,
      salary: {
        min: backendJob.salary?.min || 0,
        max: backendJob.salary?.max || 0,
        currency: backendJob.salary?.currency || 'USD',
        period: backendJob.salary?.period || 'year'
      },
      experience: experienceMap[backendJob.level || ''] ||
        backendJob.level || 'Not specified',
      description: backendJob.description || '',
      requirements: Array.isArray(backendJob.requirements) ?
        backendJob.requirements :
        (typeof backendJob.requirements === 'string' ?
          [backendJob.requirements] : []),
      responsibilities: backendJob.responsibilities || [],
      benefits: backendJob.benefits || [],
      skills: backendJob.tags || [],
      department: backendJob.department || backendJob.category || '',
      posted: this.formatDate(backendJob.publishedAt || backendJob.createdAt),
      deadline: backendJob.applicationDeadline ? this.formatDate(backendJob.applicationDeadline) : undefined,
      applicants: backendJob.applicationsCount || 0,
      views: backendJob.viewsCount || 0,
      featured: backendJob.isFeatured || false,
      urgent: backendJob.isUrgent || false,
      category: backendJob.category || 'General',
      workModel: backendJob.location?.remote ? 'Remote' : 
                (backendJob.location?.hybrid ? 'Hybrid' : 'On-site'),
      applicationStatus: getApplicationStatus(),
      maxApplicants: backendJob.maxApplicants || undefined,
      closingReason: backendJob.closingReason || undefined
    }
  }

  // Transform a list of backend jobs
  transformJobs(backendJobs: Job[]): JobFrontend[] {
    return backendJobs.map(job => this.transformJob(job))
  }
}

// Create singleton instance
export const jobsFrontendService = new JobsFrontendService()

import { Company } from '@/lib/models/company.model'
import { User } from '@/lib/models/user.model'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'

export interface CompanyProfile {
  id: string
  name: string
  slug: string
  description?: string
  website?: string
  industry?: string
  size?: string
  founded?: number
  location: {
    address?: string
    city?: string
    state?: string
    country?: string
    coordinates?: {
      lat: number
      lng: number
    }
  }
  logo?: string
  banner?: string
  socialLinks?: {
    linkedin?: string
    twitter?: string
    facebook?: string
    instagram?: string
  }
  culture?: {
    values?: string[]
    benefits?: string[]
    workEnvironment?: string
    mission?: string
    vision?: string
    perks?: string[]
  }
  specialties?: string[]
  technologies?: string[]
  awards?: string[]
  certifications?: string[]
  funding?: {
    stage?: string
    amount?: number
    currency?: string
    date?: Date
    investors?: string[]
  }
  isVerified: boolean
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  stats: {
    totalJobs: number
    activeJobs: number
    totalApplications: number
    totalHires: number
    profileViews: number
    followerCount: number
  }
}

export interface CreateCompanyRequest {
  name: string
  description?: string
  website?: string
  industry?: string
  size?: string
  location?: {
    address?: string
    city?: string
    state?: string
    country?: string
  }
  logo?: string
  socialLinks?: {
    linkedin?: string
    twitter?: string
    facebook?: string
    instagram?: string
  }
}

export interface UpdateCompanyRequest {
  name?: string
  description?: string
  website?: string
  industry?: string
  size?: string
  location?: {
    address?: string
    city?: string
    state?: string
    country?: string
  }
  logo?: string
  banner?: string
  socialLinks?: {
    linkedin?: string
    twitter?: string
    facebook?: string
    instagram?: string
  }
}

export class CompanyService {
  /**
   * Create a new company
   */
  async createCompany(companyData: CreateCompanyRequest, adminUserId: string): Promise<CompanyProfile> {
    try {
      // Check if company name already exists
      const existingCompany = await Company.findOne({ 
        name: { $regex: new RegExp(`^${companyData.name}$`, 'i') }
      })
      
      if (existingCompany) {
        throw errorService.createError(
          ErrorCode.DUPLICATE_ENTRY,
          'A company with this name already exists',
          'name'
        )
      }
      
      // Generate slug from company name
      const slug = this.generateSlug(companyData.name)
      
      // Create company
      const company = new Company({
        ...companyData,
        slug,
        isActive: true,
        isVerified: false
      })
      
      await company.save()
      
      // Update user to be company admin
      await User.findByIdAndUpdate(adminUserId, {
        companyId: company._id,
        role: 'company_admin'
      })
      
      return this.formatCompanyProfile(company)
      
    } catch (error: unknown) {
      if (typeof error === 'object' && error !== null && 'code' in error && (error as Record<string, unknown>).code === 11000) {
        throw errorService.createError(
          ErrorCode.DUPLICATE_ENTRY,
          'A company with this name already exists',
          'name'
        )
      }
      throw error
    }
  }

  /**
   * Get company by ID
   */
  async getCompanyById(companyId: string): Promise<CompanyProfile> {
    const company = await Company.findById(companyId)

    if (!company) {
      throw errorService.createError(
        ErrorCode.NOT_FOUND,
        'Company not found',
        'companyId'
      )
    }

    // Ensure stats are populated
    const [companyWithStats] = await this.ensureCompanyStats([company])

    return this.formatCompanyProfile(companyWithStats)
  }

  /**
   * Get company by slug
   */
  async getCompanyBySlug(slug: string): Promise<CompanyProfile> {
    const company = await Company.findOne({ slug })

    if (!company) {
      throw errorService.createError(
        ErrorCode.NOT_FOUND,
        'Company not found',
        'slug'
      )
    }

    // Ensure stats are populated
    const [companyWithStats] = await this.ensureCompanyStats([company])

    return this.formatCompanyProfile(companyWithStats)
  }

  /**
   * Update company profile
   */
  async updateCompany(companyId: string, updateData: UpdateCompanyRequest): Promise<CompanyProfile> {
    const company = await Company.findById(companyId)
    
    if (!company) {
      throw errorService.createError(
        ErrorCode.NOT_FOUND,
        'Company not found',
        'companyId'
      )
    }
    
    // Update fields
    Object.keys(updateData).forEach(key => {
      if (updateData[key as keyof UpdateCompanyRequest] !== undefined) {
        if (key === 'location' && updateData.location) {
          company.location = { ...company.location, ...updateData.location }
        } else if (key === 'socialLinks' && updateData.socialLinks) {
          company.socialLinks = { ...company.socialLinks, ...updateData.socialLinks }
        } else {
          ;(company as Record<string, unknown>)[key] = updateData[key as keyof UpdateCompanyRequest]
        }
      }
    })
    
    // Update slug if name changed
    if (updateData.name) {
      company.slug = this.generateSlug(updateData.name)
    }
    
    await company.save()
    
    return this.formatCompanyProfile(company)
  }

  /**
   * Get companies with pagination and filters
   */
  async getCompanies(
    page: number = 1,
    limit: number = 10,
    filters?: {
      industry?: string
      size?: string
      location?: string
      isVerified?: boolean
      isActive?: boolean
    }
  ): Promise<{
    companies: CompanyProfile[]
    pagination: {
      page: number
      limit: number
      total: number
      pages: number
    }
  }> {
    const query: Record<string, unknown> = {}

    if (filters?.industry) query.industry = filters.industry
    if (filters?.size) query.size = filters.size
    if (filters?.location) {
      query.$or = [
        { 'location.city': { $regex: filters.location, $options: 'i' } },
        { 'location.state': { $regex: filters.location, $options: 'i' } },
        { 'location.country': { $regex: filters.location, $options: 'i' } }
      ]
    }
    if (filters?.isVerified !== undefined) query['verification.isVerified'] = filters.isVerified
    if (filters?.isActive !== undefined) query.isActive = filters.isActive

    const skip = (page - 1) * limit

    const [companies, total] = await Promise.all([
      Company.find(query)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 }),
      Company.countDocuments(query)
    ])

    // Ensure stats are populated for all companies
    const companiesWithStats = await this.ensureCompanyStats(companies)

    return {
      companies: companiesWithStats.map(company => this.formatCompanyProfile(company)),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  }

  /**
   * Search companies
   */
  async searchCompanies(searchTerm: string, limit: number = 10): Promise<CompanyProfile[]> {
    const companies = await Company.find({
      $or: [
        { name: { $regex: searchTerm, $options: 'i' } },
        { description: { $regex: searchTerm, $options: 'i' } },
        { industry: { $regex: searchTerm, $options: 'i' } }
      ],
      isActive: true
    })
    .limit(limit)
    .sort({ 'verification.isVerified': -1, name: 1 })

    // Ensure stats are populated for all companies
    const companiesWithStats = await this.ensureCompanyStats(companies)

    return companiesWithStats.map(company => this.formatCompanyProfile(company))
  }

  /**
   * Ensure company job statistics are up-to-date
   * This fetches job counts for companies that don't have them
   */
  private async ensureCompanyStats(companies: any[]): Promise<any[]> {
    const { Job } = await import('@/lib/models/job.model')

    // Process companies in batches to avoid performance issues
    const batchSize = 10
    const batches = []

    for (let i = 0; i < companies.length; i += batchSize) {
      batches.push(companies.slice(i, i + batchSize))
    }

    const processedCompanies = []

    for (const batch of batches) {
      const updatedBatch = await Promise.all(
        batch.map(async (company) => {
          // Skip if stats are already populated
          if (company.stats?.activeJobs !== undefined && company.stats?.totalJobs !== undefined) {
            return company
          }

          // Get job counts
          const [activeJobs, totalJobs] = await Promise.all([
            Job.countDocuments({ companyId: company._id, status: 'active' }),
            Job.countDocuments({ companyId: company._id })
          ])

          // Update company stats
          company.stats = {
            ...company.stats,
            activeJobs,
            totalJobs
          }

          // Save the updated stats to database
          await Company.findByIdAndUpdate(company._id, {
            'stats.activeJobs': activeJobs,
            'stats.totalJobs': totalJobs
          })

          return company
        })
      )

      processedCompanies.push(...updatedBatch)
    }

    return processedCompanies
  }

  /**
   * Verify company
   */
  async verifyCompany(companyId: string): Promise<void> {
    const company = await Company.findById(companyId)
    
    if (!company) {
      throw errorService.createError(
        ErrorCode.NOT_FOUND,
        'Company not found',
        'companyId'
      )
    }
    
    company.isVerified = true
    await company.save()
  }

  /**
   * Generate slug from company name
   */
  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  /**
   * Format company data for API response
   */
  private formatCompanyProfile(company: Record<string, unknown>): CompanyProfile {
    // Type assertions for nested properties
    const _id = company._id as { toString: () => string }
    const stats = company.stats as Record<string, unknown> || {}
    const verification = company.verification as Record<string, unknown> || {}
    const culture = company.culture as Record<string, unknown> || {}
    const funding = company.funding as Record<string, unknown> || {}

    return {
      id: _id.toString(),
      name: String(company.name),
      slug: String(company.slug),
      description: String(company.description),
      website: company.website as string | undefined,
      industry: Array.isArray(company.industry) ? company.industry.join(', ') : String(company.industry || ''),
      size: String(company.size),
      founded: company.founded as number | undefined,
      location: company.location as Record<string, unknown>,
      logo: company.logo as string | undefined,
      banner: company.banner as string | undefined,
      socialLinks: company.socialLinks as Record<string, string> | undefined,
      culture: {
        values: culture.values as string[] | undefined,
        benefits: culture.benefits as string[] | undefined,
        workEnvironment: culture.workEnvironment as string | undefined,
        mission: culture.mission as string | undefined,
        vision: culture.vision as string | undefined,
        perks: culture.perks as string[] | undefined
      },
      specialties: company.specialties as string[] | undefined,
      technologies: company.technologies as string[] | undefined,
      awards: company.awards as string[] | undefined,
      certifications: company.certifications as string[] | undefined,
      funding: {
        stage: funding.stage as string | undefined,
        amount: funding.amount as number | undefined,
        currency: funding.currency as string | undefined,
        date: funding.date as Date | undefined,
        investors: funding.investors as string[] | undefined
      },
      isVerified: Boolean(verification.isVerified),
      isActive: Boolean(company.isActive),
      createdAt: company.createdAt as Date,
      updatedAt: company.updatedAt as Date,
      // Include job statistics
      stats: {
        totalJobs: Number(stats.totalJobs) || 0,
        activeJobs: Number(stats.activeJobs) || 0,
        totalApplications: Number(stats.totalApplications) || 0,
        totalHires: Number(stats.totalHires) || 0,
        profileViews: Number(stats.profileViews) || 0,
        followerCount: Number(stats.followerCount) || 0
      }
    }
  }
}

export const companyService = new CompanyService()

import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'

export interface RegistrationStep {
  id: string
  name: string
  status: 'pending' | 'in_progress' | 'completed' | 'failed'
  message?: string
  timestamp?: Date
  error?: string
}

export interface RegistrationProgress {
  sessionId: string
  email: string
  role: 'job_seeker' | 'company_admin'
  companyName?: string
  steps: RegistrationStep[]
  currentStep: number
  status: 'pending' | 'in_progress' | 'completed' | 'failed'
  startTime: Date
  endTime?: Date
  error?: string
}

/**
 * Registration Tracker Service
 * 
 * Provides detailed tracking and monitoring of the registration process,
 * especially useful for debugging company admin registration issues.
 */
export class RegistrationTrackerService {
  private static instance: RegistrationTrackerService
  private registrations: Map<string, RegistrationProgress> = new Map()
  private readonly maxRegistrations = 1000 // Keep last 1000 registrations

  static getInstance(): RegistrationTrackerService {
    if (!RegistrationTrackerService.instance) {
      RegistrationTrackerService.instance = new RegistrationTrackerService()
    }
    return RegistrationTrackerService.instance
  }

  /**
   * Start tracking a new registration
   */
  startRegistration(email: string, role: 'job_seeker' | 'company_admin', companyName?: string): string {
    const sessionId = this.generateSessionId()
    
    const steps: RegistrationStep[] = [
      { id: 'validation', name: 'Validate Registration Data', status: 'pending' },
      { id: 'user_creation', name: 'Create User Account', status: 'pending' }
    ]

    if (role === 'company_admin') {
      steps.push(
        { id: 'company_validation', name: 'Validate Company Data', status: 'pending' },
        { id: 'company_creation', name: 'Create Company Profile', status: 'pending' },
        { id: 'relationship_setup', name: 'Setup User-Company Relationship', status: 'pending' }
      )
    } else {
      steps.push(
        { id: 'client_creation', name: 'Create Client Profile', status: 'pending' }
      )
    }

    steps.push(
      { id: 'token_generation', name: 'Generate Authentication Tokens', status: 'pending' },
      { id: 'completion', name: 'Complete Registration', status: 'pending' }
    )

    const registration: RegistrationProgress = {
      sessionId,
      email,
      role,
      companyName,
      steps,
      currentStep: 0,
      status: 'pending',
      startTime: new Date()
    }

    this.registrations.set(sessionId, registration)
    this.cleanupOldRegistrations()

    console.log(`📊 Registration tracking started: ${sessionId}`, {
      email,
      role,
      companyName,
      stepsCount: steps.length
    })

    return sessionId
  }

  /**
   * Update step status
   */
  updateStep(sessionId: string, stepId: string, status: 'in_progress' | 'completed' | 'failed', message?: string, error?: string): void {
    const registration = this.registrations.get(sessionId)
    if (!registration) {
      console.warn(`Registration session not found: ${sessionId}`)
      return
    }

    const step = registration.steps.find(s => s.id === stepId)
    if (!step) {
      console.warn(`Step not found: ${stepId} in session ${sessionId}`)
      return
    }

    step.status = status
    step.message = message
    step.error = error
    step.timestamp = new Date()

    // Update current step index
    if (status === 'in_progress') {
      const stepIndex = registration.steps.findIndex(s => s.id === stepId)
      registration.currentStep = stepIndex
      registration.status = 'in_progress'
    }

    // If step failed, mark registration as failed
    if (status === 'failed') {
      registration.status = 'failed'
      registration.error = error || message
      registration.endTime = new Date()
    }

    // Check if all steps are completed
    if (status === 'completed' && registration.steps.every(s => s.status === 'completed')) {
      registration.status = 'completed'
      registration.endTime = new Date()
    }

    console.log(`📊 Step updated: ${stepId} -> ${status}`, {
      sessionId,
      message,
      error,
      currentStep: registration.currentStep,
      registrationStatus: registration.status
    })
  }

  /**
   * Get registration progress
   */
  getProgress(sessionId: string): RegistrationProgress | null {
    return this.registrations.get(sessionId) || null
  }

  /**
   * Get all registrations (for debugging)
   */
  getAllRegistrations(): RegistrationProgress[] {
    return Array.from(this.registrations.values())
  }

  /**
   * Get recent failed registrations
   */
  getFailedRegistrations(limit: number = 10): RegistrationProgress[] {
    return Array.from(this.registrations.values())
      .filter(r => r.status === 'failed')
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
      .slice(0, limit)
  }

  /**
   * Get registration statistics
   */
  getStatistics(): {
    total: number
    completed: number
    failed: number
    inProgress: number
    successRate: number
    averageDuration: number
  } {
    const registrations = Array.from(this.registrations.values())
    const total = registrations.length
    const completed = registrations.filter(r => r.status === 'completed').length
    const failed = registrations.filter(r => r.status === 'failed').length
    const inProgress = registrations.filter(r => r.status === 'in_progress').length

    const completedRegistrations = registrations.filter(r => r.status === 'completed' && r.endTime)
    const averageDuration = completedRegistrations.length > 0
      ? completedRegistrations.reduce((sum, r) => sum + (r.endTime!.getTime() - r.startTime.getTime()), 0) / completedRegistrations.length
      : 0

    return {
      total,
      completed,
      failed,
      inProgress,
      successRate: total > 0 ? (completed / total) * 100 : 0,
      averageDuration: Math.round(averageDuration / 1000) // Convert to seconds
    }
  }

  /**
   * Clear all registration data
   */
  clear(): void {
    this.registrations.clear()
    console.log('📊 Registration tracking data cleared')
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `reg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Clean up old registrations to prevent memory leaks
   */
  private cleanupOldRegistrations(): void {
    if (this.registrations.size <= this.maxRegistrations) {
      return
    }

    const registrations = Array.from(this.registrations.entries())
      .sort(([, a], [, b]) => b.startTime.getTime() - a.startTime.getTime())

    // Keep only the most recent registrations
    const toKeep = registrations.slice(0, this.maxRegistrations)
    this.registrations.clear()
    
    toKeep.forEach(([sessionId, registration]) => {
      this.registrations.set(sessionId, registration)
    })

    console.log(`📊 Cleaned up old registrations, kept ${toKeep.length} most recent`)
  }
}

// Export singleton instance
export const registrationTracker = RegistrationTrackerService.getInstance()

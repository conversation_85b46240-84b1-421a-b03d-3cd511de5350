// lib/services/platform-stats.service.ts
'use client'

export interface PlatformStats {
  activeJobs: number
  totalCompanies: number
  totalApplications: number
  successfulHires: number
  averageApplicationsPerJob: number
  hireRate: number
}

export interface PlatformStatsResponse {
  success: boolean
  data: PlatformStats
  timestamp: string
}

class PlatformStatsService {
  private baseUrl = '/api/v1'
  private cache: { data: PlatformStats; timestamp: number } | null = null
  private cacheExpiry = 5 * 60 * 1000 // 5 minutes

  /**
   * Get platform statistics with caching
   */
  async getPlatformStats(): Promise<PlatformStats> {
    try {
      // Check cache first
      if (this.cache && Date.now() - this.cache.timestamp < this.cacheExpiry) {
        return this.cache.data
      }

      const response = await fetch(`${this.baseUrl}/platform/stats`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch platform stats: ${response.statusText}`)
      }

      const result: PlatformStatsResponse = await response.json()
      
      if (!result.success) {
        throw new Error(result.data as any || 'Failed to fetch platform stats')
      }

      // Update cache
      this.cache = {
        data: result.data,
        timestamp: Date.now()
      }

      return result.data

    } catch (error) {
      console.error('Error fetching platform stats:', error)
      
      // Return fallback stats if API fails
      return this.getFallbackStats()
    }
  }

  /**
   * Get fallback statistics when API is unavailable
   */
  private getFallbackStats(): PlatformStats {
    return {
      activeJobs: 45000,
      totalCompanies: 12000,
      totalApplications: 850000,
      successfulHires: 125000,
      averageApplicationsPerJob: 19,
      hireRate: 15
    }
  }

  /**
   * Clear the cache (useful for testing or manual refresh)
   */
  clearCache(): void {
    this.cache = null
  }

  /**
   * Format number for display (e.g., 1000 -> "1K", 1000000 -> "1M")
   */
  formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1).replace(/\.0$/, '') + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K'
    }
    return num.toString()
  }

  /**
   * Get formatted stats for display
   */
  async getFormattedStats(): Promise<{
    activeJobs: { value: number; display: string; label: string }
    companies: { value: number; display: string; label: string }
    successStories: { value: number; display: string; label: string }
  }> {
    const stats = await this.getPlatformStats()
    
    return {
      activeJobs: {
        value: stats.activeJobs,
        display: this.formatNumber(stats.activeJobs),
        label: 'Active Jobs'
      },
      companies: {
        value: stats.totalCompanies,
        display: this.formatNumber(stats.totalCompanies),
        label: 'Companies'
      },
      successStories: {
        value: stats.successfulHires,
        display: this.formatNumber(stats.successfulHires),
        label: 'Success Stories'
      }
    }
  }
}

// Create singleton instance
export const platformStatsService = new PlatformStatsService()

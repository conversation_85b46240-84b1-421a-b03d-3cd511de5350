import { Company } from '@/lib/models/company.model'
import { errorService, ErrorCode } from '@/lib/error-service'
import { cacheService } from './cache.service'
import BaseService from './base.service'
import type {
  EnhancedCompany,
  CompanyLocation,
  CompanyCulture,
  SocialLinks,
  TeamMember,
  Job,
  UpdateCompanyProfileRequest,
  UpdateCompanyCultureRequest,
  UpdateSocialLinksRequest,
  AddLocationRequest,
  UpdateLocationRequest,
  InviteTeamMemberRequest,
  UpdateTeamMemberRequest,
  CreateJobRequest,
  UpdateJobRequest,
  CompanyProfileResponse,
  TeamMembersResponse,
  JobsResponse,
  AnalyticsResponse
} from '@/types/company-management.types'
import {
  updateCompanyProfileSchema,
  companyCultureSchema,
  socialLinksSchema,
  companyLocationSchema,
  inviteTeamMemberSchema,
  createJobSchema
} from '@/types/company-management.types'

export class CompanyManagementService extends BaseService {
  private readonly CACHE_PREFIX = 'company_mgmt'
  private readonly CACHE_TTL = 300 // 5 minutes

  /**
   * Get enhanced company profile
   */
  async getCompanyProfile(companyId: string): Promise<CompanyProfileResponse> {
    try {
      this.validateObjectId(companyId, 'companyId')

      // Check cache first
      const cacheKey = `${this.CACHE_PREFIX}:profile:${companyId}`
      const cachedProfile = await cacheService.get<EnhancedCompany>(cacheKey)
      
      if (cachedProfile) {
        return {
          success: true,
          data: cachedProfile,
          message: 'Company profile retrieved from cache'
        }
      }

      // Fetch from database with populated fields
      const company = await Company.findById(companyId)
        .populate('admins', 'name email avatar')
        .populate('recruiters', 'name email avatar')
        .populate('teamMembers.user', 'name email avatar')
        .lean()
      
      if (!company) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Company not found',
          'companyId'
        )
      }

      // Transform to enhanced company format
      const enhancedCompany = this.transformToEnhancedCompany(company)

      // Cache the profile
      await cacheService.set(cacheKey, enhancedCompany, this.CACHE_TTL)

      return {
        success: true,
        data: enhancedCompany,
        message: 'Company profile retrieved successfully'
      }

    } catch (error) {
      this.handleDatabaseError(error, 'getCompanyProfile')
    }
  }

  /**
   * Update company basic profile
   */
  async updateCompanyProfile(
    companyId: string,
    profileData: UpdateCompanyProfileRequest,
    userId: string
  ): Promise<CompanyProfileResponse> {
    try {
      this.validateObjectId(companyId, 'companyId')
      this.validateObjectId(userId, 'userId')

      // Validate profile data
      const validatedData = updateCompanyProfileSchema.parse(profileData)

      // Check if company exists and user has permission
      const company = await Company.findById(companyId)
      
      if (!company) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Company not found',
          'companyId'
        )
      }

      await this.checkCompanyPermission(company, userId, ['admin', 'owner'])

      // Update company profile
      Object.assign(company, validatedData)
      company.updatedAt = new Date()
      await company.save()

      // Get updated profile
      const updatedProfile = await this.getCompanyProfile(companyId)

      // Invalidate related caches
      await this.invalidateCompanyCaches(companyId)

      this.logOperation('updateCompanyProfile', { 
        companyId, 
        userId, 
        updatedFields: Object.keys(validatedData) 
      })

      return updatedProfile

    } catch (error) {
      this.handleDatabaseError(error, 'updateCompanyProfile')
    }
  }

  /**
   * Update company culture
   */
  async updateCompanyCulture(
    companyId: string,
    cultureData: UpdateCompanyCultureRequest,
    userId: string
  ): Promise<CompanyProfileResponse> {
    try {
      this.validateObjectId(companyId, 'companyId')
      this.validateObjectId(userId, 'userId')

      // Validate culture data
      const validatedCulture = companyCultureSchema.parse(cultureData.culture)

      const company = await Company.findById(companyId)
      
      if (!company) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Company not found',
          'companyId'
        )
      }

      await this.checkCompanyPermission(company, userId, ['admin', 'owner'])

      // Update culture
      company.culture = { ...company.culture, ...validatedCulture }
      company.updatedAt = new Date()
      await company.save()

      // Get updated profile
      const updatedProfile = await this.getCompanyProfile(companyId)

      await this.invalidateCompanyCaches(companyId)

      this.logOperation('updateCompanyCulture', { companyId, userId })

      return updatedProfile

    } catch (error) {
      this.handleDatabaseError(error, 'updateCompanyCulture')
    }
  }

  /**
   * Update social links
   */
  async updateSocialLinks(
    companyId: string,
    socialData: UpdateSocialLinksRequest,
    userId: string
  ): Promise<CompanyProfileResponse> {
    try {
      this.validateObjectId(companyId, 'companyId')
      this.validateObjectId(userId, 'userId')

      // Validate social links
      const validatedSocial = socialLinksSchema.parse(socialData.socialLinks)

      const company = await Company.findById(companyId)
      
      if (!company) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Company not found',
          'companyId'
        )
      }

      await this.checkCompanyPermission(company, userId, ['admin', 'owner'])

      // Update social links
      company.socialLinks = { ...company.socialLinks, ...validatedSocial }
      company.updatedAt = new Date()
      await company.save()

      // Get updated profile
      const updatedProfile = await this.getCompanyProfile(companyId)

      await this.invalidateCompanyCaches(companyId)

      this.logOperation('updateSocialLinks', { companyId, userId })

      return updatedProfile

    } catch (error) {
      this.handleDatabaseError(error, 'updateSocialLinks')
    }
  }

  /**
   * Add company location
   */
  async addLocation(
    companyId: string,
    locationData: AddLocationRequest,
    userId: string
  ): Promise<CompanyProfileResponse> {
    try {
      this.validateObjectId(companyId, 'companyId')
      this.validateObjectId(userId, 'userId')

      // Validate location data
      const validatedLocation = companyLocationSchema.parse(locationData.location)

      const company = await Company.findById(companyId)
      
      if (!company) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Company not found',
          'companyId'
        )
      }

      await this.checkCompanyPermission(company, userId, ['admin', 'owner'])

      // If this is set as headquarters, remove headquarters flag from other locations
      if (validatedLocation.isHeadquarters) {
        company.locations.forEach(location => {
          location.isHeadquarters = false
        })
      }

      // Add new location
      company.locations.push(validatedLocation)
      company.updatedAt = new Date()
      await company.save()

      // Get updated profile
      const updatedProfile = await this.getCompanyProfile(companyId)

      await this.invalidateCompanyCaches(companyId)

      this.logOperation('addLocation', { companyId, userId, location: validatedLocation })

      return updatedProfile

    } catch (error) {
      this.handleDatabaseError(error, 'addLocation')
    }
  }

  /**
   * Update company location
   */
  async updateLocation(
    companyId: string,
    locationData: UpdateLocationRequest,
    userId: string
  ): Promise<CompanyProfileResponse> {
    try {
      this.validateObjectId(companyId, 'companyId')
      this.validateObjectId(userId, 'userId')

      const company = await Company.findById(companyId)
      
      if (!company) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Company not found',
          'companyId'
        )
      }

      await this.checkCompanyPermission(company, userId, ['admin', 'owner'])

      // Find and update location
      const locationIndex = company.locations.findIndex(
        (loc, index) => index.toString() === locationData.locationId
      )

      if (locationIndex === -1) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Location not found',
          'locationId'
        )
      }

      // Validate partial location data
      const validatedLocation = companyLocationSchema.partial().parse(locationData.location)

      // If setting as headquarters, remove flag from others
      if (validatedLocation.isHeadquarters) {
        company.locations.forEach((location, index) => {
          if (index !== locationIndex) {
            location.isHeadquarters = false
          }
        })
      }

      // Update location
      Object.assign(company.locations[locationIndex], validatedLocation)
      company.updatedAt = new Date()
      await company.save()

      // Get updated profile
      const updatedProfile = await this.getCompanyProfile(companyId)

      await this.invalidateCompanyCaches(companyId)

      this.logOperation('updateLocation', { companyId, userId, locationId: locationData.locationId })

      return updatedProfile

    } catch (error) {
      this.handleDatabaseError(error, 'updateLocation')
    }
  }

  /**
   * Remove company location
   */
  async removeLocation(
    companyId: string,
    locationId: string,
    userId: string
  ): Promise<CompanyProfileResponse> {
    try {
      this.validateObjectId(companyId, 'companyId')
      this.validateObjectId(userId, 'userId')

      const company = await Company.findById(companyId)
      
      if (!company) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Company not found',
          'companyId'
        )
      }

      await this.checkCompanyPermission(company, userId, ['admin', 'owner'])

      // Find location index
      const locationIndex = company.locations.findIndex(
        (loc, index) => index.toString() === locationId
      )

      if (locationIndex === -1) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Location not found',
          'locationId'
        )
      }

      // Remove location
      company.locations.splice(locationIndex, 1)
      company.updatedAt = new Date()
      await company.save()

      // Get updated profile
      const updatedProfile = await this.getCompanyProfile(companyId)

      await this.invalidateCompanyCaches(companyId)

      this.logOperation('removeLocation', { companyId, userId, locationId })

      return updatedProfile

    } catch (error) {
      this.handleDatabaseError(error, 'removeLocation')
    }
  }

  /**
   * Transform database company to enhanced company format
   */
  private transformToEnhancedCompany(company: Record<string, unknown>): EnhancedCompany {
    return {
      _id: company._id as string,
      name: company.name as string,
      slug: company.slug as string,
      description: company.description as string,
      tagline: company.tagline as string,
      logo: company.logo as string,
      coverImage: company.coverImage as string,
      website: company.website as string,
      industry: company.industry as string[],
      size: company.size as EnhancedCompany['size'],
      founded: company.founded as number,
      employeeCount: company.employeeCount as EnhancedCompany['employeeCount'],
      locations: company.locations as CompanyLocation[],
      contact: company.contact as EnhancedCompany['contact'],
      culture: company.culture as CompanyCulture,
      socialLinks: company.socialLinks as SocialLinks,
      subscription: company.subscription as EnhancedCompany['subscription'],
      stats: company.stats as EnhancedCompany['stats'],
      verification: company.verification as EnhancedCompany['verification'],
      isActive: company.isActive as boolean,
      isFeatured: company.isFeatured as boolean,
      createdAt: company.createdAt as Date,
      updatedAt: company.updatedAt as Date
    }
  }

  /**
   * Check if user has permission to modify company
   */
  private async checkCompanyPermission(
    company: InstanceType<typeof Company>,
    userId: string,
    _requiredRoles: string[]
  ): Promise<void> {
    const userObjectId = this.toObjectId(userId)
    
    // Check if user is company admin
    const isAdmin = company.admins.some((adminId: unknown) => {
      if (adminId && typeof adminId === 'object' && 'equals' in adminId) {
        return (adminId as { equals: (id: unknown) => boolean }).equals(userObjectId)
      }
      return false
    })
    
    if (!isAdmin) {
      throw errorService.createError(
        ErrorCode.FORBIDDEN,
        'Insufficient permissions to modify company',
        'permissions'
      )
    }
  }

  /**
   * Invalidate company-related caches
   */
  private async invalidateCompanyCaches(companyId: string): Promise<void> {
    const cacheKeys = [
      `${this.CACHE_PREFIX}:profile:${companyId}`,
      `company:${companyId}`,
      `company_profile:${companyId}`,
      `company_public:${companyId}`,
      `company_settings:${companyId}`
    ]

    await Promise.all(
      cacheKeys.map(key => cacheService.del(key))
    )
  }
}

// Export singleton instance
export const companyManagementService = new CompanyManagementService()

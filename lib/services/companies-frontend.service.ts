// lib/services/companies-frontend.service.ts
import { CompanySearchQuery, CompanySearchResult, CompanyFrontend, CompanySize, FundingStage } from '@/types/companies-frontend.types'

// Backend data types
interface BackendLocation {
  city: string
  state?: string
  country: string
  isHeadquarters?: boolean
  address?: string
  postalCode?: string
}

interface BackendCompany {
  _id: string
  id?: string
  name: string
  slug?: string
  description: string
  logo?: string
  website?: string
  industry?: string | string[]
  industries?: string[]
  size: string
  founded?: number
  locations?: BackendLocation[]
  location?: string | BackendLocation
  stats?: {
    activeJobs?: number
    jobsCount?: number
    totalJobs?: number
    totalApplications?: number
    applicationsCount?: number
    totalHires?: number
    followerCount?: number
    profileViews?: number
  }
  culture?: {
    benefits?: string[]
    values?: string[]
    workEnvironment?: string
  }
  specialties?: string[]
  technologies?: string[]
  socialLinks?: {
    linkedin?: string
    twitter?: string
    github?: string
  }
  verification?: {
    isVerified?: boolean
    verifiedAt?: string
  }
  isVerified?: boolean
  funding?: {
    stage?: string
    amount?: number
    investors?: string[]
  }
  rating?: number
  openJobs?: number
  followers?: number
  isActive?: boolean
  isFeatured?: boolean
  createdAt?: string | Date
  updatedAt?: string | Date
}

interface BackendSearchResult {
  companies: BackendCompany[]
  pagination?: {
    page?: number
    limit?: number
    total?: number
    pages?: number
    totalPages?: number
  }
  aggregations?: {
    industries?: string[]
    sizes?: string[]
    locations?: string[]
    fundingStages?: string[]
  }
  searchMeta?: {
    query?: string
    totalResults?: number
    searchTime?: number
  }
}

interface CacheEntry {
  data: CompanySearchResult | CompanyFrontend
  timestamp: number
}

export class CompaniesFrontendService {
  private baseUrl = '/api/v1/companies'
  private cache = new Map<string, CacheEntry>()
  private cacheExpiry = 5 * 60 * 1000 // 5 minutes

  async searchCompanies(query: CompanySearchQuery): Promise<CompanySearchResult> {
    const cacheKey = this.generateCacheKey('search', query)
    const cached = this.getFromCache<CompanySearchResult>(cacheKey)
    if (cached) return cached

    const params = this.buildSearchParams(query)
    const response = await fetch(`${this.baseUrl}?${params}`)
    
    if (!response.ok) {
      throw new Error(`Search failed: ${response.statusText}`)
    }
    
    const data = await response.json()

    // Extract the actual data from the API response
    const apiData = data.success ? data.data : data
    const result = this.transformSearchResult(apiData)

    this.setCache(cacheKey, result)
    return result
  }

  async getCompanyById(id: string): Promise<CompanyFrontend> {
    const cacheKey = this.generateCacheKey('company', { id })
    const cached = this.getFromCache<CompanyFrontend>(cacheKey)
    if (cached) return cached

    const response = await fetch(`${this.baseUrl}/${id}`)
    
    if (!response.ok) {
      throw new Error(`Failed to fetch company: ${response.statusText}`)
    }
    
    const data = await response.json()
    const company = this.transformCompany(data.company)
    
    this.setCache(cacheKey, company)
    return company
  }

  async followCompany(companyId: string): Promise<void> {
    const token = this.getAuthToken()
    const response = await fetch(`${this.baseUrl}/${companyId}/follow`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`Failed to follow company: ${response.statusText}`)
    }

    // Invalidate relevant caches
    this.invalidateCompanyCache(companyId)
  }

  async unfollowCompany(companyId: string): Promise<void> {
    const token = this.getAuthToken()
    const response = await fetch(`${this.baseUrl}/${companyId}/follow`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`Failed to unfollow company: ${response.statusText}`)
    }

    // Invalidate relevant caches
    this.invalidateCompanyCache(companyId)
  }

  private transformCompany(backendCompany: BackendCompany): CompanyFrontend {
    // Handle both full company objects and simplified API responses
    const primaryLocation = backendCompany.locations?.find((loc: BackendLocation) => loc.isHeadquarters)
      || backendCompany.locations?.[0]
      || backendCompany.location // fallback for simplified structure

    return {
      id: backendCompany._id || backendCompany.id || '',
      name: backendCompany.name,
      slug: backendCompany.slug || '',
      description: backendCompany.description,
      logo: backendCompany.logo,
      website: backendCompany.website,
      industries: Array.isArray(backendCompany.industry)
        ? backendCompany.industry.filter(Boolean)
        : backendCompany.industry
          ? [backendCompany.industry].filter(Boolean)
          : [],
      size: (backendCompany.size as CompanySize) || 'startup',
      founded: backendCompany.founded,
      primaryLocation: primaryLocation ? (
        typeof primaryLocation === 'string' ? {
          city: primaryLocation,
          country: 'Unknown',
          displayName: primaryLocation
        } : {
          city: primaryLocation.city,
          state: primaryLocation.state,
          country: primaryLocation.country,
          displayName: this.formatLocationDisplay(primaryLocation)
        }
      ) : {
        city: 'Unknown',
        country: 'Unknown',
        displayName: 'Location not specified'
      },
      allLocations: (backendCompany.locations || []).map((loc: BackendLocation) => ({
        city: loc.city,
        state: loc.state,
        country: loc.country,
        isHeadquarters: loc.isHeadquarters,
        displayName: this.formatLocationDisplay(loc)
      })),
      rating: this.calculateCompanyRating(backendCompany),
      stats: {
        activeJobs: backendCompany.stats?.activeJobs || backendCompany.openJobs || 0,
        totalJobs: backendCompany.stats?.totalJobs || 0,
        totalApplications: backendCompany.stats?.totalApplications || 0,
        totalHires: backendCompany.stats?.totalHires || 0,
        followerCount: backendCompany.stats?.followerCount || backendCompany.followers || 0,
        profileViews: backendCompany.stats?.profileViews || 0
      },
      culture: {
        benefits: backendCompany.culture?.benefits || [],
        values: backendCompany.culture?.values || [],
        workEnvironment: backendCompany.culture?.workEnvironment
      },
      specialties: backendCompany.specialties || [],
      technologies: backendCompany.technologies || [],
      socialLinks: {
        linkedin: backendCompany.socialLinks?.linkedin,
        twitter: backendCompany.socialLinks?.twitter,
        github: backendCompany.socialLinks?.github
      },
      verification: {
        isVerified: backendCompany.isVerified || backendCompany.verification?.isVerified || false,
        verifiedAt: backendCompany.verification?.verifiedAt
      },
      funding: backendCompany.funding && backendCompany.funding.stage ? {
        stage: backendCompany.funding.stage as FundingStage,
        amount: backendCompany.funding.amount,
        investors: backendCompany.funding.investors || []
      } : undefined,
      isActive: backendCompany.isActive || true,
      isFeatured: backendCompany.isFeatured || false,
      createdAt: typeof backendCompany.createdAt === 'string' ? backendCompany.createdAt : backendCompany.createdAt?.toISOString(),
      updatedAt: typeof backendCompany.updatedAt === 'string' ? backendCompany.updatedAt : backendCompany.updatedAt?.toISOString()
    }
  }

  private transformSearchResult(backendResult: BackendSearchResult): CompanySearchResult {
    return {
      companies: (backendResult.companies || []).map((company: BackendCompany) => this.transformCompany(company)),
      pagination: {
        page: backendResult.pagination?.page || 1,
        limit: backendResult.pagination?.limit || 20,
        total: backendResult.pagination?.total || 0,
        totalPages: backendResult.pagination?.pages || backendResult.pagination?.totalPages || 0
      },
      aggregations: {
        industries: backendResult.aggregations?.industries || [],
        sizes: backendResult.aggregations?.sizes || [],
        locations: backendResult.aggregations?.locations || [],
        fundingStages: backendResult.aggregations?.fundingStages || []
      },
      searchMeta: {
        query: backendResult.searchMeta?.query || '',
        totalResults: backendResult.searchMeta?.totalResults || 0,
        searchTime: backendResult.searchMeta?.searchTime || 0
      }
    }
  }

  private calculateCompanyRating(company: BackendCompany): number {
    // Calculate rating based on various factors
    let rating = 3.0 // Base rating
    
    if (company.verification?.isVerified) rating += 0.5
    if ((company.stats?.activeJobs || 0) > 0) rating += 0.3
    if ((company.culture?.benefits?.length || 0) > 5) rating += 0.2
    if (company.funding?.stage) rating += 0.3
    if ((company.stats?.followerCount || 0) > 100) rating += 0.2
    
    return Math.min(5.0, Math.round(rating * 10) / 10)
  }

  private formatLocationDisplay(location: BackendLocation): string {
    const parts = [location.city]
    if (location.state) parts.push(location.state)
    parts.push(location.country)
    return parts.join(', ')
  }

  private buildSearchParams(query: CompanySearchQuery): URLSearchParams {
    const params = new URLSearchParams()

    if (query.search) params.append('search', query.search)
    params.append('page', (query.page || 1).toString())
    params.append('limit', (query.limit || 20).toString())

    // Add filters
    if (query.industries?.length) {
      params.append('industry', query.industries.join(','))
    }
    if (query.sizes?.length) {
      params.append('size', query.sizes.join(','))
    }
    if (query.locations?.length) {
      params.append('location', query.locations.join(','))
    }
    if (query.verified !== undefined) {
      params.append('verified', query.verified.toString())
    }
    if (query.hasJobs) {
      params.append('hasJobs', 'true')
    }
    if (query.sortBy) {
      params.append('sortBy', query.sortBy)
    }
    if (query.sortOrder) {
      params.append('sortOrder', query.sortOrder)
    }
    
    return params
  }

  private generateCacheKey(type: string, data: CompanySearchQuery | Record<string, unknown>): string {
    return `${type}_${JSON.stringify(data)}`
  }

  private getFromCache<T = CompanySearchResult | CompanyFrontend>(key: string): T | null {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data as T
    }
    this.cache.delete(key)
    return null
  }

  private setCache(key: string, data: CompanySearchResult | CompanyFrontend): void {
    this.cache.set(key, { data, timestamp: Date.now() })
  }

  private invalidateCompanyCache(companyId: string): void {
    for (const [key] of this.cache) {
      if (key.includes(companyId)) {
        this.cache.delete(key)
      }
    }
  }

  private getAuthToken(): string {
    const token = localStorage.getItem('auth-token')
    if (!token) {
      throw new Error('Authentication required')
    }
    return token
  }

  clearCache(): void {
    this.cache.clear()
  }
}

export const companiesFrontendService = new CompaniesFrontendService()

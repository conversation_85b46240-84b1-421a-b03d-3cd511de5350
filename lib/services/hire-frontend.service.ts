// lib/services/hire-frontend.service.ts
import { 
  <PERSON>reRequest, 
  <PERSON>reFrontend, 
  HireSearchQuery, 
  HireSearchResult, 
  HireStats, 
  CreateHireRequest, 
  UpdateHireRequest,
  HireStatus,
  HireType
} from '@/types/hire.types'

export class HireFrontendService {
  private baseUrl = '/api/v1/hire-requests'
  private cache = new Map<string, { data: any; timestamp: number }>()
  private cacheExpiry = 5 * 60 * 1000 // 5 minutes

  // Cache Management
  private generateCacheKey(operation: string, params: any): string {
    return `${operation}_${JSON.stringify(params)}`
  }

  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data as T
    }
    this.cache.delete(key)
    return null
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() })
  }

  private clearCache(): void {
    this.cache.clear()
  }

  // Data Transformation
  private transformHireRequest(backendHire: any): HireFrontend {
    return {
      id: backendHire._id || backendHire.id,
      hireType: backendHire.hireType,
      status: backendHire.status,
      projectDetails: {
        ...backendHire.projectDetails,
        timeline: {
          ...backendHire.projectDetails.timeline,
          startDate: new Date(backendHire.projectDetails.timeline.startDate),
          endDate: backendHire.projectDetails.timeline.endDate 
            ? new Date(backendHire.projectDetails.timeline.endDate) 
            : undefined,
          milestones: backendHire.projectDetails.timeline.milestones?.map((milestone: any) => ({
            ...milestone,
            dueDate: new Date(milestone.dueDate)
          })) || []
        }
      },
      contractTerms: {
        ...backendHire.contractTerms,
        duration: backendHire.contractTerms.duration ? {
          ...backendHire.contractTerms.duration,
          startDate: new Date(backendHire.contractTerms.duration.startDate),
          endDate: backendHire.contractTerms.duration.endDate 
            ? new Date(backendHire.contractTerms.duration.endDate) 
            : undefined
        } : undefined
      },
      message: backendHire.message,
      companyNotes: backendHire.companyNotes,
      talentResponse: backendHire.talentResponse,
      timeline: backendHire.timeline.map((entry: any) => ({
        ...entry,
        date: new Date(entry.date)
      })),
      priority: backendHire.priority,
      source: backendHire.source,
      referenceId: backendHire.referenceId,
      contractDocuments: backendHire.contractDocuments?.map((doc: any) => ({
        ...doc,
        uploadDate: new Date(doc.uploadDate),
        signedBy: doc.signedBy?.map((signature: any) => ({
          ...signature,
          signedAt: new Date(signature.signedAt)
        })) || []
      })) || [],
      expiresAt: backendHire.expiresAt ? new Date(backendHire.expiresAt) : undefined,
      responseDeadline: backendHire.responseDeadline ? new Date(backendHire.responseDeadline) : undefined,
      isActive: backendHire.isActive,
      createdAt: new Date(backendHire.createdAt),
      updatedAt: new Date(backendHire.updatedAt),
      
      // Populated fields
      company: {
        id: backendHire.company?._id || backendHire.company?.id || backendHire.companyId,
        name: backendHire.company?.name || 'Unknown Company',
        logo: backendHire.company?.logo,
        industry: backendHire.company?.industry
      },
      talent: {
        id: backendHire.talent?._id || backendHire.talent?.id || backendHire.talentId,
        name: backendHire.talent?.name || 
              `${backendHire.talent?.user?.profile?.firstName || ''} ${backendHire.talent?.user?.profile?.lastName || ''}`.trim() || 
              'Unknown Talent',
        avatar: backendHire.talent?.user?.profile?.avatar || backendHire.talent?.avatar,
        title: backendHire.talent?.currentTitle || backendHire.talent?.headline || 'Professional',
        location: this.formatLocation(backendHire.talent?.location),
        skills: backendHire.talent?.skills?.map((skill: any) => skill.name || skill) || []
      },
      createdBy: {
        id: backendHire.createdBy?._id || backendHire.createdBy?.id || backendHire.createdBy,
        name: backendHire.createdBy?.profile?.firstName && backendHire.createdBy?.profile?.lastName
          ? `${backendHire.createdBy.profile.firstName} ${backendHire.createdBy.profile.lastName}`
          : 'System User',
        role: backendHire.createdBy?.role || 'user'
      },
      lastUpdatedBy: {
        id: backendHire.lastUpdatedBy?._id || backendHire.lastUpdatedBy?.id || backendHire.lastUpdatedBy,
        name: backendHire.lastUpdatedBy?.profile?.firstName && backendHire.lastUpdatedBy?.profile?.lastName
          ? `${backendHire.lastUpdatedBy.profile.firstName} ${backendHire.lastUpdatedBy.profile.lastName}`
          : 'System User',
        role: backendHire.lastUpdatedBy?.role || 'user'
      }
    }
  }

  private formatLocation(location: any): string {
    if (!location) return 'Remote'
    
    if (typeof location === 'string') return location
    
    const parts = []
    if (location.city) parts.push(location.city)
    if (location.state) parts.push(location.state)
    if (location.country) parts.push(location.country)
    
    return parts.length > 0 ? parts.join(', ') : 'Remote'
  }

  private transformSearchResult(apiData: any): HireSearchResult {
    return {
      hireRequests: apiData.hireRequests?.map((hire: any) => this.transformHireRequest(hire)) || [],
      pagination: {
        page: apiData.pagination?.page || 1,
        limit: apiData.pagination?.limit || 20,
        total: apiData.pagination?.total || 0,
        totalPages: apiData.pagination?.totalPages || 0
      },
      filters: {
        statuses: apiData.filters?.statuses || [],
        types: apiData.filters?.types || [],
        priorities: apiData.filters?.priorities || []
      }
    }
  }

  // API Methods
  async searchHireRequests(query: HireSearchQuery): Promise<HireSearchResult> {
    const cacheKey = this.generateCacheKey('search', query)
    const cached = this.getFromCache<HireSearchResult>(cacheKey)
    if (cached) return cached

    const params = this.buildSearchParams(query)
    const response = await fetch(`${this.baseUrl}?${params}`)

    if (!response.ok) {
      throw new Error(`Search failed: ${response.statusText}`)
    }

    const data = await response.json()
    const apiData = data.success ? data.data : data
    const result = this.transformSearchResult(apiData)

    this.setCache(cacheKey, result)
    return result
  }

  async getHireRequestById(id: string): Promise<HireFrontend> {
    const cacheKey = this.generateCacheKey('hire', { id })
    const cached = this.getFromCache<HireFrontend>(cacheKey)
    if (cached) return cached

    const response = await fetch(`${this.baseUrl}/${id}`)

    if (!response.ok) {
      throw new Error(`Failed to fetch hire request: ${response.statusText}`)
    }

    const data = await response.json()
    const hire = this.transformHireRequest(data.data || data)

    this.setCache(cacheKey, hire)
    return hire
  }

  async createHireRequest(data: CreateHireRequest): Promise<HireFrontend> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to create hire request')
    }

    const result = await response.json()
    const hire = this.transformHireRequest(result.data || result)

    // Clear search cache
    this.clearCache()
    
    return hire
  }

  async updateHireRequest(id: string, data: UpdateHireRequest): Promise<HireFrontend> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to update hire request')
    }

    const result = await response.json()
    const hire = this.transformHireRequest(result.data || result)

    // Clear cache
    this.clearCache()
    
    return hire
  }

  async deleteHireRequest(id: string): Promise<boolean> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'DELETE'
    })

    if (!response.ok) {
      throw new Error(`Failed to delete hire request: ${response.statusText}`)
    }

    // Clear cache
    this.clearCache()
    
    return true
  }

  async updateHireStatus(id: string, status: HireStatus, notes?: string): Promise<HireFrontend> {
    return this.updateHireRequest(id, { status, companyNotes: notes })
  }

  async getHireStats(companyId?: string): Promise<HireStats> {
    const cacheKey = this.generateCacheKey('stats', { companyId })
    const cached = this.getFromCache<HireStats>(cacheKey)
    if (cached) return cached

    const url = companyId 
      ? `${this.baseUrl}/stats?companyId=${companyId}`
      : `${this.baseUrl}/stats`
      
    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`Failed to load stats: ${response.statusText}`)
    }

    const data = await response.json()
    const stats = data.success ? data.data : data

    this.setCache(cacheKey, stats)
    return stats
  }

  private buildSearchParams(query: HireSearchQuery): string {
    const params = new URLSearchParams()

    if (query.companyId) params.append('companyId', query.companyId)
    if (query.talentId) params.append('talentId', query.talentId)
    if (query.status) {
      if (Array.isArray(query.status)) {
        query.status.forEach(status => params.append('status', status))
      } else {
        params.append('status', query.status)
      }
    }
    if (query.hireType) {
      if (Array.isArray(query.hireType)) {
        query.hireType.forEach(type => params.append('hireType', type))
      } else {
        params.append('hireType', query.hireType)
      }
    }
    if (query.priority) params.append('priority', query.priority)
    if (query.search) params.append('search', query.search)
    if (query.dateRange) {
      params.append('dateFrom', query.dateRange.from)
      params.append('dateTo', query.dateRange.to)
    }
    if (query.page) params.append('page', query.page.toString())
    if (query.limit) params.append('limit', query.limit.toString())
    if (query.sortBy) params.append('sortBy', query.sortBy)
    if (query.sortOrder) params.append('sortOrder', query.sortOrder)

    return params.toString()
  }

  // Utility methods
  getStatusColor(status: HireStatus): string {
    const statusColors: Record<HireStatus, string> = {
      pending: 'bg-yellow-100 text-yellow-800',
      reviewing: 'bg-blue-100 text-blue-800',
      negotiating: 'bg-purple-100 text-purple-800',
      contract_sent: 'bg-indigo-100 text-indigo-800',
      contract_signed: 'bg-green-100 text-green-800',
      hired: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800',
      cancelled: 'bg-gray-100 text-gray-800',
      expired: 'bg-orange-100 text-orange-800'
    }
    return statusColors[status] || 'bg-gray-100 text-gray-800'
  }

  getTypeLabel(type: HireType): string {
    const typeLabels: Record<HireType, string> = {
      full_time: 'Full Time',
      part_time: 'Part Time',
      contract: 'Contract',
      freelance: 'Freelance',
      internship: 'Internship',
      temporary: 'Temporary'
    }
    return typeLabels[type] || type
  }

  getPriorityColor(priority: string): string {
    const priorityColors: Record<string, string> = {
      low: 'bg-gray-100 text-gray-800',
      medium: 'bg-blue-100 text-blue-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800'
    }
    return priorityColors[priority] || 'bg-gray-100 text-gray-800'
  }

  formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  calculateDaysUntilDeadline(deadline: Date): number {
    return Math.ceil((deadline.getTime() - Date.now()) / (1000 * 60 * 60 * 24))
  }

  isExpiringSoon(deadline: Date, days: number = 7): boolean {
    return this.calculateDaysUntilDeadline(deadline) <= days
  }
}

// Export singleton instance
export const hireFrontendService = new HireFrontendService()

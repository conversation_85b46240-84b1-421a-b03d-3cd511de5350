// lib/services/client-detail.service.ts
import { Client } from '@/lib/models/client.model'
import { User } from '@/lib/models/user.model'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import { cacheService } from '@/lib/services/cache.service'
import { 
  ClientDetailProfile, 
  UpdateClientDetailRequest,
  ClientDetailAnalytics
} from '@/types/client-detail.types'

export class ClientDetailService {
  private readonly CACHE_TTL = 5 * 60 * 1000 // 5 minutes
  private readonly ANALYTICS_CACHE_TTL = 15 * 60 * 1000 // 15 minutes

  /**
   * Get detailed client profile by ID
   */
  async getClientDetail(clientId: string): Promise<ClientDetailProfile> {
    try {
      const cacheKey = `client_detail:${clientId}`
      const cached = await cacheService.get<ClientDetailProfile>(cacheKey)
      if (cached) return cached

      const client = await Client.findById(clientId)
        .populate({
          path: 'user',
          select: 'profile preferences'
        })
        .lean()

      if (!client) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Client profile not found',
          'client'
        )
      }

      if (!client.isActive || !client.isPublic) {
        throw errorService.createError(
          ErrorCode.FORBIDDEN,
          'Client profile is not accessible',
          'client'
        )
      }

      const detailProfile = this.transformToDetailProfile(client)
      
      // Cache the result
      await cacheService.set(cacheKey, detailProfile, this.CACHE_TTL)
      
      // Track profile view
      await this.trackProfileView(clientId)
      
      return detailProfile

    } catch (error: unknown) {
      if (error instanceof Error && 'code' in error) {
        throw error
      }
      throw errorService.createError(
        ErrorCode.INTERNAL_ERROR,
        'Failed to fetch client details',
        'client'
      )
    }
  }

  /**
   * Update client profile
   */
  async updateClientDetail(
    clientId: string, 
    updateData: UpdateClientDetailRequest,
    userId: string
  ): Promise<ClientDetailProfile> {
    try {
      // Verify ownership
      const client = await Client.findById(clientId)
      if (!client) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Client profile not found',
          'client'
        )
      }

      if (client.user.toString() !== userId) {
        throw errorService.createError(
          ErrorCode.FORBIDDEN,
          'Not authorized to update this profile',
          'client'
        )
      }

      // Update the client
      const updatedClient = await Client.findByIdAndUpdate(
        clientId,
        {
          ...updateData,
          'activity.lastProfileUpdate': new Date(),
          updatedAt: new Date()
        },
        { new: true, runValidators: true }
      ).populate({
        path: 'user',
        select: 'profile preferences'
      }).lean()

      if (!updatedClient) {
        throw errorService.createError(
          ErrorCode.INTERNAL_ERROR,
          'Failed to update client profile',
          'client'
        )
      }

      // Recalculate profile completeness
      await this.updateProfileCompleteness(clientId)

      // Clear cache
      const cacheKey = `client_detail:${clientId}`
      await cacheService.delete(cacheKey)

      return this.transformToDetailProfile(updatedClient)

    } catch (error: unknown) {
      if (error instanceof Error && 'code' in error) {
        throw error
      }
      throw errorService.createError(
        ErrorCode.INTERNAL_ERROR,
        'Failed to update client profile',
        'client'
      )
    }
  }

  /**
   * Get client analytics
   */
  async getClientAnalytics(clientId: string, userId: string): Promise<ClientDetailAnalytics> {
    try {
      // Verify ownership
      const client = await Client.findById(clientId)
      if (!client) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Client profile not found',
          'client'
        )
      }

      if (client.user.toString() !== userId) {
        throw errorService.createError(
          ErrorCode.FORBIDDEN,
          'Not authorized to view analytics',
          'client'
        )
      }

      const cacheKey = `client_analytics:${clientId}`
      const cached = await cacheService.get<ClientDetailAnalytics>(cacheKey)
      if (cached) return cached

      const analytics = await this.calculateAnalytics(client)
      
      // Cache analytics
      await cacheService.set(cacheKey, analytics, this.ANALYTICS_CACHE_TTL)
      
      return analytics

    } catch (error: unknown) {
      if (error instanceof Error && 'code' in error) {
        throw error
      }
      throw errorService.createError(
        ErrorCode.INTERNAL_ERROR,
        'Failed to fetch client analytics',
        'client'
      )
    }
  }

  /**
   * Add work experience
   */
  async addWorkExperience(
    clientId: string, 
    workData: ClientDetailProfile['workHistory'][0],
    userId: string
  ): Promise<ClientDetailProfile> {
    try {
      const client = await Client.findById(clientId)
      if (!client || client.user.toString() !== userId) {
        throw errorService.createError(
          ErrorCode.FORBIDDEN,
          'Not authorized to update this profile',
          'client'
        )
      }

      const newWork = {
        ...workData,
        id: new Date().getTime().toString()
      }

      client.workHistory.push(newWork as any)
      client.activity.lastProfileUpdate = new Date()
      await client.save()

      await this.updateProfileCompleteness(clientId)
      
      // Clear cache
      const cacheKey = `client_detail:${clientId}`
      await cacheService.delete(cacheKey)

      return this.getClientDetail(clientId)

    } catch (error: unknown) {
      if (error instanceof Error && 'code' in error) {
        throw error
      }
      throw errorService.createError(
        ErrorCode.INTERNAL_ERROR,
        'Failed to add work experience',
        'client'
      )
    }
  }

  /**
   * Add education
   */
  async addEducation(
    clientId: string, 
    educationData: ClientDetailProfile['education'][0],
    userId: string
  ): Promise<ClientDetailProfile> {
    try {
      const client = await Client.findById(clientId)
      if (!client || client.user.toString() !== userId) {
        throw errorService.createError(
          ErrorCode.FORBIDDEN,
          'Not authorized to update this profile',
          'client'
        )
      }

      const newEducation = {
        ...educationData,
        id: new Date().getTime().toString()
      }

      client.education.push(newEducation as any)
      client.activity.lastProfileUpdate = new Date()
      await client.save()

      await this.updateProfileCompleteness(clientId)
      
      // Clear cache
      const cacheKey = `client_detail:${clientId}`
      await cacheService.delete(cacheKey)

      return this.getClientDetail(clientId)

    } catch (error: unknown) {
      if (error instanceof Error && 'code' in error) {
        throw error
      }
      throw errorService.createError(
        ErrorCode.INTERNAL_ERROR,
        'Failed to add education',
        'client'
      )
    }
  }

  /**
   * Add skill
   */
  async addSkill(
    clientId: string, 
    skillData: ClientDetailProfile['skills'][0],
    userId: string
  ): Promise<ClientDetailProfile> {
    try {
      const client = await Client.findById(clientId)
      if (!client || client.user.toString() !== userId) {
        throw errorService.createError(
          ErrorCode.FORBIDDEN,
          'Not authorized to update this profile',
          'client'
        )
      }

      // Check if skill already exists
      const existingSkill = client.skills.find((skill: any) => 
        skill.name.toLowerCase() === skillData.name.toLowerCase()
      )

      if (existingSkill) {
        throw errorService.createError(
          ErrorCode.DUPLICATE_ENTRY,
          'Skill already exists',
          'client'
        )
      }

      client.skills.push(skillData as any)
      client.activity.lastProfileUpdate = new Date()
      await client.save()

      await this.updateProfileCompleteness(clientId)
      
      // Clear cache
      const cacheKey = `client_detail:${clientId}`
      await cacheService.delete(cacheKey)

      return this.getClientDetail(clientId)

    } catch (error: unknown) {
      if (error instanceof Error && 'code' in error) {
        throw error
      }
      throw errorService.createError(
        ErrorCode.INTERNAL_ERROR,
        'Failed to add skill',
        'client'
      )
    }
  }

  /**
   * Track profile view
   */
  private async trackProfileView(clientId: string): Promise<void> {
    try {
      await Client.findByIdAndUpdate(
        clientId,
        {
          $inc: { 'activity.profileViews': 1 }
        }
      )
    } catch (error) {
      // Don't throw error for tracking failures
      console.error('Failed to track profile view:', error)
    }
  }

  /**
   * Update profile completeness score
   */
  private async updateProfileCompleteness(clientId: string): Promise<void> {
    try {
      const client = await Client.findById(clientId)
      if (!client) return

      const completeness = this.calculateProfileCompleteness(client)
      
      await Client.findByIdAndUpdate(
        clientId,
        { 'activity.profileCompleteness': completeness }
      )
    } catch (error) {
      console.error('Failed to update profile completeness:', error)
    }
  }

  /**
   * Calculate profile completeness percentage
   */
  private calculateProfileCompleteness(client: any): number {
    const sections = [
      { name: 'Basic Info', weight: 15, completed: !!(client.headline && client.summary) },
      { name: 'Experience', weight: 20, completed: client.workHistory?.length > 0 },
      { name: 'Education', weight: 15, completed: client.education?.length > 0 },
      { name: 'Skills', weight: 20, completed: client.skills?.length >= 3 },
      { name: 'Job Preferences', weight: 15, completed: !!(client.jobPreferences?.desiredRoles?.length > 0) },
      { name: 'Documents', weight: 10, completed: client.documents?.resume?.length > 0 },
      { name: 'Languages', weight: 5, completed: client.languages?.length > 0 }
    ]

    const completed = sections.filter(section => section.completed)
    const totalWeight = sections.reduce((sum, section) => sum + section.weight, 0)
    const completedWeight = completed.reduce((sum, section) => sum + section.weight, 0)

    return Math.round((completedWeight / totalWeight) * 100)
  }

  /**
   * Calculate analytics
   */
  private async calculateAnalytics(client: any): Promise<ClientDetailAnalytics> {
    const now = new Date()
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

    return {
      profileViews: {
        total: client.activity.profileViews || 0,
        thisWeek: Math.floor((client.activity.profileViews || 0) * 0.3), // Placeholder calculation
        thisMonth: Math.floor((client.activity.profileViews || 0) * 0.7), // Placeholder calculation
        trend: 'up'
      },
      searchAppearances: {
        total: client.activity.searchAppearances || 0,
        thisWeek: Math.floor((client.activity.searchAppearances || 0) * 0.3),
        thisMonth: Math.floor((client.activity.searchAppearances || 0) * 0.7),
        trend: 'stable'
      },
      applicationSuccess: {
        rate: client.applicationStats.successRate || 0,
        totalApplications: client.applicationStats.totalApplications || 0,
        interviews: client.applicationStats.interviewsReceived || 0,
        offers: client.applicationStats.offersReceived || 0
      },
      profileStrength: {
        score: this.calculateProfileCompleteness(client),
        completeness: client.activity.profileCompleteness || 0,
        suggestions: this.getProfileSuggestions(client)
      }
    }
  }

  /**
   * Get profile improvement suggestions
   */
  private getProfileSuggestions(client: any): string[] {
    const suggestions: string[] = []

    if (!client.headline || client.headline.length < 10) {
      suggestions.push('Add a compelling professional headline')
    }
    if (!client.summary || client.summary.length < 50) {
      suggestions.push('Write a detailed professional summary')
    }
    if (!client.workHistory || client.workHistory.length === 0) {
      suggestions.push('Add your work experience')
    }
    if (!client.education || client.education.length === 0) {
      suggestions.push('Add your education background')
    }
    if (!client.skills || client.skills.length < 5) {
      suggestions.push('Add more relevant skills')
    }
    if (!client.documents?.resume || client.documents.resume.length === 0) {
      suggestions.push('Upload your resume')
    }
    if (!client.portfolio || client.portfolio.length === 0) {
      suggestions.push('Showcase your work with portfolio items')
    }

    return suggestions
  }

  /**
   * Transform database client to detail profile
   */
  private transformToDetailProfile(client: any): ClientDetailProfile {
    return {
      id: client._id.toString(),
      user: {
        id: client.user._id?.toString() || client.user.toString(),
        profile: client.user.profile || {
          firstName: '',
          lastName: '',
          email: '',
        },
        preferences: client.user.preferences || {
          theme: 'light',
          language: 'en',
          timezone: 'UTC',
          notifications: {
            email: true,
            sms: false,
            push: true
          }
        }
      },
      headline: client.headline || '',
      summary: client.summary || '',
      experience: {
        level: client.experience?.level || 'entry',
        yearsOfExperience: client.experience?.yearsOfExperience || 0,
        industries: client.experience?.industries || [],
        currentSalary: {
          amount: client.experience?.currentSalary?.amount,
          currency: client.experience?.currentSalary?.currency || 'USD',
          period: client.experience?.currentSalary?.period || 'yearly'
        }
      },
      jobPreferences: {
        desiredRoles: client.jobPreferences?.desiredRoles || [],
        industries: client.jobPreferences?.industries || [],
        locations: client.jobPreferences?.locations || [],
        salaryExpectation: client.jobPreferences?.salaryExpectation || {
          min: 0,
          max: 0,
          currency: 'USD',
          period: 'yearly',
          negotiable: true
        },
        jobTypes: client.jobPreferences?.jobTypes || [],
        workArrangement: client.jobPreferences?.workArrangement || [],
        availability: client.jobPreferences?.availability || 'immediately',
        benefits: client.jobPreferences?.benefits || [],
        companySize: client.jobPreferences?.companySize || []
      },
      privacy: {
        profileVisibility: client.privacy?.profileVisibility || 'public',
        showSalaryExpectation: client.privacy?.showSalaryExpectation ?? true,
        showCurrentCompany: client.privacy?.showCurrentCompany ?? true,
        allowRecruiterContact: client.privacy?.allowRecruiterContact ?? true,
        showProfileToCurrentEmployer: client.privacy?.showProfileToCurrentEmployer ?? false
      },
      workHistory: client.workHistory || [],
      education: client.education || [],
      skills: client.skills || [],
      certifications: client.certifications || [],
      portfolio: client.portfolio || [],
      documents: {
        resume: client.documents?.resume || [],
        coverLetter: client.documents?.cover_letter || [],
        portfolio: client.documents?.portfolio || [],
        certificate: client.documents?.certificate || [],
        other: client.documents?.other || []
      },
      languages: client.languages || [],
      activity: {
        profileViews: client.activity?.profileViews || 0,
        searchAppearances: client.activity?.searchAppearances || 0,
        recruiterViews: client.activity?.recruiterViews || 0,
        profileCompleteness: client.activity?.profileCompleteness || 0,
        lastProfileUpdate: client.activity?.lastProfileUpdate?.toISOString() || new Date().toISOString(),
        viewHistory: []
      },
      applicationStats: {
        totalApplications: client.applicationStats?.totalApplications || 0,
        pendingApplications: client.applicationStats?.pendingApplications || 0,
        interviewsReceived: client.applicationStats?.interviewsReceived || 0,
        offersReceived: client.applicationStats?.offersReceived || 0,
        successRate: client.applicationStats?.successRate || 0,
        averageResponseTime: 0,
        recentApplications: []
      },
      savedJobs: client.savedJobs || [],
      followedCompanies: client.followedCompanies || [],
      followedRecruiters: client.followedRecruiters || [],
      messages: {
        unreadCount: client.messages?.unreadCount || 0,
        sent: client.messages?.sent || [],
        received: client.messages?.received || []
      },
      jobAlerts: client.jobAlerts || [],
      searchHistory: client.searchHistory || [],
      verification: {
        emailVerified: client.verification?.emailVerified || false,
        phoneVerified: client.verification?.phoneVerified || false,
        identityVerified: client.verification?.identityVerified || false,
        backgroundCheckCompleted: client.verification?.backgroundCheckCompleted || false,
        references: client.verification?.references || []
      },
      isActive: client.isActive || false,
      isPublic: client.isPublic || false,
      lastLogin: client.lastLogin?.toISOString() || new Date().toISOString(),
      createdAt: client.createdAt?.toISOString() || new Date().toISOString(),
      updatedAt: client.updatedAt?.toISOString() || new Date().toISOString()
    }
  }
}

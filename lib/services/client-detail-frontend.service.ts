// lib/services/client-detail-frontend.service.ts
import { 
  ClientDetailProfile, 
  UpdateClientDetailRequest,
  ClientDetailAnalytics,
  ClientDetailResponse
} from '@/types/client-detail.types'

export class ClientDetailFrontendService {
  private baseUrl: string
  private cache: Map<string, { data: unknown; timestamp: number }> = new Map()
  private readonly CACHE_TTL = 5 * 60 * 1000 // 5 minutes

  constructor() {
    this.baseUrl = '/api/v1/clients'
  }

  /**
   * Get client detail profile
   */
  async getClientDetail(clientId: string): Promise<ClientDetailProfile> {
    const cacheKey = `client_detail:${clientId}`
    const cached = this.getFromCache<ClientDetailProfile>(cacheKey)
    if (cached) return cached

    const response = await fetch(`${this.baseUrl}/${clientId}`)

    if (!response.ok) {
      throw new Error(`Failed to fetch client detail: ${response.statusText}`)
    }

    const data: ClientDetailResponse = await response.json()
    const clientDetail = data.data

    this.setCache(cacheKey, clientDetail)
    return clientDetail
  }

  /**
   * Update client profile
   */
  async updateClientDetail(
    clientId: string, 
    updateData: UpdateClientDetailRequest
  ): Promise<ClientDetailProfile> {
    const response = await fetch(`${this.baseUrl}/${clientId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData),
    })

    if (!response.ok) {
      throw new Error(`Failed to update client: ${response.statusText}`)
    }

    const data: ClientDetailResponse = await response.json()
    const updatedClient = data.data

    // Clear cache
    this.clearClientCache(clientId)
    
    return updatedClient
  }

  /**
   * Get client analytics
   */
  async getClientAnalytics(clientId: string): Promise<ClientDetailAnalytics> {
    const cacheKey = `client_analytics:${clientId}`
    const cached = this.getFromCache<ClientDetailAnalytics>(cacheKey)
    if (cached) return cached

    const response = await fetch(`${this.baseUrl}/${clientId}/analytics`)

    if (!response.ok) {
      throw new Error(`Failed to fetch analytics: ${response.statusText}`)
    }

    const data = await response.json()
    const analytics = data.data

    this.setCache(cacheKey, analytics)
    return analytics
  }

  /**
   * Add work experience
   */
  async addWorkExperience(
    clientId: string, 
    workData: ClientDetailProfile['workHistory'][0]
  ): Promise<ClientDetailProfile> {
    const response = await fetch(`${this.baseUrl}/${clientId}/experience`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(workData),
    })

    if (!response.ok) {
      throw new Error(`Failed to add work experience: ${response.statusText}`)
    }

    const data: ClientDetailResponse = await response.json()
    const updatedClient = data.data

    // Clear cache
    this.clearClientCache(clientId)
    
    return updatedClient
  }

  /**
   * Add education
   */
  async addEducation(
    clientId: string, 
    educationData: ClientDetailProfile['education'][0]
  ): Promise<ClientDetailProfile> {
    const response = await fetch(`${this.baseUrl}/${clientId}/education`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(educationData),
    })

    if (!response.ok) {
      throw new Error(`Failed to add education: ${response.statusText}`)
    }

    const data: ClientDetailResponse = await response.json()
    const updatedClient = data.data

    // Clear cache
    this.clearClientCache(clientId)
    
    return updatedClient
  }

  /**
   * Add skill
   */
  async addSkill(
    clientId: string, 
    skillData: ClientDetailProfile['skills'][0]
  ): Promise<ClientDetailProfile> {
    const response = await fetch(`${this.baseUrl}/${clientId}/skills`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(skillData),
    })

    if (!response.ok) {
      throw new Error(`Failed to add skill: ${response.statusText}`)
    }

    const data: ClientDetailResponse = await response.json()
    const updatedClient = data.data

    // Clear cache
    this.clearClientCache(clientId)
    
    return updatedClient
  }

  /**
   * Update basic profile info
   */
  async updateBasicInfo(
    clientId: string,
    basicInfo: {
      headline?: string
      summary?: string
    }
  ): Promise<ClientDetailProfile> {
    return this.updateClientDetail(clientId, basicInfo)
  }

  /**
   * Update job preferences
   */
  async updateJobPreferences(
    clientId: string,
    jobPreferences: Partial<ClientDetailProfile['jobPreferences']>
  ): Promise<ClientDetailProfile> {
    return this.updateClientDetail(clientId, { jobPreferences })
  }

  /**
   * Update privacy settings
   */
  async updatePrivacySettings(
    clientId: string,
    privacy: Partial<ClientDetailProfile['privacy']>
  ): Promise<ClientDetailProfile> {
    return this.updateClientDetail(clientId, { privacy })
  }

  /**
   * Update experience info
   */
  async updateExperience(
    clientId: string,
    experience: Partial<ClientDetailProfile['experience']>
  ): Promise<ClientDetailProfile> {
    return this.updateClientDetail(clientId, { experience })
  }

  /**
   * Get profile completeness suggestions
   */
  getProfileSuggestions(client: ClientDetailProfile): string[] {
    const suggestions: string[] = []

    if (!client.headline || client.headline.length < 10) {
      suggestions.push('Add a compelling professional headline')
    }
    if (!client.summary || client.summary.length < 50) {
      suggestions.push('Write a detailed professional summary')
    }
    if (!client.workHistory || client.workHistory.length === 0) {
      suggestions.push('Add your work experience')
    }
    if (!client.education || client.education.length === 0) {
      suggestions.push('Add your education background')
    }
    if (!client.skills || client.skills.length < 5) {
      suggestions.push('Add more relevant skills')
    }
    if (!client.documents?.resume || client.documents.resume.length === 0) {
      suggestions.push('Upload your resume')
    }
    if (!client.portfolio || client.portfolio.length === 0) {
      suggestions.push('Showcase your work with portfolio items')
    }
    if (!client.certifications || client.certifications.length === 0) {
      suggestions.push('Add professional certifications')
    }

    return suggestions
  }

  /**
   * Calculate profile strength score
   */
  calculateProfileStrength(client: ClientDetailProfile): number {
    const sections = [
      { weight: 15, completed: !!(client.headline && client.summary) },
      { weight: 20, completed: client.workHistory?.length > 0 },
      { weight: 15, completed: client.education?.length > 0 },
      { weight: 20, completed: client.skills?.length >= 3 },
      { weight: 15, completed: client.jobPreferences?.desiredRoles?.length > 0 },
      { weight: 10, completed: client.documents?.resume?.length > 0 },
      { weight: 5, completed: client.languages?.length > 0 }
    ]

    const completed = sections.filter(section => section.completed)
    const totalWeight = sections.reduce((sum, section) => sum + section.weight, 0)
    const completedWeight = completed.reduce((sum, section) => sum + section.weight, 0)

    return Math.round((completedWeight / totalWeight) * 100)
  }

  /**
   * Format salary range
   */
  formatSalaryRange(salaryExpectation: ClientDetailProfile['jobPreferences']['salaryExpectation']): string {
    if (!salaryExpectation.min && !salaryExpectation.max) {
      return 'Salary negotiable'
    }

    const { min, max, currency, period } = salaryExpectation
    const periodText = period === 'yearly' ? '/year' : period === 'monthly' ? '/month' : '/hour'
    
    if (min && max) {
      return `${currency}${min.toLocaleString()} - ${currency}${max.toLocaleString()}${periodText}`
    } else if (min) {
      return `${currency}${min.toLocaleString()}+${periodText}`
    } else if (max) {
      return `Up to ${currency}${max.toLocaleString()}${periodText}`
    }

    return 'Salary negotiable'
  }

  /**
   * Format experience level
   */
  formatExperienceLevel(level: string, years: number): string {
    const levelMap: Record<string, string> = {
      entry: 'Entry Level',
      junior: 'Junior',
      mid: 'Mid Level',
      senior: 'Senior',
      lead: 'Lead',
      executive: 'Executive'
    }

    const levelText = levelMap[level] || level
    return years > 0 ? `${levelText} (${years} years)` : levelText
  }

  /**
   * Cache management
   */
  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key)
    if (!cached) return null

    const isExpired = Date.now() - cached.timestamp > this.CACHE_TTL
    if (isExpired) {
      this.cache.delete(key)
      return null
    }

    return cached.data as T
  }

  private setCache(key: string, data: unknown): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  private clearClientCache(clientId: string): void {
    const keysToDelete = Array.from(this.cache.keys()).filter(key => 
      key.includes(clientId)
    )
    keysToDelete.forEach(key => this.cache.delete(key))
  }

  /**
   * Clear all cache
   */
  clearCache(): void {
    this.cache.clear()
  }
}

// Export singleton instance
export const clientDetailFrontendService = new ClientDetailFrontendService()

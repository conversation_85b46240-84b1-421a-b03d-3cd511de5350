import Stripe from 'stripe'
import { connectDB } from '@/lib/db'
import { Subscription } from '@/lib/models/subscription.model'
import { Payment } from '@/lib/models/payment.model'
import { Invoice } from '@/lib/models/invoice.model'
import { BillingPlan } from '@/lib/models/billing-plan.model'
import { Company } from '@/lib/models/company.model'

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set in environment variables')
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2024-06-20'
})

export class StripeService {
  // Create or get Stripe customer
  static async createOrGetCustomer(companyId: string) {
    await connectDB()
    
    const company = await Company.findById(companyId)
    if (!company) {
      throw new Error('Company not found')
    }

    // Check if customer already exists
    if (company.stripeCustomerId) {
      try {
        const customer = await stripe.customers.retrieve(company.stripeCustomerId)
        return customer as Stripe.Customer
      } catch (error) {
        // Customer doesn't exist in Stripe, create new one
        console.warn('Stripe customer not found, creating new one')
      }
    }

    // Create new Stripe customer
    const customer = await stripe.customers.create({
      email: company.email,
      name: company.name,
      metadata: {
        companyId: companyId,
        environment: process.env.NODE_ENV || 'development'
      }
    })

    // Update company with Stripe customer ID
    await Company.findByIdAndUpdate(companyId, {
      stripeCustomerId: customer.id
    })

    return customer
  }

  // Create Stripe product and prices for billing plan
  static async createPlanInStripe(planId: string) {
    await connectDB()
    
    const plan = await BillingPlan.findById(planId)
    if (!plan) {
      throw new Error('Billing plan not found')
    }

    // Create Stripe product
    const product = await stripe.products.create({
      name: plan.name,
      description: plan.description,
      metadata: {
        planId: planId,
        environment: process.env.NODE_ENV || 'development'
      }
    })

    // Create monthly price
    const monthlyPrice = await stripe.prices.create({
      product: product.id,
      unit_amount: Math.round(plan.price.monthly * 100), // Convert to cents
      currency: plan.price.currency.toLowerCase(),
      recurring: {
        interval: 'month'
      },
      metadata: {
        planId: planId,
        billingCycle: 'monthly'
      }
    })

    // Create yearly price
    const yearlyPrice = await stripe.prices.create({
      product: product.id,
      unit_amount: Math.round(plan.price.yearly * 100), // Convert to cents
      currency: plan.price.currency.toLowerCase(),
      recurring: {
        interval: 'year'
      },
      metadata: {
        planId: planId,
        billingCycle: 'yearly'
      }
    })

    // Update plan with Stripe IDs
    await BillingPlan.findByIdAndUpdate(planId, {
      stripeProductId: product.id,
      stripeMonthlyPriceId: monthlyPrice.id,
      stripeYearlyPriceId: yearlyPrice.id
    })

    return {
      product,
      monthlyPrice,
      yearlyPrice
    }
  }

  // Create Stripe subscription
  static async createSubscription(subscriptionId: string, paymentMethodId?: string) {
    await connectDB()
    
    const subscription = await Subscription.findById(subscriptionId)
      .populate('company')
      .populate('plan')
    
    if (!subscription) {
      throw new Error('Subscription not found')
    }

    const customer = await this.createOrGetCustomer(subscription.company._id)
    
    // Get or create Stripe price
    let priceId = subscription.billingCycle === 'monthly' 
      ? subscription.plan.stripeMonthlyPriceId 
      : subscription.plan.stripeYearlyPriceId

    if (!priceId) {
      const stripeData = await this.createPlanInStripe(subscription.plan._id)
      priceId = subscription.billingCycle === 'monthly' 
        ? stripeData.monthlyPrice.id 
        : stripeData.yearlyPrice.id
    }

    // Create Stripe subscription
    const stripeSubscriptionData: Stripe.SubscriptionCreateParams = {
      customer: customer.id,
      items: [{
        price: priceId
      }],
      metadata: {
        subscriptionId: subscriptionId,
        companyId: subscription.company._id.toString(),
        planId: subscription.plan._id.toString()
      },
      trial_period_days: subscription.trialEnd ? 
        Math.ceil((subscription.trialEnd.getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : 
        undefined
    }

    if (paymentMethodId) {
      stripeSubscriptionData.default_payment_method = paymentMethodId
    }

    const stripeSubscription = await stripe.subscriptions.create(stripeSubscriptionData)

    // Update subscription with Stripe data
    await Subscription.findByIdAndUpdate(subscriptionId, {
      stripeSubscriptionId: stripeSubscription.id,
      stripeCustomerId: customer.id,
      status: stripeSubscription.status === 'trialing' ? 'trialing' : 'active'
    })

    return stripeSubscription
  }

  // Create payment intent
  static async createPaymentIntent(amount: number, currency: string, companyId: string, metadata?: any) {
    const customer = await this.createOrGetCustomer(companyId)

    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency: currency.toLowerCase(),
      customer: customer.id,
      metadata: {
        companyId,
        ...metadata
      },
      automatic_payment_methods: {
        enabled: true
      }
    })

    return paymentIntent
  }

  // Process refund
  static async createRefund(paymentId: string, amount?: number, reason?: string) {
    await connectDB()
    
    const payment = await Payment.findById(paymentId)
    if (!payment) {
      throw new Error('Payment not found')
    }

    if (!payment.stripeChargeId && !payment.stripePaymentId) {
      throw new Error('No Stripe charge ID found for this payment')
    }

    const refundData: Stripe.RefundCreateParams = {
      charge: payment.stripeChargeId,
      metadata: {
        paymentId: paymentId,
        reason: reason || 'requested_by_customer'
      }
    }

    if (amount) {
      refundData.amount = Math.round(amount * 100) // Convert to cents
    }

    const refund = await stripe.refunds.create(refundData)

    // Update payment record
    const refundAmount = amount || payment.amount
    await Payment.findByIdAndUpdate(paymentId, {
      status: amount && amount < payment.amount ? 'partially_refunded' : 'refunded',
      refundAmount: (payment.refundAmount || 0) + refundAmount,
      refundReason: reason,
      refundedAt: new Date()
    })

    return refund
  }

  // Cancel subscription
  static async cancelSubscription(subscriptionId: string, cancelAtPeriodEnd = true) {
    await connectDB()
    
    const subscription = await Subscription.findById(subscriptionId)
    if (!subscription) {
      throw new Error('Subscription not found')
    }

    if (!subscription.stripeSubscriptionId) {
      throw new Error('No Stripe subscription ID found')
    }

    const stripeSubscription = await stripe.subscriptions.update(
      subscription.stripeSubscriptionId,
      {
        cancel_at_period_end: cancelAtPeriodEnd,
        metadata: {
          canceledBy: 'admin',
          canceledAt: new Date().toISOString()
        }
      }
    )

    // Update subscription record
    await Subscription.findByIdAndUpdate(subscriptionId, {
      cancelAtPeriodEnd,
      canceledAt: cancelAtPeriodEnd ? undefined : new Date(),
      status: cancelAtPeriodEnd ? subscription.status : 'canceled'
    })

    return stripeSubscription
  }

  // Update subscription
  static async updateSubscription(subscriptionId: string, updates: any) {
    await connectDB()
    
    const subscription = await Subscription.findById(subscriptionId)
    if (!subscription) {
      throw new Error('Subscription not found')
    }

    if (!subscription.stripeSubscriptionId) {
      throw new Error('No Stripe subscription ID found')
    }

    const stripeSubscription = await stripe.subscriptions.update(
      subscription.stripeSubscriptionId,
      updates
    )

    return stripeSubscription
  }

  // Get subscription from Stripe
  static async getStripeSubscription(stripeSubscriptionId: string) {
    return await stripe.subscriptions.retrieve(stripeSubscriptionId)
  }

  // Get customer from Stripe
  static async getStripeCustomer(stripeCustomerId: string) {
    return await stripe.customers.retrieve(stripeCustomerId)
  }

  // List payment methods for customer
  static async getCustomerPaymentMethods(stripeCustomerId: string) {
    return await stripe.paymentMethods.list({
      customer: stripeCustomerId,
      type: 'card'
    })
  }

  // Create setup intent for saving payment method
  static async createSetupIntent(stripeCustomerId: string) {
    return await stripe.setupIntents.create({
      customer: stripeCustomerId,
      payment_method_types: ['card'],
      usage: 'off_session'
    })
  }
}

export default StripeService

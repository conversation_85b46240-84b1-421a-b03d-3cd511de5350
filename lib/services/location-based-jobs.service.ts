// lib/services/location-based-jobs.service.ts
import { Job } from '@/lib/models/job.model'
import { Company } from '@/lib/models/company.model'

export interface LocationData {
  city: string
  region: string
  country: string
  continent: string
  coordinates?: {
    latitude: number
    longitude: number
  }
}

export interface LocationBasedJobQuery {
  userLocation?: LocationData
  locationLevel?: 'local' | 'regional' | 'national' | 'continental' | 'international'
  maxDistance?: number
  searchTerm?: string
  locationTerm?: string
  jobTypes?: string[]
  experienceLevels?: string[]
  salaryRange?: { min?: number; max?: number }
  workModels?: string[]
  categories?: string[]
  skills?: string[]
  companies?: string[]
  page?: number
  limit?: number
  sortBy?: 'relevance' | 'date' | 'salary' | 'distance'
  sortOrder?: 'asc' | 'desc'
  featuredOnly?: boolean
  urgentOnly?: boolean
  remoteOnly?: boolean
  recentOnly?: boolean
}

export interface JobWithDistance {
  job: any
  distance?: number
  relevanceScore?: number
}

class LocationBasedJobsService {
  /**
   * Calculate distance between two coordinates using Haversine formula
   */
  private calculateDistance(
    lat1: number, 
    lon1: number, 
    lat2: number, 
    lon2: number
  ): number {
    const R = 6371 // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1)
    const dLon = this.toRadians(lon2 - lon1)
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2)
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180)
  }

  /**
   * Build location filter based on location level
   */
  buildLocationFilter(query: LocationBasedJobQuery): any {
    const locationFilter: any = {}
    
    if (!query.userLocation) return locationFilter
    
    const { userLocation, locationLevel } = query
    
    switch (locationLevel) {
      case 'local':
        locationFilter.$or = [
          { 'location.city': { $regex: userLocation.city, $options: 'i' } },
          { 'location.remote': true }
        ]
        break
        
      case 'regional':
        locationFilter.$or = [
          { 'location.state': { $regex: userLocation.region, $options: 'i' } },
          { 'location.city': { $regex: userLocation.city, $options: 'i' } },
          { 'location.remote': true }
        ]
        break
        
      case 'national':
        locationFilter.$or = [
          { 'location.country': { $regex: userLocation.country, $options: 'i' } },
          { 'location.remote': true }
        ]
        break
        
      case 'continental':
        // For continental, we'd need continent data in job location
        // For now, use country-based filtering with broader scope
        locationFilter.$or = [
          { 'location.country': { $regex: userLocation.country, $options: 'i' } },
          { 'location.remote': true }
        ]
        break
        
      case 'international':
        locationFilter.$or = [
          { 'location.remote': true },
          { 'location.country': { $ne: userLocation.country } }
        ]
        break
    }
    
    return locationFilter
  }

  /**
   * Build search filter for text-based search
   */
  buildSearchFilter(query: LocationBasedJobQuery): any {
    const searchFilter: any = {}
    
    if (query.searchTerm) {
      const searchRegex = { $regex: query.searchTerm, $options: 'i' }
      searchFilter.$or = [
        { title: searchRegex },
        { description: searchRegex },
        { tags: { $in: [searchRegex] } },
        { category: searchRegex },
        { department: searchRegex }
      ]
    }
    
    if (query.locationTerm) {
      const locationRegex = { $regex: query.locationTerm, $options: 'i' }
      const locationConditions = [
        { 'location.city': locationRegex },
        { 'location.state': locationRegex },
        { 'location.country': locationRegex }
      ]
      
      if (searchFilter.$or) {
        searchFilter.$and = [
          { $or: searchFilter.$or },
          { $or: locationConditions }
        ]
        delete searchFilter.$or
      } else {
        searchFilter.$or = locationConditions
      }
    }
    
    return searchFilter
  }

  /**
   * Build additional filters (job type, salary, etc.)
   */
  buildAdditionalFilters(query: LocationBasedJobQuery): any {
    const filters: any = { isActive: true }
    
    // Job types
    if (query.jobTypes?.length) {
      filters.type = { $in: query.jobTypes }
    }
    
    // Experience levels
    if (query.experienceLevels?.length) {
      filters.level = { $in: query.experienceLevels }
    }
    
    // Work models
    if (query.workModels?.length) {
      const workModelConditions = []
      if (query.workModels.includes('remote')) workModelConditions.push({ 'location.remote': true })
      if (query.workModels.includes('hybrid')) workModelConditions.push({ 'location.hybrid': true })
      if (query.workModels.includes('onsite')) {
        workModelConditions.push({ 
          'location.remote': { $ne: true }, 
          'location.hybrid': { $ne: true } 
        })
      }
      if (workModelConditions.length) {
        filters.$or = workModelConditions
      }
    }
    
    // Categories
    if (query.categories?.length) {
      filters.category = { $in: query.categories }
    }
    
    // Skills
    if (query.skills?.length) {
      filters.tags = { $in: query.skills }
    }
    
    // Salary range
    if (query.salaryRange) {
      const salaryConditions: any = {}
      if (query.salaryRange.min) {
        salaryConditions['salary.min'] = { $gte: query.salaryRange.min }
      }
      if (query.salaryRange.max) {
        salaryConditions['salary.max'] = { $lte: query.salaryRange.max }
      }
      Object.assign(filters, salaryConditions)
    }
    
    // Special flags
    if (query.featuredOnly) filters.isFeatured = true
    if (query.urgentOnly) filters.isUrgent = true
    if (query.remoteOnly) filters['location.remote'] = true
    
    // Recent jobs (last 7 days)
    if (query.recentOnly) {
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
      filters.createdAt = { $gte: sevenDaysAgo }
    }
    
    return filters
  }

  /**
   * Build sort options
   */
  buildSortOptions(query: LocationBasedJobQuery): any {
    const { sortBy = 'relevance', sortOrder = 'desc' } = query
    const sortDirection = sortOrder === 'asc' ? 1 : -1
    
    switch (sortBy) {
      case 'date':
        return { createdAt: sortDirection }
      case 'salary':
        return { 'salary.max': sortDirection }
      case 'distance':
        return { distance: sortDirection }
      case 'relevance':
      default:
        return { 
          isFeatured: -1, 
          isUrgent: -1, 
          relevanceScore: -1,
          createdAt: -1,
          viewsCount: -1 
        }
    }
  }

  /**
   * Calculate relevance score for search results
   */
  calculateRelevanceScore(job: any, query: LocationBasedJobQuery): number {
    let score = 0
    
    if (!query.searchTerm) return score
    
    const searchTerm = query.searchTerm.toLowerCase()
    
    // Title match (highest priority)
    if (job.title?.toLowerCase().includes(searchTerm)) {
      score += 10
    }
    
    // Company name match
    if (job.company?.name?.toLowerCase().includes(searchTerm)) {
      score += 5
    }
    
    // Skills/tags match
    if (job.tags?.some((tag: string) => tag.toLowerCase().includes(searchTerm))) {
      score += 8
    }
    
    // Category match
    if (job.category?.toLowerCase().includes(searchTerm)) {
      score += 6
    }
    
    // Description match (lower priority)
    if (job.description?.toLowerCase().includes(searchTerm)) {
      score += 2
    }
    
    // Featured bonus
    if (job.isFeatured) score += 3
    
    // Urgent bonus
    if (job.isUrgent) score += 2
    
    // Recent posting bonus
    const daysSincePosted = job.createdAt ? 
      (Date.now() - new Date(job.createdAt).getTime()) / (1000 * 60 * 60 * 24) : 0
    if (daysSincePosted <= 7) score += 1
    
    return score
  }

  /**
   * Get location statistics for user location
   */
  async getLocationStats(userLocation?: LocationData): Promise<{
    local: number
    regional: number
    national: number
    continental: number
    international: number
  }> {
    if (!userLocation) {
      const total = await Job.countDocuments({ isActive: true })
      return { local: 0, regional: 0, national: 0, continental: 0, international: total }
    }
    
    const [local, regional, national, continental, international] = await Promise.all([
      Job.countDocuments({
        isActive: true,
        $or: [
          { 'location.city': { $regex: userLocation.city, $options: 'i' } },
          { 'location.remote': true }
        ]
      }),
      Job.countDocuments({
        isActive: true,
        $or: [
          { 'location.state': { $regex: userLocation.region, $options: 'i' } },
          { 'location.remote': true }
        ]
      }),
      Job.countDocuments({
        isActive: true,
        $or: [
          { 'location.country': { $regex: userLocation.country, $options: 'i' } },
          { 'location.remote': true }
        ]
      }),
      Job.countDocuments({
        isActive: true,
        $or: [
          { 'location.country': { $regex: userLocation.country, $options: 'i' } },
          { 'location.remote': true }
        ]
      }),
      Job.countDocuments({
        isActive: true,
        $or: [
          { 'location.remote': true },
          { 'location.country': { $ne: userLocation.country } }
        ]
      })
    ])
    
    return { local, regional, national, continental, international }
  }

  /**
   * Search jobs with location-based filtering
   */
  async searchLocationBasedJobs(query: LocationBasedJobQuery): Promise<{
    jobs: any[]
    total: number
    locationStats: any
  }> {
    // Build all filters
    const locationFilter = this.buildLocationFilter(query)
    const searchFilter = this.buildSearchFilter(query)
    const additionalFilters = this.buildAdditionalFilters(query)
    
    // Combine all filters
    const matchStage = {
      ...additionalFilters,
      ...locationFilter,
      ...searchFilter
    }
    
    // Build aggregation pipeline
    const pipeline: any[] = [
      { $match: matchStage },
      {
        $lookup: {
          from: 'companies',
          localField: 'companyId',
          foreignField: '_id',
          as: 'company'
        }
      },
      {
        $unwind: {
          path: '$company',
          preserveNullAndEmptyArrays: true
        }
      }
    ]
    
    // Add relevance scoring if search term provided
    if (query.searchTerm) {
      pipeline.push({
        $addFields: {
          relevanceScore: {
            $add: [
              { $cond: [
                { $regexMatch: { input: '$title', regex: query.searchTerm, options: 'i' } },
                10, 0
              ]},
              { $cond: [
                { $regexMatch: { input: '$company.name', regex: query.searchTerm, options: 'i' } },
                5, 0
              ]},
              { $cond: [
                { $in: [{ $regex: query.searchTerm, $options: 'i' }, '$tags'] },
                8, 0
              ]},
              { $cond: [
                { $regexMatch: { input: '$category', regex: query.searchTerm, options: 'i' } },
                6, 0
              ]},
              { $cond: ['$isFeatured', 3, 0] },
              { $cond: ['$isUrgent', 2, 0] }
            ]
          }
        }
      })
    }
    
    // Sort
    const sortOptions = this.buildSortOptions(query)
    pipeline.push({ $sort: sortOptions })
    
    // Pagination
    const page = query.page || 1
    const limit = query.limit || 10
    const skip = (page - 1) * limit
    
    pipeline.push({ $skip: skip })
    pipeline.push({ $limit: limit })
    
    // Execute query
    const [jobs, total] = await Promise.all([
      Job.aggregate(pipeline),
      Job.countDocuments(matchStage)
    ])
    
    // Get location stats
    const locationStats = await this.getLocationStats(query.userLocation)
    
    return { jobs, total, locationStats }
  }
}

export const locationBasedJobsService = new LocationBasedJobsService()
export default locationBasedJobsService

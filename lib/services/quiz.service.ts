// lib/services/quiz.service.ts
'use client'

export interface QuizCategory {
  id: string
  name: string
  slug: string
  description: string
  icon: string
  color: string
  skills: string[]
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  estimatedTime: number
  totalQuestions: number
  passingScore: number
  availableQuestions?: number
  isActive: boolean
}

export interface QuizQuestion {
  id: string
  questionNumber: number
  question: string
  options: string[]
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  points: number
  tags: string[]
}

export interface QuizSession {
  sessionId: string
  category: QuizCategory
  questions: QuizQuestion[]
  totalQuestions: number
  maxPoints: number
  timeLimit: number
}

export interface QuizAnswer {
  questionId: string
  selectedAnswer: number
  timeSpent: number
  isCorrect?: boolean
  points?: number
}

export interface QuizResult {
  attemptId: string
  score: number
  totalPoints: number
  maxPoints: number
  correctAnswers: number
  totalQuestions: number
  timeSpent: number
  skillLevel: {
    level: string
    color: string
    tier: string
  }
  passed: boolean
  passingScore: number
  earnedAwards: Award[]
  category: {
    id: string
    name: string
    icon: string
    difficulty: string
  }
  recommendations: {
    jobMatches: string
    nextSteps: string[]
  }
}

export interface Award {
  id: string
  name: string
  description: string
  badgeIcon: string
  badgeColor: string
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary'
  benefits: string[]
}

class QuizService {
  private baseUrl = '/api/v1'
  private currentSession: QuizSession | null = null

  /**
   * Get all available quiz categories
   */
  async getCategories(): Promise<QuizCategory[]> {
    try {
      const response = await fetch(`${this.baseUrl}/quiz/categories?includeStats=true`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch categories: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch categories')
      }

      return result.data.categories

    } catch (error) {
      console.error('Error fetching quiz categories:', error)
      
      // Return fallback categories
      return this.getFallbackCategories()
    }
  }

  /**
   * Start a new quiz session
   */
  async startQuiz(categoryId: string, sessionId?: string): Promise<QuizSession> {
    try {
      const response = await fetch(`${this.baseUrl}/quiz/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          categoryId,
          sessionId
        })
      })
      
      if (!response.ok) {
        throw new Error(`Failed to start quiz: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to start quiz')
      }

      this.currentSession = result.data
      return result.data

    } catch (error) {
      console.error('Error starting quiz:', error)
      throw error
    }
  }

  /**
   * Submit quiz answers and get results
   */
  async submitQuiz(
    sessionId: string,
    categoryId: string,
    answers: QuizAnswer[],
    timeSpent: number,
    userId?: string
  ): Promise<QuizResult> {
    try {
      const response = await fetch(`${this.baseUrl}/quiz/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId,
          categoryId,
          answers,
          timeSpent,
          userId
        })
      })
      
      if (!response.ok) {
        throw new Error(`Failed to submit quiz: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to submit quiz')
      }

      // Clear current session
      this.currentSession = null

      return result.data

    } catch (error) {
      console.error('Error submitting quiz:', error)
      throw error
    }
  }

  /**
   * Get current quiz session
   */
  getCurrentSession(): QuizSession | null {
    return this.currentSession
  }

  /**
   * Clear current session
   */
  clearSession(): void {
    this.currentSession = null
  }

  /**
   * Generate a unique session ID
   */
  generateSessionId(): string {
    return `quiz_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Get fallback categories when API is unavailable
   */
  private getFallbackCategories(): QuizCategory[] {
    return [
      {
        id: 'frontend',
        name: 'Frontend Development',
        slug: 'frontend-development',
        description: 'Test your knowledge of modern frontend technologies and frameworks.',
        icon: '💻',
        color: 'bg-blue-500',
        skills: ['React', 'Vue.js', 'Angular', 'TypeScript', 'CSS', 'HTML'],
        difficulty: 'intermediate',
        estimatedTime: 15,
        totalQuestions: 15,
        passingScore: 70,
        availableQuestions: 15,
        isActive: true
      },
      {
        id: 'backend',
        name: 'Backend Development',
        slug: 'backend-development',
        description: 'Assess your backend development and server-side programming skills.',
        icon: '⚙️',
        color: 'bg-green-500',
        skills: ['Node.js', 'Python', 'Java', 'Go', 'PostgreSQL', 'MongoDB'],
        difficulty: 'intermediate',
        estimatedTime: 20,
        totalQuestions: 15,
        passingScore: 70,
        availableQuestions: 15,
        isActive: true
      },
      {
        id: 'design',
        name: 'UI/UX Design',
        slug: 'ui-ux-design',
        description: 'Evaluate your design thinking and user experience expertise.',
        icon: '🎨',
        color: 'bg-purple-500',
        skills: ['Figma', 'Sketch', 'Adobe XD', 'Prototyping', 'User Research'],
        difficulty: 'intermediate',
        estimatedTime: 12,
        totalQuestions: 12,
        passingScore: 70,
        availableQuestions: 12,
        isActive: true
      },
      {
        id: 'data',
        name: 'Data Science',
        slug: 'data-science',
        description: 'Test your data analysis and machine learning knowledge.',
        icon: '📊',
        color: 'bg-orange-500',
        skills: ['Python', 'R', 'SQL', 'Machine Learning', 'Statistics'],
        difficulty: 'advanced',
        estimatedTime: 25,
        totalQuestions: 20,
        passingScore: 75,
        availableQuestions: 20,
        isActive: true
      }
    ]
  }

  /**
   * Format time for display
   */
  formatTime(seconds: number): string {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  /**
   * Get skill level badge color
   */
  getSkillLevelColor(tier: string): string {
    const colors = {
      expert: 'text-green-600 bg-green-100',
      advanced: 'text-blue-600 bg-blue-100',
      intermediate: 'text-orange-600 bg-orange-100',
      beginner: 'text-gray-600 bg-gray-100'
    }
    return colors[tier as keyof typeof colors] || colors.beginner
  }

  /**
   * Get award rarity color
   */
  getAwardRarityColor(rarity: string): string {
    const colors = {
      common: 'text-gray-600 bg-gray-100',
      uncommon: 'text-green-600 bg-green-100',
      rare: 'text-blue-600 bg-blue-100',
      epic: 'text-purple-600 bg-purple-100',
      legendary: 'text-yellow-600 bg-yellow-100'
    }
    return colors[rarity as keyof typeof colors] || colors.common
  }
}

// Create singleton instance
export const quizService = new QuizService()

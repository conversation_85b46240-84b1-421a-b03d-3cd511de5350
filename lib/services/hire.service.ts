// lib/services/hire.service.ts
import { HireRequest, IHireRequest } from '@/lib/models/hire-request.model'
import { Company } from '@/lib/models/company.model'
import { Client } from '@/lib/models/client.model'
import { User } from '@/lib/models/user.model'
import { 
  CreateHireRequest, 
  UpdateHireRequest, 
  HireSearchQuery, 
  HireSearchResult, 
  HireStats,
  HireStatus,
  HireFrontend
} from '@/types/hire.types'
// Remove these imports for now to fix compilation
// import { BaseService } from './base.service'
// import { errorService, ErrorCode } from './error.service'

export class HireService {
  /**
   * Create a new hire request
   */
  async createHireRequest(
    hireData: CreateHireRequest, 
    companyId: string, 
    createdBy: string
  ): Promise<HireFrontend> {
    try {
      // Basic validation
      if (!hireData.talentId || !hireData.hireType || !hireData.projectDetails || !hireData.contractTerms) {
        throw new Error('Missing required fields')
      }

      // Verify company exists
      const company = await Company.findById(companyId)
      if (!company) {
        throw new Error('Company not found')
      }

      // Verify talent exists
      const talent = await Client.findById(hireData.talentId).populate('user')
      if (!talent) {
        throw new Error('Talent not found')
      }

      // Check for existing active hire request
      const existingHireRequest = await HireRequest.findOne({
        companyId,
        talentId: hireData.talentId,
        status: { $in: ['pending', 'reviewing', 'negotiating', 'contract_sent'] },
        isActive: true
      })

      if (existingHireRequest) {
        throw new Error('An active hire request already exists for this talent')
      }

      // Validate project details
      this.validateProjectDetails(hireData.projectDetails)
      
      // Validate contract terms
      this.validateContractTerms(hireData.contractTerms)

      // Create hire request
      const hireRequest = new HireRequest({
        companyId,
        talentId: hireData.talentId,
        hireType: hireData.hireType,
        status: 'pending',
        projectDetails: {
          ...hireData.projectDetails,
          timeline: {
            ...hireData.projectDetails.timeline,
            startDate: new Date(hireData.projectDetails.timeline.startDate),
            endDate: hireData.projectDetails.timeline.endDate 
              ? new Date(hireData.projectDetails.timeline.endDate) 
              : undefined,
            milestones: hireData.projectDetails.timeline.milestones?.map(milestone => ({
              ...milestone,
              dueDate: new Date(milestone.dueDate)
            })) || []
          }
        },
        contractTerms: {
          ...hireData.contractTerms,
          duration: hireData.contractTerms.duration ? {
            ...hireData.contractTerms.duration,
            startDate: new Date(hireData.contractTerms.duration.startDate),
            endDate: hireData.contractTerms.duration.endDate 
              ? new Date(hireData.contractTerms.duration.endDate) 
              : undefined
          } : undefined
        },
        message: hireData.message,
        priority: hireData.priority || 'medium',
        source: 'talent_search',
        timeline: [{
          status: 'pending',
          date: new Date(),
          updatedBy: createdBy,
          updatedByRole: 'company',
          notes: 'Hire request created'
        }],
        expiresAt: hireData.responseDeadline ? new Date(hireData.responseDeadline) : undefined,
        responseDeadline: hireData.responseDeadline ? new Date(hireData.responseDeadline) : undefined,
        createdBy,
        lastUpdatedBy: createdBy
      })

      await hireRequest.save()

      // Populate for response
      await hireRequest.populate([
        { path: 'companyId', select: 'name logo industry' },
        { path: 'talentId', select: 'user headline currentTitle skills location' },
        { path: 'createdBy', select: 'profile.firstName profile.lastName role' },
        { path: 'lastUpdatedBy', select: 'profile.firstName profile.lastName role' }
      ])

      const result = this.transformToFrontend(hireRequest)
      console.log('Hire request created:', result.id)

      return result

    } catch (error) {
      console.error('Create hire request error:', error)
      throw error
    }
  }

  /**
   * Update hire request
   */
  async updateHireRequest(
    id: string,
    updateData: UpdateHireRequest,
    updatedBy: string,
    userRole: string
  ): Promise<HireFrontend> {
    try {
      // Basic validation
      if (!id || !updatedBy) {
        throw new Error('Missing required parameters')
      }

      const hireRequest = await HireRequest.findById(id)
      if (!hireRequest) {
        throw new Error('Hire request not found')
      }

      // Validate status transition if status is being updated
      if (updateData.status) {
        this.validateStatusTransition(hireRequest.status, updateData.status)
      }

      // Build update object
      const updates: any = {
        lastUpdatedBy: updatedBy,
        updatedAt: new Date()
      }

      // Update allowed fields
      const allowedUpdates = [
        'status', 'message', 'companyNotes', 'talentResponse', 
        'priority', 'responseDeadline'
      ]

      for (const field of allowedUpdates) {
        if (updateData[field as keyof UpdateHireRequest] !== undefined) {
          updates[field] = updateData[field as keyof UpdateHireRequest]
        }
      }

      // Handle date fields
      if (updateData.responseDeadline) {
        updates.responseDeadline = new Date(updateData.responseDeadline)
        updates.expiresAt = new Date(updateData.responseDeadline)
      }

      // Add timeline entry if status is changing
      if (updateData.status && updateData.status !== hireRequest.status) {
        const timelineEntry = {
          status: updateData.status,
          date: new Date(),
          updatedBy,
          updatedByRole: userRole === 'job_seeker' ? 'talent' : 'company',
          notes: updateData.companyNotes || updateData.talentResponse || `Status changed to ${updateData.status}`
        }
        
        updates.$push = { timeline: timelineEntry }
      }

      // Update hire request
      const updatedHireRequest = await HireRequest.findByIdAndUpdate(
        id,
        updates,
        { new: true, runValidators: true }
      )
        .populate('companyId', 'name logo industry')
        .populate('talentId', 'user headline currentTitle skills location')
        .populate('createdBy', 'profile.firstName profile.lastName role')
        .populate('lastUpdatedBy', 'profile.firstName profile.lastName role')

      if (!updatedHireRequest) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Hire request not found after update',
          'id'
        )
      }

      const result = this.transformToFrontend(updatedHireRequest)
      this.logOperation('updateHireRequest', { 
        hireRequestId: result.id, 
        status: updateData.status 
      })

      return result

    } catch (error) {
      this.logError('updateHireRequest', error, { id, status: updateData.status })
      throw error
    }
  }

  /**
   * Get hire request by ID
   */
  async getHireRequestById(id: string): Promise<HireFrontend> {
    try {
      this.validateObjectId(id, 'id')

      const hireRequest = await HireRequest.findById(id)
        .populate('companyId', 'name logo industry')
        .populate('talentId', 'user headline currentTitle skills location')
        .populate('createdBy', 'profile.firstName profile.lastName role')
        .populate('lastUpdatedBy', 'profile.firstName profile.lastName role')

      if (!hireRequest) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Hire request not found',
          'id'
        )
      }

      return this.transformToFrontend(hireRequest)

    } catch (error) {
      this.logError('getHireRequestById', error, { id })
      throw error
    }
  }

  /**
   * Search hire requests
   */
  async searchHireRequests(query: HireSearchQuery, userContext: any): Promise<HireSearchResult> {
    try {
      // Build base query based on user permissions
      let baseQuery: any = { isActive: true }

      if (userContext.role === 'company_admin' || userContext.role === 'recruiter') {
        baseQuery.companyId = userContext.companyId
      } else if (userContext.role === 'job_seeker') {
        baseQuery.talentId = userContext.clientId
      }

      // Apply search filters
      if (query.companyId && userContext.role === 'admin') {
        baseQuery.companyId = query.companyId
      }
      if (query.talentId && ['admin', 'company_admin', 'recruiter'].includes(userContext.role)) {
        baseQuery.talentId = query.talentId
      }
      if (query.status) {
        baseQuery.status = Array.isArray(query.status) ? { $in: query.status } : query.status
      }
      if (query.hireType) {
        baseQuery.hireType = Array.isArray(query.hireType) ? { $in: query.hireType } : query.hireType
      }
      if (query.priority) {
        baseQuery.priority = query.priority
      }
      if (query.dateRange) {
        baseQuery.createdAt = {}
        if (query.dateRange.from) baseQuery.createdAt.$gte = new Date(query.dateRange.from)
        if (query.dateRange.to) baseQuery.createdAt.$lte = new Date(query.dateRange.to)
      }
      if (query.search) {
        baseQuery.$text = { $search: query.search }
      }

      // Pagination
      const page = query.page || 1
      const limit = query.limit || 20
      const skip = (page - 1) * limit

      // Sorting
      const sortOptions: any = {}
      const sortBy = query.sortBy || 'createdAt'
      const sortOrder = query.sortOrder || 'desc'
      sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1

      // Execute query
      const [hireRequests, total] = await Promise.all([
        HireRequest.find(baseQuery)
          .populate('companyId', 'name logo industry')
          .populate('talentId', 'user headline currentTitle skills location')
          .populate('createdBy', 'profile.firstName profile.lastName role')
          .populate('lastUpdatedBy', 'profile.firstName profile.lastName role')
          .sort(sortOptions)
          .skip(skip)
          .limit(limit)
          .lean(),
        HireRequest.countDocuments(baseQuery)
      ])

      // Get filter aggregations
      const filterAggregations = await this.getFilterAggregations(baseQuery)

      const result: HireSearchResult = {
        hireRequests: hireRequests.map(hr => this.transformToFrontend(hr)),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        },
        filters: filterAggregations
      }

      return result

    } catch (error) {
      this.logError('searchHireRequests', error, { query })
      throw error
    }
  }

  /**
   * Get hire statistics
   */
  async getHireStats(companyId?: string): Promise<HireStats> {
    try {
      let baseQuery: any = { isActive: true }
      if (companyId) {
        this.validateObjectId(companyId, 'companyId')
        baseQuery.companyId = companyId
      }

      const [
        totalCount,
        statusStats,
        typeStats,
        priorityStats,
        successRateData,
        timeToHireData,
        expiringSoonCount
      ] = await Promise.all([
        HireRequest.countDocuments(baseQuery),
        this.getStatusStats(baseQuery),
        this.getTypeStats(baseQuery),
        this.getPriorityStats(baseQuery),
        this.getSuccessRateData(baseQuery),
        this.getTimeToHireData(baseQuery),
        this.getExpiringSoonCount(baseQuery)
      ])

      const stats: HireStats = {
        total: totalCount,
        byStatus: statusStats,
        byType: typeStats,
        byPriority: priorityStats,
        successRate: successRateData.successRate,
        averageTimeToHire: timeToHireData.averageTimeToHire,
        activeRequests: successRateData.activeRequests,
        expiringSoon: expiringSoonCount
      }

      return stats

    } catch (error) {
      this.logError('getHireStats', error, { companyId })
      throw error
    }
  }

  // Private helper methods
  private validateProjectDetails(projectDetails: any): void {
    if (!projectDetails.title?.trim()) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        'Project title is required',
        'projectDetails.title'
      )
    }

    if (!projectDetails.description?.trim()) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        'Project description is required',
        'projectDetails.description'
      )
    }

    if (!projectDetails.timeline?.startDate) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        'Project start date is required',
        'projectDetails.timeline.startDate'
      )
    }
  }

  private validateContractTerms(contractTerms: any): void {
    if (!contractTerms.compensation?.amount || contractTerms.compensation.amount <= 0) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        'Valid compensation amount is required',
        'contractTerms.compensation.amount'
      )
    }

    if (!contractTerms.compensation?.frequency) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        'Compensation frequency is required',
        'contractTerms.compensation.frequency'
      )
    }
  }

  private validateStatusTransition(currentStatus: HireStatus, newStatus: HireStatus): void {
    const validTransitions: Record<HireStatus, HireStatus[]> = {
      pending: ['reviewing', 'rejected', 'cancelled'],
      reviewing: ['negotiating', 'contract_sent', 'rejected', 'cancelled'],
      negotiating: ['contract_sent', 'rejected', 'cancelled'],
      contract_sent: ['contract_signed', 'negotiating', 'rejected', 'cancelled'],
      contract_signed: ['hired', 'cancelled'],
      hired: [], // Final state
      rejected: [], // Final state
      cancelled: [], // Final state
      expired: [] // Final state
    }

    if (!validTransitions[currentStatus]?.includes(newStatus)) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        `Invalid status transition from ${currentStatus} to ${newStatus}`,
        'status'
      )
    }
  }

  private transformToFrontend(hireRequest: any): HireFrontend {
    return {
      id: hireRequest._id?.toString() || hireRequest.id,
      hireType: hireRequest.hireType,
      status: hireRequest.status,
      projectDetails: hireRequest.projectDetails,
      contractTerms: hireRequest.contractTerms,
      message: hireRequest.message,
      companyNotes: hireRequest.companyNotes,
      talentResponse: hireRequest.talentResponse,
      timeline: hireRequest.timeline,
      priority: hireRequest.priority,
      source: hireRequest.source,
      referenceId: hireRequest.referenceId,
      contractDocuments: hireRequest.contractDocuments || [],
      expiresAt: hireRequest.expiresAt,
      responseDeadline: hireRequest.responseDeadline,
      isActive: hireRequest.isActive,
      createdAt: hireRequest.createdAt,
      updatedAt: hireRequest.updatedAt,
      company: {
        id: hireRequest.companyId?._id?.toString() || hireRequest.companyId?.id || hireRequest.companyId,
        name: hireRequest.companyId?.name || 'Unknown Company',
        logo: hireRequest.companyId?.logo,
        industry: hireRequest.companyId?.industry
      },
      talent: {
        id: hireRequest.talentId?._id?.toString() || hireRequest.talentId?.id || hireRequest.talentId,
        name: hireRequest.talentId?.user?.profile?.firstName && hireRequest.talentId?.user?.profile?.lastName
          ? `${hireRequest.talentId.user.profile.firstName} ${hireRequest.talentId.user.profile.lastName}`
          : 'Unknown Talent',
        avatar: hireRequest.talentId?.user?.profile?.avatar,
        title: hireRequest.talentId?.currentTitle || hireRequest.talentId?.headline || 'Professional',
        location: this.formatLocation(hireRequest.talentId?.location),
        skills: hireRequest.talentId?.skills?.map((skill: any) => skill.name || skill) || []
      },
      createdBy: {
        id: hireRequest.createdBy?._id?.toString() || hireRequest.createdBy?.id || hireRequest.createdBy,
        name: hireRequest.createdBy?.profile?.firstName && hireRequest.createdBy?.profile?.lastName
          ? `${hireRequest.createdBy.profile.firstName} ${hireRequest.createdBy.profile.lastName}`
          : 'System User',
        role: hireRequest.createdBy?.role || 'user'
      },
      lastUpdatedBy: {
        id: hireRequest.lastUpdatedBy?._id?.toString() || hireRequest.lastUpdatedBy?.id || hireRequest.lastUpdatedBy,
        name: hireRequest.lastUpdatedBy?.profile?.firstName && hireRequest.lastUpdatedBy?.profile?.lastName
          ? `${hireRequest.lastUpdatedBy.profile.firstName} ${hireRequest.lastUpdatedBy.profile.lastName}`
          : 'System User',
        role: hireRequest.lastUpdatedBy?.role || 'user'
      }
    }
  }

  private formatLocation(location: any): string {
    if (!location) return 'Remote'
    if (typeof location === 'string') return location
    
    const parts = []
    if (location.city) parts.push(location.city)
    if (location.state) parts.push(location.state)
    if (location.country) parts.push(location.country)
    
    return parts.length > 0 ? parts.join(', ') : 'Remote'
  }

  // Additional helper methods for stats
  private async getStatusStats(baseQuery: any): Promise<Record<string, number>> {
    const stats = await HireRequest.aggregate([
      { $match: baseQuery },
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ])

    const result: Record<string, number> = {
      pending: 0, reviewing: 0, negotiating: 0, contract_sent: 0,
      contract_signed: 0, hired: 0, rejected: 0, cancelled: 0, expired: 0
    }

    stats.forEach((stat: any) => {
      result[stat._id] = stat.count
    })

    return result
  }

  private async getTypeStats(baseQuery: any): Promise<Record<string, number>> {
    const stats = await HireRequest.aggregate([
      { $match: baseQuery },
      { $group: { _id: '$hireType', count: { $sum: 1 } } }
    ])

    const result: Record<string, number> = {
      full_time: 0, part_time: 0, contract: 0, freelance: 0, internship: 0, temporary: 0
    }

    stats.forEach((stat: any) => {
      result[stat._id] = stat.count
    })

    return result
  }

  private async getPriorityStats(baseQuery: any): Promise<Record<string, number>> {
    const stats = await HireRequest.aggregate([
      { $match: baseQuery },
      { $group: { _id: '$priority', count: { $sum: 1 } } }
    ])

    const result: Record<string, number> = {
      low: 0, medium: 0, high: 0, urgent: 0
    }

    stats.forEach((stat: any) => {
      result[stat._id] = stat.count
    })

    return result
  }

  private async getSuccessRateData(baseQuery: any): Promise<{ successRate: number; activeRequests: number }> {
    const data = await HireRequest.aggregate([
      { $match: baseQuery },
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          hired: { $sum: { $cond: [{ $eq: ['$status', 'hired'] }, 1, 0] } },
          rejected: { $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] } },
          active: { 
            $sum: { 
              $cond: [
                { $in: ['$status', ['pending', 'reviewing', 'negotiating', 'contract_sent', 'contract_signed']] }, 
                1, 
                0
              ] 
            } 
          }
        }
      }
    ])

    const result = data[0] || { total: 0, hired: 0, rejected: 0, active: 0 }
    const completedRequests = result.hired + result.rejected
    const successRate = completedRequests > 0 ? Math.round((result.hired / completedRequests) * 100) : 0

    return {
      successRate,
      activeRequests: result.active
    }
  }

  private async getTimeToHireData(baseQuery: any): Promise<{ averageTimeToHire: number }> {
    const data = await HireRequest.aggregate([
      { $match: { ...baseQuery, status: 'hired' } },
      {
        $addFields: {
          timeToHire: {
            $divide: [
              { $subtract: ['$updatedAt', '$createdAt'] },
              1000 * 60 * 60 * 24 // Convert to days
            ]
          }
        }
      },
      {
        $group: {
          _id: null,
          averageTimeToHire: { $avg: '$timeToHire' }
        }
      }
    ])

    const result = data[0] || { averageTimeToHire: 0 }
    return {
      averageTimeToHire: Math.round(result.averageTimeToHire)
    }
  }

  private async getExpiringSoonCount(baseQuery: any): Promise<number> {
    return HireRequest.countDocuments({
      ...baseQuery,
      expiresAt: {
        $lte: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        $gte: new Date()
      },
      status: { $in: ['pending', 'reviewing', 'negotiating'] }
    })
  }

  private async getFilterAggregations(baseQuery: any): Promise<any> {
    const aggregations = await HireRequest.aggregate([
      { $match: baseQuery },
      {
        $group: {
          _id: null,
          statuses: { $push: '$status' },
          types: { $push: '$hireType' },
          priorities: { $push: '$priority' }
        }
      }
    ])

    const filters = aggregations[0] || { statuses: [], types: [], priorities: [] }
    
    return {
      statuses: this.countOccurrences(filters.statuses),
      types: this.countOccurrences(filters.types),
      priorities: this.countOccurrences(filters.priorities)
    }
  }

  private countOccurrences(array: string[]): Array<{ status?: string; type?: string; priority?: string; count: number }> {
    const counts = array.reduce((acc: any, item: string) => {
      acc[item] = (acc[item] || 0) + 1
      return acc
    }, {})

    return Object.entries(counts).map(([key, count]) => ({
      status: key,
      count: count as number
    }))
  }
}

// Export singleton instance
export const hireService = new HireService()

// lib/services/talent-frontend.service.ts
import { Client } from '@/stores/client.store'

// Frontend talent type that matches the expected structure in components
export interface TalentFrontend {
  id: string
  name: string
  headline: string
  summary: string
  avatar?: string
  currentTitle?: string
  location: string
  locationDetails?: {
    city: string
    state?: string
    country: string
    remote: boolean
    relocationWilling: boolean
  }
  experience: {
    level: string
    yearsOfExperience: number
    industries: string[]
    currentCompany?: string
  }
  skills: Array<{
    name: string
    level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
    yearsOfExperience?: number
  }>
  workHistory: Array<{
    company: string
    position: string
    duration: string
    isCurrent: boolean
    description?: string
    technologies?: string[]
  }>
  education: Array<{
    institution: string
    degree: string
    field: string
    graduationYear?: number
    gpa?: number
  }>
  certifications: Array<{
    name: string
    issuer: string
    issueDate: Date
    expiryDate?: Date
    credentialId?: string
  }>
  languages: Array<{
    language: string
    proficiency: string
  }>
  jobPreferences: {
    desiredRoles: string[]
    industries: string[]
    locations: Array<{
      city: string
      state?: string
      country: string
      remote: boolean
    }>
    salaryExpectation: {
      min: number
      max: number
      currency: string
      period: string
      negotiable: boolean
    }
    jobTypes: string[]
    workArrangement: string[]
    availability: string
  }
  portfolio: Array<{
    title: string
    description: string
    url?: string
    technologies: string[]
    type: string
  }>
  socialLinks: {
    linkedin?: string
    github?: string
    portfolio?: string
    website?: string
    twitter?: string
  }
  stats: {
    profileViews: number
    searchAppearances: number
    recruiterViews: number
    profileCompleteness: number
    responseRate?: number
  }
  applicationStats: {
    totalApplications: number
    pendingApplications: number
    interviewsReceived: number
    offersReceived: number
    successRate: number
  }
  verification: {
    emailVerified: boolean
    phoneVerified: boolean
    identityVerified: boolean
    backgroundCheckCompleted: boolean
  }
  availability: {
    status: 'available' | 'open_to_opportunities' | 'not_looking' | 'employed'
    availableStartDate?: string
    preferredContactMethod?: string
  }
  privacy: {
    profileVisibility: 'public' | 'private' | 'connections_only'
    showSalaryExpectation: boolean
    allowRecruiterContact: boolean
    showCurrentCompany: boolean
  }
  rating?: number
  featured: boolean
  premium: boolean
  lastActive: string
  joinedDate: string
  isActive: boolean
}

// Search and filter types
export interface TalentSearchQuery {
  query?: string
  location?: string
  skills?: string[]
  experience?: string
  industries?: string[]
  jobTypes?: string[]
  salaryRange?: {
    min?: number
    max?: number
    currency?: string
  }
  availability?: string[]
  remote?: boolean
  page?: number
  limit?: number
  sortBy?: 'relevance' | 'experience' | 'rating' | 'lastActive' | 'joinedDate'
  sortOrder?: 'asc' | 'desc'
}

export interface TalentSearchResult {
  talent: TalentFrontend[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  aggregations: {
    skills: Array<{ name: string; count: number }>
    industries: Array<{ name: string; count: number }>
    locations: Array<{ name: string; count: number }>
    experienceLevels: Array<{ level: string; count: number }>
    availability: Array<{ status: string; count: number }>
  }
  searchMeta: {
    query: string
    totalResults: number
    searchTime: number
  }
}

// Backend client interface (from database)
interface BackendClient {
  _id: string
  user: {
    _id: string
    email: string
    profile?: {
      firstName?: string
      lastName?: string
      avatar?: string
    }
  }
  headline: string
  summary: string
  currentTitle?: string
  experience: {
    level: string
    yearsOfExperience: number
    industries: string[]
    currentCompany?: string
    currentRole?: string
    currentSalary?: {
      amount: number
      currency: string
      period: string
    }
  }
  workHistory: Array<{
    company: string
    position: string
    startDate: Date
    endDate?: Date
    isCurrent: boolean
    description: string
    achievements: string[]
    technologies: string[]
    location: {
      city: string
      state?: string
      country: string
      remote: boolean
    }
  }>
  education: Array<{
    institution: string
    degree: string
    field: string
    startDate: Date
    endDate?: Date
    gpa?: number
    achievements: string[]
  }>
  skills: Array<{
    name: string
    level: string
    yearsOfExperience?: number
    endorsements: number
    lastUsed?: Date
  }>
  certifications: Array<{
    name: string
    issuer: string
    issueDate: Date
    expiryDate?: Date
    credentialId?: string
    verificationUrl?: string
  }>
  portfolio: Array<{
    title: string
    description: string
    url?: string
    imageUrl?: string
    technologies: string[]
    type: string
    completedAt: Date
  }>
  languages: Array<{
    language: string
    proficiency: string
    certified: boolean
  }>
  jobPreferences: {
    desiredRoles: string[]
    industries: string[]
    locations: Array<{
      city: string
      state?: string
      country: string
      remote: boolean
      relocationWilling: boolean
    }>
    salaryExpectation: {
      min: number
      max: number
      currency: string
      period: string
      negotiable: boolean
    }
    jobTypes: string[]
    workArrangement: string[]
    availability: string
    benefits: string[]
    companySize: string[]
  }
  socialLinks: {
    linkedin?: string
    github?: string
    portfolio?: string
    website?: string
    twitter?: string
    behance?: string
    dribbble?: string
  }
  privacy: {
    profileVisibility: string
    showSalaryExpectation: boolean
    showCurrentCompany: boolean
    allowRecruiterContact: boolean
    showProfileToCurrentEmployer: boolean
  }
  activity: {
    profileViews: number
    searchAppearances: number
    recruiterViews: number
    profileCompleteness: number
    lastProfileUpdate: Date
  }
  applicationStats: {
    totalApplications: number
    pendingApplications: number
    interviewsReceived: number
    offersReceived: number
    successRate: number
  }
  verification: {
    emailVerified: boolean
    phoneVerified: boolean
    identityVerified: boolean
    backgroundCheckCompleted: boolean
    references: Array<{
      name: string
      position: string
      company: string
      email: string
      phone?: string
      relationship: string
      verified: boolean
    }>
  }
  isActive: boolean
  isPublic: boolean
  lastLogin: Date
  createdAt: Date
  updatedAt: Date
}

export class TalentFrontendService {
  private baseUrl = '/api/v1/clients'
  private cache = new Map<string, { data: unknown; timestamp: number }>()
  private cacheExpiry = 5 * 60 * 1000 // 5 minutes

  // Helper method to format dates
  private formatDate(dateInput: string | Date | undefined): string {
    if (!dateInput) return new Date().toLocaleDateString()
    
    const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) return 'Today'
    if (diffDays === 1) return 'Yesterday'
    if (diffDays < 7) return `${diffDays} days ago`
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`
    
    return date.toLocaleDateString()
  }

  // Helper method to calculate work duration
  private calculateDuration(startDate: Date, endDate?: Date): string {
    const start = new Date(startDate)
    const end = endDate ? new Date(endDate) : new Date()
    
    const diffTime = end.getTime() - start.getTime()
    const diffMonths = Math.floor(diffTime / (1000 * 60 * 60 * 24 * 30))
    
    if (diffMonths < 1) return 'Less than 1 month'
    if (diffMonths < 12) return `${diffMonths} month${diffMonths > 1 ? 's' : ''}`
    
    const years = Math.floor(diffMonths / 12)
    const remainingMonths = diffMonths % 12
    
    if (remainingMonths === 0) return `${years} year${years > 1 ? 's' : ''}`
    return `${years} year${years > 1 ? 's' : ''} ${remainingMonths} month${remainingMonths > 1 ? 's' : ''}`
  }

  // Transform a backend client to frontend format
  transformTalent(backendClient: BackendClient): TalentFrontend {
    // Map experience level
    const experienceMap: Record<string, string> = {
      'entry': 'Entry Level',
      'junior': 'Junior Level',
      'mid': 'Mid Level',
      'senior': 'Senior Level',
      'executive': 'Executive',
      'lead': 'Lead Level'
    }

    // Format primary location
    const primaryLocation = backendClient.jobPreferences.locations[0]
    const locationDisplay = primaryLocation ? 
      `${primaryLocation.city}${primaryLocation.state ? `, ${primaryLocation.state}` : ''}, ${primaryLocation.country}` :
      'Location not specified'

    return {
      id: backendClient._id,
      name: `${backendClient.user.profile?.firstName || ''} ${backendClient.user.profile?.lastName || ''}`.trim() || 'Anonymous',
      headline: backendClient.headline,
      summary: backendClient.summary,
      avatar: backendClient.user.profile?.avatar,
      currentTitle: backendClient.currentTitle || backendClient.experience.currentRole,
      location: locationDisplay,
      locationDetails: primaryLocation ? {
        city: primaryLocation.city,
        state: primaryLocation.state,
        country: primaryLocation.country,
        remote: primaryLocation.remote,
        relocationWilling: primaryLocation.relocationWilling
      } : undefined,
      experience: {
        level: experienceMap[backendClient.experience.level] || backendClient.experience.level,
        yearsOfExperience: backendClient.experience.yearsOfExperience,
        industries: backendClient.experience.industries,
        currentCompany: backendClient.experience.currentCompany
      },
      skills: backendClient.skills.map(skill => ({
        name: skill.name,
        level: skill.level as 'beginner' | 'intermediate' | 'advanced' | 'expert',
        yearsOfExperience: skill.yearsOfExperience
      })),
      workHistory: backendClient.workHistory.map(work => ({
        company: work.company,
        position: work.position,
        duration: this.calculateDuration(work.startDate, work.endDate),
        isCurrent: work.isCurrent,
        description: work.description,
        technologies: work.technologies
      })),
      education: backendClient.education.map(edu => ({
        institution: edu.institution,
        degree: edu.degree,
        field: edu.field,
        graduationYear: edu.endDate ? new Date(edu.endDate).getFullYear() : undefined,
        gpa: edu.gpa
      })),
      certifications: backendClient.certifications,
      languages: backendClient.languages.map(lang => ({
        language: lang.language,
        proficiency: lang.proficiency
      })),
      jobPreferences: {
        desiredRoles: backendClient.jobPreferences.desiredRoles,
        industries: backendClient.jobPreferences.industries,
        locations: backendClient.jobPreferences.locations,
        salaryExpectation: {
          min: backendClient.jobPreferences.salaryExpectation.min,
          max: backendClient.jobPreferences.salaryExpectation.max,
          currency: backendClient.jobPreferences.salaryExpectation.currency,
          period: backendClient.jobPreferences.salaryExpectation.period,
          negotiable: backendClient.jobPreferences.salaryExpectation.negotiable
        },
        jobTypes: backendClient.jobPreferences.jobTypes,
        workArrangement: backendClient.jobPreferences.workArrangement,
        availability: backendClient.jobPreferences.availability
      },
      portfolio: backendClient.portfolio.map(item => ({
        title: item.title,
        description: item.description,
        url: item.url,
        technologies: item.technologies,
        type: item.type
      })),
      socialLinks: backendClient.socialLinks,
      stats: {
        profileViews: backendClient.activity.profileViews,
        searchAppearances: backendClient.activity.searchAppearances,
        recruiterViews: backendClient.activity.recruiterViews,
        profileCompleteness: backendClient.activity.profileCompleteness
      },
      applicationStats: backendClient.applicationStats,
      verification: {
        emailVerified: backendClient.verification.emailVerified,
        phoneVerified: backendClient.verification.phoneVerified,
        identityVerified: backendClient.verification.identityVerified,
        backgroundCheckCompleted: backendClient.verification.backgroundCheckCompleted
      },
      availability: {
        status: this.mapAvailabilityStatus(backendClient.jobPreferences.availability),
        availableStartDate: backendClient.jobPreferences.availability === 'immediately' ? 'Immediately' : undefined
      },
      privacy: {
        profileVisibility: backendClient.privacy.profileVisibility as 'public' | 'private' | 'connections_only',
        showSalaryExpectation: backendClient.privacy.showSalaryExpectation,
        allowRecruiterContact: backendClient.privacy.allowRecruiterContact,
        showCurrentCompany: backendClient.privacy.showCurrentCompany
      },
      rating: this.calculateTalentRating(backendClient),
      featured: false, // TODO: Add featured logic
      premium: false, // TODO: Add premium logic
      lastActive: this.formatDate(backendClient.lastLogin),
      joinedDate: this.formatDate(backendClient.createdAt),
      isActive: backendClient.isActive && backendClient.isPublic
    }
  }

  private mapAvailabilityStatus(availability: string): 'available' | 'open_to_opportunities' | 'not_looking' | 'employed' {
    switch (availability) {
      case 'immediately':
      case 'within_1_month':
        return 'available'
      case 'within_3_months':
      case 'within_6_months':
        return 'open_to_opportunities'
      case 'not_looking':
        return 'not_looking'
      default:
        return 'employed'
    }
  }

  private calculateTalentRating(client: BackendClient): number {
    let score = 3.0 // Base score
    
    // Profile completeness bonus
    if (client.activity.profileCompleteness > 80) score += 1.0
    else if (client.activity.profileCompleteness > 60) score += 0.5
    
    // Verification bonus
    if (client.verification.emailVerified) score += 0.2
    if (client.verification.phoneVerified) score += 0.2
    if (client.verification.identityVerified) score += 0.3
    if (client.verification.backgroundCheckCompleted) score += 0.3
    
    // Experience bonus
    if (client.experience.yearsOfExperience > 5) score += 0.5
    if (client.experience.yearsOfExperience > 10) score += 0.3
    
    // Activity bonus
    if (client.activity.profileViews > 100) score += 0.2
    
    return Math.min(5.0, Math.max(1.0, score))
  }

  // Cache management
  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data as T
    }
    return null
  }

  private setCache(key: string, data: unknown): void {
    this.cache.set(key, { data, timestamp: Date.now() })
  }

  private generateCacheKey(type: string, params: Record<string, unknown>): string {
    return `${type}_${JSON.stringify(params)}`
  }

  // API Methods
  async searchTalent(query: TalentSearchQuery): Promise<TalentSearchResult> {
    const cacheKey = this.generateCacheKey('search', query)
    const cached = this.getFromCache<TalentSearchResult>(cacheKey)
    if (cached) return cached

    const params = this.buildSearchParams(query)
    const response = await fetch(`${this.baseUrl}?${params}`)

    if (!response.ok) {
      throw new Error(`Search failed: ${response.statusText}`)
    }

    const data = await response.json()
    const apiData = data.success ? data.data : data
    const result = this.transformSearchResult(apiData)

    this.setCache(cacheKey, result)
    return result
  }

  async getTalentById(id: string): Promise<TalentFrontend> {
    const cacheKey = this.generateCacheKey('talent', { id })
    const cached = this.getFromCache<TalentFrontend>(cacheKey)
    if (cached) return cached

    const response = await fetch(`${this.baseUrl}/${id}`)

    if (!response.ok) {
      throw new Error(`Failed to fetch talent: ${response.statusText}`)
    }

    const data = await response.json()
    const talent = this.transformTalent(data.data || data.client)

    this.setCache(cacheKey, talent)
    return talent
  }

  private buildSearchParams(query: TalentSearchQuery): string {
    const params = new URLSearchParams()

    if (query.query) params.append('search', query.query)
    if (query.location) params.append('location', query.location)
    if (query.skills?.length) params.append('skills', query.skills.join(','))
    if (query.experience) params.append('experience', query.experience)
    if (query.industries?.length) params.append('industries', query.industries.join(','))
    if (query.jobTypes?.length) params.append('jobTypes', query.jobTypes.join(','))
    if (query.availability?.length) params.append('availability', query.availability.join(','))
    if (query.remote !== undefined) params.append('remote', query.remote.toString())
    if (query.salaryRange?.min) params.append('salaryMin', query.salaryRange.min.toString())
    if (query.salaryRange?.max) params.append('salaryMax', query.salaryRange.max.toString())
    if (query.salaryRange?.currency) params.append('salaryCurrency', query.salaryRange.currency)
    if (query.page) params.append('page', query.page.toString())
    if (query.limit) params.append('limit', query.limit.toString())
    if (query.sortBy) params.append('sortBy', query.sortBy)
    if (query.sortOrder) params.append('sortOrder', query.sortOrder)

    return params.toString()
  }

  private transformSearchResult(backendResult: {
    clients?: BackendClient[]
    total?: number
    page?: number
    limit?: number
    hasMore?: boolean
    pagination?: {
      page: number
      limit: number
      total: number
      pages?: number
      totalPages?: number
    }
    aggregations?: {
      skills?: Array<{ _id: string; count: number }>
      industries?: Array<{ _id: string; count: number }>
      locations?: Array<{ _id: string; count: number }>
      experienceLevels?: Array<{ _id: string; count: number }>
      availability?: Array<{ _id: string; count: number }>
    }
    searchMeta?: {
      query?: string
      totalResults?: number
      searchTime?: number
    }
  }): TalentSearchResult {
    // Handle both pagination formats - nested pagination object or flat structure
    const page = backendResult.pagination?.page || backendResult.page || 1
    const limit = backendResult.pagination?.limit || backendResult.limit || 20
    const total = backendResult.pagination?.total || backendResult.total || 0
    const totalPages = backendResult.pagination?.pages || backendResult.pagination?.totalPages || Math.ceil(total / limit)

    return {
      talent: (backendResult.clients || []).map((client: BackendClient) => this.transformTalent(client)),
      pagination: {
        page,
        limit,
        total,
        totalPages
      },
      aggregations: {
        skills: (backendResult.aggregations?.skills || []).map(item => ({
          name: item._id,
          count: item.count
        })),
        industries: (backendResult.aggregations?.industries || []).map(item => ({
          name: item._id,
          count: item.count
        })),
        locations: (backendResult.aggregations?.locations || []).map(item => ({
          name: item._id,
          count: item.count
        })),
        experienceLevels: (backendResult.aggregations?.experienceLevels || []).map(item => ({
          level: item._id,
          count: item.count
        })),
        availability: (backendResult.aggregations?.availability || []).map(item => ({
          status: item._id,
          count: item.count
        }))
      },
      searchMeta: {
        query: backendResult.searchMeta?.query || '',
        totalResults: backendResult.searchMeta?.totalResults || 0,
        searchTime: backendResult.searchMeta?.searchTime || 0
      }
    }
  }
}

// Export singleton instance
export const talentFrontendService = new TalentFrontendService()

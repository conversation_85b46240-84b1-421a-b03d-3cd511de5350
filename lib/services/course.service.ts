import { Types } from 'mongoose'
import { 
  Course, 
  CourseCategory, 
  CourseSection, 
  CourseLesson, 
  CourseEnrollment, 
  CourseProgress, 
  CourseReview,
  CourseCertificate,
  ICourse,
  ICourseCategory,
  ICourseEnrollment,
  ICourseProgress
} from '@/lib/models/course.model'

export interface CourseFilters {
  category?: string
  level?: string
  price?: 'free' | 'paid' | 'premium'
  rating?: number
  duration?: string
  language?: string
  search?: string
  tags?: string[]
  featured?: boolean
}

export interface CourseListOptions {
  page?: number
  limit?: number
  sort?: string
  order?: 'asc' | 'desc'
}

export class CourseService {
  private baseUrl: string

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
  }

  // Course Categories
  async getCategories(): Promise<ICourseCategory[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/courses/categories`)
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch course categories')
      }
      
      return result.data.categories
    } catch (error) {
      console.error('Error fetching course categories:', error)
      return this.getFallbackCategories()
    }
  }

  // Get Courses with Filters
  async getCourses(filters: CourseFilters = {}, options: CourseListOptions = {}): Promise<{
    courses: ICourse[]
    pagination: {
      page: number
      limit: number
      total: number
      pages: number
    }
  }> {
    try {
      const queryParams = new URLSearchParams()
      
      // Add filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            value.forEach(v => queryParams.append(key, v))
          } else {
            queryParams.append(key, value.toString())
          }
        }
      })
      
      // Add options
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString())
        }
      })

      const response = await fetch(`${this.baseUrl}/api/v1/courses?${queryParams}`)
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch courses')
      }
      
      return result.data
    } catch (error) {
      console.error('Error fetching courses:', error)
      return {
        courses: [],
        pagination: { page: 1, limit: 12, total: 0, pages: 0 }
      }
    }
  }

  // Get Single Course
  async getCourse(courseId: string): Promise<ICourse | null> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/courses/${courseId}`)
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch course')
      }
      
      return result.data.course
    } catch (error) {
      console.error('Error fetching course:', error)
      return null
    }
  }

  // Get Course Content (for enrolled users)
  async getCourseContent(courseId: string): Promise<{
    sections: any[]
    lessons: any[]
    enrollment?: ICourseEnrollment
    progress: ICourseProgress[]
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/courses/${courseId}/content`, {
        credentials: 'include'
      })
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch course content')
      }
      
      return result.data
    } catch (error) {
      console.error('Error fetching course content:', error)
      return { sections: [], lessons: [], progress: [] }
    }
  }

  // Enroll in Course
  async enrollInCourse(courseId: string, paymentData?: any): Promise<{
    success: boolean
    enrollmentId?: string
    paymentUrl?: string
    error?: string
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/courses/${courseId}/enroll`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(paymentData || {})
      })
      
      const result = await response.json()
      return result
    } catch (error) {
      console.error('Error enrolling in course:', error)
      return { success: false, error: 'Failed to enroll in course' }
    }
  }

  // Update Progress
  async updateProgress(courseId: string, lessonId: string, progressData: {
    isCompleted?: boolean
    timeSpent?: number
    lastPosition?: number
    notes?: string
  }): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/courses/${courseId}/progress`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({ lessonId, ...progressData })
      })
      
      const result = await response.json()
      return result.success
    } catch (error) {
      console.error('Error updating progress:', error)
      return false
    }
  }

  // Submit Review
  async submitReview(courseId: string, reviewData: {
    rating: number
    title: string
    comment: string
  }): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/courses/${courseId}/reviews`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(reviewData)
      })
      
      const result = await response.json()
      return result.success
    } catch (error) {
      console.error('Error submitting review:', error)
      return false
    }
  }

  // Get User Enrollments
  async getUserEnrollments(): Promise<ICourseEnrollment[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/courses/my-courses`, {
        credentials: 'include'
      })
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch enrollments')
      }
      
      return result.data.enrollments
    } catch (error) {
      console.error('Error fetching user enrollments:', error)
      return []
    }
  }

  // Get Certificate
  async getCertificate(courseId: string): Promise<string | null> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/courses/${courseId}/certificate`, {
        credentials: 'include'
      })
      
      if (response.ok) {
        const blob = await response.blob()
        return URL.createObjectURL(blob)
      }
      
      return null
    } catch (error) {
      console.error('Error fetching certificate:', error)
      return null
    }
  }

  // Search Courses
  async searchCourses(query: string, filters: CourseFilters = {}): Promise<ICourse[]> {
    try {
      const searchFilters = { ...filters, search: query }
      const result = await this.getCourses(searchFilters, { limit: 20 })
      return result.courses
    } catch (error) {
      console.error('Error searching courses:', error)
      return []
    }
  }

  // Get Featured Courses
  async getFeaturedCourses(limit: number = 6): Promise<ICourse[]> {
    try {
      const result = await this.getCourses({ featured: true }, { limit })
      return result.courses
    } catch (error) {
      console.error('Error fetching featured courses:', error)
      return []
    }
  }

  // Get Popular Courses
  async getPopularCourses(limit: number = 6): Promise<ICourse[]> {
    try {
      const result = await this.getCourses({}, { limit, sort: 'totalStudents', order: 'desc' })
      return result.courses
    } catch (error) {
      console.error('Error fetching popular courses:', error)
      return []
    }
  }

  // Get Course Reviews
  async getCourseReviews(courseId: string, page: number = 1, limit: number = 10): Promise<{
    reviews: any[]
    pagination: any
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/courses/${courseId}/reviews?page=${page}&limit=${limit}`)
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch reviews')
      }
      
      return result.data
    } catch (error) {
      console.error('Error fetching course reviews:', error)
      return { reviews: [], pagination: { page: 1, limit: 10, total: 0, pages: 0 } }
    }
  }

  // Fallback Categories
  private getFallbackCategories(): ICourseCategory[] {
    return [
      {
        _id: new Types.ObjectId(),
        name: 'Web Development',
        description: 'Learn modern web development technologies',
        icon: '💻',
        color: '#3B82F6',
        slug: 'web-development',
        isActive: true,
        sortOrder: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        _id: new Types.ObjectId(),
        name: 'Data Science',
        description: 'Master data analysis and machine learning',
        icon: '📊',
        color: '#10B981',
        slug: 'data-science',
        isActive: true,
        sortOrder: 2,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        _id: new Types.ObjectId(),
        name: 'Mobile Development',
        description: 'Build mobile apps for iOS and Android',
        icon: '📱',
        color: '#8B5CF6',
        slug: 'mobile-development',
        isActive: true,
        sortOrder: 3,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        _id: new Types.ObjectId(),
        name: 'Design',
        description: 'UI/UX design and graphic design',
        icon: '🎨',
        color: '#F59E0B',
        slug: 'design',
        isActive: true,
        sortOrder: 4,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ] as ICourseCategory[]
  }

  // Generate session ID for tracking
  generateSessionId(): string {
    return 'course_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }
}

// Export singleton instance
export const courseService = new CourseService()

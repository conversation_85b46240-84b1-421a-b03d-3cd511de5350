import { NextRequest, NextResponse } from 'next/server'

// Dynamic import for Redis to avoid build errors when ioredis is not installed
type Redis = any

// Cache configuration
interface CacheConfig {
  ttl: number // Time to live in seconds
  key: string | ((req: NextRequest) => string)
  condition?: (req: NextRequest) => boolean
  tags?: string[]
}

// In-memory cache fallback when Redis is not available
class MemoryCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()
  private maxSize = 1000

  set(key: string, value: any, ttl: number) {
    // Clean expired entries periodically
    if (this.cache.size >= this.maxSize) {
      this.cleanup()
    }

    this.cache.set(key, {
      data: value,
      timestamp: Date.now(),
      ttl: ttl * 1000 // Convert to milliseconds
    })
  }

  get(key: string): any | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }

    return entry.data
  }

  delete(key: string) {
    this.cache.delete(key)
  }

  clear() {
    this.cache.clear()
  }

  private cleanup() {
    const now = Date.now()
    const toDelete: string[] = []

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        toDelete.push(key)
      }
    }

    toDelete.forEach(key => this.cache.delete(key))

    // If still too large, remove oldest entries
    if (this.cache.size >= this.maxSize) {
      const entries = Array.from(this.cache.entries())
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp)
      
      const toRemove = entries.slice(0, Math.floor(this.maxSize * 0.2))
      toRemove.forEach(([key]) => this.cache.delete(key))
    }
  }
}

// Cache manager
class CacheManager {
  private redis: Redis | null = null
  private memoryCache = new MemoryCache()

  constructor() {
    this.initializeRedis()
  }

  private async initializeRedis() {
    try {
      if (process.env.REDIS_URL) {
        const { Redis } = await import('ioredis')
        this.redis = new Redis(process.env.REDIS_URL)
        console.log('Redis cache initialized')
      }
    } catch (error) {
      console.warn('Redis not available, using memory cache:', error)
    }
  }

  async get(key: string): Promise<any | null> {
    try {
      if (this.redis) {
        const value = await this.redis.get(key)
        return value ? JSON.parse(value) : null
      } else {
        return this.memoryCache.get(key)
      }
    } catch (error) {
      console.error('Cache get error:', error)
      return null
    }
  }

  async set(key: string, value: any, ttl: number): Promise<void> {
    try {
      if (this.redis) {
        await this.redis.setex(key, ttl, JSON.stringify(value))
      } else {
        this.memoryCache.set(key, value, ttl)
      }
    } catch (error) {
      console.error('Cache set error:', error)
    }
  }

  async delete(key: string): Promise<void> {
    try {
      if (this.redis) {
        await this.redis.del(key)
      } else {
        this.memoryCache.delete(key)
      }
    } catch (error) {
      console.error('Cache delete error:', error)
    }
  }

  async deleteByPattern(pattern: string): Promise<void> {
    try {
      if (this.redis) {
        const keys = await this.redis.keys(pattern)
        if (keys.length > 0) {
          await this.redis.del(...keys)
        }
      } else {
        // For memory cache, we'll need to iterate through keys
        // This is less efficient but works as a fallback
        this.memoryCache.clear() // Simple approach for memory cache
      }
    } catch (error) {
      console.error('Cache delete by pattern error:', error)
    }
  }

  async clear(): Promise<void> {
    try {
      if (this.redis) {
        await this.redis.flushall()
      } else {
        this.memoryCache.clear()
      }
    } catch (error) {
      console.error('Cache clear error:', error)
    }
  }
}

// Singleton cache manager
const cacheManager = new CacheManager()

// Cache middleware function
export function withCache(config: CacheConfig) {
  return function cacheMiddleware(
    handler: (req: NextRequest) => Promise<NextResponse>
  ) {
    return async function cachedHandler(req: NextRequest): Promise<NextResponse> {
      // Check if caching condition is met
      if (config.condition && !config.condition(req)) {
        return handler(req)
      }

      // Only cache GET requests
      if (req.method !== 'GET') {
        return handler(req)
      }

      // Generate cache key
      const cacheKey = typeof config.key === 'function' 
        ? config.key(req) 
        : config.key

      try {
        // Try to get from cache
        const cachedResponse = await cacheManager.get(cacheKey)
        if (cachedResponse) {
          console.log(`Cache hit for key: ${cacheKey}`)
          
          // Return cached response with cache headers
          const response = new NextResponse(JSON.stringify(cachedResponse.body), {
            status: cachedResponse.status,
            headers: {
              'Content-Type': 'application/json',
              'X-Cache': 'HIT',
              'Cache-Control': `public, max-age=${config.ttl}`,
              ...cachedResponse.headers
            }
          })
          
          return response
        }

        // Cache miss - execute handler
        console.log(`Cache miss for key: ${cacheKey}`)
        const response = await handler(req)

        // Only cache successful responses
        if (response.status >= 200 && response.status < 300) {
          const responseBody = await response.json()
          
          // Store in cache
          const cacheData = {
            body: responseBody,
            status: response.status,
            headers: Object.fromEntries(response.headers.entries())
          }
          
          await cacheManager.set(cacheKey, cacheData, config.ttl)
          
          // Return response with cache headers
          return new NextResponse(JSON.stringify(responseBody), {
            status: response.status,
            headers: {
              'Content-Type': 'application/json',
              'X-Cache': 'MISS',
              'Cache-Control': `public, max-age=${config.ttl}`,
              ...Object.fromEntries(response.headers.entries())
            }
          })
        }

        return response
      } catch (error) {
        console.error('Cache middleware error:', error)
        // Fallback to original handler if cache fails
        return handler(req)
      }
    }
  }
}

// Cache invalidation utilities
export async function invalidateCache(pattern: string) {
  await cacheManager.deleteByPattern(pattern)
}

export async function invalidateCacheByKey(key: string) {
  await cacheManager.delete(key)
}

export async function clearAllCache() {
  await cacheManager.clear()
}

// Predefined cache configurations
export const CacheConfigs = {
  // Short-term cache for frequently changing data
  SHORT: {
    ttl: 300, // 5 minutes
  },
  
  // Medium-term cache for moderately changing data
  MEDIUM: {
    ttl: 1800, // 30 minutes
  },
  
  // Long-term cache for rarely changing data
  LONG: {
    ttl: 3600, // 1 hour
  },
  
  // Extended cache for static-like data
  EXTENDED: {
    ttl: 86400, // 24 hours
  }
}

// Helper function to generate cache keys
export function generateCacheKey(prefix: string, params: Record<string, any>): string {
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}:${params[key]}`)
    .join('|')
  
  return `${prefix}:${sortedParams}`
}

// Cache key generators for common patterns
export const CacheKeys = {
  userProfile: (userId: string) => `user:profile:${userId}`,
  userApplications: (userId: string, page: number, limit: number) => 
    `user:applications:${userId}:${page}:${limit}`,
  jobSearch: (query: string, filters: Record<string, any>) => 
    generateCacheKey('jobs:search', { query, ...filters }),
  companyProfile: (companyId: string) => `company:profile:${companyId}`,
  jobDetails: (jobId: string) => `job:details:${jobId}`,
  analytics: (userId: string, timeframe: string, type: string) =>
    `analytics:${userId}:${timeframe}:${type}`
}

// React hook for client-side caching
export function useClientCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttl: number = 300000 // 5 minutes in milliseconds
) {
  const [data, setData] = React.useState<T | null>(null)
  const [loading, setLoading] = React.useState(true)
  const [error, setError] = React.useState<Error | null>(null)

  React.useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        setError(null)

        // Check localStorage cache
        const cached = localStorage.getItem(key)
        if (cached) {
          const { data: cachedData, timestamp } = JSON.parse(cached)
          if (Date.now() - timestamp < ttl) {
            setData(cachedData)
            setLoading(false)
            return
          }
        }

        // Fetch fresh data
        const freshData = await fetcher()
        
        // Cache in localStorage
        localStorage.setItem(key, JSON.stringify({
          data: freshData,
          timestamp: Date.now()
        }))

        setData(freshData)
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Unknown error'))
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [key, ttl])

  const invalidate = () => {
    localStorage.removeItem(key)
    setData(null)
    setLoading(true)
  }

  return { data, loading, error, invalidate }
}

export { cacheManager }

// React import for hooks
import React from 'react'

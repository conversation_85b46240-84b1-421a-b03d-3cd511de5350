import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDuration(minutes: number): string {
  if (minutes < 60) {
    return `${minutes}m`
  }

  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60

  if (remainingMinutes === 0) {
    return `${hours}h`
  }

  return `${hours}h ${remainingMinutes}m`
}

// Password strength validation
export interface PasswordStrength {
  score: number // 0-4 (0: very weak, 4: very strong)
  feedback: string[]
  isValid: boolean
}

export function validatePasswordStrength(password: string): PasswordStrength {
  const feedback: string[] = []
  let score = 0

  // Length check
  if (password.length >= 8) {
    score += 1
  } else {
    feedback.push("At least 8 characters")
  }

  // Uppercase check
  if (/[A-Z]/.test(password)) {
    score += 1
  } else {
    feedback.push("One uppercase letter")
  }

  // Lowercase check
  if (/[a-z]/.test(password)) {
    score += 1
  } else {
    feedback.push("One lowercase letter")
  }

  // Number check
  if (/\d/.test(password)) {
    score += 1
  } else {
    feedback.push("One number")
  }

  // Special character check
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    score += 1
  } else {
    feedback.push("One special character")
  }

  // Bonus points for length
  if (password.length >= 12) score = Math.min(score + 1, 4)

  return {
    score: Math.min(score, 4),
    feedback,
    isValid: score >= 3 && password.length >= 8
  }
}

// URL validation and normalization
export function normalizeWebsiteUrl(url: string): string {
  if (!url) return ""

  // Remove whitespace
  url = url.trim()

  // If it already has a protocol, return as is
  if (/^https?:\/\//.test(url)) {
    return url
  }

  // Add http:// if it doesn't have a protocol
  return `http://${url}`
}

export function validateWebsiteUrl(url: string): boolean {
  if (!url) return true // Optional field

  const normalizedUrl = normalizeWebsiteUrl(url)

  try {
    const urlObj = new URL(normalizedUrl)
    // Basic domain validation
    return /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/.test(urlObj.hostname)
  } catch {
    return false
  }
}

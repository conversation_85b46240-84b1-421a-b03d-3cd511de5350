// Performance monitoring and optimization utilities

interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  metadata?: Record<string, any>
}

interface CacheConfig {
  ttl: number // Time to live in milliseconds
  maxSize: number // Maximum number of items in cache
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()
  private observers: PerformanceObserver[] = []

  constructor() {
    this.initializeObservers()
  }

  // Initialize performance observers
  private initializeObservers() {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      // Observe navigation timing
      const navObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming
            this.recordMetric('page_load_time', navEntry.loadEventEnd - navEntry.fetchStart)
            this.recordMetric('dom_content_loaded', navEntry.domContentLoadedEventEnd - navEntry.fetchStart)
            this.recordMetric('first_contentful_paint', navEntry.loadEventEnd - navEntry.fetchStart)
          }
        }
      })

      try {
        navObserver.observe({ entryTypes: ['navigation'] })
        this.observers.push(navObserver)
      } catch (error) {
        console.warn('Navigation timing observer not supported')
      }

      // Observe resource timing
      const resourceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'resource') {
            const resourceEntry = entry as PerformanceResourceTiming
            this.recordMetric('resource_load_time', resourceEntry.responseEnd - resourceEntry.fetchStart, {
              resource: resourceEntry.name,
              type: resourceEntry.initiatorType
            })
          }
        }
      })

      try {
        resourceObserver.observe({ entryTypes: ['resource'] })
        this.observers.push(resourceObserver)
      } catch (error) {
        console.warn('Resource timing observer not supported')
      }

      // Observe largest contentful paint
      const lcpObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric('largest_contentful_paint', entry.startTime)
        }
      })

      try {
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        this.observers.push(lcpObserver)
      } catch (error) {
        console.warn('LCP observer not supported')
      }

      // Observe first input delay
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric('first_input_delay', entry.processingStart - entry.startTime)
        }
      })

      try {
        fidObserver.observe({ entryTypes: ['first-input'] })
        this.observers.push(fidObserver)
      } catch (error) {
        console.warn('FID observer not supported')
      }
    }
  }

  // Record a performance metric
  recordMetric(name: string, value: number, metadata?: Record<string, any>) {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      metadata
    }

    this.metrics.push(metric)

    // Keep only last 1000 metrics to prevent memory leaks
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000)
    }

    // Log performance issues
    this.checkPerformanceThresholds(metric)
  }

  // Check if metrics exceed performance thresholds
  private checkPerformanceThresholds(metric: PerformanceMetric) {
    const thresholds: Record<string, number> = {
      'page_load_time': 3000, // 3 seconds
      'largest_contentful_paint': 2500, // 2.5 seconds
      'first_input_delay': 100, // 100ms
      'api_response_time': 1000, // 1 second
      'component_render_time': 16 // 16ms (60fps)
    }

    const threshold = thresholds[metric.name]
    if (threshold && metric.value > threshold) {
      console.warn(`Performance threshold exceeded for ${metric.name}: ${metric.value}ms (threshold: ${threshold}ms)`, metric.metadata)
      
      // Send to analytics service in production
      if (process.env.NODE_ENV === 'production') {
        this.sendToAnalytics(metric)
      }
    }
  }

  // Send metrics to analytics service
  private async sendToAnalytics(metric: PerformanceMetric) {
    try {
      await fetch('/api/v1/analytics/performance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(metric)
      })
    } catch (error) {
      console.error('Failed to send performance metric to analytics:', error)
    }
  }

  // Get performance metrics
  getMetrics(name?: string): PerformanceMetric[] {
    if (name) {
      return this.metrics.filter(metric => metric.name === name)
    }
    return [...this.metrics]
  }

  // Get average metric value
  getAverageMetric(name: string, timeWindow?: number): number {
    const now = Date.now()
    const windowStart = timeWindow ? now - timeWindow : 0
    
    const relevantMetrics = this.metrics.filter(
      metric => metric.name === name && metric.timestamp >= windowStart
    )

    if (relevantMetrics.length === 0) return 0

    const sum = relevantMetrics.reduce((acc, metric) => acc + metric.value, 0)
    return sum / relevantMetrics.length
  }

  // Cache management
  setCache(key: string, data: any, config: CacheConfig = { ttl: 300000, maxSize: 100 }) {
    // Clean expired entries
    this.cleanExpiredCache()

    // Check cache size limit
    if (this.cache.size >= config.maxSize) {
      // Remove oldest entry
      const oldestKey = this.cache.keys().next().value
      this.cache.delete(oldestKey)
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: config.ttl
    })
  }

  getCache(key: string): any | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }

    return entry.data
  }

  clearCache(key?: string) {
    if (key) {
      this.cache.delete(key)
    } else {
      this.cache.clear()
    }
  }

  private cleanExpiredCache() {
    const now = Date.now()
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key)
      }
    }
  }

  // Cleanup observers
  cleanup() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.cache.clear()
    this.metrics = []
  }
}

// Singleton instance
const performanceMonitor = new PerformanceMonitor()

// Utility functions
export const measureAsync = async <T>(
  name: string,
  fn: () => Promise<T>,
  metadata?: Record<string, any>
): Promise<T> => {
  const start = performance.now()
  try {
    const result = await fn()
    const duration = performance.now() - start
    performanceMonitor.recordMetric(name, duration, metadata)
    return result
  } catch (error) {
    const duration = performance.now() - start
    performanceMonitor.recordMetric(name, duration, { ...metadata, error: true })
    throw error
  }
}

export const measureSync = <T>(
  name: string,
  fn: () => T,
  metadata?: Record<string, any>
): T => {
  const start = performance.now()
  try {
    const result = fn()
    const duration = performance.now() - start
    performanceMonitor.recordMetric(name, duration, metadata)
    return result
  } catch (error) {
    const duration = performance.now() - start
    performanceMonitor.recordMetric(name, duration, { ...metadata, error: true })
    throw error
  }
}

// Debounce utility for performance optimization
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

// Throttle utility for performance optimization
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean = false
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// Lazy loading utility
export const createLazyComponent = <T extends React.ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) => {
  const LazyComponent = React.lazy(importFunc)
  
  return (props: React.ComponentProps<T>) =>
    React.createElement(
      React.Suspense,
      {
        fallback: fallback
          ? React.createElement(fallback)
          : React.createElement('div', null, 'Loading...')
      },
      React.createElement(LazyComponent, props)
    )
}

// Image lazy loading with intersection observer
export const useLazyImage = (src: string, options?: IntersectionObserverInit) => {
  const [imageSrc, setImageSrc] = React.useState<string>('')
  const [isLoaded, setIsLoaded] = React.useState(false)
  const imgRef = React.useRef<HTMLImageElement>(null)

  React.useEffect(() => {
    if (!imgRef.current) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setImageSrc(src)
          observer.disconnect()
        }
      },
      options
    )

    observer.observe(imgRef.current)

    return () => observer.disconnect()
  }, [src, options])

  React.useEffect(() => {
    if (imageSrc) {
      const img = new Image()
      img.onload = () => setIsLoaded(true)
      img.src = imageSrc
    }
  }, [imageSrc])

  return { imgRef, imageSrc, isLoaded }
}

// Virtual scrolling hook for large lists
export const useVirtualScrolling = (
  items: any[],
  itemHeight: number,
  containerHeight: number
) => {
  const [scrollTop, setScrollTop] = React.useState(0)
  
  const startIndex = Math.floor(scrollTop / itemHeight)
  const endIndex = Math.min(
    startIndex + Math.ceil(containerHeight / itemHeight) + 1,
    items.length
  )
  
  const visibleItems = items.slice(startIndex, endIndex)
  const totalHeight = items.length * itemHeight
  const offsetY = startIndex * itemHeight

  return {
    visibleItems,
    totalHeight,
    offsetY,
    onScroll: (e: React.UIEvent<HTMLDivElement>) => {
      setScrollTop(e.currentTarget.scrollTop)
    }
  }
}

export {
  performanceMonitor,
  type PerformanceMetric,
  type CacheConfig
}

// React import for lazy loading utilities
import React from 'react'

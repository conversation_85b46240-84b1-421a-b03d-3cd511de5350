import crypto from 'crypto'
import bcrypt from 'bcryptjs'

// Dynamic import for express-rate-limit to avoid build errors when not installed
type RateLimitFunction = any

// Security configuration
export const SecurityConfig = {
  // Password requirements
  password: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    maxLength: 128
  },
  
  // Session configuration
  session: {
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    sameSite: 'strict' as const
  },
  
  // Rate limiting
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.'
  },
  
  // File upload security
  fileUpload: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    maxFiles: 10
  },
  
  // Data encryption
  encryption: {
    algorithm: 'aes-256-gcm',
    keyLength: 32,
    ivLength: 16,
    tagLength: 16
  }
}

// Password validation
export function validatePassword(password: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  const config = SecurityConfig.password

  if (password.length < config.minLength) {
    errors.push(`Password must be at least ${config.minLength} characters long`)
  }

  if (password.length > config.maxLength) {
    errors.push(`Password must not exceed ${config.maxLength} characters`)
  }

  if (config.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  }

  if (config.requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  }

  if (config.requireNumbers && !/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  }

  if (config.requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character')
  }

  // Check for common weak passwords
  const commonPasswords = [
    'password', '123456', '123456789', 'qwerty', 'abc123', 'password123',
    'admin', 'letmein', 'welcome', 'monkey', '1234567890'
  ]

  if (commonPasswords.includes(password.toLowerCase())) {
    errors.push('Password is too common and easily guessable')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Password hashing
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12
  return bcrypt.hash(password, saltRounds)
}

export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash)
}

// Data encryption/decryption
export function encryptData(data: string, key?: string): { encrypted: string; iv: string; tag: string } {
  const algorithm = SecurityConfig.encryption.algorithm
  const encryptionKey = key ? Buffer.from(key, 'hex') : crypto.randomBytes(SecurityConfig.encryption.keyLength)
  const iv = crypto.randomBytes(SecurityConfig.encryption.ivLength)
  
  const cipher = crypto.createCipher(algorithm, encryptionKey)
  cipher.setAAD(Buffer.from('additional-data'))
  
  let encrypted = cipher.update(data, 'utf8', 'hex')
  encrypted += cipher.final('hex')
  
  const tag = cipher.getAuthTag()
  
  return {
    encrypted,
    iv: iv.toString('hex'),
    tag: tag.toString('hex')
  }
}

export function decryptData(encryptedData: string, iv: string, tag: string, key: string): string {
  const algorithm = SecurityConfig.encryption.algorithm
  const encryptionKey = Buffer.from(key, 'hex')
  const ivBuffer = Buffer.from(iv, 'hex')
  const tagBuffer = Buffer.from(tag, 'hex')
  
  const decipher = crypto.createDecipher(algorithm, encryptionKey)
  decipher.setAAD(Buffer.from('additional-data'))
  decipher.setAuthTag(tagBuffer)
  
  let decrypted = decipher.update(encryptedData, 'hex', 'utf8')
  decrypted += decipher.final('utf8')
  
  return decrypted
}

// Input sanitization
export function sanitizeInput(input: string): string {
  if (typeof input !== 'string') return ''
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .slice(0, 1000) // Limit length
}

export function sanitizeEmail(email: string): string {
  if (typeof email !== 'string') return ''
  
  return email
    .trim()
    .toLowerCase()
    .replace(/[^\w@.-]/g, '') // Only allow word chars, @, ., and -
    .slice(0, 254) // RFC 5321 limit
}

export function sanitizeFilename(filename: string): string {
  if (typeof filename !== 'string') return ''
  
  return filename
    .trim()
    .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace special chars with underscore
    .replace(/_{2,}/g, '_') // Replace multiple underscores with single
    .slice(0, 255) // Filesystem limit
}

// File validation
export function validateFile(file: { name: string; type: string; size: number }): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  const config = SecurityConfig.fileUpload

  // Check file size
  if (file.size > config.maxSize) {
    errors.push(`File size exceeds maximum allowed size of ${config.maxSize / (1024 * 1024)}MB`)
  }

  // Check file type
  if (!config.allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed`)
  }

  // Check filename
  const sanitizedName = sanitizeFilename(file.name)
  if (sanitizedName !== file.name) {
    errors.push('Filename contains invalid characters')
  }

  // Check for executable extensions
  const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com', '.js', '.jar', '.vbs']
  const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
  if (dangerousExtensions.includes(fileExtension)) {
    errors.push('File type is not allowed for security reasons')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Generate secure tokens
export function generateSecureToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex')
}

export function generateCSRFToken(): string {
  return crypto.randomBytes(32).toString('base64')
}

// Rate limiting middleware
export const createRateLimit = async (options?: Partial<typeof SecurityConfig.rateLimit>) => {
  try {
    const { rateLimit } = await import('express-rate-limit')
    return rateLimit({
      ...SecurityConfig.rateLimit,
      ...options,
      standardHeaders: true,
      legacyHeaders: false,
    })
  } catch (error) {
    console.warn('express-rate-limit not available, rate limiting disabled:', error)
    // Return a no-op middleware when express-rate-limit is not available
    return (req: any, res: any, next: any) => next()
  }
}

// Security headers
export function getSecurityHeaders(): Record<string, string> {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'none';"
  }
}

// Privacy utilities
export function anonymizeData(data: any, fields: string[]): any {
  const anonymized = { ...data }
  
  fields.forEach(field => {
    if (anonymized[field]) {
      if (typeof anonymized[field] === 'string') {
        if (field.includes('email')) {
          anonymized[field] = anonymizeEmail(anonymized[field])
        } else if (field.includes('phone')) {
          anonymized[field] = anonymizePhone(anonymized[field])
        } else {
          anonymized[field] = '[REDACTED]'
        }
      } else {
        anonymized[field] = '[REDACTED]'
      }
    }
  })
  
  return anonymized
}

function anonymizeEmail(email: string): string {
  const [local, domain] = email.split('@')
  if (!local || !domain) return '[REDACTED]'
  
  const anonymizedLocal = local.length > 2 
    ? local[0] + '*'.repeat(local.length - 2) + local[local.length - 1]
    : '*'.repeat(local.length)
  
  return `${anonymizedLocal}@${domain}`
}

function anonymizePhone(phone: string): string {
  const digits = phone.replace(/\D/g, '')
  if (digits.length < 4) return '[REDACTED]'
  
  return '*'.repeat(digits.length - 4) + digits.slice(-4)
}

// Data retention utilities
export function shouldRetainData(createdAt: Date, retentionPeriodDays: number): boolean {
  const retentionPeriodMs = retentionPeriodDays * 24 * 60 * 60 * 1000
  const now = new Date().getTime()
  const dataAge = now - createdAt.getTime()
  
  return dataAge < retentionPeriodMs
}

export function getDataRetentionPolicy() {
  return {
    userProfiles: 2555, // 7 years
    applications: 1095, // 3 years
    searchHistory: 365, // 1 year
    messages: 1095, // 3 years
    analytics: 730, // 2 years
    logs: 90, // 3 months
    sessions: 30 // 30 days
  }
}

// Audit logging
export interface AuditLog {
  userId: string
  action: string
  resource: string
  resourceId?: string
  ipAddress: string
  userAgent: string
  timestamp: Date
  success: boolean
  details?: any
}

export function createAuditLog(
  userId: string,
  action: string,
  resource: string,
  ipAddress: string,
  userAgent: string,
  success: boolean,
  resourceId?: string,
  details?: any
): AuditLog {
  return {
    userId,
    action,
    resource,
    resourceId,
    ipAddress,
    userAgent,
    timestamp: new Date(),
    success,
    details
  }
}

// Security monitoring
export function detectSuspiciousActivity(logs: AuditLog[]): { suspicious: boolean; reasons: string[] } {
  const reasons: string[] = []
  
  // Check for rapid successive failed logins
  const recentFailedLogins = logs.filter(log => 
    log.action === 'login' && 
    !log.success && 
    Date.now() - log.timestamp.getTime() < 15 * 60 * 1000 // Last 15 minutes
  )
  
  if (recentFailedLogins.length >= 5) {
    reasons.push('Multiple failed login attempts')
  }
  
  // Check for unusual access patterns
  const uniqueIPs = new Set(logs.map(log => log.ipAddress))
  if (uniqueIPs.size > 10) {
    reasons.push('Access from multiple IP addresses')
  }
  
  // Check for data export activities
  const dataExports = logs.filter(log => log.action === 'data_export')
  if (dataExports.length > 3) {
    reasons.push('Multiple data export requests')
  }
  
  return {
    suspicious: reasons.length > 0,
    reasons
  }
}

// GDPR compliance utilities
export function generateDataPortabilityReport(userData: any): any {
  return {
    exportDate: new Date().toISOString(),
    dataSubject: userData.user?.email || 'Unknown',
    legalBasis: 'Article 20 - Right to data portability',
    dataCategories: {
      personalData: {
        profile: userData.profile || {},
        preferences: userData.preferences || {}
      },
      activityData: {
        applications: userData.applications || [],
        searchHistory: userData.searchHistory || [],
        messages: userData.messages || {}
      },
      technicalData: {
        loginHistory: userData.loginHistory || [],
        deviceInfo: userData.deviceInfo || {}
      }
    },
    retentionPeriods: getDataRetentionPolicy(),
    rightsInformation: {
      rightToAccess: 'You can request access to your personal data',
      rightToRectification: 'You can request correction of inaccurate data',
      rightToErasure: 'You can request deletion of your data',
      rightToPortability: 'You can request a copy of your data',
      rightToObject: 'You can object to processing of your data'
    }
  }
}

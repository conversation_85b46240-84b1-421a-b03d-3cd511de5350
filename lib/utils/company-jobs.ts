import mongoose from 'mongoose'
import { Job } from '@/lib/models/job.model'
import { Company } from '@/lib/models/company.model'

/**
 * Utility functions for company-job operations
 * Based on the data structure where jobs have companyId field referencing company._id
 */

export interface JobFilters {
  status?: string
  type?: string
  location?: string
  category?: string
  isRemote?: boolean
  level?: string
}

export interface JobSortOptions {
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginationOptions {
  page?: number
  limit?: number
}

export interface CompanyJobStats {
  totalJobs: number
  activeJobs: number
  draftJobs: number
  closedJobs: number
  totalApplications: number
  recentJobs: number
  jobGrowth: string
  followers: number
  profileViews: number
  jobsByType: Array<{ _id: string; count: number }>
  jobsByLocation: Array<{ _id: string; count: number }>
  jobsByCategory: Array<{ _id: string; count: number; name?: string }>
}

/**
 * Find company by ID or slug
 */
export async function findCompany(identifier: string) {
  // Try to find by ObjectId first
  if (mongoose.Types.ObjectId.isValid(identifier)) {
    const company = await Company.findById(identifier)
    if (company) return company
  }
  
  // Try to find by slug
  const company = await Company.findOne({ slug: identifier })
  return company
}

/**
 * Count jobs for a company with optional filters
 */
export async function countCompanyJobs(
  companyId: mongoose.Types.ObjectId, 
  filters: JobFilters = {}
): Promise<number> {
  const query: any = { companyId }
  
  // Apply filters
  if (filters.status) query.status = filters.status
  if (filters.type) query.type = filters.type
  if (filters.category) query.category = filters.category
  if (filters.level) query.level = filters.level
  if (filters.location) query['location.city'] = new RegExp(filters.location, 'i')
  if (typeof filters.isRemote === 'boolean') query.isRemote = filters.isRemote
  
  return await Job.countDocuments(query)
}

/**
 * Get jobs for a company with pagination and filters
 */
export async function getCompanyJobs(
  companyId: mongoose.Types.ObjectId,
  options: PaginationOptions & JobFilters & JobSortOptions = {}
) {
  const {
    page = 1,
    limit = 10,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    ...filters
  } = options

  // Build query
  const query: any = { companyId }
  
  // Apply filters
  if (filters.status) query.status = filters.status
  if (filters.type) query.type = filters.type
  if (filters.category) query.category = filters.category
  if (filters.level) query.level = filters.level
  if (filters.location) query['location.city'] = new RegExp(filters.location, 'i')
  if (typeof filters.isRemote === 'boolean') query.isRemote = filters.isRemote
  
  // Build sort
  const sort: any = {}
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1
  
  // Calculate skip
  const skip = (page - 1) * limit
  
  // Execute queries
  const [jobs, total] = await Promise.all([
    Job.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean(),
    Job.countDocuments(query)
  ])
  
  return { jobs, total }
}

/**
 * Get comprehensive job statistics for a company
 */
export async function getCompanyJobStats(
  companyId: mongoose.Types.ObjectId
): Promise<CompanyJobStats> {
  // Get basic job counts
  const [
    activeJobs,
    totalJobs,
    draftJobs,
    closedJobs
  ] = await Promise.all([
    Job.countDocuments({ companyId, status: 'active' }),
    Job.countDocuments({ companyId }),
    Job.countDocuments({ companyId, status: 'draft' }),
    Job.countDocuments({ companyId, status: 'closed' })
  ])

  // Get recent job postings (last 30 days)
  const thirtyDaysAgo = new Date()
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
  
  const recentJobs = await Job.countDocuments({
    companyId,
    createdAt: { $gte: thirtyDaysAgo }
  })

  // Get job distribution by type
  const jobsByType = await Job.aggregate([
    { $match: { companyId, status: 'active' } },
    { $group: { _id: '$type', count: { $sum: 1 } } },
    { $sort: { count: -1 } }
  ])

  // Get job distribution by location
  const jobsByLocation = await Job.aggregate([
    { $match: { companyId, status: 'active' } },
    { $group: { _id: '$location.city', count: { $sum: 1 } } },
    { $sort: { count: -1 } },
    { $limit: 5 }
  ])

  // Get job distribution by category
  const jobsByCategory = await Job.aggregate([
    { $match: { companyId, status: 'active' } },
    { $group: { _id: '$category', count: { $sum: 1 } } },
    { $sort: { count: -1 } },
    { $limit: 5 }
  ])

  // Calculate growth metrics
  const lastMonth = new Date()
  lastMonth.setMonth(lastMonth.getMonth() - 1)
  
  const jobsLastMonth = await Job.countDocuments({
    companyId,
    createdAt: { $gte: lastMonth, $lt: thirtyDaysAgo }
  })

  const jobGrowth = jobsLastMonth > 0 ? 
    ((recentJobs - jobsLastMonth) / jobsLastMonth * 100).toFixed(1) : 
    recentJobs > 0 ? '100.0' : '0.0'

  // Get company stats for additional metrics
  const company = await Company.findById(companyId)
  const companyStats = company?.stats || {}

  return {
    totalJobs,
    activeJobs,
    draftJobs,
    closedJobs,
    totalApplications: companyStats.totalApplications || 0,
    recentJobs,
    jobGrowth,
    followers: companyStats.followerCount || 0,
    profileViews: companyStats.profileViews || 0,
    jobsByType,
    jobsByLocation,
    jobsByCategory
  }
}

/**
 * Get active job count for a company (quick lookup)
 */
export async function getCompanyActiveJobCount(
  companyId: mongoose.Types.ObjectId
): Promise<number> {
  return await Job.countDocuments({ 
    companyId, 
    status: 'active' 
  })
}

/**
 * Get recent jobs for a company (for previews)
 */
export async function getCompanyRecentJobs(
  companyId: mongoose.Types.ObjectId,
  limit: number = 3
) {
  return await Job.find({ 
    companyId, 
    status: 'active' 
  })
  .sort({ createdAt: -1 })
  .limit(limit)
  .select('title location type createdAt')
  .lean()
}

/**
 * Update company job statistics
 * This can be called when jobs are created/updated/deleted
 */
export async function updateCompanyJobStats(companyId: mongoose.Types.ObjectId) {
  const [activeJobs, totalJobs] = await Promise.all([
    Job.countDocuments({ companyId, status: 'active' }),
    Job.countDocuments({ companyId })
  ])

  await Company.findByIdAndUpdate(companyId, {
    'stats.activeJobs': activeJobs,
    'stats.totalJobs': totalJobs
  })

  return { activeJobs, totalJobs }
}

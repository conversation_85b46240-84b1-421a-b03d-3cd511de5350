#!/usr/bin/env node

// Test script for database connection from lib/database/connection.ts
// This script tests the MongoDB connection and various database operations

const mongoose = require('mongoose')
const { GridFSBucket } = require('mongodb')
const fs = require('fs')
const path = require('path')

// Load environment variables manually
function loadEnvFile() {
  const envPath = path.join(__dirname, '..', '.env.local')
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8')
    envContent.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=')
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim()
        if (value.startsWith('"') && value.endsWith('"')) {
          process.env[key.trim()] = value.slice(1, -1)
        } else {
          process.env[key.trim()] = value
        }
      }
    })
  }
}

loadEnvFile()

// Replicate the connection logic from lib/database/connection.ts
const MONGODB_URI = process.env.MONGODB_URI

if (!MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable inside .env.local')
}

// Connection cache
let cached = { conn: null, promise: null, bucket: null }

async function connectToDatabase() {
  if (cached.conn && mongoose.connection.readyState === 1) {
    return cached.conn
  }

  if (!cached.promise) {
    const opts = {
      bufferCommands: false,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      family: 4
    }

    cached.promise = mongoose.connect(MONGODB_URI, opts).then(async (mongoose) => {
      await new Promise((resolve) => {
        if (mongoose.connection.readyState === 1) {
          resolve(true)
        } else {
          mongoose.connection.once('connected', resolve)
        }
      })
      console.log('✅ Connected to MongoDB')
      return mongoose
    })
  }

  try {
    cached.conn = await cached.promise
    if (mongoose.connection.readyState !== 1) {
      throw new Error('Database connection not ready')
    }
  } catch (e) {
    cached.promise = null
    console.error('❌ MongoDB connection error:', e)
    throw e
  }

  return cached.conn
}

async function getGridFSBucket() {
  if (cached.bucket) {
    return cached.bucket
  }

  const mongoose = await connectToDatabase()

  if (!mongoose.connection.db) {
    throw new Error('Database connection not established')
  }

  cached.bucket = new GridFSBucket(mongoose.connection.db, {
    bucketName: 'jobportal_files'
  })

  console.log('✅ GridFS bucket initialized')
  return cached.bucket
}

async function disconnectFromDatabase() {
  if (cached.conn) {
    await cached.conn.disconnect()
    cached.conn = null
    cached.promise = null
    cached.bucket = null
    console.log('✅ Disconnected from MongoDB')
  }
}

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function logSection(title) {
  console.log('\n' + '='.repeat(50))
  log(title, 'bold')
  console.log('='.repeat(50))
}

async function testDatabaseConnection() {
  logSection('🔍 DATABASE CONNECTION TEST')
  
  try {
    // Test 1: Basic Connection
    log('1. Testing basic database connection...', 'blue')
    const startTime = Date.now()
    
    const mongoose = await connectToDatabase()
    const connectionTime = Date.now() - startTime
    
    log(`✅ Connected successfully in ${connectionTime}ms`, 'green')
    log(`   Database: ${mongoose.connection.name}`, 'yellow')
    log(`   Host: ${mongoose.connection.host}:${mongoose.connection.port}`, 'yellow')
    log(`   Ready State: ${mongoose.connection.readyState} (1 = connected)`, 'yellow')
    
    // Test 2: Connection State Verification
    log('\n2. Verifying connection state...', 'blue')
    if (mongoose.connection.readyState === 1) {
      log('✅ Connection state is healthy', 'green')
    } else {
      log('❌ Connection state is not healthy', 'red')
      return false
    }
    
    // Test 3: Database Operations Test
    log('\n3. Testing basic database operations...', 'blue')
    
    // Test collection access
    const collections = await mongoose.connection.db.listCollections().toArray()
    log(`✅ Found ${collections.length} collections`, 'green')
    
    // List some collections
    const collectionNames = collections.map(c => c.name).slice(0, 5)
    if (collectionNames.length > 0) {
      log(`   Collections: ${collectionNames.join(', ')}${collections.length > 5 ? '...' : ''}`, 'yellow')
    }
    
    // Test 4: GridFS Bucket Test
    log('\n4. Testing GridFS bucket initialization...', 'blue')
    try {
      const bucket = await getGridFSBucket()
      log('✅ GridFS bucket initialized successfully', 'green')
      log(`   Bucket name: ${bucket.bucketName}`, 'yellow')
    } catch (error) {
      log(`❌ GridFS bucket initialization failed: ${error.message}`, 'red')
    }
    
    // Test 5: Quiz Collections Test
    log('\n5. Testing quiz-related collections...', 'blue')
    try {
      const quizCategories = await mongoose.connection.db.collection('quizcategories').countDocuments()
      const quizQuestions = await mongoose.connection.db.collection('quizquestions').countDocuments()
      const quizAttempts = await mongoose.connection.db.collection('quizattempts').countDocuments()
      
      log(`✅ Quiz Categories: ${quizCategories} documents`, 'green')
      log(`✅ Quiz Questions: ${quizQuestions} documents`, 'green')
      log(`✅ Quiz Attempts: ${quizAttempts} documents`, 'green')
      
      if (quizCategories === 0) {
        log('⚠️  No quiz categories found - you may need to seed data', 'yellow')
      }
      if (quizQuestions === 0) {
        log('⚠️  No quiz questions found - you may need to seed data', 'yellow')
      }
      
    } catch (error) {
      log(`❌ Quiz collections test failed: ${error.message}`, 'red')
    }
    
    // Test 6: Performance Test
    log('\n6. Testing connection performance...', 'blue')
    const perfStart = Date.now()
    
    // Test multiple rapid connections
    for (let i = 0; i < 5; i++) {
      await connectToDatabase()
    }
    
    const perfTime = Date.now() - perfStart
    log(`✅ 5 connection calls completed in ${perfTime}ms (avg: ${Math.round(perfTime/5)}ms)`, 'green')
    
    // Test 7: Connection Pool Test
    log('\n7. Testing connection pool...', 'blue')
    const poolSize = mongoose.connection.db.serverConfig?.s?.poolSize || 'unknown'
    log(`   Pool size: ${poolSize}`, 'yellow')
    log(`   Max pool size: ${mongoose.connection.options?.maxPoolSize || 'default'}`, 'yellow')
    
    return true
    
  } catch (error) {
    log(`❌ Database connection test failed: ${error.message}`, 'red')
    console.error('Full error:', error)
    return false
  }
}

async function testEnvironmentVariables() {
  logSection('🔧 ENVIRONMENT VARIABLES TEST')
  
  const requiredVars = ['MONGODB_URI']
  const optionalVars = ['MONGODB_DB_NAME']
  
  log('Required variables:', 'blue')
  for (const varName of requiredVars) {
    const value = process.env[varName]
    if (value) {
      // Mask sensitive parts of the URI
      const maskedValue = value.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@')
      log(`✅ ${varName}: ${maskedValue}`, 'green')
    } else {
      log(`❌ ${varName}: Not set`, 'red')
    }
  }
  
  log('\nOptional variables:', 'blue')
  for (const varName of optionalVars) {
    const value = process.env[varName]
    if (value) {
      log(`✅ ${varName}: ${value}`, 'green')
    } else {
      log(`⚠️  ${varName}: Not set (using default)`, 'yellow')
    }
  }
}

async function testConnectionResilience() {
  logSection('🔄 CONNECTION RESILIENCE TEST')
  
  try {
    log('1. Testing connection caching...', 'blue')
    
    // Test that multiple calls return the same connection
    const conn1 = await connectToDatabase()
    const conn2 = await connectToDatabase()
    
    if (conn1 === conn2) {
      log('✅ Connection caching works correctly', 'green')
    } else {
      log('❌ Connection caching not working', 'red')
    }
    
    log('\n2. Testing connection state monitoring...', 'blue')
    log(`   Current state: ${mongoose.connection.readyState}`, 'yellow')
    log(`   States: 0=disconnected, 1=connected, 2=connecting, 3=disconnecting`, 'yellow')
    
    return true
    
  } catch (error) {
    log(`❌ Connection resilience test failed: ${error.message}`, 'red')
    return false
  }
}

async function runAllTests() {
  console.log(`${colors.bold}${colors.blue}`)
  console.log('╔══════════════════════════════════════════════════════════════╗')
  console.log('║                    DATABASE CONNECTION TEST                  ║')
  console.log('║                     lib/database/connection.ts              ║')
  console.log('╚══════════════════════════════════════════════════════════════╝')
  console.log(`${colors.reset}`)
  
  const results = {
    environment: false,
    connection: false,
    resilience: false
  }
  
  try {
    // Test environment variables
    await testEnvironmentVariables()
    results.environment = true
    
    // Test database connection
    results.connection = await testDatabaseConnection()
    
    // Test connection resilience
    if (results.connection) {
      results.resilience = await testConnectionResilience()
    }
    
  } catch (error) {
    log(`❌ Test suite failed: ${error.message}`, 'red')
    console.error('Full error:', error)
  } finally {
    // Clean up
    try {
      await disconnectFromDatabase()
    } catch (error) {
      log(`⚠️  Cleanup warning: ${error.message}`, 'yellow')
    }
  }
  
  // Summary
  logSection('📊 TEST SUMMARY')
  
  const passed = Object.values(results).filter(Boolean).length
  const total = Object.keys(results).length
  
  log(`Environment Variables: ${results.environment ? '✅ PASS' : '❌ FAIL'}`, results.environment ? 'green' : 'red')
  log(`Database Connection: ${results.connection ? '✅ PASS' : '❌ FAIL'}`, results.connection ? 'green' : 'red')
  log(`Connection Resilience: ${results.resilience ? '✅ PASS' : '❌ FAIL'}`, results.resilience ? 'green' : 'red')
  
  console.log('\n' + '='.repeat(50))
  if (passed === total) {
    log(`🎉 ALL TESTS PASSED (${passed}/${total})`, 'green')
    log('Database connection is working perfectly!', 'green')
  } else {
    log(`⚠️  SOME TESTS FAILED (${passed}/${total})`, 'yellow')
    log('Please check the failed tests above.', 'yellow')
  }
  console.log('='.repeat(50))
  
  process.exit(passed === total ? 0 : 1)
}

// Handle uncaught errors
process.on('unhandledRejection', (error) => {
  log(`❌ Unhandled rejection: ${error.message}`, 'red')
  console.error('Full error:', error)
  process.exit(1)
})

process.on('uncaughtException', (error) => {
  log(`❌ Uncaught exception: ${error.message}`, 'red')
  console.error('Full error:', error)
  process.exit(1)
})

// Run the tests
if (require.main === module) {
  runAllTests()
}

module.exports = {
  testDatabaseConnection,
  testEnvironmentVariables,
  testConnectionResilience
}

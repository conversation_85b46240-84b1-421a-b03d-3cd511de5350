// Script to fix job application counts in the database
// This script updates jobs to have the correct application counts and references

const { MongoClient, ObjectId } = require('mongodb')

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/job-marketplace'

async function fixJobApplicationCounts() {
  const client = new MongoClient(MONGODB_URI)
  
  try {
    await client.connect()
    console.log('Connected to MongoDB')
    
    const db = client.db()
    const jobsCollection = db.collection('jobs')
    const applicationsCollection = db.collection('applications')
    
    // Get all jobs
    const jobs = await jobsCollection.find({}).toArray()
    console.log(`Found ${jobs.length} jobs`)
    
    for (const job of jobs) {
      // Count applications for this job
      const applicationCount = await applicationsCollection.countDocuments({
        job: job._id,
        isActive: true
      })
      
      // Get application IDs for this job
      const applications = await applicationsCollection.find({
        job: job._id,
        isActive: true
      }, { projection: { _id: 1 } }).toArray()
      
      const applicationIds = applications.map(app => app._id)
      
      // Update job with correct count and application references
      const updateResult = await jobsCollection.updateOne(
        { _id: job._id },
        {
          $set: {
            applicationsCount: applicationCount,
            applications: applicationIds
          }
        }
      )
      
      console.log(`Updated job ${job._id}: ${applicationCount} applications`)
    }
    
    console.log('Job application counts fixed successfully!')
    
  } catch (error) {
    console.error('Error fixing job application counts:', error)
  } finally {
    await client.close()
  }
}

// Run the script
fixJobApplicationCounts()

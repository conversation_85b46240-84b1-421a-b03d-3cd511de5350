// scripts/seed-quiz-data.js
const mongoose = require('mongoose')
const fs = require('fs')
const path = require('path')

// Read environment variables from .env.local
const loadEnvVars = () => {
  try {
    const envPath = path.join(__dirname, '..', '.env.local')
    const envContent = fs.readFileSync(envPath, 'utf8')
    const envVars = {}

    envContent.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=')
      if (key && valueParts.length > 0) {
        envVars[key.trim()] = valueParts.join('=').trim()
      }
    })

    return envVars
  } catch (error) {
    console.error('Error reading .env.local file:', error)
    return {}
  }
}

// MongoDB connection
const connectDB = async () => {
  try {
    const envVars = loadEnvVars()
    const mongoUri = envVars.MONGODB_URI

    if (!mongoUri) {
      throw new Error('MONGODB_URI not found in .env.local file')
    }

    console.log('Connecting to MongoDB...')
    await mongoose.connect(mongoUri)
    console.log('MongoDB connected successfully')
  } catch (error) {
    console.error('MongoDB connection error:', error)
    process.exit(1)
  }
}

// Quiz Category Schema
const quizCategorySchema = new mongoose.Schema({
  name: String,
  slug: String,
  description: String,
  icon: String,
  color: String,
  skills: [String],
  difficulty: String,
  estimatedTime: Number,
  totalQuestions: Number,
  passingScore: Number,
  isActive: Boolean
}, { timestamps: true })

// Quiz Question Schema
const quizQuestionSchema = new mongoose.Schema({
  categoryId: mongoose.Schema.Types.ObjectId,
  question: String,
  options: [String],
  correctAnswer: Number,
  explanation: String,
  difficulty: String,
  points: Number,
  tags: [String],
  isActive: Boolean
}, { timestamps: true })

// Award Template Schema
const awardTemplateSchema = new mongoose.Schema({
  name: String,
  slug: String,
  description: String,
  categoryId: mongoose.Schema.Types.ObjectId,
  badgeIcon: String,
  badgeColor: String,
  minimumScore: Number,
  rarity: String,
  benefits: [String],
  certificateTemplate: String,
  isActive: Boolean
}, { timestamps: true })

const QuizCategory = mongoose.model('QuizCategory', quizCategorySchema)
const QuizQuestion = mongoose.model('QuizQuestion', quizQuestionSchema)
const AwardTemplate = mongoose.model('AwardTemplate', awardTemplateSchema)

// Seed data
const seedData = async () => {
  try {
    // Clear existing data
    await QuizCategory.deleteMany({})
    await QuizQuestion.deleteMany({})
    await AwardTemplate.deleteMany({})

    // Create categories
    const categories = [
      {
        name: 'Frontend Development',
        slug: 'frontend-development',
        description: 'Test your knowledge of modern frontend technologies and frameworks.',
        icon: '💻',
        color: 'bg-blue-500',
        skills: ['React', 'Vue.js', 'Angular', 'TypeScript', 'CSS', 'HTML', 'JavaScript'],
        difficulty: 'intermediate',
        estimatedTime: 15,
        totalQuestions: 15,
        passingScore: 70,
        isActive: true
      },
      {
        name: 'Backend Development',
        slug: 'backend-development',
        description: 'Assess your backend development and server-side programming skills.',
        icon: '⚙️',
        color: 'bg-green-500',
        skills: ['Node.js', 'Python', 'Java', 'Go', 'PostgreSQL', 'MongoDB', 'API Design'],
        difficulty: 'intermediate',
        estimatedTime: 20,
        totalQuestions: 15,
        passingScore: 70,
        isActive: true
      },
      {
        name: 'UI/UX Design',
        slug: 'ui-ux-design',
        description: 'Evaluate your design thinking and user experience expertise.',
        icon: '🎨',
        color: 'bg-purple-500',
        skills: ['Figma', 'Sketch', 'Adobe XD', 'Prototyping', 'User Research', 'Design Systems'],
        difficulty: 'intermediate',
        estimatedTime: 12,
        totalQuestions: 12,
        passingScore: 70,
        isActive: true
      },
      {
        name: 'Data Science',
        slug: 'data-science',
        description: 'Test your data analysis and machine learning knowledge.',
        icon: '📊',
        color: 'bg-orange-500',
        skills: ['Python', 'R', 'SQL', 'Machine Learning', 'Statistics', 'Data Visualization'],
        difficulty: 'advanced',
        estimatedTime: 25,
        totalQuestions: 20,
        passingScore: 75,
        isActive: true
      }
    ]

    const createdCategories = await QuizCategory.insertMany(categories)
    console.log('Categories created:', createdCategories.length)

    // Create questions for Frontend Development
    const frontendCategory = createdCategories.find(c => c.slug === 'frontend-development')
    const frontendQuestions = [
      {
        categoryId: frontendCategory._id,
        question: "What is the purpose of React's useEffect hook?",
        options: [
          "To manage component state",
          "To perform side effects in functional components",
          "To create custom hooks",
          "To handle form submissions"
        ],
        correctAnswer: 1,
        explanation: "useEffect is used to perform side effects in functional components, such as data fetching, subscriptions, or manually changing the DOM.",
        difficulty: 'intermediate',
        points: 10,
        tags: ['React', 'Hooks'],
        isActive: true
      },
      {
        categoryId: frontendCategory._id,
        question: "Which CSS property is used to create a flexbox container?",
        options: ["display: block", "display: flex", "display: grid", "display: inline"],
        correctAnswer: 1,
        explanation: "The 'display: flex' property creates a flex container, enabling flexbox layout for its children.",
        difficulty: 'beginner',
        points: 5,
        tags: ['CSS', 'Flexbox'],
        isActive: true
      },
      {
        categoryId: frontendCategory._id,
        question: "What is the difference between let and const in JavaScript?",
        options: [
          "No difference",
          "let is block-scoped, const is function-scoped",
          "const creates immutable bindings, let allows reassignment",
          "let is newer than const"
        ],
        correctAnswer: 2,
        explanation: "const creates immutable bindings (cannot be reassigned), while let allows reassignment. Both are block-scoped.",
        difficulty: 'intermediate',
        points: 10,
        tags: ['JavaScript', 'Variables'],
        isActive: true
      },
      {
        categoryId: frontendCategory._id,
        question: "What is TypeScript?",
        options: [
          "A JavaScript runtime",
          "A superset of JavaScript that adds static typing",
          "A CSS preprocessor",
          "A testing framework"
        ],
        correctAnswer: 1,
        explanation: "TypeScript is a superset of JavaScript that adds static type definitions, helping catch errors at compile time.",
        difficulty: 'beginner',
        points: 5,
        tags: ['TypeScript'],
        isActive: true
      },
      {
        categoryId: frontendCategory._id,
        question: "Which method is used to update state in React functional components?",
        options: [
          "this.setState()",
          "useState() setter function",
          "updateState()",
          "setState()"
        ],
        correctAnswer: 1,
        explanation: "In functional components, you use the setter function returned by the useState hook to update state.",
        difficulty: 'intermediate',
        points: 10,
        tags: ['React', 'State'],
        isActive: true
      }
    ]

    await QuizQuestion.insertMany(frontendQuestions)
    console.log('Frontend questions created:', frontendQuestions.length)

    // Create questions for Backend Development
    const backendCategory = createdCategories.find(c => c.slug === 'backend-development')
    const backendQuestions = [
      {
        categoryId: backendCategory._id,
        question: "What is the purpose of middleware in Express.js?",
        options: [
          "To handle database connections",
          "To execute code during the request-response cycle",
          "To manage user authentication only",
          "To serve static files"
        ],
        correctAnswer: 1,
        explanation: "Middleware functions execute during the request-response cycle and can modify request/response objects, end the cycle, or call the next middleware.",
        difficulty: 'intermediate',
        points: 10,
        tags: ['Node.js', 'Express.js'],
        isActive: true
      },
      {
        categoryId: backendCategory._id,
        question: "Which HTTP status code indicates a successful POST request that created a resource?",
        options: ["200 OK", "201 Created", "202 Accepted", "204 No Content"],
        correctAnswer: 1,
        explanation: "201 Created indicates that the request was successful and a new resource was created as a result.",
        difficulty: 'beginner',
        points: 5,
        tags: ['HTTP', 'REST API'],
        isActive: true
      },
      {
        categoryId: backendCategory._id,
        question: "What is the difference between SQL and NoSQL databases?",
        options: [
          "SQL is faster than NoSQL",
          "SQL uses structured schemas, NoSQL is schema-flexible",
          "NoSQL cannot handle relationships",
          "SQL is only for small applications"
        ],
        correctAnswer: 1,
        explanation: "SQL databases use structured schemas with predefined relationships, while NoSQL databases offer schema flexibility and various data models.",
        difficulty: 'intermediate',
        points: 10,
        tags: ['Database', 'SQL', 'NoSQL'],
        isActive: true
      },
      {
        categoryId: backendCategory._id,
        question: "What is JWT (JSON Web Token) used for?",
        options: [
          "Database encryption",
          "Stateless authentication and authorization",
          "File compression",
          "API rate limiting"
        ],
        correctAnswer: 1,
        explanation: "JWT is used for stateless authentication and authorization, allowing secure transmission of information between parties.",
        difficulty: 'intermediate',
        points: 10,
        tags: ['Authentication', 'JWT', 'Security'],
        isActive: true
      },
      {
        categoryId: backendCategory._id,
        question: "Which Python framework is commonly used for building REST APIs?",
        options: [
          "Django",
          "Flask",
          "FastAPI",
          "All of the above"
        ],
        correctAnswer: 3,
        explanation: "Django, Flask, and FastAPI are all popular Python frameworks that can be used to build REST APIs, each with different strengths.",
        difficulty: 'beginner',
        points: 5,
        tags: ['Python', 'API', 'Framework'],
        isActive: true
      }
    ]

    await QuizQuestion.insertMany(backendQuestions)
    console.log('Backend questions created:', backendQuestions.length)

    // Create questions for UI/UX Design
    const designCategory = createdCategories.find(c => c.slug === 'ui-ux-design')
    const designQuestions = [
      {
        categoryId: designCategory._id,
        question: "What is the primary purpose of user personas in UX design?",
        options: [
          "To create beautiful interfaces",
          "To understand and empathize with target users",
          "To choose color schemes",
          "To write code specifications"
        ],
        correctAnswer: 1,
        explanation: "User personas help designers understand and empathize with target users, guiding design decisions based on user needs and behaviors.",
        difficulty: 'intermediate',
        points: 10,
        tags: ['UX', 'User Research', 'Personas'],
        isActive: true
      },
      {
        categoryId: designCategory._id,
        question: "What does 'responsive design' mean?",
        options: [
          "Design that responds to user clicks",
          "Design that adapts to different screen sizes",
          "Design with fast loading times",
          "Design with interactive animations"
        ],
        correctAnswer: 1,
        explanation: "Responsive design ensures that interfaces adapt and work well across different screen sizes and devices.",
        difficulty: 'beginner',
        points: 5,
        tags: ['Responsive Design', 'Mobile'],
        isActive: true
      },
      {
        categoryId: designCategory._id,
        question: "What is the purpose of wireframing in the design process?",
        options: [
          "To add final colors and images",
          "To create low-fidelity layouts and structure",
          "To write user stories",
          "To test performance"
        ],
        correctAnswer: 1,
        explanation: "Wireframing creates low-fidelity layouts that focus on structure, content hierarchy, and functionality without visual design details.",
        difficulty: 'intermediate',
        points: 10,
        tags: ['Wireframing', 'Design Process'],
        isActive: true
      },
      {
        categoryId: designCategory._id,
        question: "Which principle emphasizes that similar elements should be grouped together?",
        options: [
          "Contrast",
          "Proximity",
          "Alignment",
          "Repetition"
        ],
        correctAnswer: 1,
        explanation: "The proximity principle states that related elements should be grouped together to show their relationship and improve organization.",
        difficulty: 'intermediate',
        points: 10,
        tags: ['Design Principles', 'Visual Design'],
        isActive: true
      }
    ]

    await QuizQuestion.insertMany(designQuestions)
    console.log('Design questions created:', designQuestions.length)

    // Create questions for Data Science
    const dataCategory = createdCategories.find(c => c.slug === 'data-science')
    const dataQuestions = [
      {
        categoryId: dataCategory._id,
        question: "What is the difference between supervised and unsupervised learning?",
        options: [
          "Supervised learning uses labeled data, unsupervised finds patterns in unlabeled data",
          "Supervised learning is faster than unsupervised learning",
          "Unsupervised learning requires more data than supervised learning",
          "There is no difference between them"
        ],
        correctAnswer: 0,
        explanation: "Supervised learning uses labeled training data to learn patterns, while unsupervised learning finds hidden patterns in unlabeled data.",
        difficulty: 'intermediate',
        points: 10,
        tags: ['Machine Learning', 'Supervised Learning', 'Unsupervised Learning'],
        isActive: true
      },
      {
        categoryId: dataCategory._id,
        question: "Which Python library is most commonly used for data manipulation and analysis?",
        options: [
          "NumPy",
          "Pandas",
          "Matplotlib",
          "Scikit-learn"
        ],
        correctAnswer: 1,
        explanation: "Pandas is the most popular Python library for data manipulation and analysis, providing data structures and operations for manipulating numerical tables.",
        difficulty: 'beginner',
        points: 5,
        tags: ['Python', 'Pandas', 'Data Analysis'],
        isActive: true
      },
      {
        categoryId: dataCategory._id,
        question: "What does 'overfitting' mean in machine learning?",
        options: [
          "The model performs well on training data but poorly on new data",
          "The model takes too long to train",
          "The model uses too much memory",
          "The model has too few parameters"
        ],
        correctAnswer: 0,
        explanation: "Overfitting occurs when a model learns the training data too well, including noise, resulting in poor performance on new, unseen data.",
        difficulty: 'intermediate',
        points: 10,
        tags: ['Machine Learning', 'Overfitting', 'Model Validation'],
        isActive: true
      },
      {
        categoryId: dataCategory._id,
        question: "What is the purpose of cross-validation in machine learning?",
        options: [
          "To increase model accuracy",
          "To assess model performance and reduce overfitting",
          "To speed up training",
          "To visualize data"
        ],
        correctAnswer: 1,
        explanation: "Cross-validation helps assess how well a model will generalize to new data and helps detect overfitting by testing on multiple data splits.",
        difficulty: 'advanced',
        points: 15,
        tags: ['Machine Learning', 'Cross-validation', 'Model Evaluation'],
        isActive: true
      },
      {
        categoryId: dataCategory._id,
        question: "Which SQL command is used to retrieve data from a database?",
        options: [
          "INSERT",
          "UPDATE",
          "SELECT",
          "DELETE"
        ],
        correctAnswer: 2,
        explanation: "SELECT is the SQL command used to retrieve data from one or more tables in a database.",
        difficulty: 'beginner',
        points: 5,
        tags: ['SQL', 'Database', 'Query'],
        isActive: true
      }
    ]

    await QuizQuestion.insertMany(dataQuestions)
    console.log('Data Science questions created:', dataQuestions.length)

    // Create comprehensive award templates for all categories
    const awardTemplates = [
      // Frontend Awards
      {
        name: 'Frontend Developer',
        slug: 'frontend-developer',
        description: 'Certified Frontend Development Skills',
        categoryId: frontendCategory._id,
        badgeIcon: '💻',
        badgeColor: '#3B82F6',
        minimumScore: 70,
        rarity: 'common',
        benefits: [
          'Digital certificate download',
          'Profile badge display',
          'Access to React tutorials'
        ],
        certificateTemplate: '<div>Frontend Developer Certificate Template</div>',
        isActive: true
      },
      {
        name: 'Frontend Expert',
        slug: 'frontend-expert',
        description: 'Expert-level Frontend Development Mastery',
        categoryId: frontendCategory._id,
        badgeIcon: '🌟',
        badgeColor: '#10B981',
        minimumScore: 90,
        rarity: 'rare',
        benefits: [
          'Premium certificate download',
          'Expert badge display',
          'Free AI training course access',
          'Priority job matching'
        ],
        certificateTemplate: '<div>Frontend Expert Certificate Template</div>',
        isActive: true
      },

      // Backend Awards
      {
        name: 'Backend Developer',
        slug: 'backend-developer',
        description: 'Certified Backend Development Skills',
        categoryId: backendCategory._id,
        badgeIcon: '⚙️',
        badgeColor: '#059669',
        minimumScore: 70,
        rarity: 'common',
        benefits: [
          'Digital certificate download',
          'Profile badge display',
          'Access to API design tutorials'
        ],
        certificateTemplate: '<div>Backend Developer Certificate Template</div>',
        isActive: true
      },
      {
        name: 'Backend Architect',
        slug: 'backend-architect',
        description: 'Advanced Backend Architecture Expertise',
        categoryId: backendCategory._id,
        badgeIcon: '🏗️',
        badgeColor: '#047857',
        minimumScore: 90,
        rarity: 'epic',
        benefits: [
          'Premium certificate download',
          'Architect badge display',
          'Free system design course',
          'Senior role recommendations'
        ],
        certificateTemplate: '<div>Backend Architect Certificate Template</div>',
        isActive: true
      },

      // Design Awards
      {
        name: 'UI/UX Designer',
        slug: 'ui-ux-designer',
        description: 'Certified UI/UX Design Skills',
        categoryId: designCategory._id,
        badgeIcon: '🎨',
        badgeColor: '#7C3AED',
        minimumScore: 70,
        rarity: 'common',
        benefits: [
          'Digital certificate download',
          'Designer badge display',
          'Access to design system templates'
        ],
        certificateTemplate: '<div>UI/UX Designer Certificate Template</div>',
        isActive: true
      },
      {
        name: 'Design Master',
        slug: 'design-master',
        description: 'Master-level Design Thinking and Execution',
        categoryId: designCategory._id,
        badgeIcon: '🎭',
        badgeColor: '#6D28D9',
        minimumScore: 90,
        rarity: 'legendary',
        benefits: [
          'Premium certificate download',
          'Master designer badge',
          'Free advanced design course',
          'Design leadership opportunities'
        ],
        certificateTemplate: '<div>Design Master Certificate Template</div>',
        isActive: true
      },

      // Data Science Awards
      {
        name: 'Data Scientist',
        slug: 'data-scientist',
        description: 'Certified Data Science Skills',
        categoryId: dataCategory._id,
        badgeIcon: '📊',
        badgeColor: '#EA580C',
        minimumScore: 75,
        rarity: 'uncommon',
        benefits: [
          'Digital certificate download',
          'Data scientist badge display',
          'Access to ML tutorials'
        ],
        certificateTemplate: '<div>Data Scientist Certificate Template</div>',
        isActive: true
      },
      {
        name: 'ML Engineer',
        slug: 'ml-engineer',
        description: 'Advanced Machine Learning Engineering',
        categoryId: dataCategory._id,
        badgeIcon: '🤖',
        badgeColor: '#DC2626',
        minimumScore: 90,
        rarity: 'legendary',
        benefits: [
          'Premium certificate download',
          'ML Engineer badge display',
          'Free AI/ML specialization course',
          'Tech lead role recommendations'
        ],
        certificateTemplate: '<div>ML Engineer Certificate Template</div>',
        isActive: true
      }
    ]

    await AwardTemplate.insertMany(awardTemplates)
    console.log('Award templates created:', awardTemplates.length)

    console.log('Seed data created successfully!')
    process.exit(0)

  } catch (error) {
    console.error('Error seeding data:', error)
    process.exit(1)
  }
}

// Run the seed script
const run = async () => {
  await connectDB()
  await seedData()
}

run()

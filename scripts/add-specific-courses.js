const mongoose = require('mongoose')

// Simple connection function
async function connectToDatabase() {
  const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/jobportal'
  
  if (mongoose.connection.readyState === 1) {
    return mongoose
  }

  try {
    await mongoose.connect(MONGODB_URI, {
      bufferCommands: false,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      family: 4
    })
    console.log('✅ Connected to MongoDB')
    return mongoose
  } catch (error) {
    console.error('❌ MongoDB connection error:', error)
    throw error
  }
}

// Define schemas
const CourseCategorySchema = new mongoose.Schema({
  name: { type: String, required: true, trim: true },
  description: { type: String, required: true },
  icon: { type: String, required: true },
  color: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  isActive: { type: Boolean, default: true },
  sortOrder: { type: Number, default: 0 }
}, {
  timestamps: true
})

const CourseSchema = new mongoose.Schema({
  title: { type: String, required: true, trim: true },
  description: { type: String, required: true },
  shortDescription: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  thumbnail: { type: String, required: true },
  previewVideo: { type: String },
  categoryId: { type: mongoose.Schema.Types.ObjectId, ref: 'CourseCategory', required: true },
  instructorId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  level: { type: String, enum: ['beginner', 'intermediate', 'advanced', 'expert'], required: true },
  duration: { type: Number, required: true },
  price: { type: Number, required: true, min: 0 },
  originalPrice: { type: Number, min: 0 },
  currency: { type: String, default: 'USD' },
  language: { type: String, default: 'English' },
  tags: [{ type: String }],
  skills: [{ type: String }],
  requirements: [{ type: String }],
  whatYouWillLearn: [{ type: String }],
  targetAudience: [{ type: String }],
  isPublished: { type: Boolean, default: false },
  isFeatured: { type: Boolean, default: false },
  isPremium: { type: Boolean, default: false },
  rating: { type: Number, default: 0, min: 0, max: 5 },
  totalRatings: { type: Number, default: 0 },
  totalStudents: { type: Number, default: 0 },
  totalLessons: { type: Number, default: 0 },
  lastUpdated: { type: Date, default: Date.now }
}, {
  timestamps: true
})

// Create models
const CourseCategory = mongoose.models.CourseCategory || mongoose.model('CourseCategory', CourseCategorySchema)
const Course = mongoose.models.Course || mongoose.model('Course', CourseSchema)

async function addSpecificCourses() {
  try {
    await connectToDatabase()
    console.log('Connected to database')

    // Find or create categories
    let webDevCategory = await CourseCategory.findOne({ slug: 'web-development' })
    let dataScienceCategory = await CourseCategory.findOne({ slug: 'data-science' })

    if (!webDevCategory) {
      webDevCategory = await CourseCategory.create({
        name: 'Web Development',
        description: 'Learn modern web development technologies and frameworks',
        icon: '💻',
        color: '#3B82F6',
        slug: 'web-development',
        isActive: true,
        sortOrder: 1
      })
      console.log('Created Web Development category')
    }

    if (!dataScienceCategory) {
      dataScienceCategory = await CourseCategory.create({
        name: 'Data Science',
        description: 'Master data analysis, machine learning, and AI',
        icon: '📊',
        color: '#10B981',
        slug: 'data-science',
        isActive: true,
        sortOrder: 2
      })
      console.log('Created Data Science category')
    }

    // Create a dummy instructor ID
    const instructorId = new mongoose.Types.ObjectId()

    // Check if courses already exist
    const existingPython = await Course.findOne({ slug: 'basics-of-python-for-beginners' })
    const existingReact = await Course.findOne({ slug: 'react-from-the-start' })

    const coursesToAdd = []

    if (!existingPython) {
      coursesToAdd.push({
        title: 'Basics of Python for Beginners',
        description: 'Start your programming journey with Python! This comprehensive beginner-friendly course covers Python fundamentals, data types, control structures, functions, and object-oriented programming. Perfect for complete beginners who want to learn programming from scratch.',
        shortDescription: 'Learn Python programming from scratch with hands-on exercises and real-world examples',
        slug: 'basics-of-python-for-beginners',
        thumbnail: 'https://images.unsplash.com/photo-1526379879527-8559ecfcaec0?w=800&h=600&fit=crop',
        previewVideo: 'https://example.com/preview-python-basics.mp4',
        categoryId: dataScienceCategory._id,
        instructorId: instructorId,
        level: 'beginner',
        duration: 420, // 7 hours
        price: 49.99,
        originalPrice: 79.99,
        currency: 'USD',
        language: 'English',
        tags: ['Python', 'Programming', 'Beginner', 'Fundamentals'],
        skills: ['Python Syntax', 'Variables and Data Types', 'Control Flow', 'Functions', 'Object-Oriented Programming'],
        requirements: ['No programming experience required', 'Computer with internet access', 'Willingness to learn'],
        whatYouWillLearn: [
          'Understand Python syntax and basic programming concepts',
          'Work with variables, data types, and operators',
          'Use control structures like loops and conditionals',
          'Create and use functions effectively',
          'Understand object-oriented programming basics',
          'Handle files and exceptions',
          'Build simple Python applications',
          'Debug and troubleshoot Python code'
        ],
        targetAudience: ['Complete programming beginners', 'Students wanting to learn Python', 'Career changers entering tech', 'Anyone interested in data science or web development'],
        isPublished: true,
        isFeatured: true,
        isPremium: false,
        rating: 4.6,
        totalRatings: 892,
        totalStudents: 5420,
        totalLessons: 35,
        lastUpdated: new Date()
      })
    }

    if (!existingReact) {
      coursesToAdd.push({
        title: 'React from the Start',
        description: 'Master React.js from the ground up! This comprehensive course takes you from React basics to building complex, interactive web applications. Learn modern React patterns, hooks, state management, and best practices used by professional developers.',
        shortDescription: 'Build modern web applications with React.js from beginner to advanced level',
        slug: 'react-from-the-start',
        thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800&h=600&fit=crop',
        previewVideo: 'https://example.com/preview-react-start.mp4',
        categoryId: webDevCategory._id,
        instructorId: instructorId,
        level: 'beginner',
        duration: 540, // 9 hours
        price: 69.99,
        originalPrice: 99.99,
        currency: 'USD',
        language: 'English',
        tags: ['React', 'JavaScript', 'Frontend', 'Web Development', 'Hooks'],
        skills: ['React Components', 'JSX', 'State Management', 'Event Handling', 'React Hooks', 'API Integration'],
        requirements: ['Basic HTML and CSS knowledge', 'JavaScript fundamentals', 'Node.js installed on your computer'],
        whatYouWillLearn: [
          'Build React applications from scratch',
          'Understand React components and JSX',
          'Master React hooks (useState, useEffect, useContext)',
          'Handle events and user interactions',
          'Manage application state effectively',
          'Connect to APIs and handle data',
          'Implement routing with React Router',
          'Deploy React applications to production',
          'Follow React best practices and patterns',
          'Debug React applications efficiently'
        ],
        targetAudience: ['Web developers new to React', 'JavaScript developers', 'Frontend development students', 'Developers wanting to modernize their skills'],
        isPublished: true,
        isFeatured: true,
        isPremium: false,
        rating: 4.8,
        totalRatings: 1156,
        totalStudents: 7230,
        totalLessons: 42,
        lastUpdated: new Date()
      })
    }

    if (coursesToAdd.length > 0) {
      await Course.insertMany(coursesToAdd)
      console.log(`✅ Added ${coursesToAdd.length} new courses:`)
      coursesToAdd.forEach(course => {
        console.log(`  - ${course.title}`)
      })
    } else {
      console.log('ℹ️  Both courses already exist in the database')
    }

    console.log('✅ Specific courses operation completed successfully!')

  } catch (error) {
    console.error('❌ Error adding specific courses:', error)
  } finally {
    await mongoose.disconnect()
    process.exit(0)
  }
}

// Run the function
addSpecificCourses()

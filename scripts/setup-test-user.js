// Setup Test User for Company Management Testing
const mongoose = require('mongoose')
const fs = require('fs')
const path = require('path')

// Load environment variables manually
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '..', '.env.local')
    const envContent = fs.readFileSync(envPath, 'utf8')

    envContent.split('\n').forEach(line => {
      const trimmedLine = line.trim()
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const equalIndex = trimmedLine.indexOf('=')
        if (equalIndex > 0) {
          const key = trimmedLine.substring(0, equalIndex).trim()
          const value = trimmedLine.substring(equalIndex + 1).trim()
          if (key && value) {
            process.env[key] = value
          }
        }
      }
    })
  } catch (error) {
    console.log('⚠️  Could not load .env.local file:', error.message)
  }
}

loadEnvFile()

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/jobportal'

console.log('🔗 Using MongoDB URI:', MONGODB_URI.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'))

// User Schema (simplified)
const userSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: { type: String, enum: ['user', 'company_admin', 'admin', 'super_admin'], default: 'user' },
  companyId: { type: mongoose.Schema.Types.ObjectId, ref: 'Company' },
  isActive: { type: Boolean, default: true },
  isVerified: { type: Boolean, default: false },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
})

const User = mongoose.models.User || mongoose.model('User', userSchema)

async function setupTestUser() {
  try {
    console.log('🔌 Connecting to MongoDB...')
    await mongoose.connect(MONGODB_URI)
    console.log('✅ Connected to MongoDB')

    // Check if test user already exists
    const existingUser = await User.findOne({ email: '<EMAIL>' })
    
    if (existingUser) {
      console.log('✅ Test user already exists:')
      console.log(`   ID: ${existingUser._id}`)
      console.log(`   Email: ${existingUser.email}`)
      console.log(`   Role: ${existingUser.role}`)
      console.log(`   Company: ${existingUser.companyId || 'None'}`)
      
      // Update .env.local with user ID
      const fs = require('fs')
      const envPath = '.env.local'
      let envContent = ''
      
      if (fs.existsSync(envPath)) {
        envContent = fs.readFileSync(envPath, 'utf8')
      }
      
      // Remove existing DEV_USER_ID and BYPASS_AUTH lines
      envContent = envContent
        .split('\n')
        .filter(line => !line.startsWith('DEV_USER_ID=') && !line.startsWith('BYPASS_AUTH='))
        .join('\n')
      
      // Add new values
      envContent += `\nDEV_USER_ID=${existingUser._id}`
      envContent += `\nBYPASS_AUTH=true`
      
      fs.writeFileSync(envPath, envContent)
      console.log('✅ Updated .env.local with test user ID')
      
      return existingUser
    }

    // Create test user
    console.log('👤 Creating test user...')
    const testUser = new User({
      name: 'Test Company Admin',
      email: '<EMAIL>',
      password: '$2b$10$dummy.hash.for.testing', // Dummy hash for testing
      role: 'company_admin',
      isActive: true,
      isVerified: true
    })

    await testUser.save()
    console.log('✅ Test user created successfully:')
    console.log(`   ID: ${testUser._id}`)
    console.log(`   Email: ${testUser.email}`)
    console.log(`   Role: ${testUser.role}`)

    // Update .env.local with user ID
    const fs = require('fs')
    const envPath = '.env.local'
    let envContent = ''
    
    if (fs.existsSync(envPath)) {
      envContent = fs.readFileSync(envPath, 'utf8')
    }
    
    // Remove existing DEV_USER_ID and BYPASS_AUTH lines
    envContent = envContent
      .split('\n')
      .filter(line => !line.startsWith('DEV_USER_ID=') && !line.startsWith('BYPASS_AUTH='))
      .join('\n')
    
    // Add new values
    envContent += `\nDEV_USER_ID=${testUser._id}`
    envContent += `\nBYPASS_AUTH=true`
    
    fs.writeFileSync(envPath, envContent)
    console.log('✅ Updated .env.local with test user ID')

    return testUser

  } catch (error) {
    console.error('❌ Error setting up test user:', error)
    throw error
  } finally {
    await mongoose.disconnect()
    console.log('🔌 Disconnected from MongoDB')
  }
}

async function main() {
  console.log('🧪 Setting up test user for company management testing')
  console.log('=====================================================')
  
  try {
    const user = await setupTestUser()
    
    console.log('\n🎯 Test Setup Complete!')
    console.log('========================')
    console.log('✅ Test user is ready')
    console.log('✅ Authentication bypass enabled for development')
    console.log('✅ Environment variables updated')
    console.log('')
    console.log('📋 Next Steps:')
    console.log('1. Restart your development server: npm run dev')
    console.log('2. Navigate to: http://localhost:3000/company-dashboard/company')
    console.log('3. You should now see the company creation form')
    console.log('4. Test creating a company profile')
    console.log('')
    console.log('⚠️  Note: Authentication bypass is enabled for development only')
    console.log('   Make sure to disable it in production by removing BYPASS_AUTH=true')
    console.log('')
    console.log('🚀 Ready for testing!')
    
  } catch (error) {
    console.error('\n❌ Setup failed:', error.message)
    process.exit(1)
  }
}

// Run the setup
if (require.main === module) {
  main()
}

module.exports = { setupTestUser }

// Script to restore and populate questions for all categories
const mongoose = require('mongoose')

// Database connection
const connectToDatabase = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb+srv://winstonmhango23:<EMAIL>/?retryWrites=true&w=majority&appName=jobs'
    await mongoose.connect(mongoUri)
    console.log('✅ Connected to MongoDB')
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message)
    process.exit(1)
  }
}

// Quiz Category Schema
const quizCategorySchema = new mongoose.Schema({
  name: String,
  slug: String,
  description: String,
  icon: String,
  color: String,
  skills: [String],
  difficulty: String,
  estimatedTime: Number,
  totalQuestions: Number,
  passingScore: Number,
  isActive: Boolean
}, { timestamps: true })

// Quiz Question Schema
const quizQuestionSchema = new mongoose.Schema({
  categoryId: mongoose.Schema.Types.ObjectId,
  question: String,
  options: [String],
  correctAnswer: Number,
  explanation: String,
  difficulty: String,
  points: Number,
  tags: [String],
  isActive: Boolean
}, { timestamps: true })

const QuizCategory = mongoose.model('QuizCategory', quizCategorySchema)
const QuizQuestion = mongoose.model('QuizQuestion', quizQuestionSchema)

// Question templates for different categories
const questionTemplates = {
  'javascript': [
    {
      question: "What is the difference between 'let', 'const', and 'var' in JavaScript?",
      options: [
        "'let' and 'const' are block-scoped, 'var' is function-scoped",
        "'var' is block-scoped, 'let' and 'const' are function-scoped",
        "They are all the same",
        "'const' can be reassigned, 'let' cannot"
      ],
      correctAnswer: 0,
      explanation: "'let' and 'const' have block scope and temporal dead zone, while 'var' has function scope and is hoisted.",
      tags: ['Variables', 'Scope', 'ES6']
    },
    {
      question: "What is a closure in JavaScript?",
      options: [
        "A way to close functions",
        "A function that has access to variables in its outer scope",
        "A method to end loops",
        "A type of object"
      ],
      correctAnswer: 1,
      explanation: "A closure is a function that retains access to variables from its outer (enclosing) scope even after the outer function has returned.",
      tags: ['Closures', 'Functions', 'Scope']
    },
    {
      question: "What is the purpose of 'async/await' in JavaScript?",
      options: [
        "To make functions run faster",
        "To handle asynchronous operations more readably",
        "To create loops",
        "To define variables"
      ],
      correctAnswer: 1,
      explanation: "async/await provides a more readable way to handle asynchronous operations, making asynchronous code look and behave more like synchronous code.",
      tags: ['Async', 'Promises', 'ES2017']
    },
    {
      question: "What is the difference between '==' and '===' in JavaScript?",
      options: [
        "'==' checks type and value, '===' checks only value",
        "'===' checks type and value, '==' performs type coercion",
        "They are exactly the same",
        "'===' is faster but less accurate"
      ],
      correctAnswer: 1,
      explanation: "'===' (strict equality) checks both type and value without type coercion, while '==' performs type coercion before comparison.",
      tags: ['Comparison', 'Operators', 'Type Coercion']
    },
    {
      question: "What is event bubbling in JavaScript?",
      options: [
        "Events moving from child to parent elements",
        "Events moving from parent to child elements",
        "Events that create bubbles",
        "A way to prevent events"
      ],
      correctAnswer: 0,
      explanation: "Event bubbling is when an event starts from the target element and bubbles up through its parent elements in the DOM hierarchy.",
      tags: ['Events', 'DOM', 'Event Propagation']
    },
    {
      question: "What is the 'this' keyword in JavaScript?",
      options: [
        "A reference to the current object",
        "A way to create variables",
        "A loop keyword",
        "A function parameter"
      ],
      correctAnswer: 0,
      explanation: "The 'this' keyword refers to the object that is executing the current function, and its value depends on how the function is called.",
      tags: ['This', 'Context', 'Objects']
    },
    {
      question: "What is a Promise in JavaScript?",
      options: [
        "A guarantee that code will work",
        "An object representing the eventual completion or failure of an asynchronous operation",
        "A type of loop",
        "A way to create functions"
      ],
      correctAnswer: 1,
      explanation: "A Promise is an object that represents the eventual completion (or failure) of an asynchronous operation and its resulting value.",
      tags: ['Promises', 'Async', 'ES6']
    },
    {
      question: "What is destructuring in JavaScript?",
      options: [
        "Breaking objects and arrays",
        "A way to extract values from arrays or properties from objects",
        "Deleting variables",
        "A type of loop"
      ],
      correctAnswer: 1,
      explanation: "Destructuring is a JavaScript expression that allows you to extract values from arrays or properties from objects into distinct variables.",
      tags: ['Destructuring', 'ES6', 'Arrays', 'Objects']
    },
    {
      question: "What is the difference between 'null' and 'undefined' in JavaScript?",
      options: [
        "'null' is assigned, 'undefined' means no value has been assigned",
        "'undefined' is assigned, 'null' means no value has been assigned",
        "They are exactly the same",
        "'null' is for numbers, 'undefined' is for strings"
      ],
      correctAnswer: 0,
      explanation: "'null' is an intentional absence of value that must be assigned, while 'undefined' means a variable has been declared but not assigned a value.",
      tags: ['Null', 'Undefined', 'Data Types']
    },
    {
      question: "What is the purpose of the 'map()' method in JavaScript?",
      options: [
        "To create geographical maps",
        "To create a new array by transforming each element of an existing array",
        "To find elements in an array",
        "To sort arrays"
      ],
      correctAnswer: 1,
      explanation: "The map() method creates a new array by calling a provided function on every element in the calling array.",
      tags: ['Array Methods', 'Functional Programming', 'Map']
    },
    {
      question: "What is hoisting in JavaScript?",
      options: [
        "Moving variables and functions to the top of their scope during compilation",
        "A way to lift heavy objects",
        "A method to sort arrays",
        "A type of loop"
      ],
      correctAnswer: 0,
      explanation: "Hoisting is JavaScript's behavior of moving variable and function declarations to the top of their containing scope during the compilation phase.",
      tags: ['Hoisting', 'Variables', 'Functions']
    },
    {
      question: "What is the difference between 'forEach()' and 'map()' in JavaScript?",
      options: [
        "'forEach()' returns a new array, 'map()' doesn't return anything",
        "'map()' returns a new array, 'forEach()' doesn't return anything",
        "They do exactly the same thing",
        "'forEach()' is faster than 'map()'"
      ],
      correctAnswer: 1,
      explanation: "'map()' returns a new array with transformed elements, while 'forEach()' executes a function for each element but returns undefined.",
      tags: ['Array Methods', 'ForEach', 'Map']
    },
    {
      question: "What is an arrow function in JavaScript?",
      options: [
        "A function that points in a direction",
        "A shorter syntax for writing functions introduced in ES6",
        "A function that returns arrows",
        "A way to create loops"
      ],
      correctAnswer: 1,
      explanation: "Arrow functions provide a shorter syntax for writing functions and have lexical 'this' binding, meaning they inherit 'this' from the enclosing scope.",
      tags: ['Arrow Functions', 'ES6', 'Functions']
    },
    {
      question: "What is the purpose of 'JSON.stringify()' and 'JSON.parse()'?",
      options: [
        "'JSON.stringify()' converts JSON to object, 'JSON.parse()' converts object to JSON",
        "'JSON.stringify()' converts object to JSON string, 'JSON.parse()' converts JSON string to object",
        "They do the same thing",
        "They are used for string manipulation"
      ],
      correctAnswer: 1,
      explanation: "'JSON.stringify()' converts a JavaScript object to a JSON string, while 'JSON.parse()' converts a JSON string back to a JavaScript object.",
      tags: ['JSON', 'Serialization', 'Data Conversion']
    },
    {
      question: "What is event delegation in JavaScript?",
      options: [
        "Assigning events to delegates",
        "Using event bubbling to handle events on parent elements instead of individual child elements",
        "A way to prevent events",
        "Creating custom events"
      ],
      correctAnswer: 1,
      explanation: "Event delegation uses event bubbling to handle events at a higher level in the DOM rather than attaching event listeners to individual elements.",
      tags: ['Event Delegation', 'Events', 'DOM']
    },
    {
      question: "What is the difference between 'call()', 'apply()', and 'bind()' methods?",
      options: [
        "They all do the same thing",
        "'call()' and 'apply()' invoke immediately with different argument formats, 'bind()' returns a new function",
        "'bind()' is the fastest method",
        "They are used for mathematical operations"
      ],
      correctAnswer: 1,
      explanation: "'call()' invokes with arguments as a list, 'apply()' with arguments as an array, and 'bind()' returns a new function with a bound 'this' context.",
      tags: ['Call', 'Apply', 'Bind', 'Function Methods']
    },
    {
      question: "What is a callback function in JavaScript?",
      options: [
        "A function that calls back to the server",
        "A function passed as an argument to another function",
        "A function that returns callbacks",
        "A way to create loops"
      ],
      correctAnswer: 1,
      explanation: "A callback function is a function passed as an argument to another function, which is then invoked inside the outer function.",
      tags: ['Callbacks', 'Functions', 'Async Programming']
    },
    {
      question: "What is the purpose of 'use strict' in JavaScript?",
      options: [
        "To make JavaScript run faster",
        "To enable strict mode which catches common coding errors",
        "To create strict variables",
        "To enforce strict typing"
      ],
      correctAnswer: 1,
      explanation: "'use strict' enables strict mode, which catches common coding mistakes, prevents the use of undeclared variables, and makes JavaScript more secure.",
      tags: ['Strict Mode', 'Best Practices', 'Error Prevention']
    },
    {
      question: "What is the difference between 'setTimeout()' and 'setInterval()'?",
      options: [
        "'setTimeout()' executes once after a delay, 'setInterval()' executes repeatedly at intervals",
        "'setInterval()' executes once, 'setTimeout()' executes repeatedly",
        "They do the same thing",
        "'setTimeout()' is more accurate than 'setInterval()'"
      ],
      correctAnswer: 0,
      explanation: "'setTimeout()' executes a function once after a specified delay, while 'setInterval()' executes a function repeatedly at specified intervals.",
      tags: ['Timers', 'SetTimeout', 'SetInterval', 'Async']
    },
    {
      question: "What is prototypal inheritance in JavaScript?",
      options: [
        "A way to inherit from prototypes",
        "Objects can inherit properties and methods from other objects through the prototype chain",
        "A type of class inheritance",
        "A way to create prototypes"
      ],
      correctAnswer: 1,
      explanation: "Prototypal inheritance allows objects to inherit properties and methods from other objects through the prototype chain, which is JavaScript's inheritance model.",
      tags: ['Prototypes', 'Inheritance', 'OOP']
    }
  ],
  'react': [
    {
      question: "What is JSX in React?",
      options: [
        "A JavaScript extension",
        "A syntax extension that allows writing HTML-like code in JavaScript",
        "A new programming language",
        "A React component"
      ],
      correctAnswer: 1,
      explanation: "JSX is a syntax extension for JavaScript that allows you to write HTML-like code within JavaScript, making it easier to create React elements.",
      tags: ['JSX', 'Syntax', 'React Basics']
    },
    {
      question: "What is the difference between functional and class components in React?",
      options: [
        "Functional components use functions, class components use ES6 classes",
        "Class components are faster than functional components",
        "Functional components can't use state",
        "There is no difference"
      ],
      correctAnswer: 0,
      explanation: "Functional components are defined as functions and use hooks for state and lifecycle, while class components are ES6 classes with built-in state and lifecycle methods.",
      tags: ['Components', 'Functional Components', 'Class Components']
    },
    {
      question: "What is the purpose of the 'key' prop in React lists?",
      options: [
        "To unlock components",
        "To help React identify which items have changed, added, or removed",
        "To create unique components",
        "To sort list items"
      ],
      correctAnswer: 1,
      explanation: "The 'key' prop helps React identify which list items have changed, been added, or removed, enabling efficient re-rendering of lists.",
      tags: ['Keys', 'Lists', 'Performance']
    },
    {
      question: "What is the useState hook in React?",
      options: [
        "A hook for using state in functional components",
        "A hook for creating components",
        "A hook for handling events",
        "A hook for routing"
      ],
      correctAnswer: 0,
      explanation: "useState is a React hook that allows you to add state to functional components, returning the current state value and a function to update it.",
      tags: ['Hooks', 'State', 'useState']
    },
    {
      question: "What is the useEffect hook used for?",
      options: [
        "Creating effects",
        "Performing side effects in functional components",
        "Adding visual effects",
        "Handling user input"
      ],
      correctAnswer: 1,
      explanation: "useEffect is used to perform side effects in functional components, such as data fetching, subscriptions, or manually changing the DOM.",
      tags: ['Hooks', 'useEffect', 'Side Effects']
    }
  ],
  'python': [
    {
      question: "What is a list comprehension in Python?",
      options: [
        "A way to compress lists",
        "A concise way to create lists based on existing lists",
        "A method to understand lists",
        "A way to delete list elements"
      ],
      correctAnswer: 1,
      explanation: "List comprehension provides a concise way to create lists by applying an expression to each item in an existing iterable.",
      tags: ['List Comprehension', 'Lists', 'Python Syntax']
    },
    {
      question: "What is the difference between a list and a tuple in Python?",
      options: [
        "Lists are mutable, tuples are immutable",
        "Lists are immutable, tuples are mutable",
        "Lists are faster than tuples",
        "There is no difference"
      ],
      correctAnswer: 0,
      explanation: "Lists are mutable (can be changed after creation) while tuples are immutable (cannot be changed after creation).",
      tags: ['Data Structures', 'Lists', 'Tuples']
    }
  ],
  'nodejs': [
    {
      question: "What is Node.js?",
      options: [
        "A JavaScript framework",
        "A JavaScript runtime built on Chrome's V8 engine",
        "A database",
        "A web browser"
      ],
      correctAnswer: 1,
      explanation: "Node.js is a JavaScript runtime environment that allows you to run JavaScript on the server side, built on Chrome's V8 JavaScript engine.",
      tags: ['Node.js', 'Runtime', 'Server-side']
    },
    {
      question: "What is npm in Node.js?",
      options: [
        "Node Package Manager - a package manager for JavaScript",
        "Node Programming Module",
        "Node Process Manager",
        "Node Project Manager"
      ],
      correctAnswer: 0,
      explanation: "npm (Node Package Manager) is the default package manager for Node.js, used to install and manage JavaScript packages.",
      tags: ['npm', 'Package Manager', 'Dependencies']
    }
  ],
  'backend': [
    {
      question: "What is an API?",
      options: [
        "Application Programming Interface - a set of protocols for building software",
        "Advanced Programming Interface",
        "Automated Programming Interface",
        "Application Process Interface"
      ],
      correctAnswer: 0,
      explanation: "An API (Application Programming Interface) is a set of protocols, routines, and tools for building software applications, defining how components should interact.",
      tags: ['API', 'Backend', 'Integration']
    },
    {
      question: "What is REST in web development?",
      options: [
        "A way to rest the server",
        "Representational State Transfer - an architectural style for web services",
        "A database technology",
        "A programming language"
      ],
      correctAnswer: 1,
      explanation: "REST (Representational State Transfer) is an architectural style for designing web services that use HTTP methods and are stateless.",
      tags: ['REST', 'Web Services', 'Architecture']
    }
  ],
  'frontend': [
    {
      question: "What is the DOM in web development?",
      options: [
        "Document Object Model - a programming interface for web documents",
        "Data Object Model",
        "Dynamic Object Model",
        "Document Operation Model"
      ],
      correctAnswer: 0,
      explanation: "The DOM (Document Object Model) is a programming interface for web documents, representing the page structure as a tree of objects.",
      tags: ['DOM', 'Web Development', 'JavaScript']
    },
    {
      question: "What is responsive web design?",
      options: [
        "Design that responds to user input",
        "Design that adapts to different screen sizes and devices",
        "Design that loads quickly",
        "Design with animations"
      ],
      correctAnswer: 1,
      explanation: "Responsive web design is an approach that makes web pages render well on different devices and screen sizes using flexible layouts and media queries.",
      tags: ['Responsive Design', 'CSS', 'Mobile-first']
    }
  ]
}

const restoreAllQuestions = async () => {
  try {
    await connectToDatabase()
    
    // Get all existing categories
    const categories = await QuizCategory.find({ isActive: true }).lean()
    console.log(`📋 Found ${categories.length} categories:`)
    
    categories.forEach(cat => {
      console.log(`  - ${cat.name} (${cat.difficulty})`)
    })
    
    console.log('\n🔄 Starting question restoration...')
    
    for (const category of categories) {
      console.log(`\n📝 Processing category: ${category.name}`)
      
      // Check if category already has questions
      const existingQuestions = await QuizQuestion.countDocuments({ categoryId: category._id })
      console.log(`  Current questions: ${existingQuestions}`)
      
      if (existingQuestions >= 20) {
        console.log(`  ✅ Category already has sufficient questions, skipping...`)
        continue
      }
      
      // Generate questions based on category
      const questions = await generateQuestionsForCategory(category)
      
      if (questions.length > 0) {
        // Clear existing questions for this category only
        await QuizQuestion.deleteMany({ categoryId: category._id })
        
        // Insert new questions
        await QuizQuestion.insertMany(questions)
        console.log(`  ✅ Created ${questions.length} questions for ${category.name}`)
      } else {
        console.log(`  ⚠️ No questions generated for ${category.name}`)
      }
    }
    
    console.log('\n🎉 Question restoration completed!')
    
    // Summary
    const totalQuestions = await QuizQuestion.countDocuments()
    console.log(`📊 Total questions in database: ${totalQuestions}`)
    
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Error restoring questions:', error)
    process.exit(1)
  }
}

const generateQuestionsForCategory = async (category) => {
  const categoryName = category.name.toLowerCase()
  const difficulty = category.difficulty
  
  // Check if we have predefined questions for this category
  const categoryKey = Object.keys(questionTemplates).find(key => 
    categoryName.includes(key) || category.slug.includes(key)
  )
  
  if (categoryKey && questionTemplates[categoryKey]) {
    // Use predefined questions
    const templates = questionTemplates[categoryKey]
    return templates.map(template => ({
      categoryId: category._id,
      question: template.question,
      options: template.options,
      correctAnswer: template.correctAnswer,
      explanation: template.explanation,
      difficulty: difficulty,
      points: difficulty === 'beginner' ? 5 : difficulty === 'intermediate' ? 10 : 15,
      tags: template.tags,
      isActive: true
    }))
  }
  
  // Generate generic questions for categories without predefined templates
  return generateGenericQuestions(category)
}

const generateGenericQuestions = (category) => {
  const genericQuestions = []
  const skills = category.skills || []
  const categoryName = category.name
  const difficulty = category.difficulty

  // Generate 20 generic questions based on category info
  for (let i = 1; i <= 20; i++) {
    const skill = skills[Math.floor(Math.random() * skills.length)] || categoryName

    const templates = [
      {
        question: `What is a key concept in ${skill}?`,
        options: [
          `Understanding ${skill} fundamentals`,
          `Advanced ${skill} techniques`,
          `${skill} best practices`,
          `All of the above`
        ],
        correctAnswer: 3,
        explanation: `${skill} encompasses fundamentals, advanced techniques, and best practices that are all important for mastery.`,
        tags: [skill, 'Concepts', 'Fundamentals']
      },
      {
        question: `Which of the following is important when working with ${skill}?`,
        options: [
          'Following best practices',
          'Understanding core principles',
          'Staying updated with latest features',
          'All of the above'
        ],
        correctAnswer: 3,
        explanation: `Working effectively with ${skill} requires following best practices, understanding core principles, and staying current with developments.`,
        tags: [skill, 'Best Practices', 'Principles']
      },
      {
        question: `What is a common use case for ${skill}?`,
        options: [
          'Building applications',
          'Solving problems',
          'Improving efficiency',
          'All of the above'
        ],
        correctAnswer: 3,
        explanation: `${skill} is commonly used for building applications, solving problems, and improving development efficiency.`,
        tags: [skill, 'Use Cases', 'Applications']
      },
      {
        question: `When learning ${skill}, what should you focus on first?`,
        options: [
          'Basic syntax and concepts',
          'Advanced features only',
          'Complex projects immediately',
          'Memorizing everything'
        ],
        correctAnswer: 0,
        explanation: `When learning ${skill}, it's important to start with basic syntax and fundamental concepts before moving to advanced topics.`,
        tags: [skill, 'Learning', 'Fundamentals']
      },
      {
        question: `What makes ${skill} valuable in modern development?`,
        options: [
          'Its versatility and widespread adoption',
          'Easy learning curve',
          'Strong community support',
          'All of the above'
        ],
        correctAnswer: 3,
        explanation: `${skill} is valuable due to its versatility, widespread adoption, relatively accessible learning curve, and strong community support.`,
        tags: [skill, 'Value', 'Community']
      }
    ]

    const template = templates[i % templates.length]

    genericQuestions.push({
      categoryId: category._id,
      question: template.question,
      options: template.options,
      correctAnswer: template.correctAnswer,
      explanation: template.explanation,
      difficulty: difficulty,
      points: difficulty === 'beginner' ? 5 : difficulty === 'intermediate' ? 10 : 15,
      tags: template.tags,
      isActive: true
    })
  }

  return genericQuestions
}

// Run the script
restoreAllQuestions()

# Database Cleanup Scripts

This directory contains scripts for safely cleaning up the database during development and testing.

## 🚨 **IMPORTANT WARNING**
These scripts will **permanently delete data** from your database. Always ensure you have backups if needed, especially in production environments.

## Scripts Overview

### 1. `cleanup-database.js` - Interactive Cleanup
**Full-featured cleanup script with safety prompts**

```bash
npm run cleanup
# or
node scripts/cleanup-database.js
```

**Features:**
- Interactive menu with confirmation prompts
- Shows database statistics before and after cleanup
- Multiple cleanup options:
  - Delete all users only
  - Delete all companies only
  - Delete everything (users + companies + clients)
  - Delete test data only
  - Show statistics and exit

**Use when:** You want full control and safety prompts

### 2. `quick-cleanup.js` - Automated Cleanup
**Fast cleanup script for development and testing**

```bash
# Delete test data only (default)
npm run cleanup:quick
node scripts/quick-cleanup.js

# Delete ALL data (dangerous!)
npm run cleanup:all
node scripts/quick-cleanup.js --all

# Show database statistics
npm run db:stats
node scripts/quick-cleanup.js --stats
```

**Features:**
- No confirmation prompts (fast execution)
- Perfect for automated testing
- Shows before/after statistics
- Multiple modes via command line arguments

**Use when:** You need quick cleanup during development/testing

## Usage Examples

### During Development
```bash
# Quick cleanup of test data after testing
npm run cleanup:quick

# Check database status
npm run db:stats

# Full interactive cleanup when needed
npm run cleanup
```

### Before Testing
```bash
# Clean slate for testing
npm run cleanup:quick

# Run your tests
npm test

# Clean up after tests
npm run cleanup:quick
```

### Production Safety
```bash
# NEVER run these commands in production without backups!
# Always use the interactive script with confirmations
npm run cleanup
```

## What Gets Deleted

### Test Data Cleanup (Default)
- **Users** with emails containing "test" (case-insensitive)
- **Users** with first/last names containing "test" (case-insensitive)
- **Companies** with names containing "test" (case-insensitive)
- **Client profiles** associated with deleted test users

### Full Cleanup (--all flag)
- **ALL users** in the database
- **ALL companies** in the database
- **ALL client profiles** in the database

## Database Relationships Handled

The scripts properly handle database relationships:

1. **User-Company Relationships**
   - When deleting companies: User `companyId` references are removed
   - When deleting users: Associated client profiles are deleted
   - Company admin/team member arrays are updated

2. **Referential Integrity**
   - Uses MongoDB transactions for atomic operations
   - Either all related deletions succeed or all are rolled back
   - No orphaned references left in the database

## Safety Features

### Interactive Script (`cleanup-database.js`)
- ✅ Confirmation prompts for all destructive operations
- ✅ Shows detailed statistics before deletion
- ✅ "yes" confirmation required (not just "y")
- ✅ Option to exit without changes

### Quick Script (`quick-cleanup.js`)
- ✅ Atomic transactions (all-or-nothing)
- ✅ Before/after statistics
- ✅ Error handling with rollback
- ✅ Clear command line options

## Database Statistics

Both scripts show comprehensive database statistics:

```
📊 Current Database Statistics:
================================
👥 Users: 15
🏢 Companies: 8
📋 Clients: 12

👥 User Breakdown:
   company_admin: 5
   job_seeker: 10

🏢 Recent Companies:
   Test Company 1752194843115 (2024-01-15)
   Acme Corp (2024-01-14)
   Tech Startup (2024-01-13)

🧪 Test data:
   Test users: 3
   Test companies: 2
```

## Error Handling

All scripts include comprehensive error handling:

- **Database Connection Errors**: Clear error messages and graceful exit
- **Transaction Failures**: Automatic rollback of partial changes
- **Permission Errors**: Helpful error messages
- **Invalid Operations**: Safe fallbacks

## Environment Variables

The scripts respect your environment configuration:

```bash
# Default MongoDB connection
MONGODB_URI=mongodb://localhost:27017/jobportal

# Or your custom connection string
MONGODB_URI=mongodb://your-host:port/your-database
```

## Integration with Testing

Perfect for integration with your test suite:

```json
{
  "scripts": {
    "test:clean": "npm run cleanup:quick && npm test && npm run cleanup:quick",
    "test:integration": "npm run cleanup:quick && npm run test:integration"
  }
}
```

## Troubleshooting

### Common Issues

1. **Connection Failed**
   ```
   ❌ MongoDB connection failed: connect ECONNREFUSED
   ```
   - Ensure MongoDB is running
   - Check your MONGODB_URI environment variable

2. **Permission Denied**
   ```
   ❌ Error deleting data: not authorized
   ```
   - Check database user permissions
   - Ensure user has delete permissions

3. **Transaction Failed**
   ```
   ❌ Error deleting users: Transaction was aborted
   ```
   - Usually indicates a connection issue
   - Script automatically rolls back changes

### Getting Help

If you encounter issues:

1. Check the error message carefully
2. Verify MongoDB is running and accessible
3. Check your database connection string
4. Ensure you have proper permissions

## Best Practices

1. **Always backup production data** before running cleanup scripts
2. **Use test data cleanup** during development (default behavior)
3. **Use interactive script** for important operations
4. **Check statistics** before and after cleanup
5. **Test scripts** in development environment first

## Contributing

When modifying these scripts:

1. Maintain transaction safety
2. Add appropriate error handling
3. Update this README with any changes
4. Test thoroughly in development environment

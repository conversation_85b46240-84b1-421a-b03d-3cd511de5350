#!/usr/bin/env node

// Simple test for lib/database/connection.ts
// Tests the core connection functionality

const mongoose = require('mongoose')
const fs = require('fs')
const path = require('path')

// Load environment variables
function loadEnvFile() {
  const envPath = path.join(__dirname, '..', '.env.local')
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8')
    envContent.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=')
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim()
        process.env[key.trim()] = value.replace(/^"|"$/g, '')
      }
    })
  }
}

loadEnvFile()

async function testConnection() {
  console.log('🔍 Testing Database Connection from lib/database/connection.ts\n')
  
  const MONGODB_URI = process.env.MONGODB_URI
  
  if (!MONGODB_URI) {
    console.log('❌ MONGODB_URI not found in environment variables')
    return false
  }
  
  console.log('✅ Environment variables loaded')
  console.log(`   URI: ${MONGODB_URI.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@')}\n`)
  
  try {
    console.log('⏳ Connecting to MongoDB...')
    const startTime = Date.now()
    
    await mongoose.connect(MONGODB_URI, {
      bufferCommands: false,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      family: 4
    })
    
    const connectionTime = Date.now() - startTime
    console.log(`✅ Connected successfully in ${connectionTime}ms`)
    console.log(`   Database: ${mongoose.connection.name}`)
    console.log(`   Host: ${mongoose.connection.host}:${mongoose.connection.port}`)
    console.log(`   Ready State: ${mongoose.connection.readyState} (1 = connected)\n`)
    
    // Test basic operations
    console.log('🔍 Testing basic database operations...')
    
    const collections = await mongoose.connection.db.listCollections().toArray()
    console.log(`✅ Found ${collections.length} collections`)
    
    // Test quiz collections specifically
    const quizCategories = await mongoose.connection.db.collection('quizcategories').countDocuments()
    const quizQuestions = await mongoose.connection.db.collection('quizquestions').countDocuments()
    
    console.log(`✅ Quiz Categories: ${quizCategories}`)
    console.log(`✅ Quiz Questions: ${quizQuestions}`)
    
    // Test a simple query
    const sampleCategory = await mongoose.connection.db.collection('quizcategories').findOne()
    if (sampleCategory) {
      console.log(`✅ Sample category: ${sampleCategory.name}`)
    }
    
    console.log('\n🎉 All tests passed! Database connection is working perfectly.')
    
    return true
    
  } catch (error) {
    console.log(`❌ Connection failed: ${error.message}`)
    console.error('Full error:', error)
    return false
  } finally {
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect()
      console.log('✅ Disconnected from MongoDB')
    }
  }
}

// Run the test
testConnection().then(success => {
  process.exit(success ? 0 : 1)
}).catch(error => {
  console.error('❌ Test failed:', error)
  process.exit(1)
})

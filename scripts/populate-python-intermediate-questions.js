// Script to populate 50 Python Intermediate questions
const mongoose = require('mongoose')

// Database connection
const connectToDatabase = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb+srv://winstonmhango23:<EMAIL>/?retryWrites=true&w=majority&appName=jobs'
    await mongoose.connect(mongoUri)
    console.log('✅ Connected to MongoDB')
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message)
    process.exit(1)
  }
}

// Quiz Category Schema
const quizCategorySchema = new mongoose.Schema({
  name: String,
  slug: String,
  description: String,
  icon: String,
  color: String,
  skills: [String],
  difficulty: String,
  estimatedTime: Number,
  totalQuestions: Number,
  passingScore: Number,
  isActive: Boolean
}, { timestamps: true })

// Quiz Question Schema
const quizQuestionSchema = new mongoose.Schema({
  categoryId: mongoose.Schema.Types.ObjectId,
  question: String,
  options: [String],
  correctAnswer: Number,
  explanation: String,
  difficulty: String,
  points: Number,
  tags: [String],
  isActive: Boolean
}, { timestamps: true })

const QuizCategory = mongoose.model('QuizCategory', quizCategorySchema)
const QuizQuestion = mongoose.model('QuizQuestion', quizQuestionSchema)

// 50 Python Intermediate Questions
const pythonIntermediateQuestions = [
  {
    question: "What is the difference between a list and a tuple in Python?",
    options: [
      "Lists are mutable, tuples are immutable",
      "Lists are immutable, tuples are mutable", 
      "Lists are faster than tuples",
      "There is no difference"
    ],
    correctAnswer: 0,
    explanation: "Lists are mutable (can be changed after creation) while tuples are immutable (cannot be changed after creation). This makes tuples hashable and suitable as dictionary keys.",
    tags: ['Data Structures', 'Lists', 'Tuples']
  },
  {
    question: "What does the 'yield' keyword do in Python?",
    options: [
      "Returns a value and exits the function",
      "Creates a generator function that can pause and resume execution",
      "Raises an exception",
      "Imports a module"
    ],
    correctAnswer: 1,
    explanation: "The 'yield' keyword creates a generator function that can pause execution and yield values one at a time, maintaining state between calls.",
    tags: ['Generators', 'Functions', 'Yield']
  },
  {
    question: "What is a decorator in Python?",
    options: [
      "A function that modifies or extends another function",
      "A way to add comments to code",
      "A type of loop",
      "A built-in data type"
    ],
    correctAnswer: 0,
    explanation: "A decorator is a function that takes another function as an argument and extends or modifies its behavior without explicitly modifying the function itself.",
    tags: ['Decorators', 'Functions', 'Metaprogramming']
  },
  {
    question: "What is the purpose of the '__init__' method in a Python class?",
    options: [
      "To delete an object",
      "To initialize object attributes when an instance is created",
      "To define class variables",
      "To create static methods"
    ],
    correctAnswer: 1,
    explanation: "The '__init__' method is a constructor that initializes object attributes when a new instance of the class is created.",
    tags: ['Classes', 'OOP', 'Constructor']
  },
  {
    question: "What is the difference between '==' and 'is' operators in Python?",
    options: [
      "They are exactly the same",
      "'==' compares values, 'is' compares object identity",
      "'==' compares identity, 'is' compares values",
      "'is' is used for strings only"
    ],
    correctAnswer: 1,
    explanation: "The '==' operator compares the values of objects, while 'is' compares whether two variables refer to the same object in memory.",
    tags: ['Operators', 'Identity', 'Comparison']
  },
  {
    question: "What is a lambda function in Python?",
    options: [
      "A function that can only return strings",
      "An anonymous function defined with the lambda keyword",
      "A function that runs in parallel",
      "A recursive function"
    ],
    correctAnswer: 1,
    explanation: "A lambda function is an anonymous function defined using the lambda keyword. It can have any number of arguments but can only have one expression.",
    tags: ['Lambda', 'Functions', 'Anonymous Functions']
  },
  {
    question: "What does the 'with' statement do in Python?",
    options: [
      "Creates a loop",
      "Defines a function",
      "Provides context management for resource handling",
      "Imports modules"
    ],
    correctAnswer: 2,
    explanation: "The 'with' statement provides context management, ensuring proper acquisition and release of resources like files, even if an exception occurs.",
    tags: ['Context Managers', 'File Handling', 'Resource Management']
  },
  {
    question: "What is list comprehension in Python?",
    options: [
      "A way to compress lists",
      "A concise way to create lists based on existing lists",
      "A method to sort lists",
      "A way to delete list elements"
    ],
    correctAnswer: 1,
    explanation: "List comprehension provides a concise way to create lists by applying an expression to each item in an existing iterable, optionally with filtering conditions.",
    tags: ['List Comprehension', 'Lists', 'Functional Programming']
  },
  {
    question: "What is the Global Interpreter Lock (GIL) in Python?",
    options: [
      "A security feature",
      "A mechanism that prevents multiple threads from executing Python code simultaneously",
      "A way to lock variables",
      "A debugging tool"
    ],
    correctAnswer: 1,
    explanation: "The GIL is a mutex that protects access to Python objects, preventing multiple threads from executing Python bytecode simultaneously in CPython.",
    tags: ['GIL', 'Threading', 'Concurrency']
  },
  {
    question: "What is the difference between 'append()' and 'extend()' methods for lists?",
    options: [
      "They do the same thing",
      "'append()' adds a single element, 'extend()' adds multiple elements",
      "'extend()' adds a single element, 'append()' adds multiple elements",
      "'append()' is faster than 'extend()'"
    ],
    correctAnswer: 1,
    explanation: "'append()' adds a single element to the end of a list, while 'extend()' adds all elements from an iterable to the end of the list.",
    tags: ['Lists', 'Methods', 'Data Manipulation']
  },
  {
    question: "What is multiple inheritance in Python?",
    options: [
      "A class inheriting from multiple parent classes",
      "Creating multiple instances of a class",
      "A class with multiple methods",
      "Importing multiple modules"
    ],
    correctAnswer: 0,
    explanation: "Multiple inheritance allows a class to inherit attributes and methods from more than one parent class.",
    tags: ['OOP', 'Inheritance', 'Classes']
  },
  {
    question: "What is the purpose of the 'super()' function?",
    options: [
      "To create a superclass",
      "To access methods and properties of a parent class",
      "To make a function run faster",
      "To handle exceptions"
    ],
    correctAnswer: 1,
    explanation: "The 'super()' function provides access to methods and properties of a parent class from a child class, enabling proper method resolution in inheritance.",
    tags: ['OOP', 'Inheritance', 'Super']
  },
  {
    question: "What is a property in Python classes?",
    options: [
      "A class variable",
      "A method that can be accessed like an attribute",
      "A way to inherit from multiple classes",
      "A type of exception"
    ],
    correctAnswer: 1,
    explanation: "A property is a method that can be accessed like an attribute, allowing you to define custom getter, setter, and deleter behavior.",
    tags: ['Properties', 'OOP', 'Encapsulation']
  },
  {
    question: "What is the difference between 'deepcopy' and 'copy' in Python?",
    options: [
      "They are the same",
      "'copy' creates a shallow copy, 'deepcopy' creates a complete independent copy",
      "'deepcopy' is faster than 'copy'",
      "'copy' works only with lists"
    ],
    correctAnswer: 1,
    explanation: "'copy' creates a shallow copy (new object but references to nested objects), while 'deepcopy' creates a complete independent copy of the object and all nested objects.",
    tags: ['Copy', 'Objects', 'Memory Management']
  },
  {
    question: "What is a closure in Python?",
    options: [
      "A way to close files",
      "A function that captures variables from its enclosing scope",
      "A type of loop",
      "A way to end a program"
    ],
    correctAnswer: 1,
    explanation: "A closure is a function that captures and retains access to variables from its enclosing scope, even after the outer function has finished executing.",
    tags: ['Closures', 'Functions', 'Scope']
  },
  {
    question: "What is the difference between 'staticmethod' and 'classmethod' decorators?",
    options: [
      "They are the same",
      "'staticmethod' doesn't receive any implicit first argument, 'classmethod' receives the class as first argument",
      "'classmethod' is faster than 'staticmethod'",
      "'staticmethod' can only be used with strings"
    ],
    correctAnswer: 1,
    explanation: "'staticmethod' doesn't receive any implicit first argument and behaves like a regular function. 'classmethod' receives the class (cls) as the first argument.",
    tags: ['Static Methods', 'Class Methods', 'Decorators']
  },
  {
    question: "What is the purpose of the '__str__' and '__repr__' methods?",
    options: [
      "They do the same thing",
      "'__str__' is for human-readable representation, '__repr__' is for developer representation",
      "'__repr__' is for users, '__str__' is for developers",
      "They are used for string concatenation"
    ],
    correctAnswer: 1,
    explanation: "'__str__' provides a human-readable string representation, while '__repr__' provides an unambiguous representation primarily for developers and debugging.",
    tags: ['Magic Methods', 'String Representation', 'OOP']
  },
  {
    question: "What is a generator expression in Python?",
    options: [
      "A way to generate random numbers",
      "A memory-efficient way to create generators using syntax similar to list comprehensions",
      "A method to create classes",
      "A way to handle exceptions"
    ],
    correctAnswer: 1,
    explanation: "Generator expressions provide a memory-efficient way to create generators using syntax similar to list comprehensions, but with parentheses instead of square brackets.",
    tags: ['Generators', 'Generator Expressions', 'Memory Efficiency']
  },
  {
    question: "What is the difference between 'args' and 'kwargs' in function definitions?",
    options: [
      "'args' handles positional arguments, 'kwargs' handles keyword arguments",
      "'kwargs' handles positional arguments, 'args' handles keyword arguments",
      "They are the same thing",
      "'args' is for strings, 'kwargs' is for numbers"
    ],
    correctAnswer: 0,
    explanation: "'*args' allows a function to accept any number of positional arguments, while '**kwargs' allows it to accept any number of keyword arguments.",
    tags: ['Functions', 'Arguments', 'Variable Arguments']
  },
  {
    question: "What is monkey patching in Python?",
    options: [
      "A way to fix bugs",
      "Dynamically modifying classes or modules at runtime",
      "A debugging technique",
      "A way to optimize code"
    ],
    correctAnswer: 1,
    explanation: "Monkey patching is the practice of dynamically modifying classes or modules at runtime, allowing you to extend or modify existing code without changing the source.",
    tags: ['Monkey Patching', 'Dynamic Modification', 'Runtime']
  },
  {
    question: "What is the purpose of the 'enumerate()' function?",
    options: [
      "To count items in a list",
      "To add index numbers to an iterable",
      "To sort a list",
      "To reverse a list"
    ],
    correctAnswer: 1,
    explanation: "The 'enumerate()' function adds counter/index numbers to an iterable and returns it as an enumerate object, useful for getting both index and value in loops.",
    tags: ['Enumerate', 'Iteration', 'Built-in Functions']
  },
  {
    question: "What is the difference between 'remove()' and 'pop()' methods for lists?",
    options: [
      "'remove()' removes by value, 'pop()' removes by index and returns the value",
      "'pop()' removes by value, 'remove()' removes by index",
      "They do the same thing",
      "'remove()' is faster than 'pop()'"
    ],
    correctAnswer: 0,
    explanation: "'remove()' removes the first occurrence of a specified value, while 'pop()' removes an element at a specified index (or last element if no index) and returns it.",
    tags: ['Lists', 'Methods', 'Data Manipulation']
  },
  {
    question: "What is a metaclass in Python?",
    options: [
      "A class that inherits from multiple classes",
      "A class whose instances are classes themselves",
      "A class with multiple methods",
      "A class that cannot be instantiated"
    ],
    correctAnswer: 1,
    explanation: "A metaclass is a class whose instances are classes themselves. It defines how classes are constructed and can be used to customize class creation.",
    tags: ['Metaclasses', 'Advanced OOP', 'Class Creation']
  },
  {
    question: "What is the purpose of the 'zip()' function?",
    options: [
      "To compress files",
      "To combine multiple iterables element-wise",
      "To sort lists",
      "To create dictionaries"
    ],
    correctAnswer: 1,
    explanation: "The 'zip()' function combines multiple iterables (lists, tuples, etc.) element-wise, creating tuples of corresponding elements.",
    tags: ['Zip', 'Iterables', 'Built-in Functions']
  },
  {
    question: "What is the difference between 'shallow copy' and 'deep copy'?",
    options: [
      "Shallow copy copies nested objects, deep copy doesn't",
      "Deep copy creates independent copies of nested objects, shallow copy shares references",
      "They are the same",
      "Shallow copy is faster but less accurate"
    ],
    correctAnswer: 1,
    explanation: "Shallow copy creates a new object but inserts references to nested objects. Deep copy creates a new object and recursively copies all nested objects.",
    tags: ['Copy', 'Objects', 'Memory Management']
  },
  {
    question: "What is the purpose of the '__call__' method?",
    options: [
      "To call other methods",
      "To make an object callable like a function",
      "To handle phone calls",
      "To create new instances"
    ],
    correctAnswer: 1,
    explanation: "The '__call__' method allows an object to be called like a function. When obj() is called, Python executes obj.__call__().",
    tags: ['Magic Methods', 'Callable Objects', 'OOP']
  },
  {
    question: "What is a context manager in Python?",
    options: [
      "A way to manage memory",
      "An object that defines methods for use with 'with' statements",
      "A type of decorator",
      "A way to handle exceptions"
    ],
    correctAnswer: 1,
    explanation: "A context manager is an object that defines methods (__enter__ and __exit__) to be used with 'with' statements for resource management.",
    tags: ['Context Managers', 'Resource Management', 'With Statement']
  },
  {
    question: "What is the difference between 'map()' and list comprehension?",
    options: [
      "map() returns a list, list comprehension returns an iterator",
      "map() returns an iterator, list comprehension returns a list",
      "They are exactly the same",
      "map() is only for numbers"
    ],
    correctAnswer: 1,
    explanation: "map() returns an iterator (map object) that applies a function to each item, while list comprehension directly creates and returns a list.",
    tags: ['Map', 'List Comprehension', 'Functional Programming']
  },
  {
    question: "What is the purpose of the 'filter()' function?",
    options: [
      "To sort elements",
      "To select elements from an iterable based on a condition",
      "To remove duplicates",
      "To reverse a list"
    ],
    correctAnswer: 1,
    explanation: "The 'filter()' function creates an iterator from elements of an iterable for which a function returns True, effectively filtering based on a condition.",
    tags: ['Filter', 'Functional Programming', 'Iteration']
  },
  {
    question: "What is the difference between 'break' and 'continue' statements?",
    options: [
      "'break' exits the loop, 'continue' skips to the next iteration",
      "'continue' exits the loop, 'break' skips to the next iteration",
      "They do the same thing",
      "'break' is only for while loops"
    ],
    correctAnswer: 0,
    explanation: "'break' completely exits the current loop, while 'continue' skips the rest of the current iteration and moves to the next iteration.",
    tags: ['Control Flow', 'Loops', 'Break Continue']
  },
  {
    question: "What is the purpose of the 'reduce()' function from functools?",
    options: [
      "To reduce the size of a list",
      "To apply a function cumulatively to items in an iterable",
      "To remove elements from a list",
      "To sort a list"
    ],
    correctAnswer: 1,
    explanation: "The 'reduce()' function applies a function of two arguments cumulatively to the items of an iterable, from left to right, to reduce the iterable to a single value.",
    tags: ['Reduce', 'Functools', 'Functional Programming']
  },
  {
    question: "What is the difference between 'is None' and '== None'?",
    options: [
      "They are exactly the same",
      "'is None' checks identity, '== None' checks value equality",
      "'== None' is faster than 'is None'",
      "'is None' only works with strings"
    ],
    correctAnswer: 1,
    explanation: "'is None' checks if the object is the same None object in memory (identity), while '== None' checks value equality. 'is None' is preferred and faster.",
    tags: ['None', 'Identity', 'Comparison']
  },
  {
    question: "What is a descriptor in Python?",
    options: [
      "A way to describe functions",
      "An object that defines how attribute access is handled",
      "A type of comment",
      "A debugging tool"
    ],
    correctAnswer: 1,
    explanation: "A descriptor is an object that defines how attribute access is handled through __get__, __set__, and __delete__ methods. Properties are implemented using descriptors.",
    tags: ['Descriptors', 'Attributes', 'Advanced OOP']
  },
  {
    question: "What is the purpose of the 'any()' and 'all()' functions?",
    options: [
      "'any()' returns True if any element is True, 'all()' returns True if all elements are True",
      "'all()' returns True if any element is True, 'any()' returns True if all elements are True",
      "They do the same thing",
      "They are used for string operations only"
    ],
    correctAnswer: 0,
    explanation: "'any()' returns True if at least one element in an iterable is True, while 'all()' returns True only if all elements in an iterable are True.",
    tags: ['Any', 'All', 'Boolean Operations']
  },
  {
    question: "What is the difference between 'del' and 'remove()' for lists?",
    options: [
      "'del' removes by index, 'remove()' removes by value",
      "'remove()' removes by index, 'del' removes by value",
      "They do the same thing",
      "'del' is only for dictionaries"
    ],
    correctAnswer: 0,
    explanation: "'del' removes an element at a specific index or slice, while 'remove()' removes the first occurrence of a specific value.",
    tags: ['Lists', 'Deletion', 'Data Manipulation']
  },
  {
    question: "What is method resolution order (MRO) in Python?",
    options: [
      "The order in which methods are defined",
      "The order in which Python searches for methods in inheritance hierarchies",
      "The speed of method execution",
      "The order of method parameters"
    ],
    correctAnswer: 1,
    explanation: "MRO is the order in which Python searches for methods in class inheritance hierarchies, following the C3 linearization algorithm.",
    tags: ['MRO', 'Inheritance', 'Method Resolution']
  },
  {
    question: "What is the purpose of the 'setdefault()' method for dictionaries?",
    options: [
      "To set default values for all keys",
      "To get a value or set a default if the key doesn't exist",
      "To delete default values",
      "To sort dictionary keys"
    ],
    correctAnswer: 1,
    explanation: "'setdefault()' returns the value of a key if it exists, or sets and returns a default value if the key doesn't exist.",
    tags: ['Dictionaries', 'Default Values', 'Data Structures']
  },
  {
    question: "What is the difference between 'sort()' and 'sorted()'?",
    options: [
      "'sort()' modifies the original list, 'sorted()' returns a new sorted list",
      "'sorted()' modifies the original list, 'sort()' returns a new sorted list",
      "They do the same thing",
      "'sort()' is faster than 'sorted()'"
    ],
    correctAnswer: 0,
    explanation: "'sort()' is a list method that sorts the list in-place (modifies original), while 'sorted()' is a built-in function that returns a new sorted list.",
    tags: ['Sorting', 'Lists', 'Built-in Functions']
  },
  {
    question: "What is a namespace in Python?",
    options: [
      "A way to name variables",
      "A mapping from names to objects",
      "A type of function",
      "A way to import modules"
    ],
    correctAnswer: 1,
    explanation: "A namespace is a mapping from names to objects, providing a way to organize and access variables, functions, and other objects in different scopes.",
    tags: ['Namespaces', 'Scope', 'Variables']
  },
  {
    question: "What is the purpose of the 'itertools' module?",
    options: [
      "To create iterations",
      "To provide efficient looping constructs and iterator functions",
      "To handle file iterations",
      "To create tool functions"
    ],
    correctAnswer: 1,
    explanation: "The 'itertools' module provides efficient looping constructs and functions for creating iterators, including combinations, permutations, and infinite iterators.",
    tags: ['Itertools', 'Iterators', 'Functional Programming']
  },
  {
    question: "What is the difference between 'encode()' and 'decode()' methods?",
    options: [
      "'encode()' converts string to bytes, 'decode()' converts bytes to string",
      "'decode()' converts string to bytes, 'encode()' converts bytes to string",
      "They do the same thing",
      "They are only for numbers"
    ],
    correctAnswer: 0,
    explanation: "'encode()' converts a string to bytes using a specified encoding, while 'decode()' converts bytes back to a string using a specified encoding.",
    tags: ['Encoding', 'Strings', 'Bytes']
  },
  {
    question: "What is a weak reference in Python?",
    options: [
      "A reference that doesn't work properly",
      "A reference that doesn't prevent garbage collection of the referenced object",
      "A reference to small objects only",
      "A temporary reference"
    ],
    correctAnswer: 1,
    explanation: "A weak reference is a reference to an object that doesn't prevent the object from being garbage collected when there are no other references to it.",
    tags: ['Weak References', 'Garbage Collection', 'Memory Management']
  },
  {
    question: "What is the purpose of the 'collections' module?",
    options: [
      "To collect garbage",
      "To provide specialized container datatypes",
      "To handle file collections",
      "To create collections of functions"
    ],
    correctAnswer: 1,
    explanation: "The 'collections' module provides specialized container datatypes like namedtuple, deque, Counter, defaultdict, and OrderedDict.",
    tags: ['Collections', 'Data Structures', 'Containers']
  },
  {
    question: "What is the difference between 'classmethod' and instance method?",
    options: [
      "Classmethod receives the class as first argument, instance method receives the instance",
      "Instance method receives the class as first argument, classmethod receives the instance",
      "They are the same",
      "Classmethod is faster"
    ],
    correctAnswer: 0,
    explanation: "A classmethod receives the class (cls) as the first argument and can be called on the class itself, while an instance method receives the instance (self) as the first argument.",
    tags: ['Class Methods', 'Instance Methods', 'OOP']
  },
  {
    question: "What is the purpose of the '__slots__' attribute in classes?",
    options: [
      "To create time slots",
      "To restrict the attributes that can be added to instances and save memory",
      "To define method slots",
      "To handle inheritance"
    ],
    correctAnswer: 1,
    explanation: "'__slots__' restricts the attributes that can be added to instances of a class and can save memory by preventing the creation of __dict__ for each instance.",
    tags: ['Slots', 'Memory Optimization', 'Classes']
  },
  {
    question: "What is the difference between 'raise' and 'raise from' in exception handling?",
    options: [
      "They do the same thing",
      "'raise from' preserves the original exception context for chaining",
      "'raise' is faster than 'raise from'",
      "'raise from' is only for custom exceptions"
    ],
    correctAnswer: 1,
    explanation: "'raise from' explicitly chains exceptions, preserving the original exception context and providing better debugging information.",
    tags: ['Exceptions', 'Exception Chaining', 'Error Handling']
  },
  {
    question: "What is a coroutine in Python?",
    options: [
      "A type of loop",
      "A function that can be paused and resumed, used in async programming",
      "A way to handle errors",
      "A type of class"
    ],
    correctAnswer: 1,
    explanation: "A coroutine is a function that can be paused and resumed, allowing for cooperative multitasking and asynchronous programming using async/await syntax.",
    tags: ['Coroutines', 'Async Programming', 'Concurrency']
  },
  {
    question: "What is the purpose of the 'functools.wraps' decorator?",
    options: [
      "To wrap functions in try-catch blocks",
      "To preserve the original function's metadata when creating decorators",
      "To create wrapper classes",
      "To handle function arguments"
    ],
    correctAnswer: 1,
    explanation: "'functools.wraps' is used in decorator functions to preserve the original function's metadata (name, docstring, etc.) in the decorated function.",
    tags: ['Functools', 'Decorators', 'Metadata']
  },
  {
    question: "What is the difference between 'hasattr()' and 'getattr()' functions?",
    options: [
      "'hasattr()' checks if an attribute exists, 'getattr()' gets the attribute value",
      "'getattr()' checks if an attribute exists, 'hasattr()' gets the attribute value",
      "They do the same thing",
      "'hasattr()' is only for classes"
    ],
    correctAnswer: 0,
    explanation: "'hasattr()' returns True if an object has a specified attribute, while 'getattr()' returns the value of a specified attribute (with optional default).",
    tags: ['Attributes', 'Introspection', 'Built-in Functions']
  },
  {
    question: "What is the purpose of the 'abc' module in Python?",
    options: [
      "To handle alphabets",
      "To create abstract base classes",
      "To perform basic calculations",
      "To handle ASCII characters"
    ],
    correctAnswer: 1,
    explanation: "The 'abc' (Abstract Base Classes) module provides infrastructure for defining abstract base classes, which cannot be instantiated and require subclasses to implement specific methods.",
    tags: ['ABC', 'Abstract Classes', 'OOP']
  }
]

const populateQuestions = async () => {
  try {
    await connectToDatabase()
    
    // Find or create the Python Intermediate category
    let category = await QuizCategory.findOne({ 
      $or: [
        { name: 'Python Intermediate' },
        { slug: 'python-intermediate' }
      ]
    })
    
    if (!category) {
      console.log('Creating Python Intermediate category...')
      category = new QuizCategory({
        name: 'Python Intermediate',
        slug: 'python-intermediate',
        description: 'Test your intermediate Python programming skills including OOP, decorators, generators, and advanced concepts.',
        icon: '🐍',
        color: 'bg-green-500',
        skills: ['Python', 'OOP', 'Decorators', 'Generators', 'Data Structures', 'Functions'],
        difficulty: 'intermediate',
        estimatedTime: 25,
        totalQuestions: 50,
        passingScore: 70,
        isActive: true
      })
      await category.save()
      console.log('✅ Python Intermediate category created')
    } else {
      console.log('✅ Found existing Python Intermediate category')
    }
    
    // Delete existing questions for this category to avoid duplicates
    await QuizQuestion.deleteMany({ categoryId: category._id })
    console.log('🗑️ Cleared existing questions for Python Intermediate category')
    
    console.log('📝 Creating 50 Python Intermediate questions...')
    
    // Create the first 15 questions
    const questionsToInsert = pythonIntermediateQuestions.map(q => ({
      categoryId: category._id,
      question: q.question,
      options: q.options,
      correctAnswer: q.correctAnswer,
      explanation: q.explanation,
      difficulty: 'intermediate',
      points: 10,
      tags: q.tags,
      isActive: true
    }))
    
    await QuizQuestion.insertMany(questionsToInsert)
    console.log(`✅ Created ${questionsToInsert.length} questions`)
    
    console.log('🎉 Python Intermediate questions populated successfully!')
    console.log(`📊 Category: ${category.name}`)
    console.log(`📝 Total Questions: ${questionsToInsert.length}`)
    console.log(`🎯 Difficulty: ${category.difficulty}`)
    console.log(`⏱️ Estimated Time: ${category.estimatedTime} minutes`)
    
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Error populating questions:', error)
    process.exit(1)
  }
}

// Run the script
populateQuestions()

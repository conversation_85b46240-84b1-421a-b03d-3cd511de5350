// Comprehensive script to ensure each category has exactly 20 high-quality questions
const mongoose = require('mongoose')

// Database connection
const connectToDatabase = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb+srv://winstonmhango23:<EMAIL>/?retryWrites=true&w=majority&appName=jobs'
    await mongoose.connect(mongoUri)
    console.log('✅ Connected to MongoDB')
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message)
    process.exit(1)
  }
}

// Schemas
const quizCategorySchema = new mongoose.Schema({
  name: String,
  slug: String,
  description: String,
  icon: String,
  color: String,
  skills: [String],
  difficulty: String,
  estimatedTime: Number,
  totalQuestions: Number,
  passingScore: Number,
  isActive: Boolean
}, { timestamps: true })

const quizQuestionSchema = new mongoose.Schema({
  categoryId: mongoose.Schema.Types.ObjectId,
  question: String,
  options: [String],
  correctAnswer: Number,
  explanation: String,
  difficulty: String,
  points: Number,
  tags: [String],
  isActive: Boolean
}, { timestamps: true })

const QuizCategory = mongoose.model('QuizCategory', quizCategorySchema)
const QuizQuestion = mongoose.model('QuizQuestion', quizQuestionSchema)

// Comprehensive question sets for each category type
const questionSets = {
  frontend: [
    {
      question: "What is the difference between CSS Grid and Flexbox?",
      options: [
        "Grid is for 2D layouts, Flexbox is for 1D layouts",
        "Flexbox is for 2D layouts, Grid is for 1D layouts", 
        "They are exactly the same",
        "Grid is older than Flexbox"
      ],
      correctAnswer: 0,
      explanation: "CSS Grid is designed for 2D layouts (rows and columns), while Flexbox is designed for 1D layouts (either row or column).",
      tags: ['CSS', 'Layout', 'Grid', 'Flexbox']
    },
    {
      question: "What is the purpose of semantic HTML?",
      options: [
        "To make HTML look better",
        "To provide meaning and structure to web content",
        "To make websites load faster",
        "To add animations"
      ],
      correctAnswer: 1,
      explanation: "Semantic HTML provides meaning and structure to web content, improving accessibility, SEO, and code maintainability.",
      tags: ['HTML', 'Semantics', 'Accessibility']
    },
    {
      question: "What is the CSS box model?",
      options: [
        "A way to create boxes",
        "The rectangular layout model that describes how elements are rendered",
        "A CSS framework",
        "A design pattern"
      ],
      correctAnswer: 1,
      explanation: "The CSS box model describes how elements are rendered as rectangular boxes with content, padding, border, and margin.",
      tags: ['CSS', 'Box Model', 'Layout']
    },
    {
      question: "What is the difference between 'display: none' and 'visibility: hidden'?",
      options: [
        "'display: none' removes element from layout, 'visibility: hidden' keeps space",
        "'visibility: hidden' removes element from layout, 'display: none' keeps space",
        "They do the same thing",
        "'display: none' is faster"
      ],
      correctAnswer: 0,
      explanation: "'display: none' completely removes the element from the layout, while 'visibility: hidden' hides the element but preserves its space.",
      tags: ['CSS', 'Display', 'Visibility']
    },
    {
      question: "What is a CSS preprocessor?",
      options: [
        "A tool that processes CSS before it's used",
        "A CSS framework",
        "A browser extension",
        "A JavaScript library"
      ],
      correctAnswer: 0,
      explanation: "A CSS preprocessor is a tool that extends CSS with features like variables, nesting, and functions, then compiles to regular CSS.",
      tags: ['CSS', 'Preprocessor', 'SASS', 'LESS']
    },
    {
      question: "What is the purpose of media queries in CSS?",
      options: [
        "To query databases",
        "To apply different styles based on device characteristics",
        "To load media files",
        "To create animations"
      ],
      correctAnswer: 1,
      explanation: "Media queries allow you to apply different CSS styles based on device characteristics like screen size, orientation, and resolution.",
      tags: ['CSS', 'Media Queries', 'Responsive Design']
    },
    {
      question: "What is the difference between 'em' and 'rem' units in CSS?",
      options: [
        "'em' is relative to parent element, 'rem' is relative to root element",
        "'rem' is relative to parent element, 'em' is relative to root element",
        "They are exactly the same",
        "'em' is for fonts, 'rem' is for spacing"
      ],
      correctAnswer: 0,
      explanation: "'em' units are relative to the font size of the parent element, while 'rem' units are relative to the font size of the root element.",
      tags: ['CSS', 'Units', 'Typography']
    },
    {
      question: "What is the purpose of the 'alt' attribute in HTML images?",
      options: [
        "To make images load faster",
        "To provide alternative text for accessibility and SEO",
        "To add image effects",
        "To resize images"
      ],
      correctAnswer: 1,
      explanation: "The 'alt' attribute provides alternative text that describes the image for screen readers and search engines, improving accessibility and SEO.",
      tags: ['HTML', 'Accessibility', 'Images', 'SEO']
    },
    {
      question: "What is CSS specificity?",
      options: [
        "How specific CSS rules are",
        "The algorithm that determines which CSS rules apply to an element",
        "A CSS property",
        "A way to write specific CSS"
      ],
      correctAnswer: 1,
      explanation: "CSS specificity is the algorithm that determines which CSS rules apply to an element when multiple rules target the same element.",
      tags: ['CSS', 'Specificity', 'Selectors']
    },
    {
      question: "What is the difference between 'position: absolute' and 'position: relative'?",
      options: [
        "'absolute' positions relative to nearest positioned ancestor, 'relative' positions relative to normal position",
        "'relative' positions relative to nearest positioned ancestor, 'absolute' positions relative to normal position",
        "They do the same thing",
        "'absolute' is faster than 'relative'"
      ],
      correctAnswer: 0,
      explanation: "'position: absolute' positions an element relative to its nearest positioned ancestor, while 'position: relative' positions relative to its normal position.",
      tags: ['CSS', 'Positioning', 'Layout']
    },
    {
      question: "What is the purpose of CSS variables (custom properties)?",
      options: [
        "To create animations",
        "To store and reuse values throughout a stylesheet",
        "To make CSS load faster",
        "To add JavaScript functionality"
      ],
      correctAnswer: 1,
      explanation: "CSS variables (custom properties) allow you to store and reuse values throughout a stylesheet, making maintenance easier and enabling dynamic styling.",
      tags: ['CSS', 'Variables', 'Custom Properties']
    },
    {
      question: "What is the difference between 'inline', 'block', and 'inline-block' elements?",
      options: [
        "'inline' flows with text, 'block' takes full width, 'inline-block' combines both behaviors",
        "'block' flows with text, 'inline' takes full width, 'inline-block' is deprecated",
        "They all do the same thing",
        "'inline-block' is the fastest"
      ],
      correctAnswer: 0,
      explanation: "'inline' elements flow with text content, 'block' elements take full width and start new lines, 'inline-block' combines both behaviors.",
      tags: ['CSS', 'Display', 'Layout']
    },
    {
      question: "What is the purpose of the viewport meta tag?",
      options: [
        "To add metadata",
        "To control how a page is displayed on mobile devices",
        "To improve SEO",
        "To add animations"
      ],
      correctAnswer: 1,
      explanation: "The viewport meta tag controls how a web page is displayed on mobile devices, setting the viewport width and initial scale.",
      tags: ['HTML', 'Viewport', 'Mobile', 'Responsive']
    },
    {
      question: "What is CSS flexbox alignment?",
      options: [
        "A way to align text",
        "Properties that control how flex items are aligned within a flex container",
        "A CSS framework",
        "A JavaScript library"
      ],
      correctAnswer: 1,
      explanation: "Flexbox alignment properties like justify-content and align-items control how flex items are positioned within a flex container.",
      tags: ['CSS', 'Flexbox', 'Alignment']
    },
    {
      question: "What is the difference between 'margin' and 'padding' in CSS?",
      options: [
        "'margin' is space outside the element, 'padding' is space inside the element",
        "'padding' is space outside the element, 'margin' is space inside the element",
        "They do the same thing",
        "'margin' is for text, 'padding' is for images"
      ],
      correctAnswer: 0,
      explanation: "'margin' creates space outside an element's border, while 'padding' creates space inside an element between the content and border.",
      tags: ['CSS', 'Box Model', 'Spacing']
    },
    {
      question: "What is the purpose of CSS transitions?",
      options: [
        "To transition between pages",
        "To create smooth animations between CSS property changes",
        "To load CSS files",
        "To change CSS frameworks"
      ],
      correctAnswer: 1,
      explanation: "CSS transitions create smooth animations when CSS properties change, providing a better user experience than instant changes.",
      tags: ['CSS', 'Transitions', 'Animations']
    },
    {
      question: "What is the difference between 'class' and 'id' selectors in CSS?",
      options: [
        "'class' can be used multiple times, 'id' should be unique per page",
        "'id' can be used multiple times, 'class' should be unique per page",
        "They are exactly the same",
        "'class' is faster than 'id'"
      ],
      correctAnswer: 0,
      explanation: "'class' selectors can be applied to multiple elements, while 'id' selectors should be unique and used only once per page.",
      tags: ['CSS', 'Selectors', 'Class', 'ID']
    },
    {
      question: "What is the purpose of CSS pseudo-classes?",
      options: [
        "To create fake classes",
        "To style elements based on their state or position",
        "To add JavaScript functionality",
        "To improve performance"
      ],
      correctAnswer: 1,
      explanation: "CSS pseudo-classes allow you to style elements based on their state (like :hover, :focus) or position (like :first-child, :nth-child).",
      tags: ['CSS', 'Pseudo-classes', 'Selectors']
    },
    {
      question: "What is the CSS 'z-index' property used for?",
      options: [
        "To zoom elements",
        "To control the stacking order of positioned elements",
        "To create 3D effects",
        "To set element width"
      ],
      correctAnswer: 1,
      explanation: "The 'z-index' property controls the stacking order of positioned elements, determining which elements appear in front of others.",
      tags: ['CSS', 'Z-index', 'Stacking', 'Positioning']
    },
    {
      question: "What is the difference between CSS Grid and Flexbox for layout?",
      options: [
        "Grid is better for complex 2D layouts, Flexbox is better for simpler 1D layouts",
        "Flexbox is better for complex 2D layouts, Grid is better for simpler 1D layouts",
        "They should never be used together",
        "Grid is deprecated"
      ],
      correctAnswer: 0,
      explanation: "CSS Grid excels at complex 2D layouts with precise control, while Flexbox is ideal for simpler 1D layouts and component-level design.",
      tags: ['CSS', 'Grid', 'Flexbox', 'Layout']
    }
  ],
  backend: [
    {
      question: "What is the difference between SQL and NoSQL databases?",
      options: [
        "SQL uses structured data with relationships, NoSQL uses flexible document-based storage",
        "NoSQL uses structured data, SQL uses documents",
        "They are exactly the same",
        "SQL is newer than NoSQL"
      ],
      correctAnswer: 0,
      explanation: "SQL databases use structured data with predefined schemas and relationships, while NoSQL databases offer flexible, document-based storage without fixed schemas.",
      tags: ['Database', 'SQL', 'NoSQL']
    },
    {
      question: "What is an API endpoint?",
      options: [
        "The end of an API",
        "A specific URL where an API can be accessed",
        "A type of database",
        "A programming language"
      ],
      correctAnswer: 1,
      explanation: "An API endpoint is a specific URL where an API can be accessed, typically corresponding to a specific function or resource.",
      tags: ['API', 'Endpoints', 'Web Services']
    },
    {
      question: "What is the difference between GET and POST HTTP methods?",
      options: [
        "GET retrieves data, POST sends data to create/update resources",
        "POST retrieves data, GET sends data",
        "They do the same thing",
        "GET is faster than POST"
      ],
      correctAnswer: 0,
      explanation: "GET is used to retrieve data from a server, while POST is used to send data to a server to create or update resources.",
      tags: ['HTTP', 'Methods', 'REST']
    },
    {
      question: "What is middleware in backend development?",
      options: [
        "Software in the middle of two applications",
        "Functions that execute during the request-response cycle",
        "A type of database",
        "A programming language"
      ],
      correctAnswer: 1,
      explanation: "Middleware functions execute during the request-response cycle, often used for authentication, logging, or data processing.",
      tags: ['Middleware', 'Request-Response', 'Server']
    },
    {
      question: "What is database indexing?",
      options: [
        "Numbering database records",
        "A data structure that improves query performance",
        "A way to backup databases",
        "A type of database"
      ],
      correctAnswer: 1,
      explanation: "Database indexing creates data structures that improve the speed of data retrieval operations on a database table.",
      tags: ['Database', 'Indexing', 'Performance']
    }
  ],
  uiux: [
    {
      question: "What is the difference between UI and UX design?",
      options: [
        "UI focuses on visual interface, UX focuses on user experience and usability",
        "UX focuses on visual interface, UI focuses on user experience",
        "They are exactly the same",
        "UI is more important than UX"
      ],
      correctAnswer: 0,
      explanation: "UI (User Interface) design focuses on the visual and interactive elements, while UX (User Experience) design focuses on the overall user journey and usability.",
      tags: ['UI', 'UX', 'Design Principles']
    },
    {
      question: "What is a wireframe in UX design?",
      options: [
        "A type of wire",
        "A low-fidelity structural blueprint of a page or app",
        "A high-fidelity design mockup",
        "A programming framework"
      ],
      correctAnswer: 1,
      explanation: "A wireframe is a low-fidelity structural blueprint that shows the basic layout and functionality of a page or application without detailed design elements.",
      tags: ['Wireframes', 'UX', 'Design Process']
    },
    {
      question: "What is the purpose of user personas in UX design?",
      options: [
        "To create fictional characters",
        "To represent target users and guide design decisions",
        "To test applications",
        "To write documentation"
      ],
      correctAnswer: 1,
      explanation: "User personas are fictional characters based on research that represent different user types and help guide design decisions throughout the development process.",
      tags: ['Personas', 'User Research', 'UX']
    },
    {
      question: "What is accessibility in web design?",
      options: [
        "Making websites easy to access",
        "Designing websites to be usable by people with disabilities",
        "Making websites load faster",
        "Creating mobile-friendly designs"
      ],
      correctAnswer: 1,
      explanation: "Accessibility in web design means creating websites and applications that can be used by people with various disabilities, ensuring equal access to information.",
      tags: ['Accessibility', 'Inclusive Design', 'WCAG']
    },
    {
      question: "What is the principle of visual hierarchy in design?",
      options: [
        "Organizing elements by importance to guide user attention",
        "Making everything the same size",
        "Using only one color",
        "Placing elements randomly"
      ],
      correctAnswer: 0,
      explanation: "Visual hierarchy is the arrangement of elements in order of importance, using size, color, contrast, and positioning to guide the user's attention.",
      tags: ['Visual Hierarchy', 'Design Principles', 'UI']
    }
  ],
  datascience: [
    {
      question: "What is the difference between supervised and unsupervised learning?",
      options: [
        "Supervised learning uses labeled data, unsupervised learning finds patterns in unlabeled data",
        "Unsupervised learning uses labeled data, supervised learning uses unlabeled data",
        "They are the same thing",
        "Supervised learning is always better"
      ],
      correctAnswer: 0,
      explanation: "Supervised learning uses labeled training data to learn patterns, while unsupervised learning finds hidden patterns in data without labels.",
      tags: ['Machine Learning', 'Supervised Learning', 'Unsupervised Learning']
    },
    {
      question: "What is data preprocessing in machine learning?",
      options: [
        "Processing data before analysis",
        "Cleaning and preparing data for machine learning algorithms",
        "Storing data in databases",
        "Visualizing data"
      ],
      correctAnswer: 1,
      explanation: "Data preprocessing involves cleaning, transforming, and preparing raw data to make it suitable for machine learning algorithms.",
      tags: ['Data Preprocessing', 'Data Cleaning', 'Machine Learning']
    },
    {
      question: "What is overfitting in machine learning?",
      options: [
        "When a model performs too well on training data but poorly on new data",
        "When a model performs poorly on all data",
        "When a model is too simple",
        "When data is too large"
      ],
      correctAnswer: 0,
      explanation: "Overfitting occurs when a model learns the training data too well, including noise, resulting in poor performance on new, unseen data.",
      tags: ['Overfitting', 'Model Performance', 'Machine Learning']
    },
    {
      question: "What is cross-validation in machine learning?",
      options: [
        "Validating data twice",
        "A technique to assess model performance by testing on different data subsets",
        "A way to clean data",
        "A type of algorithm"
      ],
      correctAnswer: 1,
      explanation: "Cross-validation is a technique that divides data into multiple subsets to train and test a model multiple times, providing a more robust performance assessment.",
      tags: ['Cross-validation', 'Model Evaluation', 'Machine Learning']
    },
    {
      question: "What is feature engineering in data science?",
      options: [
        "Building engineering features",
        "Creating new features from existing data to improve model performance",
        "Removing features from data",
        "A type of algorithm"
      ],
      correctAnswer: 1,
      explanation: "Feature engineering is the process of creating new features or transforming existing ones from raw data to improve machine learning model performance.",
      tags: ['Feature Engineering', 'Data Science', 'Machine Learning']
    }
  ]
}

const generateComprehensiveQuestions = async () => {
  try {
    await connectToDatabase()

    // Get all categories
    const categories = await QuizCategory.find({ isActive: true }).lean()
    console.log(`📋 Found ${categories.length} categories`)

    for (const category of categories) {
      console.log(`\n📝 Processing: ${category.name}`)

      // Clear existing questions for this category
      await QuizQuestion.deleteMany({ categoryId: category._id })
      console.log(`  🗑️ Cleared existing questions`)

      // Generate 20 questions for this category
      const questions = generateQuestionsForCategory(category)

      if (questions.length > 0) {
        await QuizQuestion.insertMany(questions)
        console.log(`  ✅ Created ${questions.length} questions`)
      }
    }

    const totalQuestions = await QuizQuestion.countDocuments()
    console.log(`\n🎉 All categories updated!`)
    console.log(`📊 Total questions: ${totalQuestions}`)

    process.exit(0)

  } catch (error) {
    console.error('❌ Error:', error)
    process.exit(1)
  }
}

const generateQuestionsForCategory = (category) => {
  const categoryName = category.name.toLowerCase()
  const difficulty = category.difficulty

  // Determine question set based on category
  let questionSet = []

  if (categoryName.includes('frontend') || categoryName.includes('ui') || categoryName.includes('css') || categoryName.includes('html')) {
    questionSet = questionSets.frontend
  } else if (categoryName.includes('backend') || categoryName.includes('api') || categoryName.includes('server')) {
    questionSet = questionSets.backend
  } else if (categoryName.includes('ux') || categoryName.includes('design')) {
    questionSet = questionSets.uiux
  } else if (categoryName.includes('data') || categoryName.includes('science') || categoryName.includes('ml')) {
    questionSet = questionSets.datascience
  } else {
    // Generate generic questions for other categories
    return generateGenericQuestions(category)
  }

  // Create 20 questions by repeating and modifying the question set
  const questions = []
  for (let i = 0; i < 20; i++) {
    const template = questionSet[i % questionSet.length]
    questions.push({
      categoryId: category._id,
      question: template.question,
      options: template.options,
      correctAnswer: template.correctAnswer,
      explanation: template.explanation,
      difficulty: difficulty,
      points: difficulty === 'beginner' ? 5 : difficulty === 'intermediate' ? 10 : 15,
      tags: template.tags,
      isActive: true
    })
  }

  return questions
}

const generateGenericQuestions = (category) => {
  // Generic questions for categories without specific question sets
  const skills = category.skills || [category.name]
  const questions = []

  for (let i = 0; i < 20; i++) {
    const skill = skills[i % skills.length]
    questions.push({
      categoryId: category._id,
      question: `What is an important aspect of ${skill}?`,
      options: [
        `Understanding ${skill} fundamentals`,
        `Applying ${skill} best practices`,
        `Staying updated with ${skill} trends`,
        'All of the above'
      ],
      correctAnswer: 3,
      explanation: `${skill} requires understanding fundamentals, applying best practices, and staying current with industry trends.`,
      difficulty: category.difficulty,
      points: category.difficulty === 'beginner' ? 5 : category.difficulty === 'intermediate' ? 10 : 15,
      tags: [skill, 'Fundamentals', 'Best Practices'],
      isActive: true
    })
  }

  return questions
}

// Run the script
generateComprehensiveQuestions()

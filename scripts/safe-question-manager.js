// Safe script for managing questions without data erasure
const mongoose = require('mongoose')

// Database connection
const connectToDatabase = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb+srv://winstonmhango23:<EMAIL>/?retryWrites=true&w=majority&appName=jobs'
    await mongoose.connect(mongoUri)
    console.log('✅ Connected to MongoDB')
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message)
    process.exit(1)
  }
}

// Schemas
const quizCategorySchema = new mongoose.Schema({
  name: String,
  slug: String,
  description: String,
  icon: String,
  color: String,
  skills: [String],
  difficulty: String,
  estimatedTime: Number,
  totalQuestions: Number,
  passingScore: Number,
  isActive: Boolean
}, { timestamps: true })

const quizQuestionSchema = new mongoose.Schema({
  categoryId: mongoose.Schema.Types.ObjectId,
  question: String,
  options: [String],
  correctAnswer: Number,
  explanation: String,
  difficulty: String,
  points: Number,
  tags: [String],
  isActive: Boolean
}, { timestamps: true })

const QuizCategory = mongoose.model('QuizCategory', quizCategorySchema)
const QuizQuestion = mongoose.model('QuizQuestion', quizQuestionSchema)

// Safe question management functions
const safeQuestionManager = {
  // Add questions to a specific category without affecting others
  async addQuestionsToCategory(categoryName, questions) {
    try {
      const category = await QuizCategory.findOne({ 
        $or: [{ name: categoryName }, { slug: categoryName.toLowerCase().replace(/\s+/g, '-') }]
      })
      
      if (!category) {
        throw new Error(`Category '${categoryName}' not found`)
      }
      
      console.log(`📝 Adding ${questions.length} questions to category: ${category.name}`)
      
      // Only clear questions for THIS specific category
      await QuizQuestion.deleteMany({ categoryId: category._id })
      console.log(`🗑️ Cleared existing questions for ${category.name} only`)
      
      // Add new questions
      const questionsToInsert = questions.map(q => ({
        categoryId: category._id,
        question: q.question,
        options: q.options,
        correctAnswer: q.correctAnswer,
        explanation: q.explanation,
        difficulty: q.difficulty || category.difficulty,
        points: q.points || (category.difficulty === 'beginner' ? 5 : category.difficulty === 'intermediate' ? 10 : 15),
        tags: q.tags || [],
        isActive: true
      }))
      
      await QuizQuestion.insertMany(questionsToInsert)
      console.log(`✅ Added ${questionsToInsert.length} questions to ${category.name}`)
      
      return {
        category: category.name,
        questionsAdded: questionsToInsert.length
      }
      
    } catch (error) {
      console.error(`❌ Error adding questions to ${categoryName}:`, error.message)
      throw error
    }
  },

  // Get current state of all categories and their question counts
  async getCategoriesStatus() {
    try {
      const categories = await QuizCategory.find({ isActive: true }).lean()
      const status = []
      
      for (const category of categories) {
        const questionCount = await QuizQuestion.countDocuments({ 
          categoryId: category._id,
          isActive: true 
        })
        
        status.push({
          name: category.name,
          id: category._id.toString(),
          difficulty: category.difficulty,
          questions: questionCount,
          targetQuestions: category.totalQuestions || 20
        })
      }
      
      return status
    } catch (error) {
      console.error('❌ Error getting categories status:', error.message)
      throw error
    }
  },

  // Backup questions for a specific category
  async backupCategoryQuestions(categoryName) {
    try {
      const category = await QuizCategory.findOne({ 
        $or: [{ name: categoryName }, { slug: categoryName.toLowerCase().replace(/\s+/g, '-') }]
      })
      
      if (!category) {
        throw new Error(`Category '${categoryName}' not found`)
      }
      
      const questions = await QuizQuestion.find({ categoryId: category._id }).lean()
      
      const backup = {
        category: {
          name: category.name,
          id: category._id.toString()
        },
        questions: questions.map(q => ({
          question: q.question,
          options: q.options,
          correctAnswer: q.correctAnswer,
          explanation: q.explanation,
          difficulty: q.difficulty,
          points: q.points,
          tags: q.tags,
          isActive: q.isActive
        })),
        backupDate: new Date().toISOString()
      }
      
      // Save backup to file
      const fs = require('fs')
      const backupFileName = `backup-${category.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}.json`
      fs.writeFileSync(backupFileName, JSON.stringify(backup, null, 2))
      
      console.log(`💾 Backup saved: ${backupFileName}`)
      console.log(`📊 Backed up ${questions.length} questions from ${category.name}`)
      
      return backupFileName
      
    } catch (error) {
      console.error(`❌ Error backing up ${categoryName}:`, error.message)
      throw error
    }
  },

  // Restore questions from backup
  async restoreFromBackup(backupFileName) {
    try {
      const fs = require('fs')
      const backup = JSON.parse(fs.readFileSync(backupFileName, 'utf8'))
      
      const category = await QuizCategory.findById(backup.category.id)
      if (!category) {
        throw new Error(`Category with ID '${backup.category.id}' not found`)
      }
      
      console.log(`🔄 Restoring ${backup.questions.length} questions to ${category.name}`)
      
      // Clear existing questions for this category only
      await QuizQuestion.deleteMany({ categoryId: category._id })
      
      // Restore questions
      const questionsToInsert = backup.questions.map(q => ({
        categoryId: category._id,
        ...q
      }))
      
      await QuizQuestion.insertMany(questionsToInsert)
      
      console.log(`✅ Restored ${questionsToInsert.length} questions to ${category.name}`)
      
      return {
        category: category.name,
        questionsRestored: questionsToInsert.length
      }
      
    } catch (error) {
      console.error(`❌ Error restoring from backup:`, error.message)
      throw error
    }
  }
}

// Command line interface
const runCommand = async () => {
  try {
    await connectToDatabase()
    
    const command = process.argv[2]
    const arg1 = process.argv[3]
    const arg2 = process.argv[4]
    
    switch (command) {
      case 'status':
        console.log('📊 CATEGORIES STATUS REPORT')
        console.log('=' .repeat(50))
        const status = await safeQuestionManager.getCategoriesStatus()
        status.forEach(cat => {
          const statusIcon = cat.questions >= cat.targetQuestions ? '✅' : '⚠️'
          console.log(`${statusIcon} ${cat.name}: ${cat.questions}/${cat.targetQuestions} questions (${cat.difficulty})`)
        })
        console.log('\n📈 Total Questions:', status.reduce((sum, cat) => sum + cat.questions, 0))
        break
        
      case 'backup':
        if (!arg1) {
          console.log('❌ Usage: node safe-question-manager.js backup <categoryName>')
          process.exit(1)
        }
        await safeQuestionManager.backupCategoryQuestions(arg1)
        break
        
      case 'restore':
        if (!arg1) {
          console.log('❌ Usage: node safe-question-manager.js restore <backupFileName>')
          process.exit(1)
        }
        await safeQuestionManager.restoreFromBackup(arg1)
        break
        
      case 'add':
        if (!arg1 || !arg2) {
          console.log('❌ Usage: node safe-question-manager.js add <categoryName> <questionsJsonFile>')
          process.exit(1)
        }
        const fs = require('fs')
        const questions = JSON.parse(fs.readFileSync(arg2, 'utf8'))
        await safeQuestionManager.addQuestionsToCategory(arg1, questions)
        break
        
      default:
        console.log('📖 SAFE QUESTION MANAGER')
        console.log('=' .repeat(30))
        console.log('Available commands:')
        console.log('  status                           - Show current status of all categories')
        console.log('  backup <categoryName>            - Backup questions for a category')
        console.log('  restore <backupFileName>         - Restore questions from backup')
        console.log('  add <categoryName> <jsonFile>    - Add questions to a category')
        console.log('')
        console.log('Examples:')
        console.log('  node safe-question-manager.js status')
        console.log('  node safe-question-manager.js backup "Python Intermediate"')
        console.log('  node safe-question-manager.js restore backup-python-intermediate-1234567890.json')
        break
    }
    
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Error:', error.message)
    process.exit(1)
  }
}

// Export for use as module
module.exports = safeQuestionManager

// Run if called directly
if (require.main === module) {
  runCommand()
}

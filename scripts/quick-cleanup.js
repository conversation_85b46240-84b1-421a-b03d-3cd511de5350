// Quick Database Cleanup Script
// Deletes test users and companies without confirmation prompts
// Perfect for automated testing and development

const mongoose = require('mongoose')

// Database connection
const connectToDatabase = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb+srv://winstonmhango23:<EMAIL>/?retryWrites=true&w=majority&appName=jobs'
    await mongoose.connect(mongoUri)
    console.log('✅ Connected to MongoDB')
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message)
    process.exit(1)
  }
}

// Simplified schemas for deletion
const UserSchema = new mongoose.Schema({
  email: String,
  role: String,
  companyId: { type: mongoose.Schema.Types.ObjectId, ref: 'Company' },
  profile: {
    firstName: String,
    lastName: String
  }
}, { timestamps: true })

const CompanySchema = new mongoose.Schema({
  name: String,
  slug: String,
  admins: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }]
}, { timestamps: true })

const ClientSchema = new mongoose.Schema({
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
}, { timestamps: true })

const User = mongoose.model('User', UserSchema)
const Company = mongoose.model('Company', CompanySchema)
const Client = mongoose.model('Client', ClientSchema)

// Quick cleanup function
const quickCleanup = async () => {
  console.log('🧹 Quick Database Cleanup')
  console.log('========================')
  
  try {
    await connectToDatabase()
    
    // Show current stats
    const userCount = await User.countDocuments()
    const companyCount = await Company.countDocuments()
    const clientCount = await Client.countDocuments()
    
    console.log(`\n📊 Before cleanup:`)
    console.log(`   Users: ${userCount}`)
    console.log(`   Companies: ${companyCount}`)
    console.log(`   Clients: ${clientCount}`)
    
    const session = await mongoose.startSession()
    
    try {
      await session.startTransaction()
      
      // Delete test data (emails/names containing 'test')
      const testUsers = await User.find({
        $or: [
          { email: { $regex: /test/i } },
          { 'profile.firstName': { $regex: /test/i } },
          { 'profile.lastName': { $regex: /test/i } }
        ]
      }, null, { session })

      const testUserIds = testUsers.map(user => user._id)

      // Delete clients for test users
      const clientDeleteResult = await Client.deleteMany(
        { user: { $in: testUserIds } },
        { session }
      )

      // Delete test companies
      const companyDeleteResult = await Company.deleteMany(
        { name: { $regex: /test/i } },
        { session }
      )

      // Delete test users
      const userDeleteResult = await User.deleteMany(
        { _id: { $in: testUserIds } },
        { session }
      )
      
      await session.commitTransaction()
      
      console.log(`\n🗑️ Cleanup results:`)
      console.log(`   Deleted ${userDeleteResult.deletedCount} test users`)
      console.log(`   Deleted ${companyDeleteResult.deletedCount} test companies`)
      console.log(`   Deleted ${clientDeleteResult.deletedCount} test clients`)
      
    } catch (error) {
      await session.abortTransaction()
      throw error
    } finally {
      await session.endSession()
    }
    
    // Show final stats
    const finalUserCount = await User.countDocuments()
    const finalCompanyCount = await Company.countDocuments()
    const finalClientCount = await Client.countDocuments()
    
    console.log(`\n📊 After cleanup:`)
    console.log(`   Users: ${finalUserCount}`)
    console.log(`   Companies: ${finalCompanyCount}`)
    console.log(`   Clients: ${finalClientCount}`)
    
    console.log('\n✅ Quick cleanup completed successfully!')
    
  } catch (error) {
    console.error('❌ Cleanup failed:', error.message)
  } finally {
    await mongoose.disconnect()
    console.log('👋 Disconnected from database')
    process.exit(0)
  }
}

// Handle command line arguments
const args = process.argv.slice(2)

if (args.includes('--all')) {
  // Delete everything
  const deleteAll = async () => {
    console.log('🧹 Deleting ALL data (use with extreme caution!)')
    console.log('===============================================')
    
    try {
      await connectToDatabase()
      
      const session = await mongoose.startSession()
      
      try {
        await session.startTransaction()
        
        const clientDeleteResult = await Client.deleteMany({}, { session })
        const companyDeleteResult = await Company.deleteMany({}, { session })
        const userDeleteResult = await User.deleteMany({}, { session })
        
        await session.commitTransaction()
        
        console.log(`\n🗑️ Deleted ALL data:`)
        console.log(`   Users: ${userDeleteResult.deletedCount}`)
        console.log(`   Companies: ${companyDeleteResult.deletedCount}`)
        console.log(`   Clients: ${clientDeleteResult.deletedCount}`)
        
      } catch (error) {
        await session.abortTransaction()
        throw error
      } finally {
        await session.endSession()
      }
      
      console.log('\n✅ Complete cleanup finished!')
      
    } catch (error) {
      console.error('❌ Complete cleanup failed:', error.message)
    } finally {
      await mongoose.disconnect()
      console.log('👋 Disconnected from database')
      process.exit(0)
    }
  }
  
  deleteAll()
} else if (args.includes('--stats')) {
  // Show stats only
  const showStats = async () => {
    console.log('📊 Database Statistics')
    console.log('=====================')
    
    try {
      await connectToDatabase()
      
      const userCount = await User.countDocuments()
      const companyCount = await Company.countDocuments()
      const clientCount = await Client.countDocuments()
      
      console.log(`\n📈 Current counts:`)
      console.log(`   Users: ${userCount}`)
      console.log(`   Companies: ${companyCount}`)
      console.log(`   Clients: ${clientCount}`)
      
      if (userCount > 0) {
        const usersByRole = await User.aggregate([
          { $group: { _id: '$role', count: { $sum: 1 } } },
          { $sort: { _id: 1 } }
        ])
        
        console.log(`\n👥 Users by role:`)
        usersByRole.forEach(role => {
          console.log(`   ${role._id}: ${role.count}`)
        })
      }
      
      if (companyCount > 0) {
        const companies = await Company.find()
          .select('name createdAt')
          .sort({ createdAt: -1 })
          .limit(10)
          .lean()
        
        console.log(`\n🏢 Recent companies:`)
        companies.forEach(company => {
          console.log(`   ${company.name} (${company.createdAt.toISOString().split('T')[0]})`)
        })
      }
      
      // Count test data
      const testUserCount = await User.countDocuments({
        $or: [
          { email: { $regex: /test/i } },
          { 'profile.firstName': { $regex: /test/i } },
          { 'profile.lastName': { $regex: /test/i } }
        ]
      })
      
      const testCompanyCount = await Company.countDocuments({
        name: { $regex: /test/i }
      })
      
      console.log(`\n🧪 Test data:`)
      console.log(`   Test users: ${testUserCount}`)
      console.log(`   Test companies: ${testCompanyCount}`)
      
    } catch (error) {
      console.error('❌ Stats failed:', error.message)
    } finally {
      await mongoose.disconnect()
      console.log('\n👋 Disconnected from database')
      process.exit(0)
    }
  }
  
  showStats()
} else {
  // Default: quick cleanup of test data
  quickCleanup()
}

// Usage examples:
// node scripts/quick-cleanup.js           # Delete test data only
// node scripts/quick-cleanup.js --all     # Delete everything (dangerous!)
// node scripts/quick-cleanup.js --stats   # Show database statistics

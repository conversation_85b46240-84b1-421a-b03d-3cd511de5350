// Script to verify all categories and their question counts
const mongoose = require('mongoose')

// Database connection
const connectToDatabase = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb+srv://winstonmhango23:<EMAIL>/?retryWrites=true&w=majority&appName=jobs'
    await mongoose.connect(mongoUri)
    console.log('✅ Connected to MongoDB')
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message)
    process.exit(1)
  }
}

// Schemas
const quizCategorySchema = new mongoose.Schema({
  name: String,
  slug: String,
  description: String,
  icon: String,
  color: String,
  skills: [String],
  difficulty: String,
  estimatedTime: Number,
  totalQuestions: Number,
  passingScore: Number,
  isActive: Boolean
}, { timestamps: true })

const quizQuestionSchema = new mongoose.Schema({
  categoryId: mongoose.Schema.Types.ObjectId,
  question: String,
  options: [String],
  correctAnswer: Number,
  explanation: String,
  difficulty: String,
  points: Number,
  tags: [String],
  isActive: Boolean
}, { timestamps: true })

const QuizCategory = mongoose.model('QuizCategory', quizCategorySchema)
const QuizQuestion = mongoose.model('QuizQuestion', quizQuestionSchema)

const verifyAllQuestions = async () => {
  try {
    await connectToDatabase()
    
    console.log('📊 QUIZ SYSTEM VERIFICATION REPORT')
    console.log('=' .repeat(50))
    
    // Get all categories
    const categories = await QuizCategory.find({ isActive: true }).lean()
    console.log(`\n📋 Total Categories: ${categories.length}`)
    
    let totalQuestions = 0
    
    for (const category of categories) {
      const questionCount = await QuizQuestion.countDocuments({ 
        categoryId: category._id,
        isActive: true 
      })
      
      totalQuestions += questionCount
      
      console.log(`\n📝 ${category.name}`)
      console.log(`   Icon: ${category.icon}`)
      console.log(`   Difficulty: ${category.difficulty}`)
      console.log(`   Skills: ${category.skills.join(', ')}`)
      console.log(`   Questions: ${questionCount}`)
      console.log(`   Estimated Time: ${category.estimatedTime} minutes`)
      console.log(`   Passing Score: ${category.passingScore}%`)
      
      // Show sample questions
      const sampleQuestions = await QuizQuestion.find({ 
        categoryId: category._id,
        isActive: true 
      }).limit(2).lean()
      
      if (sampleQuestions.length > 0) {
        console.log(`   Sample Questions:`)
        sampleQuestions.forEach((q, index) => {
          console.log(`     ${index + 1}. ${q.question.substring(0, 60)}...`)
        })
      }
    }
    
    console.log('\n' + '=' .repeat(50))
    console.log(`📊 SUMMARY:`)
    console.log(`   Total Categories: ${categories.length}`)
    console.log(`   Total Questions: ${totalQuestions}`)
    console.log(`   Average Questions per Category: ${Math.round(totalQuestions / categories.length)}`)
    
    // Breakdown by difficulty
    const difficultyBreakdown = {}
    for (const category of categories) {
      const difficulty = category.difficulty
      if (!difficultyBreakdown[difficulty]) {
        difficultyBreakdown[difficulty] = 0
      }
      difficultyBreakdown[difficulty]++
    }
    
    console.log(`\n📈 Categories by Difficulty:`)
    Object.entries(difficultyBreakdown).forEach(([difficulty, count]) => {
      console.log(`   ${difficulty}: ${count} categories`)
    })
    
    console.log('\n✅ Verification completed successfully!')
    
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Error during verification:', error)
    process.exit(1)
  }
}

// Run the verification
verifyAllQuestions()

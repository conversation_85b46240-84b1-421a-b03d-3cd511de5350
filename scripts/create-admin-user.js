// Create Admin User Script
const mongoose = require('mongoose')
const bcrypt = require('bcryptjs')
const fs = require('fs')
const path = require('path')

// Load environment variables manually
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '..', '.env.local')
    const envContent = fs.readFileSync(envPath, 'utf8')

    envContent.split('\n').forEach(line => {
      const trimmedLine = line.trim()
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const equalIndex = trimmedLine.indexOf('=')
        if (equalIndex > 0) {
          const key = trimmedLine.substring(0, equalIndex).trim()
          const value = trimmedLine.substring(equalIndex + 1).trim()
          if (key && value) {
            process.env[key] = value
          }
        }
      }
    })
  } catch (error) {
    console.log('⚠️  Could not load .env.local file:', error.message)
  }
}

loadEnvFile()

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/jobportal'

console.log('🔗 Using MongoDB URI:', MONGODB_URI.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'))

// Location Schema
const LocationSchema = new mongoose.Schema({
  city: { type: String, trim: true },
  state: { type: String, trim: true },
  country: { type: String, trim: true },
  coordinates: {
    type: [Number]
  }
}, { _id: false })

// Profile Schema
const ProfileSchema = new mongoose.Schema({
  firstName: {
    type: String,
    required: [true, 'First name is required'],
    trim: true,
    maxlength: [50, 'First name cannot exceed 50 characters']
  },
  lastName: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true,
    maxlength: [50, 'Last name cannot exceed 50 characters']
  },
  avatar: String,
  phone: {
    type: String,
    trim: true,
    validate: {
      validator: function(v) {
        return !v || /^\+?[\d\s\-\(\)]+$/.test(v)
      },
      message: 'Invalid phone number format'
    }
  },
  location: {
    type: LocationSchema,
    default: () => ({})
  }
}, { _id: false })

// Preferences Schema
const PreferencesSchema = new mongoose.Schema({
  emailNotifications: {
    type: Boolean,
    default: true
  },
  jobAlerts: {
    type: Boolean,
    default: true
  },
  marketingEmails: {
    type: Boolean,
    default: false
  },
  theme: {
    type: String,
    enum: ['light', 'dark', 'system'],
    default: 'system'
  }
}, { _id: false })

// User Schema
const UserSchema = new mongoose.Schema({
  email: {
    type: String,
    required: [true, 'Email is required'],
    lowercase: true,
    trim: true,
    validate: {
      validator: function(v) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v)
      },
      message: 'Invalid email format'
    }
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [8, 'Password must be at least 8 characters long'],
    select: false
  },
  role: {
    type: String,
    enum: ['admin', 'company_admin', 'recruiter', 'job_seeker'],
    default: 'job_seeker',
    required: true
  },
  profile: {
    type: ProfileSchema,
    required: true
  },
  preferences: {
    type: PreferencesSchema,
    default: () => ({})
  },
  companyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Company'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: String,
  passwordResetToken: String,
  passwordResetExpires: Date,
  lastLogin: Date
}, {
  timestamps: true,
  toJSON: { 
    virtuals: true,
    transform: function(doc, ret) {
      delete ret.password
      delete ret.passwordResetToken
      delete ret.emailVerificationToken
      return ret
    }
  },
  toObject: { virtuals: true }
})

// Indexes
UserSchema.index({ email: 1 }, { unique: true })
UserSchema.index({ role: 1, isActive: 1 })

// Pre-save middleware to hash password
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next()
  
  try {
    const salt = await bcrypt.genSalt(12)
    this.password = await bcrypt.hash(this.password, salt)
    next()
  } catch (error) {
    next(error)
  }
})

// Password comparison method
UserSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password)
}

const User = mongoose.models.User || mongoose.model('User', UserSchema)

async function createAdminUser() {
  try {
    console.log('🔌 Connecting to MongoDB...')
    await mongoose.connect(MONGODB_URI)
    console.log('✅ Connected to MongoDB')

    const adminEmail = '<EMAIL>'
    const adminPassword = '@JobproAdmin2025'

    // Check if admin user already exists
    const existingAdmin = await User.findOne({ email: adminEmail })
    
    if (existingAdmin) {
      console.log('⚠️  Admin user already exists:')
      console.log(`   ID: ${existingAdmin._id}`)
      console.log(`   Email: ${existingAdmin.email}`)
      console.log(`   Role: ${existingAdmin.role}`)
      console.log(`   Active: ${existingAdmin.isActive}`)
      console.log(`   Email Verified: ${existingAdmin.isEmailVerified}`)
      
      // Update password if needed
      console.log('🔄 Updating admin user password...')
      existingAdmin.password = adminPassword
      existingAdmin.isEmailVerified = true
      existingAdmin.isActive = true
      await existingAdmin.save()
      console.log('✅ Admin user password updated')
      
      return existingAdmin
    }

    // Create admin user
    console.log('👤 Creating admin user...')
    const adminUser = new User({
      email: adminEmail,
      password: adminPassword,
      role: 'admin',
      profile: {
        firstName: 'JobPro',
        lastName: 'Admin',
        location: {
          city: 'System',
          country: 'Platform'
        }
      },
      preferences: {
        emailNotifications: true,
        jobAlerts: false,
        marketingEmails: false,
        theme: 'system'
      },
      isActive: true,
      isEmailVerified: true
    })

    await adminUser.save()
    console.log('✅ Admin user created successfully:')
    console.log(`   ID: ${adminUser._id}`)
    console.log(`   Email: ${adminUser.email}`)
    console.log(`   Role: ${adminUser.role}`)
    console.log(`   Name: ${adminUser.profile.firstName} ${adminUser.profile.lastName}`)

    return adminUser

  } catch (error) {
    console.error('❌ Error creating admin user:', error)
    throw error
  } finally {
    await mongoose.disconnect()
    console.log('🔌 Disconnected from MongoDB')
  }
}

async function main() {
  console.log('🔐 Creating Admin User for System Administration')
  console.log('===============================================')
  console.log('📧 Email: <EMAIL>')
  console.log('🔑 Password: @JobproAdmin2025')
  console.log('👤 Role: admin')
  console.log('')
  
  try {
    const adminUser = await createAdminUser()
    
    console.log('\n🎯 Admin User Setup Complete!')
    console.log('=============================')
    console.log('✅ Admin user is ready for testing')
    console.log('✅ Email verification enabled')
    console.log('✅ Account activated')
    console.log('')
    console.log('📋 Login Credentials:')
    console.log('   Email: <EMAIL>')
    console.log('   Password: @JobproAdmin2025')
    console.log('')
    console.log('🔗 Admin Access URLs:')
    console.log('   Login: http://localhost:3000/login')
    console.log('   Admin Dashboard: http://localhost:3000/admin')
    console.log('   User Management: http://localhost:3000/admin/users')
    console.log('   Company Management: http://localhost:3000/admin/companies')
    console.log('   Analytics: http://localhost:3000/admin/analytics')
    console.log('')
    console.log('🚀 Ready for admin testing!')
    
  } catch (error) {
    console.error('\n❌ Admin user creation failed:', error.message)
    process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  main()
}

module.exports = { createAdminUser }

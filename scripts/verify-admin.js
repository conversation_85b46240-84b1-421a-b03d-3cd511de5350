// Verify Admin User Script
const mongoose = require('mongoose')
const fs = require('fs')
const path = require('path')

// Load environment variables manually
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '..', '.env.local')
    const envContent = fs.readFileSync(envPath, 'utf8')

    envContent.split('\n').forEach(line => {
      const trimmedLine = line.trim()
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const equalIndex = trimmedLine.indexOf('=')
        if (equalIndex > 0) {
          const key = trimmedLine.substring(0, equalIndex).trim()
          const value = trimmedLine.substring(equalIndex + 1).trim()
          if (key && value) {
            process.env[key] = value
          }
        }
      }
    })
  } catch (error) {
    console.log('⚠️  Could not load .env.local file:', error.message)
  }
}

loadEnvFile()

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/jobportal'

// Simple User Schema for verification
const userSchema = new mongoose.Schema({
  email: String,
  role: String,
  profile: {
    firstName: String,
    lastName: String
  },
  isActive: Boolean,
  isEmailVerified: Boolean,
  createdAt: Date
})

const User = mongoose.model('User', userSchema)

async function verifyAdminUser() {
  try {
    console.log('🔍 Verifying admin user creation...')
    await mongoose.connect(MONGODB_URI)
    
    const admin = await User.findOne({ email: '<EMAIL>' })
    
    if (admin) {
      console.log('✅ Admin user verified successfully:')
      console.log(`   ID: ${admin._id}`)
      console.log(`   Email: ${admin.email}`)
      console.log(`   Role: ${admin.role}`)
      console.log(`   Name: ${admin.profile.firstName} ${admin.profile.lastName}`)
      console.log(`   Active: ${admin.isActive}`)
      console.log(`   Email Verified: ${admin.isEmailVerified}`)
      console.log(`   Created: ${admin.createdAt}`)
      
      // Check if it's actually an admin role
      if (admin.role === 'admin') {
        console.log('✅ Admin role confirmed - ready for admin panel access')
      } else {
        console.log('⚠️  Warning: User role is not admin')
      }
    } else {
      console.log('❌ Admin user not found in database')
    }
    
    await mongoose.disconnect()
    
  } catch (error) {
    console.error('❌ Error verifying admin user:', error.message)
  }
}

verifyAdminUser()

import { connectToDatabase } from '@/lib/database/connection'
import { CourseCategory, Course } from '@/lib/models/course.model'
import { User } from '@/lib/models/user.model'
import { Types } from 'mongoose'

async function seedCourses() {
  try {
    await connectToDatabase()
    console.log('Connected to database')

    // Clear existing data
    await CourseCategory.deleteMany({})
    await Course.deleteMany({})
    console.log('Cleared existing course data')

    // Create sample categories
    const categories = await CourseCategory.insertMany([
      {
        name: 'Web Development',
        description: 'Learn modern web development technologies and frameworks',
        icon: '💻',
        color: '#3B82F6',
        slug: 'web-development',
        isActive: true,
        sortOrder: 1
      },
      {
        name: 'Data Science',
        description: 'Master data analysis, machine learning, and AI',
        icon: '📊',
        color: '#10B981',
        slug: 'data-science',
        isActive: true,
        sortOrder: 2
      },
      {
        name: 'Mobile Development',
        description: 'Build mobile apps for iOS and Android',
        icon: '📱',
        color: '#8B5CF6',
        slug: 'mobile-development',
        isActive: true,
        sortOrder: 3
      },
      {
        name: 'Design',
        description: 'UI/UX design, graphic design, and creative skills',
        icon: '🎨',
        color: '#F59E0B',
        slug: 'design',
        isActive: true,
        sortOrder: 4
      },
      {
        name: 'DevOps & Cloud',
        description: 'Cloud computing, DevOps, and infrastructure',
        icon: '☁️',
        color: '#EF4444',
        slug: 'devops-cloud',
        isActive: true,
        sortOrder: 5
      },
      {
        name: 'Cybersecurity',
        description: 'Information security and ethical hacking',
        icon: '🔒',
        color: '#6366F1',
        slug: 'cybersecurity',
        isActive: true,
        sortOrder: 6
      }
    ])

    console.log('Created course categories')

    // Find or create a sample instructor
    let instructor = await User.findOne({ email: '<EMAIL>' })
    if (!instructor) {
      instructor = new User({
        name: 'John Instructor',
        email: '<EMAIL>',
        password: 'hashedpassword',
        role: 'instructor',
        isVerified: true
      })
      await instructor.save()
    }

    // Create sample courses
    const courses = [
      {
        title: 'Complete React Developer Course',
        description: 'Master React.js from beginner to advanced level with hands-on projects and real-world applications. Learn hooks, context, Redux, and modern React patterns.',
        shortDescription: 'Master React.js with hands-on projects and real-world applications',
        slug: 'complete-react-developer-course',
        thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800&h=600&fit=crop',
        previewVideo: 'https://example.com/preview-react.mp4',
        categoryId: categories[0]._id, // Web Development
        instructorId: instructor._id,
        level: 'intermediate',
        duration: 480, // 8 hours
        price: 89.99,
        originalPrice: 129.99,
        currency: 'USD',
        language: 'English',
        tags: ['React', 'JavaScript', 'Frontend', 'Web Development'],
        skills: ['React.js', 'JSX', 'Hooks', 'State Management', 'Component Design'],
        requirements: ['Basic JavaScript knowledge', 'HTML & CSS fundamentals', 'Node.js installed'],
        whatYouWillLearn: [
          'Build modern React applications',
          'Master React hooks and context',
          'Implement state management with Redux',
          'Create responsive user interfaces',
          'Deploy React apps to production'
        ],
        targetAudience: ['Frontend developers', 'JavaScript developers', 'Web development students'],
        isPublished: true,
        isFeatured: true,
        isPremium: false,
        rating: 4.8,
        totalRatings: 1250,
        totalStudents: 8500,
        totalLessons: 45,
        lastUpdated: new Date()
      },
      {
        title: 'Python for Data Science Masterclass',
        description: 'Comprehensive Python course covering data analysis, visualization, machine learning, and statistical analysis using pandas, numpy, matplotlib, and scikit-learn.',
        shortDescription: 'Learn Python for data analysis and machine learning',
        slug: 'python-data-science-masterclass',
        thumbnail: 'https://images.unsplash.com/photo-1526379879527-8559ecfcaec0?w=800&h=600&fit=crop',
        categoryId: categories[1]._id, // Data Science
        instructorId: instructor._id,
        level: 'beginner',
        duration: 720, // 12 hours
        price: 99.99,
        originalPrice: 149.99,
        currency: 'USD',
        language: 'English',
        tags: ['Python', 'Data Science', 'Machine Learning', 'Analytics'],
        skills: ['Python Programming', 'Pandas', 'NumPy', 'Matplotlib', 'Scikit-learn'],
        requirements: ['Basic programming knowledge', 'High school mathematics'],
        whatYouWillLearn: [
          'Master Python programming fundamentals',
          'Analyze data with pandas and numpy',
          'Create visualizations with matplotlib',
          'Build machine learning models',
          'Perform statistical analysis'
        ],
        targetAudience: ['Data analysts', 'Python beginners', 'Business analysts'],
        isPublished: true,
        isFeatured: true,
        isPremium: false,
        rating: 4.7,
        totalRatings: 980,
        totalStudents: 6200,
        totalLessons: 52,
        lastUpdated: new Date()
      },
      {
        title: 'iOS App Development with Swift',
        description: 'Build professional iOS applications using Swift and Xcode. Learn iOS development patterns, UI design, Core Data, networking, and App Store deployment.',
        shortDescription: 'Create professional iOS apps with Swift and Xcode',
        slug: 'ios-app-development-swift',
        thumbnail: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=800&h=600&fit=crop',
        categoryId: categories[2]._id, // Mobile Development
        instructorId: instructor._id,
        level: 'intermediate',
        duration: 600, // 10 hours
        price: 119.99,
        originalPrice: 179.99,
        currency: 'USD',
        language: 'English',
        tags: ['iOS', 'Swift', 'Mobile', 'Xcode'],
        skills: ['Swift Programming', 'iOS SDK', 'Xcode', 'UIKit', 'Core Data'],
        requirements: ['Mac computer with Xcode', 'Basic programming knowledge'],
        whatYouWillLearn: [
          'Master Swift programming language',
          'Build iOS user interfaces',
          'Implement data persistence',
          'Handle networking and APIs',
          'Publish apps to App Store'
        ],
        targetAudience: ['Mobile developers', 'iOS enthusiasts', 'App entrepreneurs'],
        isPublished: true,
        isFeatured: false,
        isPremium: true,
        rating: 4.6,
        totalRatings: 750,
        totalStudents: 4100,
        totalLessons: 38,
        lastUpdated: new Date()
      },
      {
        title: 'UI/UX Design Fundamentals',
        description: 'Learn the principles of user interface and user experience design. Master design thinking, prototyping, user research, and design tools like Figma and Adobe XD.',
        shortDescription: 'Master UI/UX design principles and modern design tools',
        slug: 'ui-ux-design-fundamentals',
        thumbnail: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=800&h=600&fit=crop',
        categoryId: categories[3]._id, // Design
        instructorId: instructor._id,
        level: 'beginner',
        duration: 360, // 6 hours
        price: 79.99,
        originalPrice: 119.99,
        currency: 'USD',
        language: 'English',
        tags: ['UI Design', 'UX Design', 'Figma', 'Design Thinking'],
        skills: ['Design Principles', 'Figma', 'Prototyping', 'User Research', 'Wireframing'],
        requirements: ['No prior design experience needed', 'Computer with internet access'],
        whatYouWillLearn: [
          'Understand design principles',
          'Create wireframes and prototypes',
          'Conduct user research',
          'Master Figma and design tools',
          'Build a design portfolio'
        ],
        targetAudience: ['Aspiring designers', 'Developers', 'Product managers'],
        isPublished: true,
        isFeatured: false,
        isPremium: false,
        rating: 4.5,
        totalRatings: 650,
        totalStudents: 3800,
        totalLessons: 28,
        lastUpdated: new Date()
      },
      {
        title: 'AWS Cloud Practitioner Complete Guide',
        description: 'Comprehensive AWS cloud computing course covering core services, architecture, security, pricing, and best practices. Prepare for AWS Cloud Practitioner certification.',
        shortDescription: 'Master AWS cloud computing and prepare for certification',
        slug: 'aws-cloud-practitioner-guide',
        thumbnail: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=800&h=600&fit=crop',
        categoryId: categories[4]._id, // DevOps & Cloud
        instructorId: instructor._id,
        level: 'beginner',
        duration: 540, // 9 hours
        price: 94.99,
        originalPrice: 139.99,
        currency: 'USD',
        language: 'English',
        tags: ['AWS', 'Cloud Computing', 'DevOps', 'Certification'],
        skills: ['AWS Services', 'Cloud Architecture', 'Security', 'Cost Management'],
        requirements: ['Basic IT knowledge', 'AWS account (free tier)'],
        whatYouWillLearn: [
          'Understand AWS core services',
          'Design cloud architectures',
          'Implement security best practices',
          'Manage costs and billing',
          'Pass AWS certification exam'
        ],
        targetAudience: ['IT professionals', 'System administrators', 'Cloud beginners'],
        isPublished: true,
        isFeatured: true,
        isPremium: false,
        rating: 4.9,
        totalRatings: 1100,
        totalStudents: 7200,
        totalLessons: 42,
        lastUpdated: new Date()
      },
      {
        title: 'Ethical Hacking & Penetration Testing',
        description: 'Learn ethical hacking techniques, penetration testing methodologies, and cybersecurity fundamentals. Master tools like Kali Linux, Metasploit, and Nmap.',
        shortDescription: 'Learn ethical hacking and penetration testing techniques',
        slug: 'ethical-hacking-penetration-testing',
        thumbnail: 'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?w=800&h=600&fit=crop',
        categoryId: categories[5]._id, // Cybersecurity
        instructorId: instructor._id,
        level: 'advanced',
        duration: 780, // 13 hours
        price: 149.99,
        originalPrice: 199.99,
        currency: 'USD',
        language: 'English',
        tags: ['Ethical Hacking', 'Penetration Testing', 'Cybersecurity', 'Kali Linux'],
        skills: ['Penetration Testing', 'Vulnerability Assessment', 'Network Security', 'Kali Linux'],
        requirements: ['Networking fundamentals', 'Linux basics', 'Virtual machine software'],
        whatYouWillLearn: [
          'Master ethical hacking techniques',
          'Perform penetration testing',
          'Use security testing tools',
          'Identify vulnerabilities',
          'Write professional reports'
        ],
        targetAudience: ['Security professionals', 'IT administrators', 'Cybersecurity students'],
        isPublished: true,
        isFeatured: false,
        isPremium: true,
        rating: 4.7,
        totalRatings: 890,
        totalStudents: 5100,
        totalLessons: 56,
        lastUpdated: new Date()
      }
    ]

    await Course.insertMany(courses)
    console.log('Created sample courses')

    console.log('✅ Course seeding completed successfully!')
    console.log(`Created ${categories.length} categories and ${courses.length} courses`)

  } catch (error) {
    console.error('❌ Error seeding courses:', error)
  } finally {
    process.exit(0)
  }
}

// Run the seed function
seedCourses()

// Test Complete Authentication Flow
const fs = require('fs')
const path = require('path')

// Load environment variables manually
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '..', '.env.local')
    const envContent = fs.readFileSync(envPath, 'utf8')

    envContent.split('\n').forEach(line => {
      const trimmedLine = line.trim()
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const equalIndex = trimmedLine.indexOf('=')
        if (equalIndex > 0) {
          const key = trimmedLine.substring(0, equalIndex).trim()
          const value = trimmedLine.substring(equalIndex + 1).trim()
          if (key && value) {
            process.env[key] = value
          }
        }
      }
    })
  } catch (error) {
    console.log('⚠️  Could not load .env.local file:', error.message)
  }
}

loadEnvFile()

async function testCompleteAuthFlow() {
  const baseUrl = 'http://localhost:3000'
  
  console.log('🔐 Testing Complete Authentication Flow')
  console.log('======================================')
  
  try {
    // Step 1: Login as admin
    console.log('\n1. 🔑 Logging in as admin...')
    const loginResponse = await fetch(`${baseUrl}/api/v1/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: '@JobproAdmin2025'
      })
    })

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.status} ${loginResponse.statusText}`)
    }

    const loginData = await loginResponse.json()
    console.log('✅ Login successful')
    console.log('   Response structure:', Object.keys(loginData))
    console.log('   Success:', loginData.success)
    console.log('   Has data:', !!loginData.data)
    
    if (loginData.data) {
      console.log('   Data keys:', Object.keys(loginData.data))
      console.log('   Has user:', !!loginData.data.user)
      console.log('   Has tokens:', !!loginData.data.tokens)
      
      if (loginData.data.user) {
        console.log('   User email:', loginData.data.user.email)
        console.log('   User role:', loginData.data.user.role)
        console.log('   User ID:', loginData.data.user.id)
      }
    }

    const token = loginData.data?.tokens?.accessToken
    if (!token) {
      throw new Error('No access token received from login')
    }

    console.log('   Token (first 50 chars):', token.substring(0, 50) + '...')

    // Step 2: Test /api/v1/auth/me endpoint
    console.log('\n2. 👤 Testing /api/v1/auth/me endpoint...')
    const meResponse = await fetch(`${baseUrl}/api/v1/auth/me`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!meResponse.ok) {
      console.log('❌ /api/v1/auth/me failed:', meResponse.status, meResponse.statusText)
      const errorData = await meResponse.text()
      console.log('   Error details:', errorData)
      return
    }

    const meData = await meResponse.json()
    console.log('✅ /api/v1/auth/me successful')
    console.log('   Response structure:', Object.keys(meData))
    console.log('   Success:', meData.success)
    console.log('   Has data:', !!meData.data)
    
    if (meData.data) {
      console.log('   Data keys:', Object.keys(meData.data))
      console.log('   Has user:', !!meData.data.user)
      
      if (meData.data.user) {
        console.log('   User email:', meData.data.user.email)
        console.log('   User role:', meData.data.user.role)
        console.log('   User ID:', meData.data.user.id)
        console.log('   Full user object keys:', Object.keys(meData.data.user))
      }
    }

    // Step 3: Simulate what the frontend should receive
    console.log('\n3. 🔍 Frontend Processing Simulation...')
    
    // This is what getCurrentUser should return
    let frontendUser
    if (meData.success && meData.data && meData.data.user) {
      frontendUser = meData.data.user
      console.log('✅ Correct API response structure detected')
    } else {
      frontendUser = meData.user || meData.data || meData
      console.log('⚠️  Using fallback response structure')
    }
    
    console.log('   Frontend user object:', {
      email: frontendUser?.email,
      role: frontendUser?.role,
      id: frontendUser?.id,
      hasProfile: !!frontendUser?.profile
    })

    // Step 4: Role check simulation
    console.log('\n4. 🛡️  Role Check Simulation...')
    const requiredRole = 'admin'
    const userRole = frontendUser?.role
    
    console.log('   Required role:', requiredRole)
    console.log('   User role:', userRole)
    console.log('   Role match:', userRole === requiredRole ? '✅ YES' : '❌ NO')
    
    if (userRole === requiredRole) {
      console.log('   ✅ User should have access to admin panel')
    } else {
      console.log('   ❌ User should be denied access to admin panel')
      console.log('   ⚠️  This is the source of the "Access Denied" error!')
    }

    console.log('\n🎯 Authentication Flow Analysis:')
    console.log('=================================')
    console.log('✅ Login API: Working correctly')
    console.log('✅ Token generation: Working correctly')
    console.log('✅ /api/v1/auth/me: Working correctly')
    console.log('✅ User data structure: Correct')
    console.log('✅ Role assignment: Correct')
    
    if (userRole === requiredRole) {
      console.log('✅ Role verification: Should work')
      console.log('\n🚀 The authentication flow should work correctly!')
      console.log('   If you\'re still seeing "Access Denied", check:')
      console.log('   1. Browser console for detailed logs')
      console.log('   2. Clear browser localStorage and try again')
      console.log('   3. Check if there are any timing issues in the frontend')
    } else {
      console.log('❌ Role verification: FAILED')
      console.log('\n🔧 Issue found: Role mismatch')
      console.log('   Expected: admin')
      console.log('   Received:', userRole)
    }

  } catch (error) {
    console.error('\n❌ Authentication flow test failed:', error.message)
    console.log('\n🔧 Troubleshooting steps:')
    console.log('1. Ensure development server is running (npm run dev)')
    console.log('2. Verify admin user exists in database')
    console.log('3. Check API endpoints are properly configured')
    console.log('4. Verify JWT secrets are set in environment variables')
  }
}

// Run the test
if (require.main === module) {
  testCompleteAuthFlow()
}

module.exports = { testCompleteAuthFlow }

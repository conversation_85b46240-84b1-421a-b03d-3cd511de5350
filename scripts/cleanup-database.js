// Database Cleanup Script
// This script safely deletes users and companies from the database
// Use with caution - this will permanently delete data!

const mongoose = require('mongoose')
const readline = require('readline')

// Database connection
const connectToDatabase = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/jobportal'
    await mongoose.connect(mongoUri)
    console.log('✅ Connected to MongoDB')
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message)
    process.exit(1)
  }
}

// User Schema (simplified for deletion)
const UserSchema = new mongoose.Schema({
  email: String,
  role: String,
  companyId: { type: mongoose.Schema.Types.ObjectId, ref: 'Company' },
  profile: {
    firstName: String,
    lastName: String
  }
}, { timestamps: true })

// Company Schema (simplified for deletion)
const CompanySchema = new mongoose.Schema({
  name: String,
  slug: String,
  admins: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
  teamMembers: [{
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    role: String
  }]
}, { timestamps: true })

// Client Schema (simplified for deletion)
const ClientSchema = new mongoose.Schema({
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
}, { timestamps: true })

const User = mongoose.model('User', UserSchema)
const Company = mongoose.model('Company', CompanySchema)
const Client = mongoose.model('Client', ClientSchema)

// Create readline interface for user confirmation
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

const askQuestion = (question) => {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer)
    })
  })
}

// Display current database statistics
const showDatabaseStats = async () => {
  console.log('\n📊 Current Database Statistics:')
  console.log('================================')
  
  const userCount = await User.countDocuments()
  const companyCount = await Company.countDocuments()
  const clientCount = await Client.countDocuments()
  
  console.log(`👥 Users: ${userCount}`)
  console.log(`🏢 Companies: ${companyCount}`)
  console.log(`📋 Clients: ${clientCount}`)
  
  if (userCount > 0) {
    console.log('\n👥 User Breakdown:')
    const usersByRole = await User.aggregate([
      { $group: { _id: '$role', count: { $sum: 1 } } },
      { $sort: { _id: 1 } }
    ])
    usersByRole.forEach(role => {
      console.log(`   ${role._id}: ${role.count}`)
    })
  }
  
  if (companyCount > 0) {
    console.log('\n🏢 Recent Companies:')
    const recentCompanies = await Company.find()
      .select('name createdAt')
      .sort({ createdAt: -1 })
      .limit(5)
      .lean()
    
    recentCompanies.forEach(company => {
      console.log(`   ${company.name} (${company.createdAt.toISOString().split('T')[0]})`)
    })
  }
}

// Delete all users and related data
const deleteAllUsers = async () => {
  console.log('\n🗑️ Deleting all users and related data...')
  
  const session = await mongoose.startSession()
  
  try {
    await session.startTransaction()
    
    // Delete clients first (they reference users)
    const clientDeleteResult = await Client.deleteMany({}, { session })
    console.log(`   Deleted ${clientDeleteResult.deletedCount} client profiles`)
    
    // Delete users
    const userDeleteResult = await User.deleteMany({}, { session })
    console.log(`   Deleted ${userDeleteResult.deletedCount} users`)
    
    await session.commitTransaction()
    console.log('✅ All users and related data deleted successfully')
    
  } catch (error) {
    await session.abortTransaction()
    console.error('❌ Error deleting users:', error.message)
    throw error
  } finally {
    await session.endSession()
  }
}

// Delete all companies and update user references
const deleteAllCompanies = async () => {
  console.log('\n🗑️ Deleting all companies and updating user references...')
  
  const session = await mongoose.startSession()
  
  try {
    await session.startTransaction()
    
    // Update users to remove company references
    const userUpdateResult = await User.updateMany(
      { companyId: { $exists: true } },
      { $unset: { companyId: 1 }, $set: { role: 'job_seeker' } },
      { session }
    )
    console.log(`   Updated ${userUpdateResult.modifiedCount} users to remove company references`)
    
    // Delete companies
    const companyDeleteResult = await Company.deleteMany({}, { session })
    console.log(`   Deleted ${companyDeleteResult.deletedCount} companies`)
    
    await session.commitTransaction()
    console.log('✅ All companies deleted and user references updated successfully')
    
  } catch (error) {
    await session.abortTransaction()
    console.error('❌ Error deleting companies:', error.message)
    throw error
  } finally {
    await session.endSession()
  }
}

// Delete everything
const deleteEverything = async () => {
  console.log('\n🗑️ Deleting ALL data (users, companies, clients)...')
  
  const session = await mongoose.startSession()
  
  try {
    await session.startTransaction()
    
    // Delete in order: clients, companies, users
    const clientDeleteResult = await Client.deleteMany({}, { session })
    console.log(`   Deleted ${clientDeleteResult.deletedCount} client profiles`)
    
    const companyDeleteResult = await Company.deleteMany({}, { session })
    console.log(`   Deleted ${companyDeleteResult.deletedCount} companies`)
    
    const userDeleteResult = await User.deleteMany({}, { session })
    console.log(`   Deleted ${userDeleteResult.deletedCount} users`)
    
    await session.commitTransaction()
    console.log('✅ All data deleted successfully')
    
  } catch (error) {
    await session.abortTransaction()
    console.error('❌ Error deleting data:', error.message)
    throw error
  } finally {
    await session.endSession()
  }
}

// Delete test data only
const deleteTestData = async () => {
  console.log('\n🗑️ Deleting test data only...')
  
  const session = await mongoose.startSession()
  
  try {
    await session.startTransaction()
    
    // Delete test users (emails containing 'test' or companies with 'test' in name)
    const testUsers = await User.find({
      $or: [
        { email: { $regex: /test/i } },
        { 'profile.firstName': { $regex: /test/i } },
        { 'profile.lastName': { $regex: /test/i } }
      ]
    }, { session })
    
    const testUserIds = testUsers.map(user => user._id)
    
    // Delete clients for test users
    const clientDeleteResult = await Client.deleteMany(
      { user: { $in: testUserIds } },
      { session }
    )
    console.log(`   Deleted ${clientDeleteResult.deletedCount} test client profiles`)
    
    // Delete test companies
    const companyDeleteResult = await Company.deleteMany(
      { name: { $regex: /test/i } },
      { session }
    )
    console.log(`   Deleted ${companyDeleteResult.deletedCount} test companies`)
    
    // Delete test users
    const userDeleteResult = await User.deleteMany(
      { _id: { $in: testUserIds } },
      { session }
    )
    console.log(`   Deleted ${userDeleteResult.deletedCount} test users`)
    
    await session.commitTransaction()
    console.log('✅ Test data deleted successfully')
    
  } catch (error) {
    await session.abortTransaction()
    console.error('❌ Error deleting test data:', error.message)
    throw error
  } finally {
    await session.endSession()
  }
}

// Main cleanup function
const runCleanup = async () => {
  console.log('🧹 Database Cleanup Script')
  console.log('==========================')
  console.log('⚠️  WARNING: This script will permanently delete data!')
  console.log('Make sure you have a backup if needed.\n')
  
  try {
    await connectToDatabase()
    await showDatabaseStats()
    
    console.log('\nCleanup Options:')
    console.log('1. Delete all users only')
    console.log('2. Delete all companies only')
    console.log('3. Delete everything (users + companies + clients)')
    console.log('4. Delete test data only')
    console.log('5. Show stats and exit')
    console.log('6. Exit without changes')
    
    const choice = await askQuestion('\nEnter your choice (1-6): ')
    
    switch (choice) {
      case '1':
        const confirmUsers = await askQuestion('Are you sure you want to delete ALL USERS? (yes/no): ')
        if (confirmUsers.toLowerCase() === 'yes') {
          await deleteAllUsers()
        } else {
          console.log('Operation cancelled.')
        }
        break
        
      case '2':
        const confirmCompanies = await askQuestion('Are you sure you want to delete ALL COMPANIES? (yes/no): ')
        if (confirmCompanies.toLowerCase() === 'yes') {
          await deleteAllCompanies()
        } else {
          console.log('Operation cancelled.')
        }
        break
        
      case '3':
        const confirmEverything = await askQuestion('Are you sure you want to delete EVERYTHING? (yes/no): ')
        if (confirmEverything.toLowerCase() === 'yes') {
          await deleteEverything()
        } else {
          console.log('Operation cancelled.')
        }
        break
        
      case '4':
        const confirmTest = await askQuestion('Delete test data only? (yes/no): ')
        if (confirmTest.toLowerCase() === 'yes') {
          await deleteTestData()
        } else {
          console.log('Operation cancelled.')
        }
        break
        
      case '5':
        console.log('Database statistics displayed above.')
        break
        
      case '6':
        console.log('Exiting without changes.')
        break
        
      default:
        console.log('Invalid choice. Exiting.')
        break
    }
    
    // Show final stats
    if (['1', '2', '3', '4'].includes(choice)) {
      console.log('\n📊 Final Database Statistics:')
      await showDatabaseStats()
    }
    
  } catch (error) {
    console.error('❌ Cleanup failed:', error.message)
  } finally {
    rl.close()
    await mongoose.disconnect()
    console.log('\n👋 Disconnected from database')
    process.exit(0)
  }
}

// Run the cleanup
runCleanup().catch(console.error)

// Test Admin Authentication Script
const fs = require('fs')
const path = require('path')

// Load environment variables manually
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '..', '.env.local')
    const envContent = fs.readFileSync(envPath, 'utf8')

    envContent.split('\n').forEach(line => {
      const trimmedLine = line.trim()
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const equalIndex = trimmedLine.indexOf('=')
        if (equalIndex > 0) {
          const key = trimmedLine.substring(0, equalIndex).trim()
          const value = trimmedLine.substring(equalIndex + 1).trim()
          if (key && value) {
            process.env[key] = value
          }
        }
      }
    })
  } catch (error) {
    console.log('⚠️  Could not load .env.local file:', error.message)
  }
}

loadEnvFile()

async function testAdminAuth() {
  const baseUrl = 'http://localhost:3000'
  
  console.log('🔐 Testing Admin Authentication Flow')
  console.log('===================================')
  
  try {
    // Step 1: Login as admin
    console.log('1. 🔑 Logging in as admin...')
    const loginResponse = await fetch(`${baseUrl}/api/v1/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: '@JobproAdmin2025'
      })
    })

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.status} ${loginResponse.statusText}`)
    }

    const loginData = await loginResponse.json()
    console.log('✅ Login successful')
    console.log('   User:', loginData.data?.user?.email)
    console.log('   Role:', loginData.data?.user?.role)
    console.log('   Access Token present:', !!loginData.data?.tokens?.accessToken)
    console.log('   Refresh Token present:', !!loginData.data?.tokens?.refreshToken)

    const token = loginData.data?.tokens?.accessToken
    if (!token) {
      throw new Error('No access token received from login')
    }

    // Step 2: Test /api/v1/auth/me endpoint
    console.log('\n2. 👤 Testing /api/v1/auth/me endpoint...')
    const meResponse = await fetch(`${baseUrl}/api/v1/auth/me`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!meResponse.ok) {
      console.log('❌ /api/v1/auth/me failed:', meResponse.status, meResponse.statusText)
      const errorData = await meResponse.text()
      console.log('   Error details:', errorData)
    } else {
      const meData = await meResponse.json()
      console.log('✅ /api/v1/auth/me successful')
      console.log('   User:', meData.data?.user?.email)
      console.log('   Role:', meData.data?.user?.role)
    }

    // Step 3: Test admin-specific endpoints
    console.log('\n3. 🛡️  Testing admin endpoints...')
    
    // Test database endpoint
    console.log('   Testing /api/v1/admin/database...')
    const dbResponse = await fetch(`${baseUrl}/api/v1/admin/database`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!dbResponse.ok) {
      console.log('   ❌ Database endpoint failed:', dbResponse.status, dbResponse.statusText)
      const errorData = await dbResponse.text()
      console.log('   Error details:', errorData)
    } else {
      const dbData = await dbResponse.json()
      console.log('   ✅ Database endpoint successful')
      console.log('   Collections found:', Object.keys(dbData.data?.collections || {}))
    }

    // Test users endpoint
    console.log('   Testing /api/v1/admin/users...')
    const usersResponse = await fetch(`${baseUrl}/api/v1/admin/users`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!usersResponse.ok) {
      console.log('   ❌ Users endpoint failed:', usersResponse.status, usersResponse.statusText)
      const errorData = await usersResponse.text()
      console.log('   Error details:', errorData)
    } else {
      const usersData = await usersResponse.json()
      console.log('   ✅ Users endpoint successful')
      console.log('   Users found:', usersData.data?.users?.length || 0)
    }

    // Test companies endpoint
    console.log('   Testing /api/v1/admin/companies...')
    const companiesResponse = await fetch(`${baseUrl}/api/v1/admin/companies`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!companiesResponse.ok) {
      console.log('   ❌ Companies endpoint failed:', companiesResponse.status, companiesResponse.statusText)
      const errorData = await companiesResponse.text()
      console.log('   Error details:', errorData)
    } else {
      const companiesData = await companiesResponse.json()
      console.log('   ✅ Companies endpoint successful')
      console.log('   Companies found:', companiesData.data?.companies?.length || 0)
    }

    console.log('\n🎯 Admin Authentication Test Summary:')
    console.log('=====================================')
    console.log('✅ Admin user login: SUCCESS')
    console.log('✅ Token generation: SUCCESS')
    console.log('✅ Admin role verification: SUCCESS')
    console.log('')
    console.log('🔗 Ready to test in browser:')
    console.log('   1. Go to: http://localhost:3000/login')
    console.log('   2. Login with: <EMAIL> / @JobproAdmin2025')
    console.log('   3. Should redirect to: http://localhost:3000/admin')
    console.log('')
    console.log('🚀 Admin system is ready for testing!')

  } catch (error) {
    console.error('\n❌ Admin authentication test failed:', error.message)
    console.log('\n🔧 Troubleshooting steps:')
    console.log('1. Ensure development server is running (npm run dev)')
    console.log('2. Verify admin user exists in database')
    console.log('3. Check API endpoints are properly configured')
    console.log('4. Verify JWT secrets are set in environment variables')
  }
}

// Run the test
if (require.main === module) {
  testAdminAuth()
}

module.exports = { testAdminAuth }

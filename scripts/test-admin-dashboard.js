// Test Admin Dashboard API
const fs = require('fs')
const path = require('path')

// Load environment variables manually
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '..', '.env.local')
    const envContent = fs.readFileSync(envPath, 'utf8')

    envContent.split('\n').forEach(line => {
      const trimmedLine = line.trim()
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const equalIndex = trimmedLine.indexOf('=')
        if (equalIndex > 0) {
          const key = trimmedLine.substring(0, equalIndex).trim()
          const value = trimmedLine.substring(equalIndex + 1).trim()
          if (key && value) {
            process.env[key] = value
          }
        }
      }
    })
  } catch (error) {
    console.log('⚠️  Could not load .env.local file:', error.message)
  }
}

loadEnvFile()

async function testAdminDashboard() {
  const baseUrl = 'http://localhost:3000'
  
  console.log('📊 Testing Admin Dashboard API')
  console.log('==============================')
  
  try {
    // Step 1: Login as admin
    console.log('1. 🔑 Logging in as admin...')
    const loginResponse = await fetch(`${baseUrl}/api/v1/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: '@JobproAdmin2025'
      })
    })

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.status} ${loginResponse.statusText}`)
    }

    const loginData = await loginResponse.json()
    const token = loginData.data?.tokens?.accessToken
    
    if (!token) {
      throw new Error('No access token received from login')
    }

    console.log('✅ Login successful')
    console.log('   Token (first 50 chars):', token.substring(0, 50) + '...')

    // Step 2: Test admin dashboard endpoint
    console.log('\n2. 📊 Testing /api/v1/admin/dashboard endpoint...')
    const dashboardResponse = await fetch(`${baseUrl}/api/v1/admin/dashboard`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    console.log('   Response status:', dashboardResponse.status, dashboardResponse.statusText)

    if (!dashboardResponse.ok) {
      const errorText = await dashboardResponse.text()
      console.log('❌ Dashboard endpoint failed')
      console.log('   Error details:', errorText.substring(0, 200) + '...')
      return
    }

    const dashboardData = await dashboardResponse.json()
    console.log('✅ Dashboard endpoint successful')
    console.log('   Response structure:', Object.keys(dashboardData))
    
    if (dashboardData.success && dashboardData.data) {
      console.log('   Data keys:', Object.keys(dashboardData.data))
      
      if (dashboardData.data.stats) {
        console.log('   Stats:', {
          totalUsers: dashboardData.data.stats.totalUsers,
          totalCompanies: dashboardData.data.stats.totalCompanies,
          totalJobs: dashboardData.data.stats.totalJobs,
          totalApplications: dashboardData.data.stats.totalApplications
        })
      }
    }

    console.log('\n🎯 Admin Dashboard Test Summary:')
    console.log('=================================')
    console.log('✅ Admin login: SUCCESS')
    console.log('✅ Dashboard API: SUCCESS')
    console.log('✅ Data retrieval: SUCCESS')
    console.log('')
    console.log('🚀 Admin dashboard should now work without errors!')

  } catch (error) {
    console.error('\n❌ Admin dashboard test failed:', error.message)
    console.log('\n🔧 Troubleshooting steps:')
    console.log('1. Ensure development server is running (npm run dev)')
    console.log('2. Check if admin dashboard API endpoint exists')
    console.log('3. Verify admin user has correct permissions')
    console.log('4. Check browser console for detailed error logs')
  }
}

// Run the test
if (require.main === module) {
  testAdminDashboard()
}

module.exports = { testAdminDashboard }

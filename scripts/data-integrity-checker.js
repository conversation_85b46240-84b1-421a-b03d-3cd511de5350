// Comprehensive data integrity checker for quiz system
const mongoose = require('mongoose')

// Database connection
const connectToDatabase = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb+srv://winstonmhango23:<EMAIL>/?retryWrites=true&w=majority&appName=jobs'
    await mongoose.connect(mongoUri)
    console.log('✅ Connected to MongoDB')
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message)
    process.exit(1)
  }
}

// Schemas
const quizCategorySchema = new mongoose.Schema({
  name: String,
  slug: String,
  description: String,
  icon: String,
  color: String,
  skills: [String],
  difficulty: String,
  estimatedTime: Number,
  totalQuestions: Number,
  passingScore: Number,
  isActive: Boolean
}, { timestamps: true })

const quizQuestionSchema = new mongoose.Schema({
  categoryId: mongoose.Schema.Types.ObjectId,
  question: String,
  options: [String],
  correctAnswer: Number,
  explanation: String,
  difficulty: String,
  points: Number,
  tags: [String],
  isActive: Boolean
}, { timestamps: true })

const QuizCategory = mongoose.model('QuizCategory', quizCategorySchema)
const QuizQuestion = mongoose.model('QuizQuestion', quizQuestionSchema)

class DataIntegrityChecker {
  constructor() {
    this.issues = []
    this.warnings = []
    this.stats = {}
  }

  async runFullCheck() {
    console.log('🔍 QUIZ SYSTEM DATA INTEGRITY CHECK')
    console.log('=' .repeat(50))
    
    await this.checkCategories()
    await this.checkQuestions()
    await this.checkRelationships()
    await this.checkDataConsistency()
    
    this.generateReport()
  }

  async checkCategories() {
    console.log('\n📋 Checking Categories...')
    
    const categories = await QuizCategory.find({}).lean()
    this.stats.totalCategories = categories.length
    this.stats.activeCategories = categories.filter(c => c.isActive).length
    
    for (const category of categories) {
      // Check required fields
      if (!category.name || category.name.trim().length === 0) {
        this.issues.push(`Category ${category._id}: Missing or empty name`)
      }
      
      if (!category.description || category.description.trim().length === 0) {
        this.issues.push(`Category ${category._id}: Missing or empty description`)
      }
      
      if (!category.difficulty || !['beginner', 'intermediate', 'advanced', 'expert'].includes(category.difficulty)) {
        this.issues.push(`Category ${category._id}: Invalid difficulty level`)
      }
      
      // Check data ranges
      if (category.estimatedTime && (category.estimatedTime < 1 || category.estimatedTime > 120)) {
        this.warnings.push(`Category ${category.name}: Unusual estimated time (${category.estimatedTime} minutes)`)
      }
      
      if (category.passingScore && (category.passingScore < 50 || category.passingScore > 100)) {
        this.issues.push(`Category ${category.name}: Invalid passing score (${category.passingScore}%)`)
      }
      
      // Check for duplicates
      const duplicates = categories.filter(c => c.name === category.name && c._id.toString() !== category._id.toString())
      if (duplicates.length > 0) {
        this.issues.push(`Category ${category.name}: Duplicate name found`)
      }
    }
    
    console.log(`  ✅ Found ${categories.length} categories (${this.stats.activeCategories} active)`)
  }

  async checkQuestions() {
    console.log('\n❓ Checking Questions...')
    
    const questions = await QuizQuestion.find({}).lean()
    this.stats.totalQuestions = questions.length
    this.stats.activeQuestions = questions.filter(q => q.isActive).length
    
    for (const question of questions) {
      // Check required fields
      if (!question.question || question.question.trim().length === 0) {
        this.issues.push(`Question ${question._id}: Missing or empty question text`)
      }
      
      if (!question.options || !Array.isArray(question.options) || question.options.length < 2) {
        this.issues.push(`Question ${question._id}: Must have at least 2 options`)
      }
      
      if (question.correctAnswer === undefined || question.correctAnswer < 0 || question.correctAnswer >= question.options.length) {
        this.issues.push(`Question ${question._id}: Invalid correct answer index`)
      }
      
      if (!question.difficulty || !['beginner', 'intermediate', 'advanced', 'expert'].includes(question.difficulty)) {
        this.issues.push(`Question ${question._id}: Invalid difficulty level`)
      }
      
      // Check data quality
      if (question.question && question.question.length < 10) {
        this.warnings.push(`Question ${question._id}: Very short question text`)
      }
      
      if (question.options) {
        const uniqueOptions = new Set(question.options.map(opt => opt.toLowerCase().trim()))
        if (uniqueOptions.size !== question.options.length) {
          this.warnings.push(`Question ${question._id}: Duplicate answer options`)
        }
      }
      
      if (question.points && (question.points < 1 || question.points > 20)) {
        this.warnings.push(`Question ${question._id}: Unusual points value (${question.points})`)
      }
    }
    
    console.log(`  ✅ Found ${questions.length} questions (${this.stats.activeQuestions} active)`)
  }

  async checkRelationships() {
    console.log('\n🔗 Checking Relationships...')
    
    const categories = await QuizCategory.find({}).lean()
    const questions = await QuizQuestion.find({}).lean()
    
    // Check for orphaned questions
    const categoryIds = new Set(categories.map(c => c._id.toString()))
    const orphanedQuestions = questions.filter(q => !categoryIds.has(q.categoryId.toString()))
    
    if (orphanedQuestions.length > 0) {
      this.issues.push(`Found ${orphanedQuestions.length} orphaned questions (no matching category)`)
    }
    
    // Check category question counts
    const questionCounts = {}
    questions.forEach(q => {
      const catId = q.categoryId.toString()
      questionCounts[catId] = (questionCounts[catId] || 0) + 1
    })
    
    for (const category of categories) {
      const actualCount = questionCounts[category._id.toString()] || 0
      const expectedCount = category.totalQuestions || 0
      
      if (actualCount !== expectedCount) {
        this.warnings.push(`Category ${category.name}: Expected ${expectedCount} questions, found ${actualCount}`)
      }
      
      if (actualCount === 0 && category.isActive) {
        this.warnings.push(`Category ${category.name}: Active category has no questions`)
      }
    }
    
    console.log(`  ✅ Relationship checks completed`)
  }

  async checkDataConsistency() {
    console.log('\n🔄 Checking Data Consistency...')
    
    const categories = await QuizCategory.find({}).lean()
    const questions = await QuizQuestion.find({}).lean()
    
    // Check difficulty distribution
    const difficultyStats = {}
    categories.forEach(c => {
      difficultyStats[c.difficulty] = (difficultyStats[c.difficulty] || 0) + 1
    })
    
    this.stats.difficultyDistribution = difficultyStats
    
    // Check for missing slugs
    const categoriesWithoutSlugs = categories.filter(c => !c.slug)
    if (categoriesWithoutSlugs.length > 0) {
      this.warnings.push(`${categoriesWithoutSlugs.length} categories missing slugs`)
    }
    
    // Check for inconsistent difficulty between categories and questions
    for (const category of categories) {
      const categoryQuestions = questions.filter(q => q.categoryId.toString() === category._id.toString())
      const questionDifficulties = [...new Set(categoryQuestions.map(q => q.difficulty))]
      
      if (questionDifficulties.length > 1) {
        this.warnings.push(`Category ${category.name}: Questions have mixed difficulties (${questionDifficulties.join(', ')})`)
      } else if (questionDifficulties.length === 1 && questionDifficulties[0] !== category.difficulty) {
        this.warnings.push(`Category ${category.name}: Category difficulty (${category.difficulty}) doesn't match question difficulty (${questionDifficulties[0]})`)
      }
    }
    
    console.log(`  ✅ Consistency checks completed`)
  }

  generateReport() {
    console.log('\n📊 DATA INTEGRITY REPORT')
    console.log('=' .repeat(50))
    
    // Summary stats
    console.log('\n📈 Statistics:')
    console.log(`  Total Categories: ${this.stats.totalCategories} (${this.stats.activeCategories} active)`)
    console.log(`  Total Questions: ${this.stats.totalQuestions} (${this.stats.activeQuestions} active)`)
    console.log(`  Average Questions per Category: ${Math.round(this.stats.totalQuestions / this.stats.totalCategories)}`)
    
    if (this.stats.difficultyDistribution) {
      console.log('\n📊 Difficulty Distribution:')
      Object.entries(this.stats.difficultyDistribution).forEach(([difficulty, count]) => {
        console.log(`  ${difficulty}: ${count} categories`)
      })
    }
    
    // Issues
    if (this.issues.length > 0) {
      console.log(`\n❌ CRITICAL ISSUES (${this.issues.length}):`)
      this.issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue}`)
      })
    } else {
      console.log('\n✅ No critical issues found!')
    }
    
    // Warnings
    if (this.warnings.length > 0) {
      console.log(`\n⚠️  WARNINGS (${this.warnings.length}):`)
      this.warnings.forEach((warning, index) => {
        console.log(`  ${index + 1}. ${warning}`)
      })
    } else {
      console.log('\n✅ No warnings!')
    }
    
    // Overall health score
    const totalChecks = this.stats.totalCategories + this.stats.totalQuestions
    const issueScore = Math.max(0, 100 - (this.issues.length * 10))
    const warningScore = Math.max(0, 100 - (this.warnings.length * 2))
    const healthScore = Math.round((issueScore + warningScore) / 2)
    
    console.log(`\n🏥 OVERALL HEALTH SCORE: ${healthScore}/100`)
    
    if (healthScore >= 90) {
      console.log('🟢 Excellent - Your quiz data is in great shape!')
    } else if (healthScore >= 70) {
      console.log('🟡 Good - Minor issues that should be addressed')
    } else if (healthScore >= 50) {
      console.log('🟠 Fair - Several issues need attention')
    } else {
      console.log('🔴 Poor - Significant data integrity issues found')
    }
  }
}

// Run the integrity check
const runIntegrityCheck = async () => {
  try {
    await connectToDatabase()
    
    const checker = new DataIntegrityChecker()
    await checker.runFullCheck()
    
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Error during integrity check:', error.message)
    process.exit(1)
  }
}

// Export for use as module
module.exports = DataIntegrityChecker

// Run if called directly
if (require.main === module) {
  runIntegrityCheck()
}

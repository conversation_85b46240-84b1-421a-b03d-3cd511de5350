// List Users Script
const mongoose = require('mongoose')
const fs = require('fs')
const path = require('path')

// Load environment variables manually
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '..', '.env.local')
    const envContent = fs.readFileSync(envPath, 'utf8')

    envContent.split('\n').forEach(line => {
      const trimmedLine = line.trim()
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const equalIndex = trimmedLine.indexOf('=')
        if (equalIndex > 0) {
          const key = trimmedLine.substring(0, equalIndex).trim()
          const value = trimmedLine.substring(equalIndex + 1).trim()
          if (key && value) {
            process.env[key] = value
          }
        }
      }
    })
  } catch (error) {
    console.log('⚠️  Could not load .env.local file:', error.message)
  }
}

loadEnvFile()

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/jobportal'

// Simple User Schema for listing
const userSchema = new mongoose.Schema({
  email: String,
  role: String,
  profile: {
    firstName: String,
    lastName: String
  },
  isActive: Boolean,
  isEmailVerified: Boolean,
  createdAt: Date
})

const User = mongoose.model('User', userSchema)

async function listUsers() {
  try {
    console.log('📋 Listing users in the system...')
    await mongoose.connect(MONGODB_URI)
    
    const users = await User.find({}).sort({ createdAt: -1 }).limit(10)
    
    console.log('================================')
    console.log(`Found ${users.length} users (showing latest 10):`)
    console.log('================================')
    
    if (users.length === 0) {
      console.log('No users found in the database')
    } else {
      users.forEach((user, index) => {
        console.log(`${index + 1}. ${user.profile.firstName} ${user.profile.lastName}`)
        console.log(`   Email: ${user.email}`)
        console.log(`   Role: ${user.role}`)
        console.log(`   Active: ${user.isActive}`)
        console.log(`   Email Verified: ${user.isEmailVerified}`)
        console.log(`   Created: ${user.createdAt}`)
        console.log('')
      })
      
      // Count by role
      const roleCounts = await User.aggregate([
        { $group: { _id: '$role', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ])
      
      console.log('📊 User Statistics by Role:')
      console.log('===========================')
      roleCounts.forEach(role => {
        console.log(`   ${role._id}: ${role.count} users`)
      })
    }
    
    await mongoose.disconnect()
    
  } catch (error) {
    console.error('❌ Error listing users:', error.message)
  }
}

listUsers()
